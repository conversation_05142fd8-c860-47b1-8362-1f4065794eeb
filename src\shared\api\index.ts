// API exports following project structure
// Your codebase uses direct imports from specific API files, so we only export for convenience
export { ordersApi } from './orders/ordersAPI';
export { deliveriesApi } from './deliveries/deliveriesAPI';
export { customersApi } from './customers/customersAPI';
export { usersApi } from './users/usersAPI';
export { inventoryApi } from './inventory/inventoryAPI';
export { dashboardApi } from './dashboard/dashboardAPI';
export { bulkApi } from './bulk/bulkAPI';
export { productApi } from './inventory/productAPI';

/**
 * Standard API response interface used across the application
 */
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data: T | null;
} 