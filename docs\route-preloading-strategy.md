# Route Preloading Strategy

## Overview

This document outlines the route preloading strategy implemented in the Washolding Internal App to improve perceived performance and reduce loading times when navigating between routes.

## Implementation Details

The preloading strategy uses a combination of techniques to optimize the loading of route components:

1. **Critical Route Identification**: Routes that users are most likely to navigate to are identified and given priority.
2. **Idle Time Utilization**: Uses `requestIdleCallback` (with fallback) to preload routes during browser idle time.
3. **User Role Based Prioritization**: Preloads routes relevant to the user's role.
4. **Navigation History Tracking**: Adapts preloading priorities based on user's navigation patterns.
5. **Non-Blocking Implementation**: Ensures preloading doesn't impact the main UI thread performance.

## Key Components

### Route Preloading Utility (`src/shared/lib/utils/route-preloading.ts`)

- **`preloadRouteComponent`**: Triggers the import of a lazy-loaded component.
- **`getCriticalRoutes`**: Defines routes that should be preloaded, with priority levels.
- **`preloadCriticalRoutes`**: Main function that orchestrates preloading during browser idle time.
- **`routeTracker`**: Maintains history of recently visited routes to optimize future preloading.

### Implementation in Main App Component (`src/app/main.tsx`)

- Initiates preloading after authentication is complete.
- Tracks route changes to optimize future preloading.
- Uses a delay mechanism to ensure initial page load is complete before starting preloading.

## How It Works

1. After authentication, the app waits 2 seconds to ensure the initial route is fully loaded.
2. The app then begins preloading critical routes in the background during browser idle time.
3. Routes are prioritized based on:
   - Base priority defined in `getCriticalRoutes`
   - User's role (e.g., admin sees admin-specific routes)
   - Recent navigation history

4. When a user navigates to a route that's already been preloaded, it loads instantly.
5. The app continuously updates its understanding of user navigation patterns to improve preloading priorities.

## Performance Benefits

- **Faster Navigation**: Subsequent page loads are significantly faster as they're preloaded.
- **Improved Perceived Performance**: Users experience smoother transitions between routes.
- **Resource Efficiency**: Uses browser idle time to load resources, minimizing impact on main UI thread.
- **Adaptive Optimization**: Learns from user behavior to prioritize the most relevant routes.
- **Error Resilience**: Gracefully handles preloading failures without affecting user experience.

## Technical Notes

### Browser Support

- Uses native `requestIdleCallback` with fallback for older browsers.
- No additional polyfills required.

### Testing

- Unit tests verify the preloading functionality works correctly.
- Tests ensure preloading doesn't block the main thread.
- Tests confirm error handling for failed preloads works correctly.

### Monitoring & Debugging

- Development mode includes console logging for preload operations.
- Errors during preloading are silently handled in production but logged in development.

## Future Enhancements

- **Link-based preloading**: Preload routes when user hovers over navigation links.
- **More advanced metrics**: Incorporate metrics like component load times to better prioritize preloading.
- **Connection-aware preloading**: Adjust preloading behavior based on network conditions.
- **Prefetching API integration**: Use the browser's native Prefetch API for improved performance. 