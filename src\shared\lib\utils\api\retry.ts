/**
 * Configuration options for the retry mechanism
 */
export interface RetryOptions {
  /** Maximum number of retry attempts */
  maxRetries?: number;
  
  /** Initial delay in milliseconds before the first retry */
  initialDelay?: number;
  
  /** Maximum delay in milliseconds between retries */
  maxDelay?: number;
  
  /** Backoff factor for exponential backoff (delay = initialDelay * backoffFactor^retryCount) */
  backoffFactor?: number;
  
  /** Function to determine if an error should be retried */
  shouldRetry?: (error: any) => boolean;
  
  /** Optional callback that executes before each retry attempt */
  onRetry?: (error: any, attempt: number) => void;
}

/**
 * Default retry options
 */
export const defaultRetryOptions: RetryOptions = {
  maxRetries: 3,
  initialDelay: 300,
  maxDelay: 5000,
  backoffFactor: 2,
  shouldRetry: (error: any) => {
    // By default, only retry on network errors, timeouts, and certain server errors
    const isNetworkError = !navigator.onLine || 
      error.message?.includes('network') || 
      error.message?.includes('fetch');
    
    const isTimeoutError = error.message?.includes('timeout') || 
      error.code === 'ECONNABORTED';
    
    const isServerError = error.status >= 500 || 
      (error.error?.status >= 500) || 
      error.message?.includes('server error');
      
    return isNetworkError || isTimeoutError || isServerError;
  },
  onRetry: (error, attempt) => {
    console.warn(`Retry attempt ${attempt} after error:`, error);
  }
};

/**
 * Calculate delay with exponential backoff
 */
function calculateDelay(attempt: number, options: Required<RetryOptions>): number {
  const delay = options.initialDelay * Math.pow(options.backoffFactor, attempt);
  return Math.min(delay, options.maxDelay);
}

/**
 * Sleep for the specified duration
 */
async function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Wraps an async function with retry logic
 * @param fn The function to wrap with retry logic
 * @param options Retry configuration options
 * @returns A function that will retry on failure based on the options
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> {
  // Merge provided options with defaults
  const retryOptions: Required<RetryOptions> = {
    ...defaultRetryOptions,
    ...options
  } as Required<RetryOptions>;

  let lastError: any;
  
  for (let attempt = 0; attempt <= retryOptions.maxRetries; attempt++) {
    try {
      // On first attempt (attempt=0), just execute the function
      if (attempt === 0) {
        return await fn();
      }
      
      // For retry attempts, wait before trying again
      const delay = calculateDelay(attempt - 1, retryOptions);
      await sleep(delay);
      
      // Notify via callback if provided
      if (retryOptions.onRetry) {
        retryOptions.onRetry(lastError, attempt);
      }
      
      // Try again
      return await fn();
    } catch (error) {
      lastError = error;
      
      // If we've reached max retries or this error shouldn't be retried, throw
      if (
        attempt >= retryOptions.maxRetries || 
        (retryOptions.shouldRetry && !retryOptions.shouldRetry(error))
      ) {
        throw error;
      }
    }
  }
  
  // This should never be reached due to the throw above
  throw lastError;
}

/**
 * Creates a version of a function with built-in retry logic
 * @param fn The function to wrap with retry logic
 * @param options Retry configuration options
 * @returns A function that will retry on failure based on the options
 */
export function createRetryableFunction<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  options: RetryOptions = {}
): T {
  return (async (...args: Parameters<T>) => {
    return withRetry(() => fn(...args), options);
  }) as T;
} 