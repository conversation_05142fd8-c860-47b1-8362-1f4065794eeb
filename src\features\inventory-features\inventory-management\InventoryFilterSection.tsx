import React from 'react';
import InventoryHeader from './InventoryHeader';
import { FilterSectionSkeleton } from '@/shared/ui/feedback/LoadingSkeleton';

interface InventoryFilterSectionProps {
  isInitialLoading: boolean;
  productCount: number;
  searchTerm: string;
  onSearchChange: (value: string) => void;
  onNewProduct: () => void;
  onOpenFilterModal: () => void;
  activeFilterCount: number;
  stockStatusFilter: string;
  onStockStatusFilterChange: (status: string) => void;
}

const InventoryFilterSection: React.FC<InventoryFilterSectionProps> = ({
  isInitialLoading,
  productCount,
  searchTerm,
  onSearchChange,
  onNewProduct,
  onOpenFilterModal,
  activeFilterCount,
  stockStatusFilter,
  onStockStatusFilterChange
}) => {
  // Use the new FilterSectionSkeleton component for initial loading
  if (isInitialLoading) {
    return (
      <div className="flex-shrink-0 bg-white border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6">
          <FilterSectionSkeleton />
        </div>
      </div>
    );
  }

  return (
    <div className="flex-shrink-0 bg-white border-b border-gray-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6">
        <InventoryHeader
          productCount={productCount}
          searchTerm={searchTerm}
          onSearchChange={onSearchChange}
          onNewProduct={onNewProduct}
          onOpenFilterModal={onOpenFilterModal}
          activeFilterCount={activeFilterCount}
          onStockStatusFilterChange={onStockStatusFilterChange}
          stockStatusFilter={stockStatusFilter}
        />
      </div>
    </div>
  );
};

export default InventoryFilterSection; 