import React from 'react';
import { BuyerGroup, FulfillOrderItem, OrderStatus } from '@/types';
import { ChannelCard } from './ChannelCard';

interface ChannelAccordionProps {
  buyerGroups: BuyerGroup[];
  onStatusChange: (orderId: string, newStatus: OrderStatus, trackingId?: string) => void;
  isUpdating: boolean;
  isConfirming: string | null;
  onDataRefresh: () => void;
  onOpenTrackingModal: (orders: FulfillOrderItem[]) => void;
  selectedOrderIds: Set<string>;
  onToggleSelection: (orderId: string) => void;
}

export const ChannelAccordion: React.FC<ChannelAccordionProps> = ({
  buyerGroups,
  onStatusChange,
  isConfirming,
  onDataRefresh,
  onOpenTrackingModal,
  selectedOrderIds,
  onToggleSelection,
}) => {
  return (
    <div>
      {buyerGroups.map((group) => (
        <div key={group.buyerName} className="mb-4">
          <ChannelCard
            buyerGroup={group}
            onStatusChange={onStatusChange}
            isUpdating={isConfirming === group.buyerName}
            onDataRefresh={onDataRefresh}
            onOpenTrackingModal={onOpenTrackingModal}
            selectedOrderIds={selectedOrderIds}
            onToggleSelection={onToggleSelection}
          />
        </div>
      ))}
    </div>
  );
}; 