import { InventoryReasonCategory } from '@/types';

export type AdjustmentType = 'increase' | 'decrease' | 'set';

export interface StockValidationErrors {
  quantity?: string;
  reason?: string;
  reasonCategory?: string;
}

/**
 * Validates stock adjustment form inputs
 */
export function validateStockForm(
  adjustmentType: AdjustmentType,
  quantityStr: string,
  reason: string,
  reasonCategory: InventoryReasonCategory | string,
  currentStock: number
): { isValid: boolean; errors: StockValidationErrors } {
  const errors: StockValidationErrors = {};
  
  // Validate quantity
  const parsedQuantity = parseFloat(quantityStr);
  if (!quantityStr) {
    errors.quantity = 'Quantity is required';
  } else if (isNaN(parsedQuantity)) {
    errors.quantity = 'Quantity must be a number';
  } else if (parsedQuantity <= 0) {
    errors.quantity = 'Quantity must be greater than zero';
  } else if (!Number.isInteger(parsedQuantity) && adjustmentType !== 'set') {
    errors.quantity = 'Quantity must be a whole number for increase/decrease';
  }
  
  // Validate for negative stock
  const newStock = calculateNewStock(currentStock, adjustmentType, parsedQuantity);
  if (newStock !== null && newStock < 0) {
    if (adjustmentType === 'decrease') {
      errors.quantity = `Cannot reduce stock below zero. Maximum decrease allowed: ${currentStock}`;
    } else if (adjustmentType === 'set') {
      errors.quantity = 'Cannot set stock to a negative value';
    }
  }
  
  // Validate reason
  if (!reason.trim()) {
    errors.reason = 'Reason is required';
  } else if (reason.trim().length < 3) {
    errors.reason = 'Reason must be at least 3 characters';
  }
  
  // Validate reason category
  if (!reasonCategory) {
    errors.reasonCategory = 'Reason category is required';
  }
  
  return { 
    isValid: Object.keys(errors).length === 0,
    errors 
  };
}

/**
 * Calculate new stock value based on adjustment type and quantity
 */
export function calculateNewStock(
  currentStock: number, 
  adjustmentType: AdjustmentType, 
  quantity: number
): number | null {
  if (isNaN(quantity)) {
    return null;
  }
  
  switch (adjustmentType) {
    case 'increase':
      return currentStock + quantity;
    case 'decrease':
      return currentStock - quantity;
    case 'set':
      return quantity;
    default:
      return null;
  }
}

/**
 * Check if adjustment is considered "large" and requires confirmation
 */
export function isLargeAdjustment(
  adjustmentType: AdjustmentType,
  quantity: number,
  currentStock: number
): boolean {
  if (isNaN(quantity) || currentStock === 0) {
    return false;
  }
  
  switch (adjustmentType) {
    case 'decrease':
      // More than 50% of current stock is considered large
      return quantity > currentStock * 0.5;
    case 'increase':
      // More than 100% of current stock (doubling) is considered large
      return quantity > currentStock;
    case 'set':
      // Setting to a value that differs from current by more than 50% is large
      return Math.abs(quantity - currentStock) > currentStock * 0.5;
    default:
      return false;
  }
}

/**
 * Generate confirmation message based on adjustment type
 */
export function getStockAdjustmentConfirmationMessage(
  adjustmentType: AdjustmentType,
  quantity: number,
  currentStock: number,
  productName?: string
): string {
  const newStock = calculateNewStock(currentStock, adjustmentType, quantity);
  
  if (isNaN(quantity) || newStock === null) {
    return 'Are you sure you want to make this large stock adjustment?';
  }
  
  const productInfo = productName ? `for "${productName}"` : '';
  
  switch (adjustmentType) {
    case 'decrease':
      return `You are about to decrease stock ${productInfo} by ${quantity} units (${Math.round(quantity / currentStock * 100)}% of current stock), resulting in ${newStock} units. Do you want to continue?`;
    case 'increase':
      return `You are about to increase stock ${productInfo} by ${quantity} units (${Math.round(quantity / currentStock * 100)}% of current stock), resulting in ${newStock} units. Do you want to continue?`;
    case 'set':
      const change = Math.abs(quantity - currentStock);
      const percentChange = Math.round(change / currentStock * 100);
      return `You are about to set stock ${productInfo} to ${quantity} units (a ${percentChange}% change from current stock). Do you want to continue?`;
    default:
      return 'Are you sure you want to make this large stock adjustment?';
  }
}

/**
 * Calculate average stock for bulk operations
 */
export function calculateAverageStock(products: Array<{ current_stock?: number | null }>): number {
  if (!products.length) return 0;
  
  const totalStock = products.reduce((sum, product) => 
    sum + (product.current_stock || 0), 0);
  
  return totalStock / products.length;
}

/**
 * Calculate minimum stock among products
 */
export function calculateMinimumStock(products: Array<{ current_stock?: number | null }>): number {
  if (!products.length) return 0;
  
  return Math.min(...products.map(p => p.current_stock || 0));
}

/**
 * Check if bulk adjustment is considered large and requires confirmation
 */
export function isBulkLargeAdjustment(
  adjustmentType: AdjustmentType,
  quantity: number,
  averageStock: number,
  minStock: number
): boolean {
  if (isNaN(quantity) || averageStock === 0) {
    return false;
  }
  
  switch (adjustmentType) {
    case 'decrease':
      // More than 25% of average stock is considered large for bulk decreases
      // Also flag if any single product would have less than 2 items left
      return quantity > averageStock * 0.25 || (minStock - quantity < 2 && minStock > 2);
    case 'increase':
      // More than 50% of average stock is considered large for bulk increases
      return quantity > averageStock * 0.5;
    case 'set':
      // Setting to a value that differs from average by more than 50% is large
      return Math.abs(quantity - averageStock) > averageStock * 0.5;
    default:
      return false;
  }
}

/**
 * Generate confirmation message for bulk adjustment
 */
export function getBulkStockAdjustmentConfirmationMessage(
  adjustmentType: AdjustmentType,
  quantity: number,
  averageStock: number,
  productsCount: number,
  productsWithLowStock: number = 0
): string {
  if (isNaN(quantity)) {
    return 'Are you sure you want to make this large bulk stock adjustment?';
  }
  
  switch (adjustmentType) {
    case 'decrease':
      const percentOfAvg = Math.round(quantity / averageStock * 100);
      let message = `You are about to decrease stock by ${quantity} units for ${productsCount} products (${percentOfAvg}% of average stock).`;
      
      if (productsWithLowStock > 0) {
        message += ` Note that ${productsWithLowStock} products will have very low stock after this change.`;
      }
      
      return message + ' Do you want to continue?';
    case 'increase':
      return `You are about to increase stock by ${quantity} units for ${productsCount} products (${Math.round(quantity / averageStock * 100)}% of average stock). Do you want to continue?`;
    case 'set':
      const changePercent = Math.round(Math.abs(quantity - averageStock) / averageStock * 100);
      return `You are about to set stock to ${quantity} units for ALL ${productsCount} products (a ${changePercent}% change from average stock). This will override individual stock levels. Do you want to continue?`;
    default:
      return 'Are you sure you want to make this large bulk stock adjustment?';
  }
} 