import { useState, useEffect, useCallback } from 'react';
import inventoryStockService from '@/shared/lib/services/inventory/inventory-stock-service';
import { InventoryReasonCategory } from '@/types';

export interface StockHistoryEntry {
  id: string;
  date: string;
  timestamp: string;
  adjustmentType: 'increase' | 'decrease' | 'set';
  quantity: number;
  previousStock: number;
  newStock: number;
  reason: string;
  reasonCategory: InventoryReasonCategory;
  orderId?: string | null;
  orderNumber?: string | null;
  userName: string;
}

export interface UseProductStockHistoryReturn {
  historyData: StockHistoryEntry[];
  isLoading: boolean;
  error: string | null;
  retryCount: number;
  retry: () => void;
}

export const useProductStockHistory = (productId: string): UseProductStockHistoryReturn => {
  const [historyData, setHistoryData] = useState<StockHistoryEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [refreshKey, setRefreshKey] = useState(0);
  
  const maxRetries = 3;
  
  const retry = useCallback(() => {
    if (retryCount < maxRetries) {
      setRetryCount(prev => prev + 1);
    }
  }, [retryCount]);

  useEffect(() => {
    if (!productId) return;
    
    const fetchStockHistory = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        // console.log(`Fetching stock movements for product ID: ${productId} (attempt ${retryCount + 1})`);
        
        // Use the service to fetch movements
        const movements = await inventoryStockService.getProductMovements(productId, {
          limit: 20, // Limit to 20 most recent entries
          sortBy: 'timestamp',
          sortDirection: 'desc'
        });
        
        // Transform the data to match our component's data structure
        const formattedHistory: StockHistoryEntry[] = movements.map(item => ({
          id: item.id,
          date: new Date(item.timestamp).toISOString().split('T')[0],
          timestamp: item.timestamp,
          adjustmentType: item.type as 'increase' | 'decrease' | 'set',
          quantity: item.quantity,
          previousStock: item.previous_stock || 0,
          newStock: item.new_stock || 0,
          reason: item.reason,
          reasonCategory: item.reason_category as InventoryReasonCategory,
          orderId: item.order_id,
          orderNumber: item.order_id ? `ORD-${item.order_id.substring(0, 6)}` : null,
          userName: item.user_name || 'System'
        }));
        
        setHistoryData(formattedHistory);
      } catch (err) {
        console.error('Error fetching stock history:', err);
        setError(err instanceof Error ? err.message : 'Failed to load stock history');
        
        // We'll still show empty state on error
        setHistoryData([]);
      } finally {
        setIsLoading(false);
      }
    };
    
    if (productId) {
      fetchStockHistory();
    }
  }, [productId, retryCount, refreshKey]);

  return {
    historyData,
    isLoading,
    error,
    retryCount,
    retry
  };
};

export default useProductStockHistory; 