import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ProductForm from '../ProductForm';
import { ProductStatusEnum } from '@/types';
import * as useProductFormHook from '@/shared/lib/hooks/inventory/products/useProductForm';

// Mock the useProductForm hook
jest.mock('@/shared/lib/hooks/inventory/useProductForm', () => {
  const originalModule = jest.requireActual('@/shared/lib/hooks/inventory/useProductForm');
  return {
    ...originalModule,
    useProductForm: jest.fn()
  };
});

describe('ProductForm', () => {
  // Setup mock implementation for the hook
  const mockHandleChange = jest.fn();
  const mockHandleSubmit = jest.fn();
  const mockValidateForm = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default mock implementation
    (useProductFormHook.useProductForm as jest.Mock).mockReturnValue({
      formData: {
        name: '',
        sku: '',
        description: '',
        status: 'active' as ProductStatusEnum,
        business_unit_id: null,
        unit_price: null,
        unit_cost: null
      },
      errors: {},
      isSubmitting: false,
      submitError: null,
      businessUnits: [
        { id: '1', name: 'Shrimp Products', description: null, is_active: true, created_at: null, updated_at: null },
        { id: '2', name: 'Dropship Products', description: null, is_active: true, created_at: null, updated_at: null }
      ],
      isLoading: false,
      handleChange: mockHandleChange,
      handleSubmit: mockHandleSubmit,
      validateForm: mockValidateForm
    });
  });

  test('renders form with essential fields matching database schema', () => {
    render(<ProductForm />);
    
    // Check essential fields exist
    expect(screen.getByLabelText(/Product Name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/SKU/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Status/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Business Unit/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Unit Price/i)).toBeInTheDocument();
  });

  test('handles form submission', async () => {
    render(<ProductForm />);
    
    // Submit the form
    fireEvent.click(screen.getByRole('button', { name: /Save Product/i }));
    
    // Check that handleSubmit was called
    await waitFor(() => {
      expect(mockHandleSubmit).toHaveBeenCalled();
    });
  });

  test('handles input changes', () => {
    render(<ProductForm />);
    
    // Change product name input
    fireEvent.change(screen.getByLabelText(/Product Name/i), { target: { value: 'Test Product' } });
    
    // Check that handleChange was called with correct values
    expect(mockHandleChange).toHaveBeenCalledWith('name', 'Test Product');
  });

  test('displays success message on successful submission', async () => {
    const onSuccess = jest.fn();
    
    // Mock successful submission
    (useProductFormHook.useProductForm as jest.Mock).mockReturnValue({
      formData: {
        name: 'Test Product',
        sku: 'TEST-123',
        description: '',
        status: 'active' as ProductStatusEnum,
        business_unit_id: null,
        unit_price: 10.99,
        unit_cost: 5.99
      },
      errors: {},
      isSubmitting: false,
      submitError: null,
      businessUnits: [],
      isLoading: false,
      handleChange: mockHandleChange,
      handleSubmit: () => {
        // Simulate successful submission
        onSuccess({
          id: '123',
          name: 'Test Product',
          sku: 'TEST-123',
          status: 'active' as ProductStatusEnum
        });
      },
      validateForm: mockValidateForm
    });
    
    render(<ProductForm onSuccess={onSuccess} />);
    
    // Submit the form
    fireEvent.click(screen.getByRole('button', { name: /Save Product/i }));
    
    // Check that onSuccess was called
    await waitFor(() => {
      expect(onSuccess).toHaveBeenCalled();
    });
  });

  test('displays validation errors', () => {
    // Mock form with validation errors
    (useProductFormHook.useProductForm as jest.Mock).mockReturnValue({
      formData: {
        name: '',
        sku: '',
        description: '',
        status: 'active' as ProductStatusEnum,
        business_unit_id: null,
        unit_price: null,
        unit_cost: null
      },
      errors: {
        name: 'Name is required',
        sku: 'SKU is required',
        unit_price: 'Unit price must be greater than 0'
      },
      isSubmitting: false,
      submitError: null,
      businessUnits: [],
      isLoading: false,
      handleChange: mockHandleChange,
      handleSubmit: mockHandleSubmit,
      validateForm: mockValidateForm
    });
    
    render(<ProductForm />);
    
    // Check that error messages are displayed
    expect(screen.getByText('Name is required')).toBeInTheDocument();
    expect(screen.getByText('SKU is required')).toBeInTheDocument();
    expect(screen.getByText('Unit price must be greater than 0')).toBeInTheDocument();
  });
}); 