import { useState, useCallback } from 'react';

interface UsePanelStateReturn {
  showFullHistory: boolean;
  toggleFullHistory: () => void;
  setShowFullHistory: (show: boolean) => void;
}

export const usePanelState = (): UsePanelStateReturn => {
  const [showFullHistory, setShowFullHistory] = useState(false);

  const toggleFullHistory = useCallback(() => {
    setShowFullHistory(prev => !prev);
  }, []);

  return {
    showFullHistory,
    toggleFullHistory,
    setShowFullHistory
  };
}; 