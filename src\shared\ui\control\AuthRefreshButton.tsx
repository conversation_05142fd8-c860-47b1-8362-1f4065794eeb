import React from 'react';
import { useAuth } from '@/app/providers/AuthProvider';

interface AuthRefreshButtonProps {
  className?: string;
  children?: React.ReactNode;
}

export const AuthRefreshButton: React.FC<AuthRefreshButtonProps> = ({ 
  className = '', 
  children = 'Refresh Auth' 
}) => {
  const { refreshAuth, isLoading } = useAuth();

  const handleRefresh = async () => {
    try {
      await refreshAuth();

    } catch (error) {
      console.error('❌ Failed to refresh auth:', error);
    }
  };

  return (
    <button
      onClick={handleRefresh}
      disabled={isLoading}
      className={`px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
    >
      {isLoading ? 'Refreshing...' : children}
    </button>
  );
}; 