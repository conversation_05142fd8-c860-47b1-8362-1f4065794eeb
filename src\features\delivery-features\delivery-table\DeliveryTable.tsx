import React, { useCallback, useMemo, memo, Profiler, useState, useEffect, useRef } from 'react';
import { DeliveryTrackingViewItem } from '@/types';
import { createProfilerCallback } from '@/shared/lib/utils/performance/profiler-utils';
import LoadingSpinner from '@/shared/ui/feedback/LoadingSpinner'; 
import ErrorMessage from '@/shared/ui/feedback/ErrorMessage';
import { DeliveryRow, DeliveryTableFooter } from './components';
import { formatDate as formatDateUtil, copyToClipboard } from '@/shared/lib/utils/domain/delivery/delivery-table-utils';

interface PaginationProps {
  currentPage: number;
  pageSize: number; // Should be limited to 20 as per requirements
  totalCount: number;
  onPageChange: (page: number) => void;
}

interface DeliveryTableProps {
  deliveries: DeliveryTrackingViewItem[];
  isLoading: boolean;
  error: Error | null;
  selectedRowIds: string[];
  onRowSelection: (rowId: string, isSelected: boolean) => void;
  onSelectAll: (isSelected: boolean) => void;
  onRowClick: (deliveryId: string) => void;
  pagination: PaginationProps;
}

// Custom comparison function for React.memo
const arePropsEqual = (prevProps: DeliveryTableProps, nextProps: DeliveryTableProps): boolean => {
  // Check if loading/error states changed
  if (prevProps.isLoading !== nextProps.isLoading || prevProps.error !== nextProps.error) {
    return false;
  }

  // Check if deliveries array changed (by reference first, then length)
  if (prevProps.deliveries !== nextProps.deliveries) {
    if (prevProps.deliveries.length !== nextProps.deliveries.length) {
      return false;
    }
    // Deep compare deliveries if needed
    for (let i = 0; i < prevProps.deliveries.length; i++) {
      if (prevProps.deliveries[i].id !== nextProps.deliveries[i].id) {
        return false;
      }
    }
  }

  // Check if selectedRowIds changed
  if (prevProps.selectedRowIds.length !== nextProps.selectedRowIds.length ||
      prevProps.selectedRowIds.some((id, index) => id !== nextProps.selectedRowIds[index])) {
    return false;
  }

  // Check if pagination changed
  if (prevProps.pagination.currentPage !== nextProps.pagination.currentPage ||
      prevProps.pagination.pageSize !== nextProps.pagination.pageSize ||
      prevProps.pagination.totalCount !== nextProps.pagination.totalCount) {
    return false;
  }

  // Check function references
  if (prevProps.onRowSelection !== nextProps.onRowSelection ||
      prevProps.onSelectAll !== nextProps.onSelectAll ||
      prevProps.onRowClick !== nextProps.onRowClick ||
      prevProps.pagination.onPageChange !== nextProps.pagination.onPageChange) {
    return false;
  }

  return true;
};

const DeliveryTable: React.FC<DeliveryTableProps> = ({
  deliveries,
  isLoading,
  error,
  selectedRowIds,
  onRowSelection,
  onSelectAll,
  onRowClick,
  pagination
}) => {
  const { currentPage, pageSize, totalCount, onPageChange } = pagination;
  
  // Track if user has scrolled horizontally
  const [hasScrolled, setHasScrolled] = useState(false);
  const tableContainerRef = useRef<HTMLDivElement>(null);
  
  // For smoother pagination transitions
  const prevLoadingState = useRef(isLoading);
  const [showTableLoader, setShowTableLoader] = useState(isLoading);

  // When loading state changes, update with a small delay for smoother transitions
  useEffect(() => {
    if (isLoading !== prevLoadingState.current) {
      prevLoadingState.current = isLoading;
      
      if (isLoading) {
        // Show loading state immediately when starting to load
        setShowTableLoader(true);
      } else {
        // Delay hiding loading state to prevent flickering
        const timer = setTimeout(() => {
          setShowTableLoader(false);
        }, 300); // 300ms delay matches the transition timing in the page
        return () => clearTimeout(timer);
      }
    }
  }, [isLoading]);
  
  // Listen for scroll events
  useEffect(() => {
    const container = tableContainerRef.current;
    if (!container) return;
    
    const handleScroll = () => {
      if (container.scrollLeft > 10 && !hasScrolled) {
        setHasScrolled(true);
      }
    };
    
    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [hasScrolled]);
  
  // Calculate pagination properties directly from props
  const totalPages = useMemo(() => Math.max(1, Math.ceil(totalCount / pageSize)), [totalCount, pageSize]);
  const hasNextPage = useMemo(() => currentPage < totalPages, [currentPage, totalPages]);
  const hasPreviousPage = useMemo(() => currentPage > 1, [currentPage]);

  // Memoized selection state calculations
  const allSelected = useMemo(() => 
    !showTableLoader && deliveries.length > 0 && deliveries.every(delivery => selectedRowIds.includes(delivery.id)),
    [showTableLoader, deliveries, selectedRowIds]
  );
  
  const someSelected = useMemo(() => 
    selectedRowIds.length > 0 && !allSelected,
    [selectedRowIds.length, allSelected]
  );

  // Memoized event handlers
  const handleHeaderCheckboxChange = useCallback(() => {
    onSelectAll(!allSelected);
  }, [onSelectAll, allSelected]);

  const handleRowCheckboxChange = useCallback((deliveryId: string) => {
    const isSelected = selectedRowIds.includes(deliveryId);
    onRowSelection(deliveryId, !isSelected);
  }, [selectedRowIds, onRowSelection]);

  // Memoized handler for copying tracking number
  const handleCopyTracking = useCallback(async (trackingNumber: string) => {
    await copyToClipboard(trackingNumber);
  }, []);

  // Memoized date formatter
  const formatDate = useCallback((dateString: string) => {
    return formatDateUtil(dateString);
  }, []);

  // Handle page change with improved UX
  const handlePageChangeWithFeedback = useCallback((page: number) => {
    // Only change page if we're not already loading
    if (!showTableLoader) {
      onPageChange(page);
    }
  }, [onPageChange, showTableLoader]);

  // Memoize DeliveryTableFooter props to prevent unnecessary re-renders
  const footerProps = useMemo(() => ({
    currentPage,
    totalPages,
    totalCount,
    itemsCount: deliveries.length,
    hasNextPage,
    hasPreviousPage,
    onPageChange: handlePageChangeWithFeedback,
    isLoading: showTableLoader
  }), [
    currentPage,
    totalPages,
    totalCount,
    deliveries.length,
    hasNextPage,
    hasPreviousPage,
    handlePageChangeWithFeedback,
    showTableLoader
  ]);

  return (
    <Profiler id="DeliveryTable" onRender={createProfilerCallback('DeliveryTable')}>
      <div className="max-w-6xl border border-gray-200 rounded-lg">
        {/* Fixed width container */}
        <div className="relative">
          <div 
            ref={tableContainerRef} 
            className="overflow-x-auto w-full"
            style={{ 
              overflowX: 'auto',
              position: 'relative',
              isolation: 'isolate', // Create a new stacking context
              willChange: 'transform' // Hardware acceleration for smoother scrolling
            }}
            data-testid="delivery-table-container"
          >
            <table 
              className="w-full min-w-[1400px] text-sm border-separate border-spacing-0" 
              style={{ tableLayout: 'fixed', borderCollapse: 'separate' }}
              data-testid="delivery-table"
            >
              <colgroup>
                <col style={{ width: '40px' }} />{/* Checkbox */}
                <col style={{ width: '128px' }} />{/* Package status */}
                <col style={{ width: '280px' }} data-testid="number-column" />{/* Number(s) - Expanded to accommodate up to 35 chars */}
                <col style={{ width: '132px' }} data-testid="carrier-column" />{/* Carrier */}
                <col />{/* Customer */}
                <col />{/* Ship Date */}
                <col />{/* Est. Delivery */}
                <col />{/* Last Update */}
              </colgroup>
              <thead>
                <tr className="border-b border-gray-200 bg-gray-50">
                  {/* Checkbox column */}
                  <th 
                    className="p-0 z-30" 
                    style={{
                      position: 'sticky',
                      left: 0,
                      backgroundColor: 'rgb(249, 250, 251)', // Explicit bg-gray-50 color
                      backgroundClip: 'padding-box',
                      boxShadow: '0px 0 0 0px white'
                    }}
                    data-testid="checkbox-header"
                  >
                    <div className="px-4 py-3 flex items-center justify-center">
                      <input
                        type="checkbox"
                        checked={allSelected}
                        ref={input => {
                          if (input) input.indeterminate = someSelected;
                        }}
                        onChange={handleHeaderCheckboxChange}
                        className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                      />
                    </div>
                  </th>
                  
                  {/* Package status */}
                  <th 
                    className="py-3 px-4 text-left align-middle font-medium text-gray-600 whitespace-nowrap z-30" 
                    style={{
                      position: 'sticky',
                      left: '40px',
                      backgroundColor: 'rgb(249, 250, 251)', // Explicit bg-gray-50 color
                      backgroundClip: 'padding-box'
                    }}
                    data-testid="status-header"
                  >
                    Package status
                  </th>
                  
                  {/* Number(s) column - Expanded width */}
                  <th 
                    className="py-3 px-4 text-left align-middle font-medium text-gray-600 whitespace-nowrap z-30" 
                    style={{
                      position: 'sticky',
                      left: '168px',
                      backgroundColor: 'rgb(249, 250, 251)', // Explicit bg-gray-50 color
                      backgroundClip: 'padding-box',
                      width: '280px', // Ensure adequate width for 35 chars
                      minWidth: '280px'
                    }}
                    data-testid="number-header"
                  >
                    Number(s)
                  </th>
                  
                  {/* Carrier column with shadow edge */}
                  <th 
                    className="py-3 px-4 text-left align-middle font-medium text-gray-600 whitespace-nowrap z-30"
                    style={{
                      position: 'sticky',
                      left: '448px', // Adjusted for wider number column
                      backgroundColor: 'rgb(249, 250, 251)', // Explicit bg-gray-50 color
                      backgroundClip: 'padding-box',
                      boxShadow: '4px 0 6px -2px rgba(0, 0, 0, 0.1)',
                      transform: 'translateZ(0)' // Force GPU acceleration for stability
                    }}
                    data-testid="carrier-header"
                  >
                    <div className="relative">
                      Carrier
                      <div 
                        className="absolute top-0 bottom-0 right-0 w-6 bg-gradient-to-r from-transparent to-gray-200/20 pointer-events-none" 
                        style={{transform: 'translateX(100%)'}}
                      ></div>
                    </div>
                  </th>
                  
                  {/* Other columns - scrollable */}
                  <th className="py-3 px-4 text-left align-middle font-medium text-gray-600 whitespace-nowrap"
                    data-testid="customer-header">
                    Customer
                  </th>
                  <th className="py-3 px-4 text-left align-middle font-medium text-gray-600 whitespace-nowrap">
                    Ship Date
                  </th>
                  <th className="py-3 px-4 text-left align-middle font-medium text-gray-600 whitespace-nowrap">
                    Est. Delivery
                  </th>
                  <th className="py-3 px-4 text-left align-middle font-medium text-gray-600 whitespace-nowrap">
                    Last Update
                  </th>
                </tr>
              </thead>
              
              <tbody>
                {showTableLoader ? (
                  <tr>
                    <td colSpan={8} className="py-24 text-center">
                      <LoadingSpinner size="lg" />
                    </td>
                  </tr>
                ) : error ? (
                  <tr>
                    <td colSpan={8} className="py-24 text-center">
                      <ErrorMessage message={error.message} />
                    </td>
                  </tr>
                ) : deliveries.length === 0 ? (
                  <tr>
                    <td colSpan={8} className="px-6 py-12 text-center">
                      <div className="text-gray-500">
                        <svg className="mx-auto h-12 w-12 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2m16-7H4m16 0l-2-2m2 2l-2 2M4 13l2-2m-2 2l2 2" />
                        </svg>
                        <h3 className="text-lg font-medium text-gray-900 mb-2">No deliveries found</h3>
                        <p className="text-gray-500">Try adjusting your filters to see more results.</p>
                      </div>
                    </td>
                  </tr>
                ) : (
                  deliveries.map((delivery) => (
                    <DeliveryRow
                      key={delivery.id} 
                      delivery={delivery}
                      isSelected={selectedRowIds.includes(delivery.id)}
                      onRowClick={onRowClick}
                      onRowCheckboxChange={handleRowCheckboxChange}
                      onCopyTracking={handleCopyTracking}
                      formatDate={formatDate}
                    />
                  ))
                )}
              </tbody>
            </table>
          </div>
          
          {/* Subtle scroll indicator */}
          {!hasScrolled && (
            <div className="absolute right-1 top-1/2 -translate-y-1/2 bg-gray-100 bg-opacity-60 rounded-l-md shadow-sm z-40 text-gray-400">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 animate-[pulse_2s_infinite]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          )}
        </div>
        
        {/* Use memoized footer props to prevent unnecessary re-renders */}
        <DeliveryTableFooter {...footerProps} />
      </div>
    </Profiler>
  );
};

// Export memoized component with custom comparison function
export default memo(DeliveryTable, arePropsEqual);