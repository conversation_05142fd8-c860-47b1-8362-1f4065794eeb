import <PERSON> from 'papa<PERSON><PERSON>';
import { FailedOrder } from '@/shared/lib/hooks/orders/useBulkOrderCreation';

interface BulkOrderRow {
  order_number: string;
  platform: string;
  channel: string;
  customer_email: string;
  customer_name?: string;
  shipping_street?: string;
  shipping_city?: string;
  shipping_state?: string;
  shipping_zip_code?: string;
  shipping_country?: string;
  product_sku: string;
  quantity: string | number;
  subtotal_item: string | number;
  [key: string]: any;
}

interface CsvValidationResult {
  isValid: boolean;
  data?: BulkOrderRow[];
  error?: string;
  failedRows?: FailedOrder[];
}

class CsvProcessingService {
  /**
   * Validate CSV file for bulk order creation
   * @param file CSV file to validate
   * @returns Validation result with processed data or error
   */
  async validateBulkOrderCsv(file: File): Promise<CsvValidationResult> {
    return new Promise((resolve) => {
      // Check file size (max 5MB)
      const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
      if (file.size > MAX_FILE_SIZE) {
        resolve({
          isValid: false,
          error: 'File size exceeds the maximum limit of 5MB'
        });
        return;
      }

      Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        complete: (results) => {
          try {
            const rows = results.data as Record<string, any>[];
            
            // Check if any data was parsed
            if (!rows || rows.length === 0) {
              resolve({
                isValid: false,
                error: 'No data found in CSV file'
              });
              return;
            }
            
            // Check maximum row count
            const MAX_ROWS = 40;
            if (rows.length > MAX_ROWS) {
              resolve({
                isValid: false,
                error: `CSV contains ${rows.length} orders, exceeding the maximum limit of ${MAX_ROWS}`
              });
              return;
            }

            // Validate required headers
            const requiredFields = ['order_number', 'platform', 'channel', 'customer_email', 'product_sku', 'quantity', 'subtotal_item'];
            const headerValidation = this.validateHeaders(results.meta.fields || [], requiredFields);
            
            if (!headerValidation.isValid) {
              resolve({
                isValid: false,
                error: headerValidation.error
              });
              return;
            }

            // Validate each row
            const { validRows, failedRows } = this.validateRows(rows);
            
            if (failedRows.length > 0 && validRows.length === 0) {
              resolve({
                isValid: false,
                error: 'All rows contain validation errors',
                failedRows
              });
              return;
            }

            resolve({
              isValid: true,
              data: validRows,
              failedRows: failedRows.length > 0 ? failedRows : undefined
            });
          } catch (error) {
            console.error('Error parsing CSV:', error);
            resolve({
              isValid: false,
              error: error instanceof Error ? error.message : 'Unknown error parsing CSV'
            });
          }
        },
        error: (error) => {
          console.error('Papa Parse error:', error);
          resolve({
            isValid: false,
            error: error.message || 'Failed to parse CSV file'
          });
        }
      });
    });
  }

  /**
   * Validate that CSV has required headers
   */
  private validateHeaders(headers: string[], requiredFields: string[]): { isValid: boolean; error?: string } {
    const missingFields = requiredFields.filter(field => 
      !headers.some(header => header.toLowerCase().trim() === field.toLowerCase())
    );

    if (missingFields.length > 0) {
      return {
        isValid: false,
        error: `CSV is missing required columns: ${missingFields.join(', ')}`
      };
    }

    return { isValid: true };
  }

  /**
   * Validate each row of the CSV data
   */
  private validateRows(rows: Record<string, any>[]): { validRows: BulkOrderRow[]; failedRows: FailedOrder[] } {
    const validRows: BulkOrderRow[] = [];
    const failedRows: FailedOrder[] = [];

    rows.forEach((row, index) => {
      // Normalize row properties (case insensitive)
      const normalizedRow = this.normalizeRowHeaders(row);
      
      // Validate required fields
      const rowNumber = index + 2; // +2 because CSV header is row 1, and indexes are 0-based
      const validationError = this.validateRowData(normalizedRow);
      
      if (validationError) {
        failedRows.push({
          row: rowNumber,
          reason: validationError,
          data: normalizedRow
        });
      } else {
        validRows.push(normalizedRow as BulkOrderRow);
      }
    });

    return { validRows, failedRows };
  }

  /**
   * Normalize row headers to handle case insensitivity and common variations
   */
  private normalizeRowHeaders(row: Record<string, any>): Record<string, any> {
    const normalized: Record<string, any> = {};
    const headerMappings: Record<string, string[]> = {
      'order_number': ['order', 'ordernumber', 'order number', 'order_id', 'orderid'],
      'platform': ['platform_name', 'platformname'],
      'channel': ['channel_name', 'channelname'],
      'customer_email': ['email', 'customer email', 'customeremail'],
      'customer_name': ['customer', 'name', 'fullname', 'full name', 'full_name'],
      'shipping_street': ['street', 'address', 'shipping_address', 'shipping address'],
      'shipping_city': ['city'],
      'shipping_state': ['state', 'province'],
      'shipping_zip_code': ['zip', 'zipcode', 'zip_code', 'postal', 'postal_code'],
      'shipping_country': ['country'],
      'product_sku': ['sku', 'product', 'product_id', 'productid'],
      'quantity': ['qty', 'amount', 'count'],
      'subtotal_item': ['subtotal_item', 'price_per_item', 'price', 'item_price', 'unit_price', 'per_item_price']
    };

    // Process each field in the row
    Object.entries(row).forEach(([key, value]) => {
      const keyLower = key.toLowerCase().trim();
      
      // Check if this key matches any of our normalized fields
      for (const [normalField, variations] of Object.entries(headerMappings)) {
        if (keyLower === normalField || variations.includes(keyLower)) {
          normalized[normalField] = value;
          return;
        }
      }
      
      // If no match, keep the original key
      normalized[key] = value;
    });

    return normalized;
  }

  /**
   * Validate a single row's data
   * @returns Error message if invalid, undefined if valid
   */
  private validateRowData(row: Record<string, any>): string | undefined {
    // Check required fields
    if (!row.order_number || row.order_number.trim() === '') {
      return 'Order number is required';
    }
    
    if (!row.platform || row.platform.trim() === '') {
      return 'Platform is required';
    }
    
    if (!row.channel || row.channel.trim() === '') {
      return 'Channel is required';
    }
    
    if (!row.customer_email || row.customer_email.trim() === '') {
      return 'Customer email is required';
    }
    
    if (!row.product_sku || row.product_sku.trim() === '') {
      return 'Product SKU is required';
    }
    
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(row.customer_email.trim())) {
      return 'Invalid email format';
    }
    
    // Validate quantity is a number if provided
    if (row.quantity !== undefined && row.quantity !== '') {
      const quantity = Number(row.quantity);
      if (isNaN(quantity) || quantity <= 0) {
        return 'Quantity must be a positive number';
      }
    }
    
    // Validate subtotal_item is a valid number if provided
    if (row.subtotal_item !== undefined && row.subtotal_item !== '') {
      const subtotalItem = Number(row.subtotal_item);
      if (isNaN(subtotalItem) || subtotalItem < 0) {
        return 'Price per item (subtotal_item) must be a non-negative number';
      }
    }
    
    return undefined;
  }
}

export const csvProcessingService = new CsvProcessingService(); 