import { useState, useCallback } from 'react';
import { AllOrdersDetail, AllOrdersStatus } from '@/types';
import { 
  updateOrderStatus,
  addOrderNote,
  cancelOrder,
  resendOrderConfirmation,
  processRefund
} from '@/shared/lib/services/order/live-order-service';

export interface UseOrderActionsReturn {
  loading: boolean;
  error: string | null;
  
  updateStatus: (orderId: string, status: AllOrdersStatus) => Promise<AllOrdersDetail | null>;
  addNote: (orderId: string, content: string, author?: string) => Promise<AllOrdersDetail | null>;
  cancel: (orderId: string, reason: string) => Promise<AllOrdersDetail | null>;
  resendConfirmation: (orderId: string) => Promise<AllOrdersDetail | null>;
  refund: (orderId: string, amount: number, reason: string) => Promise<AllOrdersDetail | null>;
  clearError: () => void;
}

export const useOrderActions = (): UseOrderActionsReturn => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleAction = useCallback(async <T>(
    actionName: string,
    action: () => Promise<T>
  ): Promise<T | null> => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await action();
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : `Failed to ${actionName}`;
      setError(errorMessage);
      console.error(`Error ${actionName}:`, err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateStatus = useCallback(async (orderId: string, status: AllOrdersStatus) => {
    return handleAction('update order status', () => updateOrderStatus(orderId, status));
  }, [handleAction]);

  const addNote = useCallback(async (orderId: string, content: string, author: string = 'System') => {
    return handleAction('add order note', () => addOrderNote(orderId, content, author));
  }, [handleAction]);

  const cancel = useCallback(async (orderId: string, reason: string) => {
    return handleAction('cancel order', () => cancelOrder(orderId, reason));
  }, [handleAction]);

  const resendConfirmation = useCallback(async (orderId: string) => {
    return handleAction('resend order confirmation', () => resendOrderConfirmation(orderId));
  }, [handleAction]);

  const refund = useCallback(async (orderId: string, amount: number, reason: string) => {
    return handleAction('process refund', () => processRefund(orderId, amount, reason));
  }, [handleAction]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    loading,
    error,
    updateStatus,
    addNote,
    cancel,
    resendConfirmation,
    refund,
    clearError
  };
}; 