import * as React from 'react';
import { useEffect, useState, ReactNode, useRef, useMemo, useCallback } from 'react';
import { AuthContextType, UserProfile } from '../../types';
import { AuthController } from '../../shared/lib/controllers/auth.controller';
import { webSocketService } from '@/shared/lib/services/websocket/websocket-service';
import { AuthContext } from './AuthContext';

interface AuthProviderProps {
  children: ReactNode;
}

// Clean, modular AuthProvider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  // State management
  const [session, setSession] = useState<any>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const initialized = useRef(false);
  // Track if logout was called (to prevent disconnect in cleanup)
  const logoutCalled = useRef(false);
  // Track if we already have an active WebSocket connection
  const wsConnected = useRef(false);
  // Track last auth event timestamp to prevent frequent auth state changes
  const lastAuthEventTime = useRef(0);

  // Memoized logout handler to avoid recreating on each render
  const handleLogout = useCallback(() => {
    logoutCalled.current = true;
    webSocketService.disconnect(true); // Force disconnect on explicit logout
    wsConnected.current = false;
  }, []);

  // Handle WebSocket connection with debounce protection
  const handleWebSocketConnection = useCallback((hasSession: boolean) => {
    // If we have a session and aren't connected, connect
    if (hasSession && !wsConnected.current) {
      webSocketService.connect();
      wsConnected.current = true;
    } 
    // If we don't have a session but are connected, disconnect
    else if (!hasSession && wsConnected.current && !logoutCalled.current) {
      webSocketService.disconnect(); // Normal disconnect (decrements counter)
      wsConnected.current = false;
    }
  }, []);

  // Handle auth state changes with protection against frequent events
  const handleAuthStateChange = useCallback((newSession: any, newProfile: UserProfile | null) => {
    const now = Date.now();
    // Throttle auth state changes to prevent rapid reconnects (at most once per second)
    if (now - lastAuthEventTime.current < 1000) {
      return;
    }
    
    lastAuthEventTime.current = now;
    setSession(newSession);
    setProfile(newProfile);
    setIsLoading(false);
    handleWebSocketConnection(!!newSession);
  }, [handleWebSocketConnection]);

  useEffect(() => {
    if (initialized.current) {
      return;
    }
    initialized.current = true;
    logoutCalled.current = false; // Reset on mount
    wsConnected.current = false; // Reset WebSocket connection state
    lastAuthEventTime.current = 0; // Reset auth event timestamp

    // Initialize authentication system
    const initializeAuth = async () => {
      try {
        const result = await AuthController.initialize();
        
        // Use the handler to ensure consistent behavior
        handleAuthStateChange(result.session, result.profile);
      } catch (error) {
        setIsLoading(false);
      }
    };

    // Subscribe to auth state changes
    const subscription = AuthController.subscribeToAuthChanges(handleAuthStateChange);

    // Initialize auth
    initializeAuth();

    // Listen for WebSocket state changes to handle reconnection properly
    const wsStateHandler = (state: any) => {
      // If the connection was closed and we should be connected, update our state
      if (!state.isConnected && !state.isReconnecting && session && wsConnected.current) {
        wsConnected.current = false;
        // Don't immediately reconnect here - let the WebSocket service handle reconnection
      }
    };
    
    webSocketService.onStateChange(wsStateHandler);

    // Cleanup subscription on unmount
    return () => {
      subscription?.unsubscribe();
      
      // Only disconnect if not already disconnected by logout
      if (wsConnected.current && !logoutCalled.current) {
        webSocketService.disconnect(); // Normal disconnect (decrements counter)
        wsConnected.current = false;
      }
      
      initialized.current = false; // Allow re-initialization if ever needed
      logoutCalled.current = false; // Reset for next mount
      lastAuthEventTime.current = 0; // Reset auth event timestamp
    };
  }, [handleAuthStateChange]);

  // Refresh authentication data
  const refreshAuth = async () => {
    try {
      setIsLoading(true);
      const result = await AuthController.refreshAuth();
      setSession(result.session);
      setProfile(result.profile);
      // Auth refreshed successfully
    } catch (error) {
      // Failed to refresh auth
    } finally {
      setIsLoading(false);
    }
  };

  // Manually fetch user role from database
  const fetchUserRole = async () => {
    if (!session?.user?.email) {
      // No user session for role fetch
      return;
    }

    try {
      setIsLoading(true);
      const updatedProfile = await AuthController.fetchUserRole(session.user.email, session);
      
      if (updatedProfile) {
        setProfile(updatedProfile);
      }
    } catch (error) {
      // Failed to fetch user role
    } finally {
      setIsLoading(false);
    }
  };

  // Memoize context value to prevent unnecessary re-renders of consuming components
  const contextValue = useMemo(() => {
    const value: AuthContextType = {
      session,
      user: session?.user || null,
      profile,
      isLoading,
      refreshAuth,
      fetchUserRole,
      logout: handleLogout, // Add memoized logout handler to context
    };
    return value;
  }, [session, profile, isLoading, handleLogout]);

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Re-export the useAuth hook for convenience
export { useAuth } from './AuthContext'; 