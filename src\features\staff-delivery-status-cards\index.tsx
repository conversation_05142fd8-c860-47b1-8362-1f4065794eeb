import React, { useState, useEffect } from 'react';
import { deliveryService } from '@/shared/lib/services/delivery/delivery-service';
import LoadingSpinner from '@/shared/ui/feedback/LoadingSpinner';
import ErrorMessage from '@/shared/ui/feedback/ErrorMessage';

interface DeliveryStatusCardsProps {
  onCardClick: (status: string) => void;
}

interface DeliveryCount {
  in_transit: number;
  delivered: number;
  delayed: number;
  exception: number;
  out_for_delivery: number;
  returned: number;
  lost: number;
}

const DeliveryStatusCards: React.FC<DeliveryStatusCardsProps> = ({ onCardClick }) => {
  const [deliveryCounts, setDeliveryCounts] = useState<Partial<DeliveryCount>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDeliveryCounts = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await deliveryService.getDeliveryStats();
      if (response.data) {
        setDeliveryCounts(response.data);
      } else {
        throw new Error('No data received from API');
      }
    } catch (err) {
      setError('Failed to load delivery status summary. Please try again later.');
      console.error('Error fetching delivery counts:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDeliveryCounts();
  }, []);

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, index) => (
          <div key={index} className="bg-white p-6 rounded-lg shadow border border-gray-200">
            <div className="flex items-center justify-center h-16">
              <LoadingSpinner />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="col-span-full">
          <ErrorMessage message={error} onRetry={fetchDeliveryCounts} />
        </div>
      </div>
    );
  }

  const deliveryCards = [
    {
      status: 'In Transit',
      key: 'in_transit',
      count: deliveryCounts.in_transit ?? 0,
      color: 'bg-blue-500',
      hoverColor: 'hover:bg-blue-600',
      iconColor: 'text-blue-100',
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
          <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
          <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-.293-.707L15 4.586A1 1 0 0014.414 4H14v3z" />
        </svg>
      ),
    },
    {
      status: 'Delivered',
      key: 'delivered',
      count: deliveryCounts.delivered ?? 0,
      color: 'bg-green-500',
      hoverColor: 'hover:bg-green-600',
      iconColor: 'text-green-100',
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
        </svg>
      ),
    },
    {
      status: 'Delayed',
      key: 'delayed',
      count: deliveryCounts.delayed ?? 0,
      color: 'bg-orange-500',
      hoverColor: 'hover:bg-orange-600',
      iconColor: 'text-orange-100',
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
        </svg>
      ),
    },
    {
      status: 'Exception',
      key: 'exception',
      count: deliveryCounts.exception ?? 0,
      color: 'bg-red-500',
      hoverColor: 'hover:bg-red-600',
      iconColor: 'text-red-100',
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
      ),
    },
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      {deliveryCards.map((card) => (
        <div
          key={card.key}
          onClick={() => onCardClick(card.key)}
          className={`${card.color} ${card.hoverColor} cursor-pointer transform transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl rounded-lg p-6 text-white`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-lg font-semibold opacity-90">{card.status}</p>
              <p className="text-3xl font-bold">{card.count.toLocaleString()}</p>
            </div>
            <div className={`${card.iconColor}`}>
              {card.icon}
            </div>
          </div>
          <div className="mt-4 text-sm opacity-80">
            Click to view {card.status.toLowerCase()} deliveries
          </div>
        </div>
      ))}
    </div>
  );
};

export default DeliveryStatusCards; 