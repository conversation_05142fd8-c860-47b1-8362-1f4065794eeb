import React from 'react';
import { Link } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { MOCK_TOP_SELLERS_DATA } from '@/shared/lib/mock-data/reports';

const TopSellersCard: React.FC = () => {
  const formatLegendValue = (value: string) => {
    const maxLength = 20;
    if (value.length > maxLength) {
      return (
        <span className="text-sm text-gray-600" title={value}>
          {value.substring(0, maxLength - 3) + '...'}
        </span>
      );
    }
    return <span className="text-sm text-gray-600">{value}</span>;
  };
  return (
    <Link
      to="/reports"
      className="bg-card-bg p-6 rounded-lg shadow cursor-pointer hover:shadow-lg transition-shadow h-full block"
      aria-label="View top sellers report"
    >
      <h4 className="text-lg font-semibold text-gray-700 mb-4">Top Sellers</h4>
      <div className="w-full h-[330px] relative">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart >
            <Pie
              data={MOCK_TOP_SELLERS_DATA}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius="80%"
              fill="#8884d8"
              dataKey="sales"
              nameKey="store"
            >
              {MOCK_TOP_SELLERS_DATA.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip 
              formatter={(value: number) => new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 2
              }).format(value)}
            />
            <Legend 
              layout="vertical" 
              verticalAlign="middle" 
              align="right"
              iconSize={10}
              iconType="circle"
              wrapperStyle={{
                fontSize: '0.875rem',
                lineHeight: '1.25rem',
                paddingLeft: '1rem',
                maxWidth: '40%'
              }}
              formatter={formatLegendValue}
            />
          </PieChart>
        </ResponsiveContainer>
      </div>
    </Link>
  );
};

export default TopSellersCard;