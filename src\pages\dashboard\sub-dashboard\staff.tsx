import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/app/providers/AuthProvider';
import OrderStatusCards from '@/features/staff-order-status-cards';
import DeliveryStatusCards from '@/features/staff-delivery-status-cards';

const StaffDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { profile } = useAuth();

  // Only allow staff role access
  if (profile?.role !== 'staff') {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Access Restricted</h3>
          <p className="text-gray-600">This dashboard is only available to staff members.</p>
        </div>
      </div>
    );
  }

  const handleOrderStatusClick = (status: string) => {
    // Navigate to orders page with status filter
    navigate(`/orders?s=${status.toLowerCase()}`);
  };

  const handleDeliveryStatusClick = (status: string) => {
    // Navigate to delivery page with status filter
    navigate(`/delivery?status=${status.toLowerCase()}`);
  };

  return (
    <main className="flex-1 overflow-x-hidden overflow-y-auto p-6 space-y-8">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Staff Dashboard</h1>
        <p className="text-gray-600 mt-1">Quick overview of orders and deliveries</p>
      </div>

      {/* Order Status Cards */}
      <section>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Order Status Overview</h2>
        <OrderStatusCards onCardClick={handleOrderStatusClick} />
      </section>

      {/* Delivery Status Cards */}
      <section>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Delivery Status Overview</h2>
        <DeliveryStatusCards onCardClick={handleDeliveryStatusClick} />
      </section>
    </main>
  );
};

export default StaffDashboard; 