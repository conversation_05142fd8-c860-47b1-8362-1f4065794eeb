import { Platform, ProductIdentifier } from '@/types';

// Mock platforms data
export const MOCK_PLATFORMS: Platform[] = [
  {
    id: 'platform-amazon',
    name: 'Amazon',
    key: 'amazon',
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: null
  },
  {
    id: 'platform-ebay',
    name: 'eBay',
    key: 'ebay',
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: null
  },
  {
    id: 'platform-walmart',
    name: 'Walmart',
    key: 'walmart',
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: null
  },
  {
    id: 'platform-shopify',
    name: 'Shopify',
    key: 'shopify',
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: null
  },
  {
    id: 'platform-website',
    name: 'Company Website',
    key: 'website',
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: null
  },
  {
    id: 'platform-tiktok',
    name: '<PERSON><PERSON><PERSON><PERSON> <PERSON>',
    key: 'tiktok',
    is_active: false,
    created_at: new Date().toISOString(),
    updated_at: null
  }
];

// Generate mock product identifiers for a given product ID
export function getMockProductIdentifiers(productId: string): ProductIdentifier[] {
  return [
    {
      id: `pid-${productId}-1`,
      product_id: productId,
      platform_id: 'platform-amazon',
      platform_identifier: `ASIN-${Math.random().toString(36).substring(2, 10).toUpperCase()}`,
      code_name: 'Amazon US',
      pack_size: 1,
      created_at: new Date().toISOString()
    },
    {
      id: `pid-${productId}-2`,
      product_id: productId,
      platform_id: 'platform-ebay',
      platform_identifier: `EB-${Math.floor(Math.random() * 90000000) + 10000000}`,
      code_name: 'eBay Store',
      pack_size: 1,
      created_at: new Date().toISOString()
    }
  ];
}

// Generate a mock product identifier
export function createMockProductIdentifier(data: {
  product_id: string;
  platform_id: string;
  platform_identifier: string;
  code_name?: string | null;
  pack_size: number;
}): ProductIdentifier {
  return {
    id: `pid-${data.product_id}-${Date.now()}`,
    product_id: data.product_id,
    platform_id: data.platform_id,
    platform_identifier: data.platform_identifier,
    code_name: data.code_name || null,
    pack_size: data.pack_size,
    created_at: new Date().toISOString()
  };
}

// Generate a mock updated product identifier
export function updateMockProductIdentifier(id: string, data: {
  platform_identifier?: string;
  code_name?: string | null;
  pack_size?: number;
}): ProductIdentifier {
  return {
    id: id,
    product_id: 'mock-product-id',
    platform_id: 'platform-amazon',
    platform_identifier: data.platform_identifier || 'ASIN-UPDATED',
    code_name: data.code_name || null,
    pack_size: data.pack_size || 1,
    created_at: new Date().toISOString()
  };
} 