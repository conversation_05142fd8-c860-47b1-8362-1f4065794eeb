import React, { useState, useCallback } from 'react';
import { PencilIcon, EyeIcon, TrashIcon, XCircleIcon } from '@heroicons/react/24/outline';
import { StockAdjustmentModal } from '@/features/inventory-features/stock-update';
import type { StockUpdateFormData, BulkStockUpdateFormData } from '@/features/inventory-features/stock-update';
import { InventoryView, InventoryReasonCategory, ProductStatusEnum } from '@/types';
import { ArrowPathIcon } from '@heroicons/react/24/outline';
import { DiscontinueProductModal } from '@/features/inventory-features/product-form';
import inventoryStockService from '@/shared/lib/services/inventory/inventory-stock-service';
import { notificationService } from '@/shared/lib/services/notification/notification-service';

interface ProductActionsProps {
  productId: string;
  productName: string;
  productStatus: ProductStatusEnum;
  onUpdateStock: (productId: string) => void;
  onEditProduct?: (productId: string) => void;
  onDiscontinueProduct?: (productId: string, reason: string, notes: string) => Promise<void>;
  onDeleteProduct?: (productId: string) => void;
}

const ProductActions: React.FC<ProductActionsProps> = ({ 
  productId, 
  productName,
  productStatus,
  onUpdateStock, 
  onEditProduct,
  onDiscontinueProduct,
  onDeleteProduct
}) => {
  // Local state for modals
  const [isDiscontinueModalOpen, setIsDiscontinueModalOpen] = useState(false);
  const [isStockModalOpen, setIsStockModalOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  
  // Stock adjustment handlers
  const handleAddStock = useCallback(() => {
    setIsStockModalOpen(true);
  }, []);

  const handleStockModalClose = useCallback(() => {
    setIsStockModalOpen(false);
  }, []);

  const handleStockUpdateSuccess = useCallback(async (data: StockUpdateFormData | BulkStockUpdateFormData) => {
    if (!productId) {
      notificationService.error('Product ID is missing');
      return;
    }
    
    setIsUpdating(true);
    
    try {
      console.log('Updating stock with data:', data);
      
      if ('productId' in data) {
        // Use bulkAdjustStock with product_id
        await inventoryStockService.bulkAdjustStock({
          productIds: [productId],
          quantity: data.quantity,
          reason: data.reason,
          reason_category: data.reasonCategory as InventoryReasonCategory,
          type: data.adjustmentType,
          notes: data.notes
        });
      } else {
        // Bulk update (should not happen in this component, but handle it anyway)
        await inventoryStockService.bulkAdjustStock({
          productIds: data.productIds,
          quantity: data.quantity,
          reason: data.reason,
          reason_category: data.reasonCategory as InventoryReasonCategory,
          type: data.adjustmentType,
          notes: data.notes
        });
      }
      
      // Show success notification
      notificationService.success('Stock updated successfully');
      
      // Call the callback to refresh data
      onUpdateStock(productId);
      
      // Close the modal
      setIsStockModalOpen(false);
    } catch (error) {
      console.error('Error updating stock:', error);
      notificationService.error(error instanceof Error ? error.message : 'Failed to update stock');
    } finally {
      setIsUpdating(false);
    }
  }, [productId, onUpdateStock]);

  // Other action handlers
  const handleEdit = useCallback(() => {
    if (onEditProduct && productId) {
      onEditProduct(productId);
    }
  }, [productId, onEditProduct]);

  const handleViewHistory = useCallback(() => {
    // Implementation will be added in a future task
  }, []);

  const handleDeleteProduct = useCallback(() => {
    if (onDeleteProduct && productId) {
      onDeleteProduct(productId);
    } else if (productId) {
      // Fallback to onUpdateStock if onDeleteProduct is not provided
      onUpdateStock(productId);
    }
  }, [productId, onDeleteProduct, onUpdateStock]);
  
  const handleDiscontinueProduct = useCallback(() => {
    setIsDiscontinueModalOpen(true);
  }, []);

  // Create products array for StockAdjustmentModal
  const products: InventoryView[] = productId ? [
    { 
      product_id: productId,
      name: productName,
      status: productStatus,
      inventory_id: '',
      sku: '',
      product_type: null,
      current_stock: 0,
      available_stock: 0,
      reserved_stock: 0,
      minimum_threshold: null,
      needs_reorder: false
    }
  ] : [];

  return (
    <>
      <section>
        <h4 className="text-lg font-medium text-gray-900 mb-3">Quick Actions</h4>
        <div className="flex flex-wrap gap-3">
          {productId && (
            <>
              <button
                onClick={handleAddStock}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                disabled={isUpdating}
              >
                <ArrowPathIcon className={`h-4 w-4 mr-2 ${isUpdating ? 'animate-spin' : ''}`} />
                {isUpdating ? 'Updating...' : 'Update Stock'}
              </button>
              
              <button
                onClick={handleEdit}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <PencilIcon className="h-4 w-4 mr-2" />
                Edit Product
              </button>
              
              {productStatus !== 'discontinued' && (
                <button
                  onClick={handleDiscontinueProduct}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <XCircleIcon className="h-4 w-4 mr-2 text-red-500" />
                  Discontinue
                </button>
              )}
            </>
          )}
          
          <button
            onClick={handleViewHistory}
            className="flex items-center justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-blue-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <EyeIcon className="h-5 w-5 mr-2 text-blue-600" />
            View History
          </button>
          
          <button
            onClick={handleDeleteProduct}
            className="col-span-2 flex items-center justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            <TrashIcon className="h-5 w-5 mr-2 text-red-600" />
            Delete Product
          </button>
        </div>
      </section>

      {/* Stock Update Modal - Using the StockAdjustmentModal with correct props */}
      <StockAdjustmentModal
        isOpen={isStockModalOpen}
        onClose={handleStockModalClose}
        products={products}
        onSuccess={handleStockUpdateSuccess}
      />
      
      {/* Discontinue Product Modal */}
      {onDiscontinueProduct && (
        <DiscontinueProductModal
          isOpen={isDiscontinueModalOpen}
          onClose={() => setIsDiscontinueModalOpen(false)}
          productId={productId}
          productName={productName}
          onDiscontinue={onDiscontinueProduct}
        />
      )}
    </>
  );
};

export default ProductActions; 