import { useState, useEffect, useCallback } from 'react';
import { productService } from '@/shared/lib/services/inventory';
import { ProductIdentifier } from '@/types';

export interface UseProductIdentifiersReturn {
  identifiers: ProductIdentifier[];
  isLoading: boolean;
  error: string | null;
  debugInfo: any | null;
  retryCount: number;
  retry: () => void;
  refresh: () => void;
}

export const useProductIdentifiers = (productId: string): UseProductIdentifiersReturn => {
  const [identifiers, setIdentifiers] = useState<ProductIdentifier[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [refreshKey, setRefreshKey] = useState(0);

  const maxRetries = 3;
  
  const retry = useCallback(() => {
    if (retryCount < maxRetries) {
      setRetryCount(prev => prev + 1);
    }
  }, [retryCount]);
  
  const refresh = useCallback(() => {
    setRefreshKey(prev => prev + 1);
    setRetryCount(0);
    setError(null);
  }, []);

  useEffect(() => {
    if (!productId) return;

    const fetchIdentifiers = async () => {
      setIsLoading(true);
      setError(null);
      setDebugInfo(null);

      try {
        // console.log(`Fetching product identifiers for product ID: ${productId} (attempt ${retryCount + 1})`);
        
        // Use the productService instead of directly calling the API
        const identifiers = await productService.getProductIdentifiers(productId);
        
        // Store debug info
        setDebugInfo({
          responseType: typeof identifiers,
          responseIsArray: Array.isArray(identifiers),
          responseLength: identifiers.length,
          firstItem: identifiers.length > 0 
            ? JSON.stringify(identifiers[0]) 
            : 'No items'
        });
        
        setIdentifiers(identifiers);
        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching product identifiers:', err);
        setError(err instanceof Error ? err.message : 'Failed to load platform identifiers');
        setIsLoading(false);
      }
    };

    // Only fetch if we have a productId
    if (productId) {
      fetchIdentifiers();
    }
  }, [productId, retryCount, refreshKey]);

  return {
    identifiers,
    isLoading,
    error,
    debugInfo,
    retryCount,
    retry,
    refresh
  };
};

export default useProductIdentifiers; 