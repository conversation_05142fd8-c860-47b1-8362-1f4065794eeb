import React, { useState } from 'react';
import Modal from '@/shared/ui/overlay/Modal';

interface RefundModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (amount: number, reason: string) => void;
  orderNumber: string;
  totalAmount: number;
  loading?: boolean;
}

const RefundModal: React.FC<RefundModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  orderNumber,
  totalAmount,
  loading = false
}) => {
  const [refundAmount, setRefundAmount] = useState(totalAmount.toString());
  const [refundReason, setRefundReason] = useState('');
  const [errors, setErrors] = useState<{ amount?: string; reason?: string }>({});

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const handleClose = () => {
    if (!loading) {
      setRefundAmount(totalAmount.toString());
      setRefundReason('');
      setErrors({});
      onClose();
    }
  };

  const validateForm = () => {
    const newErrors: { amount?: string; reason?: string } = {};
    const amount = parseFloat(refundAmount);
    
    if (!refundAmount.trim()) {
      newErrors.amount = 'Refund amount is required';
    } else if (isNaN(amount) || amount <= 0) {
      newErrors.amount = 'Refund amount must be a positive number';
    } else if (amount > totalAmount) {
      newErrors.amount = `Refund amount cannot exceed order total (${formatCurrency(totalAmount)})`;
    }
    
    if (!refundReason.trim()) {
      newErrors.reason = 'Refund reason is required';
    } else if (refundReason.trim().length < 10) {
      newErrors.reason = 'Refund reason must be at least 10 characters long';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleConfirm = () => {
    if (validateForm()) {
      const amount = parseFloat(refundAmount);
      onConfirm(amount, refundReason.trim());
      setRefundAmount(totalAmount.toString());
      setRefundReason('');
      setErrors({});
    }
  };

  const handleAmountChange = (value: string) => {
    // Allow only numeric input with up to 2 decimal places
    if (value === '' || /^\d*\.?\d{0,2}$/.test(value)) {
      setRefundAmount(value);
    }
  };

  const isFullRefund = parseFloat(refundAmount) === totalAmount;
  const isPartialRefund = parseFloat(refundAmount) < totalAmount && parseFloat(refundAmount) > 0;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Process Refund"
      size="lg"
    >
      <div className="space-y-4">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2zM10 8.5a.5.5 0 11-1 0 .5.5 0 011 0zm5 5a.5.5 0 11-1 0 .5.5 0 011 0z" />
              </svg>
            </div>
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Process Refund
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              Process a refund for order <span className="font-medium">{orderNumber}</span>. 
              You can process a full or partial refund.
            </p>

            <div className="space-y-4">
              {/* Order Amount Info */}
              <div className="p-3 bg-gray-50 rounded-md">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-700">Original Order Total:</span>
                  <span className="text-sm font-bold text-gray-900">{formatCurrency(totalAmount)}</span>
                </div>
              </div>

              {/* Refund Amount Field */}
              <div>
                <label htmlFor="refundAmount" className="block text-sm font-medium text-gray-700 mb-1">
                  Refund Amount
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 sm:text-sm">$</span>
                  </div>
                  <input
                    type="text"
                    id="refundAmount"
                    value={refundAmount}
                    onChange={(e) => handleAmountChange(e.target.value)}
                    disabled={loading}
                    className={`block w-full pl-8 pr-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 ${
                      errors.amount ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="0.00"
                  />
                </div>
                <div className="mt-1 flex justify-between">
                  {errors.amount ? (
                    <p className="text-sm text-red-600">{errors.amount}</p>
                  ) : (
                    <div className="flex space-x-2">
                      <button
                        type="button"
                        onClick={() => setRefundAmount(totalAmount.toString())}
                        className="text-sm text-orange-600 hover:text-orange-700"
                      >
                        Full Refund
                      </button>
                      <span className="text-gray-300">|</span>
                      <button
                        type="button"
                        onClick={() => setRefundAmount((totalAmount / 2).toFixed(2))}
                        className="text-sm text-orange-600 hover:text-orange-700"
                      >
                        Half Refund
                      </button>
                    </div>
                  )}
                  {isFullRefund && (
                    <span className="text-sm text-green-600 font-medium">Full Refund</span>
                  )}
                  {isPartialRefund && (
                    <span className="text-sm text-orange-600 font-medium">Partial Refund</span>
                  )}
                </div>
              </div>

              {/* Refund Reason Field */}
              <div>
                <label htmlFor="refundReason" className="block text-sm font-medium text-gray-700 mb-1">
                  Refund Reason
                </label>
                <textarea
                  id="refundReason"
                  rows={3}
                  value={refundReason}
                  onChange={(e) => setRefundReason(e.target.value)}
                  disabled={loading}
                  className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 ${
                    errors.reason ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="Enter the reason for this refund..."
                />
                <div className="mt-1 flex justify-between">
                  {errors.reason ? (
                    <p className="text-sm text-red-600">{errors.reason}</p>
                  ) : (
                    <p className="text-sm text-gray-500">Minimum 10 characters required</p>
                  )}
                  <p className="text-sm text-gray-500">{refundReason.length} characters</p>
                </div>
              </div>

              {/* Warning */}
              <div className="p-3 bg-orange-50 rounded-md">
                <p className="text-sm text-orange-700">
                  <strong>Warning:</strong> This action cannot be undone. The refund will be processed 
                  immediately and the customer will be notified. This action will be logged in the order history.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6 flex justify-end space-x-3">
        <button
          type="button"
          onClick={handleClose}
          disabled={loading}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50"
        >
          Cancel
        </button>
        <button
          type="button"
          onClick={handleConfirm}
          disabled={loading || !refundAmount.trim() || !refundReason.trim()}
          className="px-4 py-2 text-sm font-medium text-white bg-orange-600 border border-transparent rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 flex items-center"
        >
          {loading ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing...
            </>
          ) : (
            `Process Refund (${formatCurrency(parseFloat(refundAmount) || 0)})`
          )}
        </button>
      </div>
    </Modal>
  );
};

export default RefundModal; 