import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { CalendarIcon, XMarkIcon } from '@heroicons/react/24/outline';

interface DateRange {
  start: string | null;
  end: string | null;
}

interface DateRangePickerProps {
  value?: DateRange;
  onConfirm: (dateRange: DateRange) => void;
  placeholder?: string;
  className?: string;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  value = { start: null, end: null },
  onConfirm,
  placeholder = "Select date range",
  className = ""
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [tempRange, setTempRange] = useState<DateRange>(value);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Task 3.6: Memoize month names array to prevent recreation on every render
  const monthNames = useMemo(() => [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ], []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Task 3.6: Memoize expensive date formatting function
  const formatDate = useMemo(() => (dateStr: string | null) => {
    if (!dateStr) return null;
    return new Date(dateStr).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  }, []);

  // Task 3.6: Memoize display text calculation
  const getDisplayText = useMemo(() => {
    if (value.start && value.end) {
      return `${formatDate(value.start)} - ${formatDate(value.end)}`;
    } else if (value.start) {
      return `From ${formatDate(value.start)}`;
    } else if (value.end) {
      return `Until ${formatDate(value.end)}`;
    }
    return placeholder;
  }, [value.start, value.end, formatDate, placeholder]);

  // Task 3.6: Memoize expensive calendar days generation to avoid recalculation on every render
  const calendarDays = useMemo(() => {
    const firstDay = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
    const lastDay = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay()); // Start from Sunday

    const days = [];
    const currentDate = new Date(startDate);

    for (let i = 0; i < 42; i++) { // 6 weeks
      days.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return days;
  }, [currentMonth]);

  // Task 3.6: Memoize date range checking functions for better performance
  const dateRangeCheckers = useMemo(() => {
    const isDateInRange = (date: Date) => {
      if (!tempRange.start) return false;
      const dateStr = date.toISOString().split('T')[0];
      const startDate = tempRange.start;
      const endDate = tempRange.end;

      if (startDate && endDate) {
        return dateStr >= startDate && dateStr <= endDate;
      } else if (startDate) {
        return dateStr === startDate;
      }
      return false;
    };

    const isStartDate = (date: Date) => {
      return tempRange.start === date.toISOString().split('T')[0];
    };

    const isEndDate = (date: Date) => {
      return tempRange.end === date.toISOString().split('T')[0];
    };

    return { isDateInRange, isStartDate, isEndDate };
  }, [tempRange.start, tempRange.end]);

  // Handle date selection
  const handleDateClick = useCallback((date: Date) => {
    const dateStr = date.toISOString().split('T')[0];
    
    if (!tempRange.start || (tempRange.start && tempRange.end)) {
      // Start new selection
      setTempRange({ start: dateStr, end: null });
    } else if (tempRange.start && !tempRange.end) {
      // Complete the range
      const startDate = new Date(tempRange.start);
      if (date >= startDate) {
        setTempRange({ ...tempRange, end: dateStr });
      } else {
        setTempRange({ start: dateStr, end: tempRange.start });
      }
    }
  }, [tempRange]);

  // Navigate months
  const navigateMonth = useCallback((direction: 'prev' | 'next') => {
    setCurrentMonth(prev => {
      const newMonth = new Date(prev);
      newMonth.setMonth(prev.getMonth() + (direction === 'next' ? 1 : -1));
      return newMonth;
    });
  }, []);

  // Handle confirm
  const handleConfirm = useCallback(() => {
    onConfirm(tempRange);
    setIsOpen(false);
  }, [onConfirm, tempRange]);

  // Handle clear
  const handleClear = useCallback(() => {
    const clearedRange = { start: null, end: null };
    setTempRange(clearedRange);
    onConfirm(clearedRange);
    setIsOpen(false);
  }, [onConfirm]);

  // Check if date is in range
  const isDateInRange = dateRangeCheckers.isDateInRange;

  // Check if date is start or end
  const isStartDate = dateRangeCheckers.isStartDate;

  const isEndDate = dateRangeCheckers.isEndDate;

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Input trigger */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-3 py-2 text-left bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 hover:border-gray-400 transition-colors"
      >
        <div className="flex items-center justify-between">
          <span className={value.start || value.end ? "text-gray-900" : "text-gray-500"}>
            {getDisplayText}
          </span>
          <CalendarIcon className="h-5 w-5 text-gray-400" />
        </div>
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 mt-1 bg-white rounded-md shadow-lg border border-gray-200 p-4 w-80 max-w-[calc(100vw-2rem)] right-0">
          {/* Month navigation */}
          <div className="flex items-center justify-between mb-4">
            <button
              type="button"
              onClick={() => navigateMonth('prev')}
              className="p-1 hover:bg-gray-100 rounded"
            >
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <h3 className="text-lg font-medium">
              {monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
            </h3>
            <button
              type="button"
              onClick={() => navigateMonth('next')}
              className="p-1 hover:bg-gray-100 rounded"
            >
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          {/* Calendar */}
          <div className="grid grid-cols-7 gap-1 mb-4">
            {/* Day headers */}
            {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map(day => (
              <div key={day} className="text-center text-sm font-medium text-gray-500 py-2">
                {day}
              </div>
            ))}

            {/* Calendar days */}
            {calendarDays.map((date, index) => {
              const isCurrentMonth = date.getMonth() === currentMonth.getMonth();
              const isToday = date.toDateString() === new Date().toDateString();
              const inRange = isDateInRange(date);
              const isStart = isStartDate(date);
              const isEnd = isEndDate(date);

              return (
                <button
                  key={index}
                  type="button"
                  onClick={() => handleDateClick(date)}
                  className={`
                    h-8 w-8 text-sm rounded transition-colors
                    ${!isCurrentMonth ? 'text-gray-400' : 'text-gray-900'}
                    ${isToday ? 'ring-2 ring-blue-500' : ''}
                    ${inRange ? 'bg-blue-50' : 'hover:bg-gray-100'}
                    ${isStart || isEnd ? 'bg-blue-500 text-white' : ''}
                  `}
                >
                  {date.getDate()}
                </button>
              );
            })}
          </div>

          {/* Action buttons */}
          <div className="flex items-center justify-between pt-3 border-t border-gray-200">
            <button
              type="button"
              onClick={handleClear}
              className="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 transition-colors"
            >
              Clear
            </button>
            <div className="flex gap-2">
              <button
                type="button"
                onClick={() => setIsOpen(false)}
                className="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleConfirm}
                className="px-3 py-1.5 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                Apply
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DateRangePicker; 