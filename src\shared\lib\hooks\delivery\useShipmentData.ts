import { useQuery } from '@tanstack/react-query';
import { deliveriesApi } from '@/shared/api';
import { DeliveryDetails, DeliveryListParams, DeliveryTrackingViewItem } from '@/types';
import { useMemo } from 'react';
import { deliveryService } from '@/shared/lib/services/delivery/delivery-service';

// Props for the individual shipment details
interface UseShipmentDataProps {
  shipmentId: string | null;
}

// Return type for individual shipment details
interface UseShipmentDataReturn {
  shipment: DeliveryDetails | undefined;
  isLoading: boolean;
  error: Error | null;
}

// Props for the shipments list with pagination
interface UseShipmentsListProps {
  params: DeliveryListParams;
}

// Return type for shipments list with pagination metadata
interface UseShipmentsListReturn {
  shipments: DeliveryTrackingViewItem[];
  isLoading: boolean;
  isFetching: boolean;
  error: Error | null;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalCount: number;
    pageSize: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

/**
 * Hook for fetching individual shipment details
 */
export const useShipmentData = ({ shipmentId }: UseShipmentDataProps): UseShipmentDataReturn => {
  const { data: shipment, isLoading, error } = useQuery({
    queryKey: ['deliveryDetails', shipmentId],
    queryFn: async () => {
      const response = await deliveriesApi.getById(shipmentId!);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch shipment details');
      }
      return response.data;
    },
    enabled: !!shipmentId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Memoize return value to prevent unnecessary re-renders in consuming components
  return useMemo(() => ({
    shipment: shipment ?? undefined,
    isLoading,
    error: error as Error | null
  }), [shipment, isLoading, error]);
};

/**
 * Hook for fetching paginated shipments list
 */
export const useShipmentsList = ({ params }: UseShipmentsListProps): UseShipmentsListReturn => {
  // Make sure page and limit are set with defaults
  const queryParams = useMemo(() => ({
    ...params,
    page: params.page || 1,
    limit: params.limit || 20 // Default to 20 items per page as required
  }), [params]);
  
  // Fetch shipments with pagination - with improved caching
  const { 
    data, 
    isLoading, 
    isFetching,
    error 
  } = useQuery({
    queryKey: ['deliveries', queryParams],
    queryFn: async () => {
      const response = await deliveriesApi.list(queryParams);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch shipments');
      }
      return response;
    },
    staleTime: 3 * 60 * 1000, // 3 minutes - increased from 2 minutes for smoother experience
    refetchOnWindowFocus: true, // Enable auto-refresh when user returns to tab
    refetchOnMount: true, // Enable refresh on component mount
    
    // Use placeholderData for smoother transitions between pages
    placeholderData: (previousData: any) => {
      if (!previousData || !previousData.meta) return previousData;
      
      // Return previous data with current page meta updated
      return {
        ...previousData,
        meta: {
          ...previousData.meta,
          currentPage: queryParams.page
        }
      };
    },
  });
  
  // Get stats for the total count in a separate query with strong caching
  const { data: statsData } = useQuery({
    queryKey: ['deliveryStats'],
    queryFn: () => deliveryService.getDeliveryStats(),
    staleTime: 10 * 60 * 1000, // Increase to 10 minutes for better caching
    gcTime: 30 * 60 * 1000, // Keep in cache for 30 minutes even when not used (renamed from cacheTime in v4)
    refetchOnWindowFocus: false, // Don't refetch on window focus to reduce unnecessary requests
    refetchOnMount: 'always', // Always fetch fresh data on component mount
    refetchInterval: 5 * 60 * 1000, // Refresh every 5 minutes in the background
    retry: 2, // Retry failed requests twice
  });

  // Calculate pagination metadata with simplified logic
  const pagination = useMemo(() => {
    // Use the total count from the stats query if available, as it contains the complete count
    // Fall back to the meta count from the paginated query otherwise
    const totalCount = (statsData?.data?.total ?? data?.meta?.totalCount ?? 0);
    
    // console.log('API Response data:', {
    //   meta: data?.meta,
    //   totalCount,
    //   statsTotal: statsData?.data?.total,
    //   rawData: data
    // });
    
    const pageSize = queryParams.limit;
    const currentPage = queryParams.page;
    const totalPages = Math.max(1, Math.ceil(totalCount / pageSize));
    
    return {
      currentPage,
      totalPages,
      totalCount,
      pageSize,
      hasNextPage: currentPage < totalPages,
      hasPreviousPage: currentPage > 1
    };
  }, [data?.meta?.totalCount, queryParams.limit, queryParams.page, data, statsData?.data?.total]);

  // Return memoized values
  return useMemo(() => ({
    shipments: data?.data || [],
    isLoading,
    isFetching,
    error: error as Error | null,
    pagination
  }), [data, isLoading, isFetching, error, pagination]);
}; 