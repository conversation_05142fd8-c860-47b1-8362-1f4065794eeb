import React from 'react';

interface InventoryStatusProps {
  currentStock: number;
  status: 'In Stock' | 'Low Stock' | 'Out of Stock';
}

export const InventoryStatus: React.FC<InventoryStatusProps> = ({ currentStock, status }) => {
  const getStatusColor = (status: InventoryStatusProps['status']) => {
    switch (status) {
      case 'In Stock':
        return 'text-green-600';
      case 'Low Stock':
        return 'text-yellow-600';
      case 'Out of Stock':
        return 'text-red-600';
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <span className={`font-medium ${getStatusColor(status)}`}>
        {currentStock} units
      </span>
      <span className={`text-sm ${getStatusColor(status)}`}>
        ({status})
      </span>
    </div>
  );
}; 