import { useState, useEffect, useCallback } from 'react';
import { ProductIdentifier, Platform } from '@/types';
import inventoryAlertsService from '@/shared/lib/services/inventory/inventory-alerts-service';
import { performOptimisticUpdate } from '@/shared/lib/utils/data/optimistic-updates';

interface PlatformLinkingState {
  identifiers: ProductIdentifier[];
  platforms: Platform[];
  isLoading: boolean;
  isSubmitting: boolean;
  error: string | null;
}

interface PlatformLinkingActions {
  addIdentifier: (data: {
    platform_id: string;
    platform_identifier: string;
    code_name?: string | null;
    pack_size: number;
  }) => Promise<void>;
  updateIdentifier: (id: string, data: {
    platform_identifier?: string;
    code_name?: string | null;
    pack_size?: number;
  }) => Promise<void>;
  deleteIdentifier: (id: string) => Promise<void>;
  refreshIdentifiers: () => Promise<void>;
}

// Custom return type to match the performOptimisticUpdate expected format
interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data: T;
  totalCount: number;
}

interface UsePlatformLinkingProps {
  productId: string;
}

export const usePlatformLinking = ({ productId }: UsePlatformLinkingProps): [PlatformLinkingState, PlatformLinkingActions] => {
  const [state, setState] = useState<PlatformLinkingState>({
    identifiers: [],
    platforms: [],
    isLoading: true,
    isSubmitting: false,
    error: null
  });

  // Fetch platforms and product identifiers
  const fetchData = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      // Fetch platforms
      const platformsResponse = await inventoryAlertsService.getPlatforms();
      
      if (productId) {
        // Fetch product identifiers if we have a product ID
        const identifiersResponse = await inventoryAlertsService.getProductIdentifiers(productId);
        
        setState(prev => ({
          ...prev,
          platforms: platformsResponse.data,
          identifiers: identifiersResponse.data,
          isLoading: false
        }));
      } else {
        setState(prev => ({
          ...prev,
          platforms: platformsResponse.data,
          identifiers: [],
          isLoading: false
        }));
      }
    } catch (error) {
      console.error('Error fetching platform data:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to load platform data. Please try again.'
      }));
    }
  }, [productId]);

  // Initial data fetch
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Add a new platform identifier for the product with optimistic updates
  const addIdentifier = useCallback(async (data: {
    platform_id: string;
    platform_identifier: string;
    code_name?: string | null;
    pack_size: number;
  }) => {
    if (!productId) return;
    
    setState(prev => ({ ...prev, isSubmitting: true, error: null }));
    
    try {
      // Create the request payload
      const payload = {
        product_id: productId,
        ...data
      };
      
      // Generate a temporary ID for optimistic update
      const tempId = `temp-${Date.now()}`;
      
      // Find platform name for better user experience
      const platformName = state.platforms.find(p => p.id === data.platform_id)?.name || 'Unknown Platform';
      
      // Create an optimistic identifier for immediate UI feedback
      const optimisticIdentifier: ProductIdentifier = {
        id: tempId,
        product_id: productId,
        platform_id: data.platform_id,
        platform_name: platformName,
        platform_identifier: data.platform_identifier,
        code_name: data.code_name || null,
        pack_size: data.pack_size,
        created_at: new Date().toISOString(),
        updated_at: null
      };
      
      // Immediately update the UI with optimistic data
      setState(prev => ({
        ...prev,
        identifiers: [...prev.identifiers, optimisticIdentifier]
      }));

      // Create a wrapper that returns the service response in the expected format
      const apiWrapper = async (): Promise<ApiResponse<ProductIdentifier>> => {
        try {
          const result = await inventoryAlertsService.createProductIdentifier(payload);
          return { success: true, data: result, totalCount: 1 };
        } catch (error) {
          return { 
            success: false, 
            message: error instanceof Error ? error.message : 'Unknown error',
            data: {} as ProductIdentifier,
            totalCount: 0
          };
        }
      };

      // Perform the actual API call with optimistic update handling
      const result = await performOptimisticUpdate({
        initialData: state.identifiers,
        getOptimisticData: (initialData) => initialData, // Already updated the UI
        apiCall: apiWrapper,
        params: payload,
        onSuccess: (response) => {
          if (!response.data) return;
          
          // Replace the temporary identifier with the real one from the server
          setState(prev => ({
            ...prev,
            identifiers: prev.identifiers.map(identifier => 
              identifier.id === tempId ? response.data! : identifier
            ),
            isSubmitting: false
          }));
        },
        onError: (rollbackData) => {
          // Rollback by restoring the original identifiers
          setState(prev => ({
            ...prev,
            identifiers: rollbackData,
            isSubmitting: false,
            error: 'Failed to add platform identifier. Please try again.'
          }));
        }
      });
      
      if (!result.success) {
        throw new Error(result.message || 'Failed to add platform identifier');
      }
    } catch (error) {
      console.error('Error adding platform identifier:', error);
      setState(prev => ({
        ...prev,
        isSubmitting: false,
        error: 'Failed to add platform identifier. Please try again.'
      }));
    }
  }, [productId, state.platforms, state.identifiers]);

  // Update an existing platform identifier with optimistic updates
  const updateIdentifier = useCallback(async (id: string, data: {
    platform_identifier?: string;
    code_name?: string | null;
    pack_size?: number;
  }) => {
    setState(prev => ({ ...prev, isSubmitting: true, error: null }));
    
    try {
      // Find the current identifier to update
      const currentIdentifier = state.identifiers.find(identifier => identifier.id === id);
      if (!currentIdentifier) {
        throw new Error('Identifier not found');
      }
      
      // Create optimistically updated identifier
      const updatedIdentifier = {
        ...currentIdentifier,
        ...data,
        updated_at: new Date().toISOString()
      };
      
      // Immediately update the UI with optimistic data
      setState(prev => ({
        ...prev,
        identifiers: prev.identifiers.map(identifier => 
          identifier.id === id ? updatedIdentifier : identifier
        )
      }));

      // Create a wrapper that returns the service response in the expected format
      const apiWrapper = async (): Promise<ApiResponse<ProductIdentifier>> => {
        try {
          const result = await inventoryAlertsService.updateProductIdentifier(id, data);
          return { success: true, data: result, totalCount: 1 };
        } catch (error) {
          return { 
            success: false, 
            message: error instanceof Error ? error.message : 'Unknown error',
            data: {} as ProductIdentifier,
            totalCount: 0
          };
        }
      };

      // Perform the actual API call with optimistic update handling
      const result = await performOptimisticUpdate({
        initialData: state.identifiers,
        getOptimisticData: (initialData) => initialData, // Already updated the UI
        apiCall: apiWrapper,
        params: data,
        onSuccess: (response) => {
          if (!response.data) return;
          
          // Replace with actual server response data
          setState(prev => ({
            ...prev,
            identifiers: prev.identifiers.map(identifier => 
              identifier.id === id ? response.data! : identifier
            ),
            isSubmitting: false
          }));
        },
        onError: ( rollbackData) => {
          // Rollback by restoring the original identifiers
          setState(prev => ({
            ...prev,
            identifiers: rollbackData,
            isSubmitting: false,
            error: 'Failed to update platform identifier. Please try again.'
          }));
        }
      });

      if (!result.success) {
        throw new Error(result.message || 'Failed to update platform identifier');
      }
    } catch (error) {
      console.error('Error updating platform identifier:', error);
      setState(prev => ({
        ...prev,
        isSubmitting: false,
        error: 'Failed to update platform identifier. Please try again.'
      }));
    }
  }, [state.identifiers]);

  // Delete a platform identifier with optimistic updates
  const deleteIdentifier = useCallback(async (id: string) => {
    setState(prev => ({ ...prev, isSubmitting: true, error: null }));
    
    try {
      // Store original identifiers for potential rollback
      const originalIdentifiers = [...state.identifiers];
      
      // Immediately update the UI by removing the identifier
      setState(prev => ({
        ...prev,
        identifiers: prev.identifiers.filter(identifier => identifier.id !== id)
      }));

      // Create a wrapper that returns the service response in the expected format
      const apiWrapper = async (): Promise<ApiResponse<any>> => {
        try {
          await inventoryAlertsService.deleteProductIdentifier(id);
          return { success: true, data: null, totalCount: 0 };
        } catch (error) {
          return { 
            success: false, 
            message: error instanceof Error ? error.message : 'Unknown error',
            data: null,
            totalCount: 0
          };
        }
      };

      // Perform the actual API call with optimistic update handling
      const result = await performOptimisticUpdate({
        initialData: originalIdentifiers,
        getOptimisticData: (initialData) => initialData.filter(identifier => identifier.id !== id),
        apiCall: apiWrapper,
        params: id,
        onSuccess: () => {
          // UI already updated, just confirm we're done
          setState(prev => ({
            ...prev,
            isSubmitting: false
          }));
        },
        onError: (rollbackData) => {
          // Rollback by restoring the original identifiers
          setState(prev => ({
            ...prev,
            identifiers: rollbackData,
            isSubmitting: false,
            error: 'Failed to delete platform identifier. Please try again.'
          }));
        }
      });

      if (!result.success) {
        throw new Error(result.message || 'Failed to delete platform identifier');
      }
    } catch (error) {
      console.error('Error deleting platform identifier:', error);
      setState(prev => ({
        ...prev,
        isSubmitting: false,
        error: 'Failed to delete platform identifier. Please try again.'
      }));
    }
  }, [state.identifiers]);

  // Refresh identifiers for the product
  const refreshIdentifiers = useCallback(async () => {
    if (!productId) return;
    
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const response = await inventoryAlertsService.getProductIdentifiers(productId);
      
      setState(prev => ({
        ...prev,
        identifiers: response.data,
        isLoading: false
      }));
    } catch (error) {
      console.error('Error refreshing product identifiers:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to refresh product identifiers. Please try again.'
      }));
    }
  }, [productId]);

  return [state, { addIdentifier, updateIdentifier, deleteIdentifier, refreshIdentifiers }];
}; 