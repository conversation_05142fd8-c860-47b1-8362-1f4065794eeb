import React, { useMemo } from 'react';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { BUSINESS_UNIT_PERFORMANCE_DATA } from '@/shared/lib/mock-data/dashboard';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface BusinessUnitPerformanceProps {
  data?: Array<{
    name: string;
    value: number;
    color: string;
  }>;
}

const BusinessUnitPerformance: React.FC<BusinessUnitPerformanceProps> = ({ 
  data = BUSINESS_UNIT_PERFORMANCE_DATA 
}) => {
  // Task 3.6: Memoize expensive chart data transformations to avoid recalculation on every render
  const chartData = useMemo(() => ({
    labels: data.map(item => item.name),
    datasets: [
      {
        data: data.map(item => item.value),
        backgroundColor: data.map(item => item.color),
        borderColor: data.map(item => item.color),
        borderWidth: 1,
        borderRadius: 4,
        borderSkipped: false,
      },
    ],
  }), [data]);

  // Task 3.6: Memoize chart options object to prevent recreation on every render
  const options = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return `$${context.parsed.y.toLocaleString()}`;
          },
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100000,
        ticks: {
          stepSize: 10000,
          callback: function(value: any) {
            return `$${(value / 1000)}k`;
          },
        },
        grid: {
          color: '#f3f4f6',
        },
      },
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: '#6b7280',
          font: {
            size: 12,
          },
        },
      },
    },
  }), []);

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">Business Unit Performance</h3>
      <div className="h-80">
        <Bar data={chartData} options={options} />
      </div>
    </div>
  );
};

export default BusinessUnitPerformance;