import React, { useState, useRef, useEffect, useCallback, useMemo, memo } from 'react';
import { ChevronDownIcon, ChevronUpIcon, ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/outline';

export interface SortOption {
  value: string;
  label: string;
  direction?: 'asc' | 'desc';
}

interface SortDropdownProps {
  sortOptions: SortOption[];
  currentSort?: string;
  onSortChange: (sortValue: string) => void;
  placeholder?: string;
  className?: string;
  icon?: React.ReactNode;
}

// Custom comparison function for React.memo
const arePropsEqual = (prevProps: SortDropdownProps, nextProps: SortDropdownProps): boolean => {
  return (
    prevProps.currentSort === nextProps.currentSort &&
    prevProps.placeholder === nextProps.placeholder &&
    prevProps.className === nextProps.className &&
    prevProps.onSortChange === nextProps.onSortChange &&
    prevProps.icon === nextProps.icon &&
    // Compare sortOptions array (shallow comparison should be sufficient)
    prevProps.sortOptions.length === nextProps.sortOptions.length &&
    prevProps.sortOptions.every((option, index) => 
      option.value === nextProps.sortOptions[index]?.value &&
      option.label === nextProps.sortOptions[index]?.label &&
      option.direction === nextProps.sortOptions[index]?.direction
    )
  );
};

const SortDropdown: React.FC<SortDropdownProps> = ({
  sortOptions,
  currentSort,
  onSortChange,
  placeholder = "Sort by",
  className = "",
  icon
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Task 3.5: Optimize sort option lookup with Map for O(1) access instead of O(n) array.find()
  const sortOptionsMap = useMemo(() => {
    const map = new Map<string, SortOption>();
    sortOptions.forEach(option => {
      map.set(option.value, option);
    });
    return map;
  }, [sortOptions]);

  // Memoized close dropdown handler
  const handleClickOutside = useCallback((event: MouseEvent) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
      setIsOpen(false);
    }
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [handleClickOutside]);

  // Memoized current sort option
  const getCurrentSortOption = useCallback(() => {
    // Task 3.5: Use Map.get() for O(1) sort option lookup instead of array.find() O(n)
    return currentSort ? sortOptionsMap.get(currentSort) : undefined;
  }, [sortOptionsMap, currentSort]);

  // Memoized sort selection handler
  const handleSortSelect = useCallback((sortValue: string) => {
    onSortChange(sortValue);
    setIsOpen(false);
  }, [onSortChange]);

  // Memoized toggle handler
  const handleToggle = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  // Memoized display text
  const getDisplayText = useMemo(() => {
    const currentOption = getCurrentSortOption();
    return currentOption ? currentOption.label : placeholder;
  }, [getCurrentSortOption, placeholder]);

  // Memoized sort direction icon function
  const getSortIcon = useCallback((option: SortOption) => {
    if (option.direction === 'desc') {
      return <ArrowDownIcon className="h-4 w-4 text-gray-400" />;
    } else if (option.direction === 'asc') {
      return <ArrowUpIcon className="h-4 w-4 text-gray-400" />;
    }
    return null;
  }, []);

  // Memoized current sort icon
  const currentSortIcon = useMemo(() => {
    const currentOption = getCurrentSortOption();
    return currentOption ? getSortIcon(currentOption) : null;
  }, [getCurrentSortOption, getSortIcon]);

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Trigger button */}
      <button
        type="button"
        onClick={handleToggle}
        className="flex items-center justify-center w-10 h-10 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 hover:border-gray-400 transition-colors"
      >
        <div className="flex items-center justify-center align-right">
          {icon ? (
            // Show only icon when icon prop is provided
            icon
          ) : (
            // Show text and sort icon when no custom icon
            <>
              <div className="flex items-center gap-2">
                <span className={currentSort ? "text-gray-900" : "text-gray-500"}>
                  {getDisplayText}
                </span>
                {currentSort && currentSortIcon}
              </div>
              {isOpen ? (
                <ChevronUpIcon className="h-5 w-5 text-gray-400" />
              ) : (
                <ChevronDownIcon className="h-5 w-5 text-gray-400" />
              )}
            </>
          )}
        </div>
      </button>

      {/* Dropdown menu */}
      {isOpen && (
        <div className="absolute z-50 mt-2 right-0 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2">
          {sortOptions.map((option) => (
            <SortOptionItem
              key={option.value}
              option={option}
              isSelected={currentSort === option.value}
              onSelect={handleSortSelect}
              getSortIcon={getSortIcon}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Individual Sort Option Component with memoization
interface SortOptionProps {
  option: SortOption;
  isSelected: boolean;
  onSelect: (value: string) => void;
  getSortIcon: (option: SortOption) => React.ReactNode;
}

const SortOptionItem: React.FC<SortOptionProps> = memo(({ option, isSelected, onSelect, getSortIcon }) => {
  const handleClick = useCallback(() => {
    onSelect(option.value);
  }, [onSelect, option.value]);

  return (
    <button
      type="button"
      onClick={handleClick}
      className="w-full px-4 py-3 text-left text-sm transition-colors hover:bg-gray-50 flex items-center gap-3"
    >
      {/* Radio button style indicator */}
      <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
        isSelected 
          ? 'border-blue-500 bg-blue-500' 
          : 'border-gray-300'
      }`}>
        {isSelected && (
          <div className="w-2 h-2 bg-white rounded-full"></div>
        )}
      </div>
      
      {/* Sort direction icon */}
      <div className="flex items-center gap-2">
        {getSortIcon(option)}
        <span className="text-gray-700">{option.label}</span>
      </div>
    </button>
  );
});

SortOptionItem.displayName = 'SortOptionItem';

export default memo(SortDropdown, arePropsEqual);