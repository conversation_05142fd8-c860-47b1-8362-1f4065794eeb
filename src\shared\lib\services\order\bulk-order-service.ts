import { supabase } from '../../../../../supabase/supabase_client/client';
import { FailedOrder } from '@/shared/lib/hooks/orders/useBulkOrderCreation';
import { ApiResponse } from '@/shared/api';

interface BulkOrderRow {
  order_number: string;
  platform: string;
  channel: string;
  customer_email: string;
  customer_name?: string;
  shipping_street?: string;
  shipping_city?: string;
  shipping_state?: string;
  shipping_zip_code?: string;
  shipping_country?: string;
  product_sku: string;
  quantity: string | number;
  subtotal_item: string | number;
  [key: string]: any;
}

interface BulkOrderResult {
  success: boolean;
  message?: string;
  data?: any;
  successfulOrders?: any[];
  failedOrders?: FailedOrder[];
}

class BulkOrderService {
  /**
   * Create multiple orders from CSV data
   */
  async createBulkOrders(data: BulkOrderRow[]): Promise<BulkOrderResult> {
    try {
      // Group rows by order_number to combine multiple line items
      const orderMap = this.groupOrdersByOrderNumber(data);
      
      // Prepare the data for the Edge Function
      const bulkOrders = Object.values(orderMap);
      
      // Call the Edge Function for bulk processing
      const result = await this.callBulkOrderEdgeFunction(bulkOrders);
      
      return result;
    } catch (error) {
      console.error('Error in bulk order creation:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error during bulk order processing';
      
      return {
        success: false,
        message: errorMessage,
        failedOrders: data.map((item, index) => ({
          row: index + 2, // +2 because CSV header is row 1, and indexes are 0-based
          reason: errorMessage,
          data: item
        }))
      };
    }
  }
  
  /**
   * Group order rows by order number to combine multiple line items for the same order
   */
  private groupOrdersByOrderNumber(data: BulkOrderRow[]) {
    const orderMap: Record<string, any> = {};
    
    data.forEach((row, index) => {
      const orderNumber = row.order_number.trim();
      
      if (!orderMap[orderNumber]) {
        // Create new order entry
        orderMap[orderNumber] = {
          order_number: orderNumber,
          platform: row.platform,
          channel: row.channel,
          customer_email: row.customer_email,
          customer_name: row.customer_name || '',
          shipping_street: row.shipping_street || '',
          shipping_city: row.shipping_city || '',
          shipping_state: row.shipping_state || '',
          shipping_zip_code: row.shipping_zip_code || '',
          subtotal: row.subtotal || '',
          order_items: [],
          _row_indices: [index] // Keep track of row indices for error reporting
        };
      } else {
        // Add the row index to the existing order
        orderMap[orderNumber]._row_indices.push(index);
      }
      
      // Add the line item to the order
      if (row.product_sku) {
        // Parse quantity and subtotal_item from the row
        const quantity = parseInt(row.quantity as string) || 1;
        const subtotal_item = parseFloat(row.subtotal_item as string) || 0;
        
        orderMap[orderNumber].order_items.push({
          product_identifier: row.product_sku,
          quantity: quantity,
          subtotal_item: subtotal_item
        });
      }
    });
    
    return orderMap;
  }
  
  /**
   * Call the Edge Function to process bulk orders
   */
  private async callBulkOrderEdgeFunction(bulkOrders: any[]): Promise<BulkOrderResult> {
    try {
      // Call the Supabase Edge Function
      const { data, error } = await supabase.functions.invoke('bulk-order-create', {
        body: JSON.stringify({ orders: bulkOrders }),
      });
      
      if (error) {
        console.error('Edge Function error:', error);
        throw new Error(`Edge Function error: ${error.message}`);
      }
      
      // Process the response
      return {
        success: true,
        data,
        successfulOrders: data.successful || [],
        failedOrders: data.failed || []
      };
    } catch (error) {
      console.error('Error calling Edge Function:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to process orders';
      
      throw new Error(`Bulk order processing failed: ${errorMessage}`);
    }
  }
}

export const bulkOrderService = new BulkOrderService(); 