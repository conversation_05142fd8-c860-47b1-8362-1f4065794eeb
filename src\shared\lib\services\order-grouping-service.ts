import { FulfillOrderItem, ChannelGroup, BuyerGroup } from '@/types';

// Task 3.7: Optimized order grouping with minimal array operations
export const groupOrdersByChannel = (orders: FulfillOrderItem[]): ChannelGroup[] => {
  // Single-pass optimization: Build both platform map and result structure simultaneously
  const platformMap = new Map<string, Map<string, FulfillOrderItem[]>>();
  
  // Single pass through orders to build the optimized map structure
  for (const order of orders) {
    const platformName = order.platform || 'Unknown Platform';
    const channelName = order.store || 'Unknown Store';

    let channels = platformMap.get(platformName);
    if (!channels) {
      channels = new Map<string, FulfillOrderItem[]>();
      platformMap.set(platformName, channels);
    }
    
    let channelOrders = channels.get(channelName);
    if (!channelOrders) {
      channelOrders = [];
      channels.set(channelName, channelOrders);
    }
    
    channelOrders.push(order);
  }

  // Convert Map to result format with minimal iterations
  const result: ChannelGroup[] = [];
  for (const [platformName, channels] of platformMap) {
    const subChannels: { name: string; orders: FulfillOrderItem[] }[] = [];
    for (const [channelName, orders] of channels) {
      subChannels.push({ name: channelName, orders });
    }
    result.push({
      channel: platformName,
      subChannels
    });
  }
  
  return result;
};

// Task 3.7: Optimized buyer grouping with single-pass algorithm
export const groupPackedOrdersByBuyer = (orders: FulfillOrderItem[]): BuyerGroup[] => {
  // Single-pass optimization: Use Map for O(1) lookups instead of reduce with object access
  const buyerMap = new Map<string, FulfillOrderItem[]>();
  
  // Single pass through orders
  for (const order of orders) {
    const buyerName = order.buyer.name;
    let buyerOrders = buyerMap.get(buyerName);
    
    if (!buyerOrders) {
      buyerOrders = [];
      buyerMap.set(buyerName, buyerOrders);
    }
    
    buyerOrders.push(order);
  }
  
  // Convert Map to result format with minimal iterations
  const result: BuyerGroup[] = [];
  for (const [buyerName, orders] of buyerMap) {
    result.push({
      buyerName,
      orders
    });
  }
  
  return result;
};

// Task 3.7: New optimized function for combined channel and buyer grouping
export const groupOrdersOptimized = (orders: FulfillOrderItem[]): {
  channelGroups: ChannelGroup[];
  buyerGroups: BuyerGroup[];
} => {
  // Single-pass optimization: Build both groupings simultaneously
  const platformMap = new Map<string, Map<string, FulfillOrderItem[]>>();
  const buyerMap = new Map<string, FulfillOrderItem[]>();
  
  // Single pass through orders to build both groupings
  for (const order of orders) {
    // Channel grouping
    const platformName = order.platform || 'Unknown Platform';
    const channelName = order.store || 'Unknown Store';

    let channels = platformMap.get(platformName);
    if (!channels) {
      channels = new Map<string, FulfillOrderItem[]>();
      platformMap.set(platformName, channels);
    }
    
    let channelOrders = channels.get(channelName);
    if (!channelOrders) {
      channelOrders = [];
      channels.set(channelName, channelOrders);
    }
    channelOrders.push(order);
    
    // Buyer grouping (same pass)
    const buyerName = order.buyer.name;
    let buyerOrders = buyerMap.get(buyerName);
    if (!buyerOrders) {
      buyerOrders = [];
      buyerMap.set(buyerName, buyerOrders);
    }
    buyerOrders.push(order);
  }

  // Convert Maps to result formats
  const channelGroups: ChannelGroup[] = [];
  for (const [platformName, channels] of platformMap) {
    const subChannels: { name: string; orders: FulfillOrderItem[] }[] = [];
    for (const [channelName, orders] of channels) {
      subChannels.push({ name: channelName, orders });
    }
    channelGroups.push({
      channel: platformName,
      subChannels
    });
  }
  
  const buyerGroups: BuyerGroup[] = [];
  for (const [buyerName, orders] of buyerMap) {
    buyerGroups.push({
      buyerName,
      orders
    });
  }
  
  return { channelGroups, buyerGroups };
}; 