import React from 'react';
import { render, act } from '@testing-library/react';
import { AuthProvider, useAuth } from './AuthProvider';
import { AuthController } from '@/shared/lib/controllers/auth.controller';
import { webSocketService } from '@/shared/lib/services/websocket/websocket-service';
import { UserProfile } from '@/types';

// Mock dependencies
jest.mock('@/shared/lib/controllers/auth.controller');
jest.mock('@/shared/lib/services/websocket/websocket-service');

describe('AuthProvider', () => {
  // Mock data
  const mockSession = { user: { email: '<EMAIL>', id: '123' } };
  // Create a complete mock profile that matches the UserProfile interface
  const mockProfile: UserProfile = { 
    id: '123', 
    email: '<EMAIL>',
    full_name: 'Test User',
    role: 'admin',
    business_unit_id: null,
    is_active: true,
    last_login_at: '2023-07-01T12:00:00Z',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-07-01T12:00:00Z'
  };
  
  // Store original implementations
  let originalConnect: any;
  let originalDisconnect: any;
  let originalSubscribeToAuthChanges: any;
  let originalInitialize: any;
  
  // Setup for all tests
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Store original implementations
    originalConnect = webSocketService.connect;
    originalDisconnect = webSocketService.disconnect;
    originalSubscribeToAuthChanges = AuthController.subscribeToAuthChanges;
    originalInitialize = AuthController.initialize;
    
    // Setup mocks
    (webSocketService.connect as jest.Mock).mockResolvedValue(undefined);
    (webSocketService.disconnect as jest.Mock).mockResolvedValue(undefined);
    
    // Mock auth controller initialize
    (AuthController.initialize as jest.Mock).mockResolvedValue({
      session: mockSession,
      profile: mockProfile,
      isLoading: false
    });
    
    // Mock auth controller subscription
    (AuthController.subscribeToAuthChanges as jest.Mock).mockImplementation((callback) => {
      // Store callback for later use in tests
      (AuthController as any).latestCallback = callback;
      
      return {
        unsubscribe: jest.fn()
      };
    });
  });
  
  afterEach(() => {
    // Restore original implementations
    webSocketService.connect = originalConnect;
    webSocketService.disconnect = originalDisconnect;
    AuthController.subscribeToAuthChanges = originalSubscribeToAuthChanges;
    AuthController.initialize = originalInitialize;
  });
  
  // Test component to access auth context
  const TestComponent = () => {
    const auth = useAuth();
    return (
      <div data-testid="auth-info">
        {auth.user ? `Logged in: ${auth.user.email}` : 'Not logged in'}
      </div>
    );
  };
  
  it('should initialize auth and connect WebSocket on mount', async () => {
    await act(async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );
    });
    
    expect(AuthController.initialize).toHaveBeenCalledTimes(1);
    expect(webSocketService.connect).toHaveBeenCalledTimes(1);
  });
  
  it('should not connect WebSocket multiple times when auth events fire rapidly', async () => {
    await act(async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );
    });
    
    // First connect happens during initialization
    expect(webSocketService.connect).toHaveBeenCalledTimes(1);
    
    // Reset mock to check for additional calls
    (webSocketService.connect as jest.Mock).mockClear();
    
    // Simulate rapid auth state changes (multiple SIGNED_IN events)
    await act(async () => {
      // Get the callback that was stored during initialization
      const callback = (AuthController as any).latestCallback;
      
      // Call multiple times in quick succession
      callback(mockSession, mockProfile);
      callback(mockSession, mockProfile);
      callback(mockSession, mockProfile);
    });
    
    // Should only connect once despite multiple auth events
    expect(webSocketService.connect).toHaveBeenCalledTimes(0);
  });
  
  it('should disconnect WebSocket when component unmounts', async () => {
    // Explicitly define the return type and initialize
    let unmount: () => void = () => {};
    
    await act(async () => {
      const result = render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );
      unmount = result.unmount;
    });
    
    await act(async () => {
      unmount();
    });
    
    expect(webSocketService.disconnect).toHaveBeenCalledTimes(1);
  });
  
  it('should force disconnect WebSocket on logout', async () => {
    const TestLogoutComponent = () => {
      const auth = useAuth();
      React.useEffect(() => {
        // Trigger logout
        auth.logout?.();
      }, [auth]);
      
      return <div>Logout Test</div>;
    };
    
    await act(async () => {
      render(
        <AuthProvider>
          <TestLogoutComponent />
        </AuthProvider>
      );
    });
    
    // Should call disconnect with force=true
    expect(webSocketService.disconnect).toHaveBeenCalledWith(true);
  });
  
  it('should properly handle session expiry', async () => {
    await act(async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );
    });
    
    // Session expiry = null session
    await act(async () => {
      const callback = (AuthController as any).latestCallback;
      callback(null, null);
    });
    
    expect(webSocketService.disconnect).toHaveBeenCalledTimes(1);
  });
  
  it('should ignore duplicate auth state changes within throttle time', async () => {
    jest.useFakeTimers();
    
    await act(async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );
    });
    
    // Initial connect happens in initialize
    (webSocketService.connect as jest.Mock).mockClear();
    (webSocketService.disconnect as jest.Mock).mockClear();
    
    // Trigger auth state change
    await act(async () => {
      const callback = (AuthController as any).latestCallback;
      
      // First call (t=0)
      callback(mockSession, mockProfile);
      
      // Second call immediately after (t≈0)
      callback(mockSession, mockProfile);
    });
    
    // Should only process the first call
    expect(webSocketService.connect).toHaveBeenCalledTimes(0); // Already connected in initialization
    
    jest.useRealTimers();
  });
}); 