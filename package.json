{"name": "washolding-internal-dashboard", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "bundle:analyze": "node test/bundle-optimization.test.js", "test:config": "vitest run test/vite-config.test.js", "test:preloading": "node test/preloading-performance-test.js", "test:chunks": "vitest run test/chunk-dependencies.test.js", "analyze:chunks": "node scripts/analyze-vendor-chunks.js", "build:analyze": "vite build && node scripts/analyze-vendor-chunks.js"}, "dependencies": {"@heroicons/react": "^2.2.0", "@stagewise-plugins/react": "^0.4.6", "@stagewise/toolbar-react": "^0.4.6", "@supabase/supabase-js": "^2.50.0", "@tanstack/react-query": "^5.81.2", "@types/papaparse": "^5.3.16", "@types/react-window": "^1.8.8", "bootstrap-icons": "^1.13.1", "chart.js": "^4.5.0", "chartjs-adapter-date-fns": "^3.0.0", "clsx": "^2.1.1", "jest": "^30.0.3", "papaparse": "^5.5.3", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-router-dom": "^6.23.1", "react-vertical-timeline-component": "^3.5.3", "react-window": "^1.8.11", "recharts": "^2.15.3", "rxjs": "^7.8.2", "tailwind-merge": "^3.3.1", "tanstack": "^1.0.0", "use-debounce": "^10.0.5", "uuid": "^11.1.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.28.0", "@stagewise/toolbar": "^0.4.7", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.5.2", "@types/chart.js": "^2.9.41", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.19", "@types/node": "^22.14.0", "@types/react": "18.3.1", "@types/react-dom": "18.3.1", "@types/react-vertical-timeline-component": "^3.3.6", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.28.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.2.0", "jest-environment-jsdom": "^30.0.2", "postcss": "^8.4.47", "rollup-plugin-visualizer": "^6.0.3", "supabase": "^2.30.4", "tailwindcss": "^3.4.17", "terser": "^5.42.0", "ts-jest": "^29.4.0", "typescript": "~5.7.2", "typescript-eslint": "^8.34.0", "vite": "^6.3.5", "vitest": "^3.2.4"}, "overrides": {"node-domexception": {".": "npm:@remix-run/web-fetch@^4.4.2"}}, "engines": {"node": ">=18.0.0"}}