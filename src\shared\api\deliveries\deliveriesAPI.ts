import type {
  DeliveryListParams,
  DeliveryTrackingViewItem,
  DeliveryDetails,
  DeliveryEvent,
  DeliveryStatusEnum
} from '../../../types';
import { supabase } from '../../../../supabase/supabase_client/client';

// API Response type
interface ApiResponse<T> {
  data: T | null;
  success: boolean;
  message?: string;
  meta?: {
    totalCount?: number;
    page?: number;
    limit?: number;
  }
}


interface DeliveryUpdateData {
  status?: string;
  estimatedDelivery?: string;
  actualDelivery?: string;
  deliveryNotes?: string;
  recipientName?: string;
}

interface BulkAssignTrackingPayload {
  order_id: string;
  tracking_number: string;
  carrier_id: string;
}

// Deliveries API Functions
export const deliveriesApi = {
  // List deliveries with filtering and pagination
  async list(params: DeliveryListParams = {}): Promise<ApiResponse<DeliveryTrackingViewItem[]>> {
    const {
      page = 1,
      limit = 20,
      search,
      status,
      carrier,
      dateFrom,
      dateTo,
      requiresAction,
      sortBy,
      sortDirection,
    } = params;

    const rpcParams = {
      p_page: page,
      p_limit: limit,
      p_search_term: search,
      p_status_filter: status && status.length > 0 ? status : null,
      p_carrier_filter: carrier && carrier.length > 0 ? carrier : null,
      p_date_from: dateFrom,
      p_date_to: dateTo,
      p_requires_action: requiresAction,
      p_sort_by: sortBy || null,
      p_sort_direction: sortDirection || null,
    };

    const { data, error, count } = await supabase
      .rpc('get_deliveries_view', rpcParams, { 
        count: 'exact',
        head: false
      });

    if (error) {
      console.error('Error fetching deliveries:', error);
      return { success: false, data: [], message: error.message };
    }

    return {
      success: true,
      data: data || [],
      meta: {
        totalCount: count ?? 0,
        page,
        limit,
      },
    };
  },

  // Get detailed delivery information
  async getById(id: string): Promise<ApiResponse<DeliveryDetails>> {
    const { data, error } = await supabase
      .from('deliveries_view')
      .select(`
        id,
        order_id,
        order_number,
        tracking_number,
        carrier_name,
        carrier_code,
        customer_name,
        status,
        ship_date,
        estimated_delivery,
        actual_delivery,
        last_update,
        notes,
        destination,
        shipping_method
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Error fetching delivery details for id ${id}:`, error);
      return { success: false, data: null, message: error.message };
    }

    // Also fetch the delivery events for the timeline
    const { data: events, error: eventsError } = await supabase
      .from('delivery_events')
      .select('*')
      .eq('delivery_id', id)
      .order('event_timestamp', { ascending: false });

    if (eventsError) {
      // Log the error but don't fail the whole request
      console.error(`Error fetching events for delivery ${id}:`, eventsError);
    }
    
    // Combine the delivery details with its events
    const resultData = data ? { ...data, events: events || [] } : null;

    return { success: true, data: resultData as DeliveryDetails | null };
  },

  // Update delivery information
  async update(id: string, data: DeliveryUpdateData): Promise<ApiResponse<DeliveryDetails>> {
    const response = await fetch(`/api/deliveries/${id}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    return response.json();
  },

  // Get delivery events/timeline
  async getEvents(deliveryId: string): Promise<ApiResponse<DeliveryEvent[]>> {
    const response = await fetch(`/api/deliveries/${deliveryId}/events`);
    return response.json();
  },

  // Add delivery event
  async addEvent(deliveryId: string, event: {
    status: DeliveryStatusEnum;
    location?: string;
    description?: string;
    eventTimestamp?: string;
  }): Promise<ApiResponse<DeliveryEvent>> {
    const response = await fetch(`/api/deliveries/${deliveryId}/events`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(event),
    });
    return response.json();
  },

  // Update delivery status
  async updateStatus(id: string, status: DeliveryStatusEnum, notes?: string): Promise<ApiResponse<DeliveryDetails>> {
    const response = await fetch(`/api/deliveries/${id}/status`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ status, notes }),
    });
    return response.json();
  },

  // Mark for resend
  async markForResend(id: string, reason?: string): Promise<ApiResponse<void>> {
    const response = await fetch(`/api/deliveries/${id}/mark-resend`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ reason }),
    });
    return response.json();
  },

  // Mark for refund
  async markForRefund(id: string, reason?: string): Promise<ApiResponse<void>> {
    const response = await fetch(`/api/deliveries/${id}/mark-refund`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ reason }),
    });
    return response.json();
  },

  // Get delivery statistics
  async getStats(params?: { 
    period?: string;
    carrier?: string[];
    status?: string[];
  }): Promise<ApiResponse<{
    total: number;
    inTransit: number;
    outForDelivery: number;
    delivered: number;
    delayed: number;
    exception: number;
    returned: number;
    lost: number;
    requiresAction: number;
  }>> {
    const { data, error } = await supabase.rpc('get_delivery_stats', {
      p_period: params?.period,
      p_carrier: params?.carrier,
      p_status: params?.status,
    });

    if (error) {
      console.error('Error fetching delivery stats:', error);
      return { success: false, data: null, message: error.message };
    }
    
    // The RPC function is expected to return a single row with the stats object
    return { success: true, data: data[0] || null };
  },

  // Track by tracking number
  async trackByNumber(trackingNumber: string): Promise<ApiResponse<DeliveryDetails>> {
    const response = await fetch(`/api/deliveries/track/${trackingNumber}`);
    return response.json();
  },

  // Update from carrier webhook
  async updateFromWebhook(trackingNumber: string, payload: any): Promise<ApiResponse<void>> {
    const response = await fetch(`/api/deliveries/webhook/${trackingNumber}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });
    return response.json();
  },

  // Get deliveries requiring attention
  async getRequiringAction(): Promise<ApiResponse<DeliveryTrackingViewItem[]>> {
    const response = await fetch('/api/deliveries/requiring-action');
    return response.json();
  },

  // Get delayed deliveries
  async getDelayed(): Promise<ApiResponse<DeliveryTrackingViewItem[]>> {
    const response = await fetch('/api/deliveries/delayed');
    return response.json();
  },

  // Get deliveries with exceptions
  async getWithExceptions(): Promise<ApiResponse<DeliveryTrackingViewItem[]>> {
    const response = await fetch('/api/deliveries/exceptions');
    return response.json();
  },

  // // Assign delivery number to a batch of orders
  // async assignDeliveryNumber(orderIds: string[]): Promise<ApiResponse<{ success: boolean; data?: any; message?: string }>> {
  //   const response = await fetch('/api/deliveries/bulk-assign-tracking', {
  //     method: 'POST',
  //     headers: {
  //       'Content-Type': 'application/json',
  //     },
  //     body: JSON.stringify({ orderIds }),
  //   });
  //   return response.json();
  // },

  async bulkAssignTracking(payload: BulkAssignTrackingPayload[]): Promise<ApiResponse<any>> {
    try {
      // Make sure we have valid data before making the call
      if (!payload || payload.length === 0) {
        return { data: null, success: false, message: "No orders provided for tracking assignment" };
      }

      // Validate each payload item has required fields
      const invalidItems = payload.filter(item => 
        !item.order_id || !item.tracking_number || !item.carrier_id
      );
      
      if (invalidItems.length > 0) {
        return { 
          data: null, 
          success: false, 
          message: "Missing required fields in some orders" 
        };
      }

      const { data, error } = await supabase.functions.invoke('bulk-assign-tracking', {
        method: 'POST',
        body: payload,
      });

      if (error) {
        console.error('Error invoking bulk-assign-tracking function:', error);
        return { data: null, success: false, message: error.message };
      }

      return { data, success: true };
    } catch (err) {
      console.error('Exception in bulkAssignTracking:', err);
      return { 
        data: null, 
        success: false, 
        message: err instanceof Error ? err.message : 'Unknown error occurred'
      };
    }
  }
};

export default deliveriesApi; 