// Super simple debug script for chunk assignment

console.log('Testing chunk assignment');

const paths = {
  reactCore: '/node_modules/react/index.js',
  reactRouter: '/node_modules/react-router/index.js',
  reactRouterDom: '/node_modules/react-router-dom/index.js',
  reactChartJs: '/node_modules/react-chartjs-2/index.js'
};

function getChunk(path) {
  // React Router first
  if (path.includes('react-router')) {
    return 'vendor-routing';
  }
  
  // Chart libs
  if (path.includes('react-chartjs-2')) {
    return 'vendor-charts';
  }
  
  // React core
  if (path.includes('/react/')) {
    return 'vendor-react-core';
  }
  
  return 'unknown';
}

// React Router test
console.log('=== React Router test ===');
console.log(`Path: ${paths.reactRouter}`);
console.log(`Result: ${getChunk(paths.reactRouter)}`);

// React Router DOM test
console.log('\n=== React Router DOM test ===');
console.log(`Path: ${paths.reactRouterDom}`);
console.log(`Result: ${getChunk(paths.reactRouterDom)}`);

// React ChartJS test
console.log('\n=== React ChartJS test ===');
console.log(`Path: ${paths.reactChartJs}`);
console.log(`Result: ${getChunk(paths.reactChartJs)}`);

// React Core test
console.log('\n=== React Core test ===');
console.log(`Path: ${paths.reactCore}`);
console.log(`Result: ${getChunk(paths.reactCore)}`);

console.log('\nTests completed'); 