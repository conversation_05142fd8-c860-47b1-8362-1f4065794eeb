import React, { useState, useEffect, useCallback, useMemo, lazy, Suspense } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { deliveryService } from '@/shared/lib/services/delivery/delivery-service';
import { webSocketService } from '@/shared/lib/services/websocket/websocket-service';
import { WebSocketMessage } from '@/shared/lib/services/websocket/websocket-config';
import { DeliveryTrackingViewItem, DeliveryListParams } from '@/types';
import LoadingSpinner from '@/shared/ui/feedback/LoadingSpinner';
import { 
  getFiltersFromUrl, 
  updateUrlWithFilters, 
  subscribeToUrlChanges,
  ExtendedDeliveryParams
} from '@/shared/lib/utils/data/url-params';
import { useShipmentsList } from '@/shared/lib/hooks/delivery';


// Dynamic imports for delivery feature components
const DeliveryTable = lazy(() => import('@/features/delivery-features/delivery-table/DeliveryTable'));
const BulkActionsToolbar = lazy(() => import('@/features/delivery-features/delivery-actions/BulkActionsToolbar'));
const FilterBar = lazy(() => import('@/features/delivery-features/delivery-filters/FilterBar'));
const ShipmentDetailPanel = lazy(() => import('@/features/delivery-features/delivery-detail-panel/ShipmentDetailPanel'));
const DeliveryProcessBar = lazy(() => import('@/features/delivery-features/delivery-process-bar/DeliveryProcessBar'));

// Simple loading component for Suspense fallback
const FeatureLoading = () => (
  <div className="flex justify-center items-center p-6">
    <LoadingSpinner size="md" message="Loading component..." />
  </div>
);

const DeliveryPage: React.FC = () => {
  const queryClient = useQueryClient();
  
  // Get initial state from URL parameters or use defaults
  const initialFilters = useMemo(() => {
    const urlFilters = getFiltersFromUrl<ExtendedDeliveryParams>('delivery');
    // Don't set default sortBy and sortDirection if they're not provided in the URL
    return {
      ...urlFilters
    } as Omit<DeliveryListParams, 'page' | 'limit'>;
  }, []);

  const initialPage = useMemo(() => {
    const urlFilters = getFiltersFromUrl<ExtendedDeliveryParams>('delivery');
    // Ensure page is a valid number
    const pageParam = urlFilters.page;
    const page = typeof pageParam === 'string' ? parseInt(pageParam, 10) : (pageParam || 1);
    return isNaN(page) ? 1 : Math.max(1, page);
  }, []);

  // State for filters, pagination, and sorting
  const [filters, setFilters] = useState<Omit<DeliveryListParams, 'page' | 'limit'>>(initialFilters);
  const [pagination, setPagination] = useState({ page: initialPage, limit: 20 }); // 20 items per page as per requirements
  const [selectedRowIds, setSelectedRowIds] = useState<string[]>([]);
  const [selectedShipmentId, setSelectedShipmentId] = useState<string | null>(null);
  const [isPageTransitioning, setIsPageTransitioning] = useState(false);

  // Derived query parameters from state
  const queryParams: DeliveryListParams = useMemo(() => ({ 
    ...filters, 
    ...pagination 
  }), [filters, pagination]);

  // --- URL SYNCHRONIZATION ---
  // Update URL when filters or pagination change
  useEffect(() => {
    updateUrlWithFilters({ ...filters, ...pagination }, false);
  }, [filters, pagination]);

  // Subscribe to URL changes (browser back/forward navigation)
  useEffect(() => {
    const unsubscribe = subscribeToUrlChanges<ExtendedDeliveryParams>((newFilters) => {
      // Handle page and other filter changes from URL
      const { page, limit, ...restFilters } = newFilters;
      
      // Update filters
      setFilters(current => ({
        ...current,
        ...restFilters
      }));
      
      // Update pagination - ensure page is a valid number
      const pageParam = page;
      const parsedPage = typeof pageParam === 'string' ? parseInt(pageParam, 10) : (pageParam || 1);
      setPagination({
        page: isNaN(parsedPage) ? 1 : Math.max(1, parsedPage),
        limit: limit || 20
      });
    }, 'delivery');
    
    return unsubscribe;
  }, []);

  // --- DATA FETCHING using the hook ---
  const { 
    shipments: deliveries,
    isLoading,
    isFetching,
    error: deliveriesError,
    pagination: paginationData
  } = useShipmentsList({ params: queryParams });

  // Log pagination data for debugging
  useEffect(() => {
    if (!isLoading && !isFetching) {
      // console.log('Pagination data:', {
      //   currentPage: paginationData.currentPage,
      //   totalPages: paginationData.totalPages,
      //   totalCount: paginationData.totalCount,
      //   pageSize: paginationData.pageSize,
      //   hasNextPage: paginationData.hasNextPage,
      //   hasPreviousPage: paginationData.hasPreviousPage
      // });
    }
  }, [isLoading, isFetching, paginationData]);

  // Reset page transitioning state when fetching completes
  useEffect(() => {
    if (!isFetching && isPageTransitioning) {
      setIsPageTransitioning(false);
    }
  }, [isFetching, isPageTransitioning]);

  const { data: statsData, isLoading: isLoadingStats } = useQuery({
    queryKey: ['deliveryStats'],
    queryFn: () => deliveryService.getDeliveryStats(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
  
  // --- REAL-TIME UPDATES with WebSockets ---
  useEffect(() => {
    const handleDeliveryUpdate = (message: WebSocketMessage) => {
      if (message.type !== 'DELIVERY_UPDATE') return;
      
      const updatedDelivery = message.payload as DeliveryTrackingViewItem;
      console.log('Received DELIVERY_UPDATE:', updatedDelivery);

      // Surgically update the cache
      queryClient.setQueryData(['deliveries', queryParams], (oldData: any) => {
        if (!oldData || !oldData.data) return oldData;
        
        const updatedItems = oldData.data.map((item: DeliveryTrackingViewItem) =>
          item.id === updatedDelivery.id ? updatedDelivery : item
        );

        return { ...oldData, data: updatedItems };
      });
    };

    webSocketService.on('DELIVERY_UPDATE', handleDeliveryUpdate);

    return () => {
      webSocketService.off('DELIVERY_UPDATE', handleDeliveryUpdate);
    };
  }, [queryClient, queryParams]);


  // --- Event Handlers ---
  // Filter change handler - resets pagination to page 1 when filters change
  // This ensures users see the first page of results after applying filters
  const handleFilterChange = useCallback((newFilters: Partial<Omit<DeliveryListParams, 'page' | 'limit'>>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page on filter change
    setSelectedRowIds([]);
  }, []);
  
  const handlePageChange = useCallback((newPage: number) => {
    // Only set transition state if we're actually changing pages
    if (newPage !== pagination.page) {
      // Pre-cache next page to ensure smooth transitions
      const nextPageParams = { ...queryParams, page: newPage };
      queryClient.prefetchQuery({
        queryKey: ['deliveries', nextPageParams],
        queryFn: () => deliveryService.getDeliveries(nextPageParams)
      });
      
      // Set transition state to show loading
      setIsPageTransitioning(true);
      
      // Ensure page is valid
      const validPage = Math.max(1, newPage);
      setPagination(prev => ({ ...prev, page: validPage }));
      
      // Scroll to top on page change
      window.scrollTo({ top: 0, behavior: 'smooth' });
      
      // Clear selected rows when changing pages
      setSelectedRowIds([]);
    }
  }, [pagination.page, queryParams, queryClient]);

  const handleRowSelection = useCallback((rowId: string, isSelected: boolean) => {
    setSelectedRowIds(prev => isSelected ? [...prev, rowId] : prev.filter(id => id !== rowId));
  }, []);

  const handleSelectAll = useCallback((isSelected: boolean) => {
    setSelectedRowIds(isSelected ? deliveries.map(d => d.id) : []);
  }, [deliveries]);
  
  const handleRowClick = useCallback((deliveryId: string) => {
    setSelectedShipmentId(deliveryId);
  }, []);

  // --- Memoized Props ---
  const paginationProps = useMemo(() => ({
    currentPage: paginationData.currentPage,
    pageSize: paginationData.pageSize,
    totalCount: paginationData.totalCount,
    onPageChange: handlePageChange,
  }), [paginationData, handlePageChange]);

  // --- Loading State Management ---
  const combinedIsLoading = isLoading || isPageTransitioning;

  return (
    <div className="p-4 md:p-6 lg:p-8 bg-gray-50 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <header className="mb-6">
          <h1 className="text-3xl font-bold text-gray-800">Delivery Tracking</h1>
          <p className="text-gray-500 mt-1">Monitor all ongoing and completed shipments.</p>
        </header>

        <Suspense fallback={<FeatureLoading />}>
          <DeliveryProcessBar 
            stats={statsData?.data ?? undefined} 
            isLoading={isLoadingStats} 
            onReviewIssues={() => handleFilterChange({ requiresAction: true })} 
          />
        </Suspense>

        <div className="bg-white rounded-lg shadow-md mt-6">
          <Suspense fallback={<FeatureLoading />}>
            <FilterBar
              onFilterChange={handleFilterChange}
              initialFilters={filters}
            />
          </Suspense>

          <Suspense fallback={<FeatureLoading />}>
            <BulkActionsToolbar
              selectedCount={selectedRowIds.length}
              onMarkAsResend={() => console.log('Resend:', selectedRowIds)}
              onMarkForRefund={() => console.log('Refund:', selectedRowIds)}
              onClearSelection={() => setSelectedRowIds([])}
            />
          </Suspense>

          <Suspense fallback={<FeatureLoading />}>
            <DeliveryTable
              deliveries={deliveries}
              isLoading={combinedIsLoading}
              error={deliveriesError as Error | null}
              onRowClick={handleRowClick}
              selectedRowIds={selectedRowIds}
              onRowSelection={handleRowSelection}
              onSelectAll={handleSelectAll}
              pagination={paginationProps}
            />
          </Suspense>
        </div>
      </div>

      <Suspense fallback={<FeatureLoading />}>
        <ShipmentDetailPanel
          shipmentId={selectedShipmentId}
          isOpen={!!selectedShipmentId}
          onClose={() => setSelectedShipmentId(null)}
        />
      </Suspense>
    </div>
  );
};

export default DeliveryPage;