import { expect, test, describe } from 'vitest';

// Import helper functions directly from a testing module
const isModuleFrom = (id, pkgName) => {
  return id.includes(`node_modules/${pkgName}`);
};

const isInGroup = (id, pkgNames) => {
  return pkgNames.some(pkg => isModuleFrom(id, pkg));
};

// Mock function to simulate manualChunks
function getChunkName(id) {
  // Vendor chunk splitting strategy
  if (id.includes('node_modules')) {
    // 1. React Router (commonly used but not on every page)
    // Check this BEFORE generic react check
    if (id.includes('node_modules/react-router') || id.includes('node_modules/@remix-run/router')) {
      return 'vendor-routing';
    }
    
    // 2. Chart libraries (very large, only needed on specific pages)
    // Check this BEFORE generic react check
    if (
      id.includes('node_modules/recharts') || 
      id.includes('node_modules/chart.js') || 
      id.includes('node_modules/react-chartjs-2')
    ) {
      return 'vendor-charts';
    }
    
    // 3. React DOM (larger, separate chunk)
    if (id.includes('node_modules/react-dom')) {
      return 'vendor-react-dom';
    }
    
    // 4. React ecosystem - split core from DOM which is larger
    if (id.includes('node_modules/react') && !id.includes('react-dom')) {
      return 'vendor-react-core';
    }
    
    // 5. Supabase SDK (large but only needed for authenticated features)
    if (id.includes('node_modules/@supabase')) {
      return 'vendor-supabase';
    }
    
    // 6. D3 dependencies (often used by chart libraries)
    if (
      id.includes('node_modules/d3') || 
      id.includes('node_modules/internmap') ||
      id.includes('node_modules/delaunator')
    ) {
      return 'vendor-d3';
    }
    
    // 7. UI utilities commonly used across app
    const uiLibs = ['@heroicons', 'react-icons', 'bootstrap-icons', 'clsx', 'tailwind-merge'];
    if (isInGroup(id, uiLibs)) {
      return 'vendor-ui-utils';
    }
    
    // 8. Data processing libs
    const dataLibs = ['papaparse', 'zustand', '@tanstack'];
    if (isInGroup(id, dataLibs)) {
      return 'vendor-data-utils';
    }
    
    // 9. Stagewise plugins (separate chunk as they're specific to certain features)
    if (id.includes('node_modules/@stagewise')) {
      return 'vendor-stagewise';
    }
    
    // 10. Everything else in smaller shared vendor chunk
    return 'vendor-others';
  }
  
  // Features and pages chunks
  if (id.includes('/features/orders-features/')) {
    return 'feature-orders';
  }
  
  if (id.includes('/pages/dashboard/')) {
    return 'page-dashboard';
  }
  
  // Default case - let Vite handle it
  return undefined;
}

describe('Vite Config Helper Functions', () => {
  test('isModuleFrom correctly identifies modules', () => {
    expect(isModuleFrom('/node_modules/react/index.js', 'react')).toBe(true);
    expect(isModuleFrom('/node_modules/@heroicons/react/24/outline/index.js', '@heroicons')).toBe(true);
    expect(isModuleFrom('/node_modules/react/index.js', 'vue')).toBe(false);
  });
  
  test('isInGroup correctly identifies module groups', () => {
    expect(isInGroup('/node_modules/react-icons/index.js', ['react-icons', '@heroicons'])).toBe(true);
    expect(isInGroup('/node_modules/lodash/index.js', ['react', 'vue'])).toBe(false);
  });
});

describe('Manual Chunks Function', () => {
  test('assigns React core modules correctly', () => {
    expect(getChunkName('/node_modules/react/index.js')).toBe('vendor-react-core');
    expect(getChunkName('/node_modules/react/jsx-runtime.js')).toBe('vendor-react-core');
  });
  
  test('assigns React DOM modules correctly', () => {
    expect(getChunkName('/node_modules/react-dom/index.js')).toBe('vendor-react-dom');
    expect(getChunkName('/node_modules/react-dom/client.js')).toBe('vendor-react-dom');
  });
  
  test('assigns React Router modules correctly', () => {
    expect(getChunkName('/node_modules/react-router/index.js')).toBe('vendor-routing');
    expect(getChunkName('/node_modules/@remix-run/router/index.js')).toBe('vendor-routing');
  });
  
  test('assigns Chart libraries correctly', () => {
    expect(getChunkName('/node_modules/recharts/index.js')).toBe('vendor-charts');
    expect(getChunkName('/node_modules/chart.js/auto/index.js')).toBe('vendor-charts');
    expect(getChunkName('/node_modules/react-chartjs-2/index.js')).toBe('vendor-charts');
  });
  
  test('assigns UI utility libraries correctly', () => {
    expect(getChunkName('/node_modules/@heroicons/react/24/outline/index.js')).toBe('vendor-ui-utils');
    expect(getChunkName('/node_modules/clsx/index.js')).toBe('vendor-ui-utils');
  });
  
  test('assigns feature modules correctly', () => {
    expect(getChunkName('/src/features/orders-features/orders-table/OrdersTable.tsx')).toBe('feature-orders');
  });
  
  test('assigns page modules correctly', () => {
    expect(getChunkName('/src/pages/dashboard/index.tsx')).toBe('page-dashboard');
  });
  
  test('assigns other vendor modules correctly', () => {
    expect(getChunkName('/node_modules/lodash/index.js')).toBe('vendor-others');
  });
}); 