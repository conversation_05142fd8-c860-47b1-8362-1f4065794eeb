/**
 * Offline storage utility for managing data persistence when the app is offline
 */

/**
 * Storage key prefix for all offline data
 */
export const OFFLINE_STORAGE_PREFIX = 'washolding_offline_';

/**
 * Types of operations that can be queued for offline processing
 */
export enum OfflineOperationType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  BATCH = 'batch'
}

/**
 * Structure for pending offline operations
 */
export interface OfflinePendingOperation<T = any> {
  id: string; // Unique ID for the operation
  type: OfflineOperationType;
  entityType: string; // e.g., 'inventory', 'product', 'order'
  data: T; // Operation data
  timestamp: number; // When the operation was added
  endpoint?: string; // API endpoint for the operation
  retryCount?: number; // Number of times the operation has been retried
}

/**
 * Status of offline operations synchronization
 */
export interface OfflineSyncStatus {
  lastSyncTimestamp: number | null;
  pendingOperations: number;
  isSyncing: boolean;
  hasErrors: boolean;
  errorDetails?: string[];
}

/**
 * Main class for managing offline storage operations
 */
export class OfflineStorage {
  private static instance: OfflineStorage;
  private pendingOperationsKey: string;
  private offlineDataKey: string;
  private syncStatusKey: string;
  
  // Private constructor for singleton pattern
  private constructor() {
    this.pendingOperationsKey = `${OFFLINE_STORAGE_PREFIX}pending_operations`;
    this.offlineDataKey = `${OFFLINE_STORAGE_PREFIX}data`;
    this.syncStatusKey = `${OFFLINE_STORAGE_PREFIX}sync_status`;
  }
  
  /**
   * Get the singleton instance of OfflineStorage
   */
  public static getInstance(): OfflineStorage {
    if (!OfflineStorage.instance) {
      OfflineStorage.instance = new OfflineStorage();
    }
    return OfflineStorage.instance;
  }
  
  /**
   * Check if the browser supports offline storage
   */
  public isSupported(): boolean {
    try {
      return typeof localStorage !== 'undefined' && localStorage !== null;
    } catch (e) {
      return false;
    }
  }
  
  /**
   * Save entity data for offline use
   * @param entityType Type of entity (e.g., 'inventory', 'product')
   * @param key Unique key for the entity
   * @param data Entity data to store
   */
  public saveOfflineData<T>(entityType: string, key: string, data: T): void {
    if (!this.isSupported()) return;
    
    try {
      const offlineData = this.getOfflineDataStore();
      if (!offlineData[entityType]) {
        offlineData[entityType] = {};
      }
      
      offlineData[entityType][key] = {
        data,
        timestamp: Date.now()
      };
      
      localStorage.setItem(this.offlineDataKey, JSON.stringify(offlineData));
    } catch (error) {
      console.error('Error saving offline data:', error);
    }
  }
  
  /**
   * Save bulk entity data for offline use
   * @param entityType Type of entity (e.g., 'inventory', 'product')
   * @param dataMap Map of key to data entries
   */
  public saveBulkOfflineData<T>(entityType: string, dataMap: Record<string, T>): void {
    if (!this.isSupported()) return;
    
    try {
      const offlineData = this.getOfflineDataStore();
      if (!offlineData[entityType]) {
        offlineData[entityType] = {};
      }
      
      const timestamp = Date.now();
      Object.entries(dataMap).forEach(([key, data]) => {
        offlineData[entityType][key] = {
          data,
          timestamp
        };
      });
      
      localStorage.setItem(this.offlineDataKey, JSON.stringify(offlineData));
    } catch (error) {
      console.error('Error saving bulk offline data:', error);
    }
  }
  
  /**
   * Get offline data for an entity
   * @param entityType Type of entity (e.g., 'inventory', 'product')
   * @param key Unique key for the entity
   * @returns The stored entity data or null if not found
   */
  public getOfflineData<T>(entityType: string, key: string): T | null {
    if (!this.isSupported()) return null;
    
    try {
      const offlineData = this.getOfflineDataStore();
      if (!offlineData[entityType] || !offlineData[entityType][key]) {
        return null;
      }
      
      return offlineData[entityType][key].data as T;
    } catch (error) {
      console.error('Error retrieving offline data:', error);
      return null;
    }
  }
  
  /**
   * Get all offline data for an entity type
   * @param entityType Type of entity (e.g., 'inventory', 'product')
   * @returns Map of all entities of the given type
   */
  public getAllOfflineData<T>(entityType: string): Record<string, T> {
    if (!this.isSupported()) return {};
    
    try {
      const offlineData = this.getOfflineDataStore();
      if (!offlineData[entityType]) {
        return {};
      }
      
      const result: Record<string, T> = {};
      Object.entries(offlineData[entityType]).forEach(([key, entry]) => {
        result[key] = entry.data as T;
      });
      
      return result;
    } catch (error) {
      console.error('Error retrieving all offline data:', error);
      return {};
    }
  }
  
  /**
   * Add a pending operation for offline processing
   * @param operation Operation to queue
   */
  public addPendingOperation<T>(operation: Omit<OfflinePendingOperation<T>, 'id' | 'timestamp'>): string {
    if (!this.isSupported()) return '';
    
    try {
      const pendingOperations = this.getPendingOperations();
      const id = `${operation.entityType}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      
      const newOperation: OfflinePendingOperation<T> = {
        ...operation,
        id,
        timestamp: Date.now(),
        retryCount: 0
      };
      
      pendingOperations.push(newOperation);
      localStorage.setItem(this.pendingOperationsKey, JSON.stringify(pendingOperations));
      
      // Update sync status
      const syncStatus = this.getSyncStatus();
      syncStatus.pendingOperations = pendingOperations.length;
      this.updateSyncStatus(syncStatus);
      
      return id;
    } catch (error) {
      console.error('Error adding pending operation:', error);
      return '';
    }
  }
  
  /**
   * Get all pending operations
   * @returns Array of pending operations
   */
  public getPendingOperations<T = any>(): OfflinePendingOperation<T>[] {
    if (!this.isSupported()) return [];
    
    try {
      const pendingOperationsJson = localStorage.getItem(this.pendingOperationsKey);
      return pendingOperationsJson ? JSON.parse(pendingOperationsJson) : [];
    } catch (error) {
      console.error('Error retrieving pending operations:', error);
      return [];
    }
  }
  
  /**
   * Get pending operations for a specific entity type
   * @param entityType Type of entity (e.g., 'inventory', 'product')
   * @returns Array of pending operations for the entity type
   */
  public getPendingOperationsForEntity<T = any>(entityType: string): OfflinePendingOperation<T>[] {
    const allOperations = this.getPendingOperations<T>();
    return allOperations.filter(op => op.entityType === entityType);
  }
  
  /**
   * Remove a pending operation
   * @param operationId ID of the operation to remove
   */
  public removePendingOperation(operationId: string): void {
    if (!this.isSupported()) return;
    
    try {
      const pendingOperations = this.getPendingOperations();
      const updatedOperations = pendingOperations.filter(op => op.id !== operationId);
      localStorage.setItem(this.pendingOperationsKey, JSON.stringify(updatedOperations));
      
      // Update sync status
      const syncStatus = this.getSyncStatus();
      syncStatus.pendingOperations = updatedOperations.length;
      this.updateSyncStatus(syncStatus);
    } catch (error) {
      console.error('Error removing pending operation:', error);
    }
  }
  
  /**
   * Clear all offline data
   */
  public clearAllOfflineData(): void {
    if (!this.isSupported()) return;
    
    try {
      localStorage.removeItem(this.offlineDataKey);
    } catch (error) {
      console.error('Error clearing offline data:', error);
    }
  }
  
  /**
   * Clear all pending operations
   */
  public clearPendingOperations(): void {
    if (!this.isSupported()) return;
    
    try {
      localStorage.removeItem(this.pendingOperationsKey);
      
      // Update sync status
      const syncStatus = this.getSyncStatus();
      syncStatus.pendingOperations = 0;
      syncStatus.hasErrors = false;
      syncStatus.errorDetails = [];
      this.updateSyncStatus(syncStatus);
    } catch (error) {
      console.error('Error clearing pending operations:', error);
    }
  }
  
  /**
   * Get the current sync status
   */
  public getSyncStatus(): OfflineSyncStatus {
    if (!this.isSupported()) {
      return {
        lastSyncTimestamp: null,
        pendingOperations: 0,
        isSyncing: false,
        hasErrors: false
      };
    }
    
    try {
      const syncStatusJson = localStorage.getItem(this.syncStatusKey);
      if (!syncStatusJson) {
        const defaultStatus: OfflineSyncStatus = {
          lastSyncTimestamp: null,
          pendingOperations: this.getPendingOperations().length,
          isSyncing: false,
          hasErrors: false
        };
        localStorage.setItem(this.syncStatusKey, JSON.stringify(defaultStatus));
        return defaultStatus;
      }
      
      return JSON.parse(syncStatusJson);
    } catch (error) {
      console.error('Error retrieving sync status:', error);
      return {
        lastSyncTimestamp: null,
        pendingOperations: 0,
        isSyncing: false,
        hasErrors: false
      };
    }
  }
  
  /**
   * Update the sync status
   * @param status New sync status
   */
  public updateSyncStatus(status: Partial<OfflineSyncStatus>): void {
    if (!this.isSupported()) return;
    
    try {
      const currentStatus = this.getSyncStatus();
      const updatedStatus = { ...currentStatus, ...status };
      localStorage.setItem(this.syncStatusKey, JSON.stringify(updatedStatus));
    } catch (error) {
      console.error('Error updating sync status:', error);
    }
  }
  
  /**
   * Mark the start of a synchronization process
   */
  public markSyncStart(): void {
    this.updateSyncStatus({
      isSyncing: true
    });
  }
  
  /**
   * Mark the completion of a synchronization process
   * @param hasErrors Whether there were errors during synchronization
   * @param errorDetails Details of any errors that occurred
   */
  public markSyncComplete(hasErrors: boolean = false, errorDetails?: string[]): void {
    this.updateSyncStatus({
      lastSyncTimestamp: Date.now(),
      isSyncing: false,
      hasErrors,
      errorDetails
    });
  }
  
  /**
   * Check if the application is currently online
   */
  public isOnline(): boolean {
    return navigator.onLine;
  }
  
  /**
   * Helper method to get the offline data store
   */
  private getOfflineDataStore(): Record<string, Record<string, { data: any, timestamp: number }>> {
    try {
      const offlineDataJson = localStorage.getItem(this.offlineDataKey);
      return offlineDataJson ? JSON.parse(offlineDataJson) : {};
    } catch (error) {
      console.error('Error accessing offline data store:', error);
      return {};
    }
  }
} 