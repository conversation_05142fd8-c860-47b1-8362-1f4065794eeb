import React, { memo, useCallback } from 'react';
import ProductTable, { ProductTableSkeleton, SortConfig } from './product-table/ProductTable';
import FilterChip from './FilterChip';
import { ActiveFilter, InventoryView } from '@/types';

interface InventoryPageContentProps {
  // Loading states
  isInitialLoading: boolean;
  loading: boolean;
  error: Error | null;
  
  // Product data
  products: InventoryView[];
  totalCount: number;
  
  // Search and filter
  activeFilters: ActiveFilter[];
  onRemoveFilter: (filterId: string) => void;
  onClearAllFilters: () => void;
  
  // Selection
  selectedProductIds: Set<string>;
  isAllSelected: boolean;
  isSomeSelected: boolean;
  onSelectProduct: (productId: string) => void;
  onSelectAll: (event: React.ChangeEvent<HTMLInputElement>) => void;
  clearSelection: () => void;
  
  // Pagination
  currentPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  onPageChange: (page: number) => void;
  
  // Sorting
  sortConfig: SortConfig;
  onSort: (key: keyof InventoryView) => void;
  
  // Actions
  onProductClick: (productId: string) => void;
  onRetryLoad: () => void;
  onAddNewProduct: () => void;
  onOpenBulkStockUpdateModal: () => void;
}

// Custom comparison function for memoization
const areEqual = (prevProps: InventoryPageContentProps, nextProps: InventoryPageContentProps) => {
  // Check primitive values
  if (
    prevProps.isInitialLoading !== nextProps.isInitialLoading ||
    prevProps.loading !== nextProps.loading ||
    prevProps.totalCount !== nextProps.totalCount ||
    prevProps.isAllSelected !== nextProps.isAllSelected ||
    prevProps.isSomeSelected !== nextProps.isSomeSelected ||
    prevProps.currentPage !== nextProps.currentPage ||
    prevProps.totalPages !== nextProps.totalPages ||
    prevProps.hasNextPage !== nextProps.hasNextPage ||
    prevProps.hasPreviousPage !== nextProps.hasPreviousPage
  ) {
    return false;
  }

  // Compare error
  if (
    (prevProps.error === null && nextProps.error !== null) ||
    (prevProps.error !== null && nextProps.error === null) ||
    (prevProps.error !== null && nextProps.error !== null && prevProps.error.message !== nextProps.error.message)
  ) {
    return false;
  }

  // Compare arrays
  if (prevProps.products !== nextProps.products) {
    return false;
  }

  if (prevProps.activeFilters !== nextProps.activeFilters) {
    return false;
  }

  // Compare sets
  if (prevProps.selectedProductIds.size !== nextProps.selectedProductIds.size) {
    return false;
  }

  // Compare sortConfig
  if (
    prevProps.sortConfig.column !== nextProps.sortConfig.column ||
    prevProps.sortConfig.direction !== nextProps.sortConfig.direction
  ) {
    return false;
  }

  // Compare function references
  if (
    prevProps.onRemoveFilter !== nextProps.onRemoveFilter ||
    prevProps.onClearAllFilters !== nextProps.onClearAllFilters ||
    prevProps.onSelectProduct !== nextProps.onSelectProduct ||
    prevProps.onSelectAll !== nextProps.onSelectAll ||
    prevProps.clearSelection !== nextProps.clearSelection ||
    prevProps.onPageChange !== nextProps.onPageChange ||
    prevProps.onSort !== nextProps.onSort ||
    prevProps.onProductClick !== nextProps.onProductClick ||
    prevProps.onRetryLoad !== nextProps.onRetryLoad ||
    prevProps.onAddNewProduct !== nextProps.onAddNewProduct ||
    prevProps.onOpenBulkStockUpdateModal !== nextProps.onOpenBulkStockUpdateModal
  ) {
    return false;
  }

  // If all checks pass, consider props equal
  return true;
};

const InventoryPageContent: React.FC<InventoryPageContentProps> = ({
  isInitialLoading,
  loading,
  error,
  products,
  // totalCount,
  activeFilters,
  onRemoveFilter,
  onClearAllFilters,
  selectedProductIds,
  isAllSelected,
  isSomeSelected,
  onSelectProduct,
  onSelectAll,
  clearSelection,
  currentPage,
  totalPages,
  hasNextPage,
  hasPreviousPage,
  onPageChange,
  sortConfig,
  onSort,
  onProductClick,
  onRetryLoad,
  onAddNewProduct,
  onOpenBulkStockUpdateModal
}) => {
  // Bulk actions toolbar - only shown when products are selected
  const renderBulkActionToolbar = useCallback(() => {
    if (selectedProductIds.size === 0) {
      return null;
    }
      // Adjust color here
    return (
      <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-10">
        <div className="bg-gray-700 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-3">
          <span className="font-medium">
            {selectedProductIds.size} {selectedProductIds.size === 1 ? 'product' : 'products'} selected
          </span>
          <div className="flex items-center space-x-2 ml-4">
            <button 
              onClick={onOpenBulkStockUpdateModal}
              className="px-3 py-1 bg-blue-600 hover:bg-blue-400 rounded-md text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-900"
            >
              Update Stock
            </button>
            <button 
              onClick={clearSelection}
              className="px-3 py-1 bg-white hover:bg-gray-100 text-gray-900 border border-gray-900 rounded-md text-sm font-medium focus:outline-none"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    );
  }, [selectedProductIds.size, onOpenBulkStockUpdateModal, clearSelection]);

  return (
    <>
      {/* Active Filters */}
      {activeFilters.length > 0 && !isInitialLoading && (
        <div className="mb-6 p-3 sm:p-4 bg-white shadow-sm rounded-lg border border-gray-200">
          <div className="flex flex-wrap items-center">
            <span className="text-sm font-medium text-gray-600 mr-3 mb-2">Filtered By:</span>
            <div className="flex flex-wrap">
              {activeFilters.map(filter => (
                <div key={filter.id} className="mr-2 mb-2">
                  <FilterChip
                    label={filter.label}
                    onRemove={() => onRemoveFilter(filter.id)}
                  />
                </div>
              ))}
              {activeFilters.length > 1 && (
                <div className="mr-2 mb-2">
                  <button
                    onClick={onClearAllFilters}
                    className="text-xs text-gray-500 hover:text-gray-700 underline focus:outline-none"
                  >
                    Clear All
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Product Table */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
        {isInitialLoading ? (
          <ProductTableSkeleton />
        ) : (
          <ProductTable
            products={products}
            loading={loading}
            error={error}
            onSelectProduct={onSelectProduct}
            onSelectAll={onSelectAll}
            isAllSelected={isAllSelected}
            isSomeSelected={isSomeSelected}
            selectedProductIds={selectedProductIds}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={onPageChange}
            hasNextPage={hasNextPage}
            hasPreviousPage={hasPreviousPage}
            onSort={onSort}
            sortConfig={sortConfig}
            onProductClick={onProductClick}
            onRetryLoad={onRetryLoad}
            onAddNewProduct={onAddNewProduct}
            onClearFilters={onClearAllFilters}
            activeFilters={activeFilters}
          />
        )}
      </div>
      
      {/* Bulk Actions Toolbar */}
      {renderBulkActionToolbar()}
    </>
  );
};

// Export memoized component with custom comparison function
export default memo(InventoryPageContent, areEqual); 