import { supabase } from '../../../../../supabase/supabase_client/client';
import { Carrier } from '@/types';

// API Response type
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

export const carriersApi = {
  async list(): Promise<ApiResponse<Carrier[]>> {
    const { data, error } = await supabase
      .from('carriers')
      .select('*');

    if (error) {
      console.error('Error fetching carriers:', error);
      return { data: [], success: false, message: error.message };
    }

    return { data, success: true };
  },
}; 