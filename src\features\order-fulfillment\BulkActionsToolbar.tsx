import React from 'react';

interface BulkActionsToolbarProps {
  selectedCount: number;
  onMarkAsPacked: () => void;
}

export const BulkActionsToolbar: React.FC<BulkActionsToolbarProps> = ({
  selectedCount,
  onMarkAsPacked
}) => {
  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 flex items-center justify-between">
      <div className="flex items-center space-x-2">
        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
        <span className="text-sm font-medium text-blue-900">
          {selectedCount} items selected
        </span>
      </div>
      <button
        onClick={onMarkAsPacked}
        className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
      >
        Mark as Packed
      </button>
    </div>
  );
}; 