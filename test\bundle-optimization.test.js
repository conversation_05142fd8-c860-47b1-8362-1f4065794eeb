import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

// Function to get total bundle size of specific vendor chunks
function getVendorBundleSize() {
  try {
    // Build the project first
    console.log('Building project...');
    execSync('npm run build', { stdio: 'inherit' });
    
    // Get all files in the assets directory
    const assetsDir = path.resolve('dist/assets');
    const files = fs.readdirSync(assetsDir);
    
    // Filter vendor files and calculate total size
    const vendorFiles = files.filter(file => file.startsWith('vendor-') && file.endsWith('.js'));
    
    let totalSize = 0;
    const fileSizes = {};
    
    vendorFiles.forEach(file => {
      const filePath = path.join(assetsDir, file);
      const stats = fs.statSync(filePath);
      const sizeInKB = stats.size / 1024;
      fileSizes[file] = sizeInKB.toFixed(2) + ' KB';
      totalSize += stats.size;
    });
    
    return {
      totalSize: (totalSize / 1024).toFixed(2) + ' KB',
      individualSizes: fileSizes,
      files: vendorFiles
    };
  } catch (error) {
    console.error('Error measuring bundle size:', error);
    return null;
  }
}

// This is a manual test to run before and after optimizations
// Not an automated test as it requires manual verification
console.log('⚠️ This is a manual test to measure vendor bundle sizes');
console.log('Run this before and after making changes to compare results');
console.log('-'.repeat(60));
console.log('Current vendor bundle sizes:');
const bundleInfo = getVendorBundleSize();
console.log('Total vendor bundle size:', bundleInfo.totalSize);
console.log('Individual chunk sizes:');
console.table(bundleInfo.individualSizes);
console.log('-'.repeat(60));
console.log('Vendor files found:', bundleInfo.files.join(', ')); 