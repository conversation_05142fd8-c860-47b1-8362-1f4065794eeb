import * as React from 'react';
import { lazy, Suspense } from 'react';
import { SegmentedControl } from '@/shared/ui/control/SegmentedControl';
import LoadingSpinner from '@/shared/ui/feedback/LoadingSpinner';
import { useFulfillmentPage } from '@/shared/lib/hooks/order-fulfillment/useFulfillmentPage';

// Dynamic imports for feature components
const PackingStation = lazy(() =>
  import('@/features/order-fulfillment/PackingStation').then((m) => ({ default: m.PackingStation }))
);
const LabelStation = lazy(() =>
  import('@/features/order-fulfillment/LabelStation').then((m) => ({ default: m.LabelStation }))
);
const AssignTrackingModal = lazy(() =>
  import('@/features/order-fulfillment/AssignTrackingModal').then((m) => ({ default: m.AssignTrackingModal }))
);

// Simple loading component for Suspense fallback
const FeatureLoading = () => (
  <div className="flex justify-center items-center h-64">
    <LoadingSpinner size="md" message="Loading component..." />
  </div>
);

const FulfillPage: React.FC = () => {
  const {
    productBatches,
    packedOrders,
    openOrderCount,
    activeView,
    setActiveView,
    isLoading,
    error,
    isUploading,
    ordersForTracking,
    refreshFulfillData,
    handleStatusChange,
    handleCsvDataLoaded,
    handleOpenTrackingModal,
    handleCloseTrackingModal,
    handleAssignmentSuccess,
    clearError
  } = useFulfillmentPage();

  const viewOptions = [
    { value: 'packing', label: 'Packing Station' },
    { value: 'label', label: 'Label Station' }
  ];

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
              <button
                onClick={clearError}
                className="mt-2 text-sm font-medium text-red-600 hover:text-red-500"
              >
                Try again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen bg-gray-50">
      {ordersForTracking && (
        <Suspense fallback={<FeatureLoading />}>
          <AssignTrackingModal
            isOpen={!!ordersForTracking}
            onClose={handleCloseTrackingModal}
            orders={ordersForTracking}
            onAssignment={handleAssignmentSuccess}
          />
        </Suspense>
      )}
      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Page Header */}
        <header className="flex-shrink-0 bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-6 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Complete Fulfillment Dashboard</h1>
                <p className="text-gray-600 mt-1">All features integrated for maximum efficiency</p>
              </div>
            </div>
          </div>
        </header>

        {/* Navigation Section */}
        <section className="flex-shrink-0 bg-white border-b border-gray-100">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex justify-center">
              <SegmentedControl
                options={viewOptions}
                value={activeView}
                onChange={(value) => setActiveView(value as 'packing' | 'label')}
              />
            </div>
          </div>
        </section>

        {/* Main Content Area */}
        <main className="flex-1 overflow-hidden">
          <section className="flex-grow p-6">
            <Suspense fallback={<FeatureLoading />}>
              {activeView === 'packing' && (
                <PackingStation 
                  productBatches={productBatches} 
                  onStatusChange={handleStatusChange} 
                  openOrderCount={openOrderCount}
                  onDataRefresh={refreshFulfillData}
                />
              )}
              {activeView === 'label' && 
              <LabelStation 
                orders={packedOrders} 
                onDataRefresh={refreshFulfillData} 
                onCsvDataLoaded={handleCsvDataLoaded} 
                onStatusChange={handleStatusChange} 
                isUpdating={isUploading} 
                onOpenTrackingModal={handleOpenTrackingModal} 
              />
              }
            </Suspense>
          </section>
        </main>
      </div>
    </div>
  );
};

export default FulfillPage;