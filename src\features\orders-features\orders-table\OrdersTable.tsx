import React, { memo, useCallback, Profiler, useRef, useEffect, useState } from 'react';
import { UserRole } from '@/types';
import { ExtendedOrderFilters } from '@/shared/lib/utils/data/url-params';
import { createProfilerCallback } from '@/shared/lib/utils/performance/profiler-utils';
import { useOrdersTable } from '@/shared/lib/hooks/orders/useOrdersTable';
import { useSupabaseRealtime, useWebSocketEvent } from '../../../shared/lib/hooks/websocket';
import {
  OrdersTableHeader,
  OrdersTableBody,
  OrdersTableStates,
  OrdersTableFooter
} from './components';

interface OrdersTableProps {
  filters?: ExtendedOrderFilters;
  onRowClick?: (orderId: string) => void;
  selectedOrderId?: string | null;
  onFiltersChange?: (filters: ExtendedOrderFilters) => void;
  userRole?: UserRole | undefined;
}

// Component comparison for memoization
const arePropsEqual = (prevProps: OrdersTableProps, nextProps: OrdersTableProps): boolean => {
  // Check essential props
  if (prevProps.selectedOrderId !== nextProps.selectedOrderId) return false;
  if (prevProps.userRole !== nextProps.userRole) return false;
  if (prevProps.onRowClick !== nextProps.onRowClick) return false;
  if (prevProps.onFiltersChange !== nextProps.onFiltersChange) return false;

  // Compare ALL filter properties for proper filtering
  const prevFilters = prevProps.filters || {};
  const nextFilters = nextProps.filters || {};
  
  return (
    prevFilters.search === nextFilters.search &&
    prevFilters.page === nextFilters.page &&
    prevFilters.status?.length === nextFilters.status?.length &&
    prevFilters.platform?.length === nextFilters.platform?.length &&
    prevFilters.channel?.length === nextFilters.channel?.length &&
    prevFilters.isUrgent === nextFilters.isUrgent &&
    prevFilters.isProblem === nextFilters.isProblem &&
    prevFilters.hasNotes === nextFilters.hasNotes &&
    prevFilters.isResent === nextFilters.isResent &&
    prevFilters.dateFrom === nextFilters.dateFrom &&
    prevFilters.dateTo === nextFilters.dateTo
  );
};

const OrdersTableComponent: React.FC<OrdersTableProps> = ({ 
  filters = {}, 
  onRowClick,
  selectedOrderId,
  onFiltersChange,
  userRole
}) => {
  // Track if component is mounted to prevent state updates after unmounting
  const isMountedRef = useRef(true);
  
  // Manual loading state for WebSocket refreshes
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Use custom hook for data management
  const {
    ordersData,
    loading,
    error,
    handlePageChange,
    handleRetry,
    fetchOrdersData,
  } = useOrdersTable({
    filters,
    userRole,
    onFiltersChange
  });

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Simple refresh function that shows loading state
  const refreshData = useCallback(() => {
    if (!isMountedRef.current) return;
    
    setIsRefreshing(true);
    fetchOrdersData({...filters, skipCache: true}) // Skip cache on WebSocket refreshes
      .finally(() => {
        if (isMountedRef.current) {
          setIsRefreshing(false);
        }
      });
  }, [fetchOrdersData, filters]);

  // Use a single Supabase Realtime subscription for all order changes
  const handleRealtimeUpdate = useCallback((payload: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`⚡ RECEIVED ORDER ${payload.eventType} VIA SUPABASE REALTIME:`, payload);
    }
    
    // Simple refresh with loading state
    refreshData();
  }, [refreshData]);
  
  // Use Supabase Realtime for orders table changes
  useSupabaseRealtime('orders', '*', handleRealtimeUpdate, 'public', undefined, false);
  
  // Add subscription for order_items table which affects orders
  useSupabaseRealtime('order_items', '*', (payload: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('⚡ RECEIVED ORDER_ITEMS UPDATE:', payload);
    }
    // Only refresh if we know which order was affected
    if (payload.new?.order_id || payload.old?.order_id) {
      refreshData();
    }
  }, 'public', undefined, false);
  
  // Handle WebSocket events from Edge Function
  useWebSocketEvent('order_changes', (message) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Received order_changes via WebSocket:', message);
    }
    refreshData();
  }, [refreshData]);
  
  // Also use the legacy WebSocket implementation as a fallback
  useWebSocketEvent('order_update', (message) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Received order update via WebSocket:', message);
    }
    refreshData();
  }, [refreshData]);

  // Handle row click - memoized to prevent recreation
  const handleRowClick = useCallback((orderId: string) => {
    if (onRowClick) {
      onRowClick(orderId);
    }
  }, [onRowClick]);

  // Check for states that need special rendering
  const isEmpty = !ordersData || ordersData.orders.length === 0;
  const hasStateToRender = loading || isRefreshing || error || isEmpty;

  // Render state components if needed
  if (hasStateToRender) {
    return (
      <OrdersTableStates
        loading={loading || isRefreshing}
        error={error}
        isEmpty={isEmpty}
        onRetry={handleRetry}
      />
    );
  }

  // Render main table
  return (
    <Profiler id="OrdersTable" onRender={createProfilerCallback('OrdersTable')}>
      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        <OrdersTableHeader totalCount={ordersData!.totalCount} />
        
        <OrdersTableBody
          orders={ordersData!.orders}
          selectedOrderId={selectedOrderId}
          onRowClick={handleRowClick}
        />

        <OrdersTableFooter
          ordersData={ordersData!}
          onPageChange={handlePageChange}
        />
      </div>
    </Profiler>
  );
};

// Export memoized component with custom comparison function  
const OrdersTable = memo(OrdersTableComponent, arePropsEqual);
export default OrdersTable;