import { bulkOrderService } from '../bulk-order-service';

// Mock Supabase client
const mockSupabase = {
  functions: {
    invoke: jest.fn()
  }
};

// Create the mock before importing the module
jest.mock('../../../../../supabase/supabase_client/client', () => ({
  supabase: mockSupabase
}));

describe('BulkOrderService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createBulkOrders', () => {
    const mockOrderRows = [
      {
        order_number: 'ORDER-123',
        platform: 'amazon',
        channel: 'amazon_fba',
        customer_email: '<EMAIL>',
        customer_name: 'Test Customer',
        product_sku: 'SKU123',
        quantity: '1'
      },
      {
        order_number: 'ORDER-123',
        platform: 'amazon',
        channel: 'amazon_fba',
        customer_email: '<EMAIL>',
        customer_name: 'Test Customer',
        product_sku: 'SKU456',
        quantity: '2'
      },
      {
        order_number: 'ORDER-456',
        platform: 'shopify',
        channel: 'shopify_web',
        customer_email: '<EMAIL>',
        customer_name: 'Another Customer',
        product_sku: 'SKU789',
        quantity: '1'
      }
    ];

    it('should group orders by order number and send to Edge Function', async () => {
      // Mock the Supabase Edge Function response
      const mockSuccessResponse = {
        data: {
          successful: [{ id: '1', order_number: 'ORDER-123' }, { id: '2', order_number: 'ORDER-456' }],
          failed: []
        },
        error: null
      };
      
      (mockSupabase.functions.invoke as jest.Mock).mockResolvedValueOnce(mockSuccessResponse);

      const result = await bulkOrderService.createBulkOrders(mockOrderRows);

      // Verify Edge Function was called with properly grouped orders
      expect(mockSupabase.functions.invoke).toHaveBeenCalledWith(
        'bulk-order-create',
        expect.objectContaining({
          body: expect.any(String)
        })
      );

      // Verify the body contains grouped orders
      const callBody = JSON.parse((mockSupabase.functions.invoke as jest.Mock).mock.calls[0][1].body);
      expect(callBody.orders).toHaveLength(2); // Two distinct order numbers
      
      // Verify first order has two line items
      const firstOrder = callBody.orders.find((o: any) => o.order_number === 'ORDER-123');
      expect(firstOrder.order_items).toHaveLength(2);
      
      // Verify result is properly formatted
      expect(result.success).toBe(true);
      expect(result.successfulOrders).toHaveLength(2);
    });

    it('should handle Edge Function errors', async () => {
      // Mock error response
      (mockSupabase.functions.invoke as jest.Mock).mockResolvedValueOnce({
        data: null,
        error: {
          message: 'Function execution error'
        }
      });

      await expect(bulkOrderService.createBulkOrders(mockOrderRows))
        .rejects
        .toThrow('Edge Function error: Function execution error');
    });

    it('should handle network or unexpected errors', async () => {
      // Mock a network error
      (mockSupabase.functions.invoke as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      const result = await bulkOrderService.createBulkOrders(mockOrderRows);

      expect(result.success).toBe(false);
      expect(result.message).toContain('Network error');
      expect(result.failedOrders).toHaveLength(mockOrderRows.length);
    });
  });
}); 