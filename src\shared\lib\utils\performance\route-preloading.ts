import { lazy } from 'react';

// Types for route preloading
interface RoutePreloadConfig {
  path: string;
  import: () => Promise<any>;
  priority: number; // Higher number = higher priority
  roles?: string[]; // Role-based preloading
}

// Helper type for route import functions
type RouteImportFn = () => Promise<any>;

/**
 * Preloads a single route component by triggering its import
 * @param importFn - Dynamic import function for the route component
 */
export const preloadRouteComponent = async (importFn: RouteImportFn): Promise<void> => {
  try {
    await importFn();
    // We don't need to do anything with the result
    if (process.env.NODE_ENV === 'development') {
      console.debug('✅ Preloaded route component');
    }
  } catch (error) {
    // Fail silently in production, log in development
    if (process.env.NODE_ENV === 'development') {
      console.warn('⚠️ Failed to preload route component:', error);
    }
  }
};

/**
 * Returns the list of critical routes that should be preloaded
 * These are ordered by priority (higher number = higher priority)
 */
export const getCriticalRoutes = (): RoutePreloadConfig[] => {
  // Import functions for the main routes
  // Use the same imports defined in main.tsx
  const DashboardImport = () => import('@/pages/dashboard');
  const OrdersImport = () => import('@/pages/orders');
  const FulfillImport = () => import('@/pages/fulfill');
  const DeliveryImport = () => import('@/pages/delivery');
  const InventoryImport = () => import('@/pages/inventory');
  const ReportsImport = () => import('@/pages/reports');

  // Define critical routes with priority order
  return [
    { path: '/orders', import: OrdersImport, priority: 100 },
    { path: '/dashboard-center', import: DashboardImport, priority: 90 },
    { path: '/fulfill', import: FulfillImport, priority: 80 },
    { path: '/delivery', import: DeliveryImport, priority: 70, roles: ['staff', 'admin', 'manager'] },
    { path: '/inventory', import: InventoryImport, priority: 60, roles: ['inventory', 'admin'] },
    { path: '/reports', import: ReportsImport, priority: 50, roles: ['admin', 'manager'] }
  ];
};

/**
 * Custom requestIdleCallback type definition to handle browser compatibility
 */
type IdleRequestCallback = (deadline: IdleDeadline) => void;
type IdleRequestOptions = {
  timeout: number;
};

// Use browser's built-in type if available, otherwise use polyfill
const requestIdleCallbackPolyfill = (
  callback: IdleRequestCallback,
  options?: IdleRequestOptions
): number => {
  const start = Date.now();
  return window.setTimeout(() => {
    callback({
      didTimeout: false,
      timeRemaining: () => Math.max(0, 50 - (Date.now() - start))
    } as IdleDeadline);
  }, options?.timeout || 1);
};

/**
 * Get requestIdleCallback with fallback
 */
const getRequestIdleCallback = (): ((callback: IdleRequestCallback, options?: IdleRequestOptions) => number) => {
  return window.requestIdleCallback || requestIdleCallbackPolyfill;
};

/**
 * Get cancelIdleCallback with fallback
 */
const getCancelIdleCallback = (): ((handle: number) => void) => {
  return window.cancelIdleCallback || window.clearTimeout;
};

/**
 * Preload critical route components using requestIdleCallback
 * @param userContext - Optional user context for role-based preloading
 * @param recentRoutes - Optional array of recently visited routes to prioritize
 */
export const preloadCriticalRoutes = (
  userContext?: { role?: string },
  recentRoutes?: string[]
): void => {
  // Get the requestIdleCallback function with fallback
  const requestIdleCallback = getRequestIdleCallback();
  
  // Get critical routes
  let routes = getCriticalRoutes();
  
  // Apply role-based filtering if user context is provided
  if (userContext?.role) {
    // Keep routes without role requirements or matching user's role
    routes = routes.filter(
      route => !route.roles || route.roles.includes(userContext.role!)
    );
  }
  
  // Boost priority of recently visited routes
  if (recentRoutes?.length) {
    routes = routes.map(route => {
      const recentIndex = recentRoutes.indexOf(route.path);
      // If route was recently visited, boost its priority based on recency
      if (recentIndex >= 0) {
        return {
          ...route,
          priority: route.priority + (recentRoutes.length - recentIndex) * 10
        };
      }
      return route;
    });
  }
  
  // Sort routes by priority (higher first)
  routes.sort((a, b) => b.priority - a.priority);
  
  // Create a queue of routes to preload
  const routeQueue = [...routes];
  
  // Preload routes one by one during idle time
  const preloadNextRoute = (deadline: IdleDeadline) => {
    // While there's time remaining and routes to preload
    while (deadline.timeRemaining() > 0 && routeQueue.length > 0) {
      const nextRoute = routeQueue.shift();
      if (nextRoute) {
        // Trigger preload but don't await (we're in a callback)
        preloadRouteComponent(nextRoute.import);
        
        // If we're out of time but still have routes, schedule another idle callback
        if (deadline.timeRemaining() <= 0 && routeQueue.length > 0) {
          requestIdleCallback(preloadNextRoute, { timeout: 1000 });
          return;
        }
      }
    }
    
    // If we still have routes to process but ran out of time
    if (routeQueue.length > 0) {
      requestIdleCallback(preloadNextRoute, { timeout: 1000 });
    }
  };
  
  // Start preloading
  requestIdleCallback(preloadNextRoute, { timeout: 1000 });
};

/**
 * Track route navigation history to improve preloading priority
 */
export const createRouteTracker = (limit = 5) => {
  // Store up to 'limit' most recent unique routes
  const recentRoutes: string[] = [];
  
  return {
    // Record a route visit
    recordRouteVisit: (path: string) => {
      // Remove if already exists (to move to front)
      const existingIndex = recentRoutes.indexOf(path);
      if (existingIndex >= 0) {
        recentRoutes.splice(existingIndex, 1);
      }
      
      // Add to front
      recentRoutes.unshift(path);
      
      // Trim if exceeding limit
      while (recentRoutes.length > limit) {
        recentRoutes.pop();
      }
      
      return [...recentRoutes];
    },
    
    // Get recent routes
    getRecentRoutes: () => [...recentRoutes]
  };
};

// Create singleton route tracker
export const routeTracker = createRouteTracker(); 