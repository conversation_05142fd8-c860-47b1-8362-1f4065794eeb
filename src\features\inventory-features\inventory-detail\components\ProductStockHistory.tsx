import React, { useState, useMemo } from 'react';
import { ChevronDownIcon, ChevronUpIcon, ArrowUpIcon, ArrowDownIcon, MinusIcon, UserCircleIcon } from '@heroicons/react/24/outline';
import { useProductStockHistory } from '@/shared/lib/hooks/inventory/products/useProductStockHistory';

interface ProductStockHistoryProps {
  productId: string;
}

const ProductStockHistory: React.FC<ProductStockHistoryProps> = ({ productId }) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [filterType, setFilterType] = useState<string>('all');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  
  const { historyData, isLoading, error, retryCount, retry } = useProductStockHistory(productId);
  
  // Filter and sort the history data
  const filteredAndSortedHistory = useMemo(() => {
    let filtered = [...historyData];
    
    // Apply filter
    if (filterType !== 'all') {
      filtered = filtered.filter(entry => entry.adjustmentType === filterType);
    }
    
    // Apply sorting
    filtered.sort((a, b) => {
      const dateA = new Date(a.timestamp).getTime();
      const dateB = new Date(b.timestamp).getTime();
      return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
    });
    
    return filtered;
  }, [historyData, filterType, sortOrder]);
  
  const getAdjustmentStyle = (type: 'increase' | 'decrease' | 'set') => {
    switch (type) {
      case 'increase':
        return 'text-green-600';
      case 'decrease':
        return 'text-red-600';
      case 'set':
        return 'text-blue-600';
      default:
        return '';
    }
  };
  
  const getAdjustmentIcon = (type: 'increase' | 'decrease' | 'set') => {
    switch (type) {
      case 'increase':
        return <ArrowUpIcon className="h-4 w-4 text-green-600" />;
      case 'decrease':
        return <ArrowDownIcon className="h-4 w-4 text-red-600" />;
      case 'set':
        return <MinusIcon className="h-4 w-4 text-blue-600" />;
      default:
        return null;
    }
  };
  
  const getAdjustmentPrefix = (type: 'increase' | 'decrease' | 'set') => {
    switch (type) {
      case 'increase':
        return '+';
      case 'decrease':
        return '-';
      case 'set':
        return '=';
      default:
        return '';
    }
  };
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };
  
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  const formatCategoryName = (category: string) => {
    return category
      .toLowerCase()
      .replace(/_/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());
  };
  
  const getCategoryStyles = (category: string) => {
    switch (category) {
      case 'order_fulfillment':
        return 'bg-blue-100 text-blue-800';
      case 'damaged':
        return 'bg-red-100 text-red-800';
      case 'return':
        return 'bg-yellow-100 text-yellow-800';
      case 'adjustment':
        return 'bg-purple-100 text-purple-800';
      case 'restock':
        return 'bg-green-100 text-green-800';
      case 'counted':
        return 'bg-gray-100 text-gray-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };
  
  const handleToggleExpand = () => {
    setIsExpanded(prev => !prev);
  };
  
  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFilterType(e.target.value);
  };
  
  const handleSortToggle = () => {
    setSortOrder(prev => (prev === 'asc' ? 'desc' : 'asc'));
  };
  
  if (isLoading) {
    return (
      <section>
        <h4 className="text-lg font-medium text-gray-900 mb-3">Stock History</h4>
        <div className="animate-pulse">
          <div className="bg-gray-100 h-10 w-full mb-4 rounded"></div>
          <div className="space-y-2">
            <div className="bg-gray-100 h-16 w-full rounded"></div>
            <div className="bg-gray-100 h-16 w-full rounded"></div>
            <div className="bg-gray-100 h-16 w-full rounded"></div>
          </div>
        </div>
      </section>
    );
  }
  
  if (error) {
    return (
      <section>
        <h4 className="text-lg font-medium text-gray-900 mb-3">Stock History</h4>
        <div className="bg-red-50 p-4 rounded-md">
          <p className="text-red-700 mb-2">{error}</p>
          {retryCount > 0 && (
            <p className="text-sm text-gray-600 mb-2">
              Retry attempt {retryCount} of 3
            </p>
          )}
          <button 
            onClick={retry}
            disabled={retryCount >= 3}
            className={`px-3 py-1 ${retryCount >= 3 ? 'bg-gray-400' : 'bg-red-600 hover:bg-red-700'} text-white rounded text-sm`}
          >
            {retryCount >= 3 ? 'Max retries reached' : 'Try Again'}
          </button>
        </div>
      </section>
    );
  }
  
  if (historyData.length === 0) {
    return (
      <section>
        <h4 className="text-lg font-medium text-gray-900 mb-3">Stock History</h4>
        <div className="bg-gray-50 p-4 rounded-md text-gray-500 italic">
          No stock movement history found.
        </div>
      </section>
    );
  }

  return (
    <section>
      <div className="flex justify-between items-center mb-4">
        <h4 className="text-lg font-medium text-gray-900">Stock History</h4>
        <button
          onClick={handleToggleExpand}
          className="text-gray-500 hover:text-gray-700"
        >
          {isExpanded ? (
            <ChevronUpIcon className="h-5 w-5" />
          ) : (
            <ChevronDownIcon className="h-5 w-5" />
          )}
        </button>
      </div>
      
      {isExpanded && (
        <>
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center space-x-3">
              <div>
                <label htmlFor="filterType" className="sr-only">
                  Filter by type
                </label>
                <select
                  id="filterType"
                  className="border border-gray-300 rounded-md text-sm px-3 py-1.5"
                  value={filterType}
                  onChange={handleFilterChange}
                >
                  <option value="all">All Types</option>
                  <option value="increase">Increases</option>
                  <option value="decrease">Decreases</option>
                  <option value="set">Resets</option>
                </select>
              </div>
              
              <button
                onClick={handleSortToggle}
                className="flex items-center border border-gray-300 rounded-md text-sm px-3 py-1.5 hover:bg-gray-50"
              >
                <span className="mr-1">Date</span>
                {sortOrder === 'asc' ? (
                  <ArrowUpIcon className="h-3 w-3" />
                ) : (
                  <ArrowDownIcon className="h-3 w-3" />
                )}
              </button>
            </div>
            
            <span className="text-sm text-gray-500">
              {filteredAndSortedHistory.length} entries
            </span>
          </div>
          
          <div className="bg-white shadow overflow-hidden border border-gray-200 sm:rounded-lg">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Date/Time
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Change
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Stock Level
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Reason
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      User
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {filteredAndSortedHistory.map((entry) => (
                    <tr key={entry.id} className="hover:bg-gray-50">
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-900">
                        <div>{formatDate(entry.date)}</div>
                        <div className="text-xs text-gray-500">{formatTime(entry.timestamp)}</div>
                      </td>
                      <td className={`px-3 py-3 whitespace-nowrap text-sm font-medium ${getAdjustmentStyle(entry.adjustmentType)}`}>
                        <div className="flex items-center">
                          {getAdjustmentIcon(entry.adjustmentType)}
                          <span className="ml-1">
                            {getAdjustmentPrefix(entry.adjustmentType)}{entry.quantity}
                          </span>
                        </div>
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-700">
                        <div className="text-gray-900">{entry.newStock}</div>
                        <div className="text-xs text-gray-500">from {entry.previousStock}</div>
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-700">
                        <div>{entry.reason}</div>
                        <div>
                          <span className={`inline-flex px-2 py-0.5 text-xs rounded-full mt-1 ${getCategoryStyles(entry.reasonCategory)}`}>
                            {formatCategoryName(entry.reasonCategory)}
                          </span>
                          {entry.orderId && (
                            <span className="ml-2 text-xs text-gray-500">
                              Order: {entry.orderNumber}
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-700">
                        <div className="flex items-center">
                          <UserCircleIcon className="h-4 w-4 text-gray-400 mr-1" />
                          <span>{entry.userName}</span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </>
      )}
    </section>
  );
};

export default ProductStockHistory; 