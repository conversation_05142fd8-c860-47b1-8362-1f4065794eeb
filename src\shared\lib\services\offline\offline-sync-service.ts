import { OfflineStorage, OfflinePendingOperation, OfflineOperationType } from '@/shared/lib/utils/data/offline-storage';
import { inventoryApi } from '@/shared/api/inventory/inventoryAPI';
import { debounce } from 'lodash';

/**
 * Result of processing a pending operation
 */
interface OperationProcessResult {
  success: boolean;
  operationId: string;
  error?: any;
  data?: any;
}

/**
 * Service for synchronizing offline operations when the app comes online
 */
export class OfflineSyncService {
  private static instance: OfflineSyncService;
  private offlineStorage: OfflineStorage;
  private isProcessing: boolean = false;
  private onlineStatusListenerAdded: boolean = false;
  private networkListeners: Array<() => void> = [];
  
  /**
   * Private constructor for singleton pattern
   */
  private constructor() {
    this.offlineStorage = OfflineStorage.getInstance();
  }
  
  /**
   * Get the singleton instance of OfflineSyncService
   */
  public static getInstance(): OfflineSyncService {
    if (!OfflineSyncService.instance) {
      OfflineSyncService.instance = new OfflineSyncService();
    }
    return OfflineSyncService.instance;
  }
  
  /**
   * Initialize the synchronization service
   * Sets up event listeners for online/offline status changes
   */
  public initialize(): void {
    if (this.onlineStatusListenerAdded || typeof window === 'undefined') {
      return;
    }
    
    // Add event listeners for online/offline status
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);
    
    // If we're already online, check for pending operations on initialization
    if (this.offlineStorage.isOnline()) {
      this.checkAndSyncPendingOperations();
    }
    
    this.onlineStatusListenerAdded = true;
  }
  
  /**
   * Clean up event listeners when service is no longer needed
   */
  public cleanup(): void {
    if (!this.onlineStatusListenerAdded || typeof window === 'undefined') {
      return;
    }
    
    window.removeEventListener('online', this.handleOnline);
    window.removeEventListener('offline', this.handleOffline);
    
    // Clean up any registered network listeners
    this.networkListeners.forEach(listener => {
      window.removeEventListener('online', listener);
    });
    this.networkListeners = [];
    
    this.onlineStatusListenerAdded = false;
  }
  
  /**
   * Register a callback to be notified when the app comes online
   * @param callback Function to call when the app comes online
   * @returns Function to unregister the callback
   */
  public registerOnlineCallback(callback: () => void): () => void {
    const listener = (): void => {
      if (this.offlineStorage.isOnline()) {
        callback();
      }
    };
    
    window.addEventListener('online', listener);
    this.networkListeners.push(listener);
    
    return () => {
      window.removeEventListener('online', listener);
      this.networkListeners = this.networkListeners.filter(l => l !== listener);
    };
  }
  
  /**
   * Debounced handler for when the app comes online
   */
  private handleOnline = debounce(() => {
    console.log('App is online. Checking for pending operations...');
    this.checkAndSyncPendingOperations();
  }, 1000);
  
  /**
   * Handler for when the app goes offline
   */
  private handleOffline = (): void => {
    console.log('App is offline.');
    // Additional offline handling could be added here
  };
  
  /**
   * Check for pending operations and sync them if any exist
   */
  public checkAndSyncPendingOperations = async (): Promise<void> => {
    if (!this.offlineStorage.isOnline() || this.isProcessing) {
      return;
    }
    
    const pendingOperations = this.offlineStorage.getPendingOperations();
    if (pendingOperations.length === 0) {
      return;
    }
    
    console.log(`Found ${pendingOperations.length} pending operations to sync.`);
    await this.processPendingOperations(pendingOperations);
  };
  
  /**
   * Process all pending operations
   * @param operations Array of pending operations to process
   */
  public async processPendingOperations(operations: OfflinePendingOperation[]): Promise<OperationProcessResult[]> {
    if (this.isProcessing || operations.length === 0) {
      return [];
    }
    
    this.isProcessing = true;
    this.offlineStorage.markSyncStart();
    
    const results: OperationProcessResult[] = [];
    const errors: string[] = [];
    
    try {
      // Process operations in order
      for (const operation of operations) {
        try {
          const result = await this.processSingleOperation(operation);
          results.push(result);
          
          if (result.success) {
            // Remove the operation if it was successful
            this.offlineStorage.removePendingOperation(operation.id);
          } else {
            errors.push(`Failed to process operation ${operation.id}: ${result.error}`);
          }
        } catch (error) {
          console.error(`Error processing operation ${operation.id}:`, error);
          results.push({
            success: false,
            operationId: operation.id,
            error
          });
          
          errors.push(`Error processing operation ${operation.id}: ${error instanceof Error ? error.message : String(error)}`);
        }
      }
    } finally {
      this.isProcessing = false;
      this.offlineStorage.markSyncComplete(errors.length > 0, errors);
    }
    
    return results;
  }
  
  /**
   * Process a single pending operation
   * @param operation The operation to process
   */
  private async processSingleOperation(operation: OfflinePendingOperation): Promise<OperationProcessResult> {
    console.log(`Processing operation: ${operation.type} for ${operation.entityType}`);
    
    try {
      // Inventory operations
      if (operation.entityType === 'inventory') {
        return await this.processInventoryOperation(operation);
      }
      
      // Product operations
      if (operation.entityType === 'product') {
        return await this.processProductOperation(operation);
      }
      
      // Add other entity types as needed
      
      return {
        success: false,
        operationId: operation.id,
        error: `Unsupported entity type: ${operation.entityType}`
      };
    } catch (error) {
      console.error(`Error processing operation ${operation.id}:`, error);
      return {
        success: false,
        operationId: operation.id,
        error
      };
    }
  }
  
  /**
   * Process inventory-related operations
   * @param operation The inventory operation to process
   */
  private async processInventoryOperation(operation: OfflinePendingOperation): Promise<OperationProcessResult> {
    switch (operation.type) {
      case OfflineOperationType.CREATE:
        // Not applicable for inventory (created with product)
        return {
          success: false,
          operationId: operation.id,
          error: 'Create operation not supported for inventory'
        };
        
      case OfflineOperationType.UPDATE:
        if ('productId' in operation.data && 'adjustment' in operation.data) {
          const { productId, adjustment } = operation.data as { productId: string; adjustment: any };
          const response = await inventoryApi.adjustStock(productId, adjustment);
          
          return {
            success: response.success,
            operationId: operation.id,
            error: response.success ? undefined : response.error,
            data: response.data
          };
        }
        
        // Handle the generic inventory update case
        if ('inventoryId' in operation.data && 'updateData' in operation.data) {
          const { inventoryId, updateData } = operation.data as { inventoryId: string; updateData: any };
          const response = await inventoryApi.update(inventoryId, updateData);
          
          return {
            success: response.success,
            operationId: operation.id,
            error: response.success ? undefined : response.error,
            data: response.data
          };
        }
        
        return {
          success: false,
          operationId: operation.id,
          error: 'Invalid data format for inventory update'
        };
        
      case OfflineOperationType.DELETE:
        // Not applicable for inventory
        return {
          success: false,
          operationId: operation.id,
          error: 'Delete operation not supported for inventory'
        };
        
      default:
        return {
          success: false,
          operationId: operation.id,
          error: `Unsupported operation type: ${operation.type} for inventory`
        };
    }
  }
  
  /**
   * Process product-related operations
   * @param operation The product operation to process
   */
  private async processProductOperation(operation: OfflinePendingOperation): Promise<OperationProcessResult> {
    switch (operation.type) {
      case OfflineOperationType.CREATE:
        // Log that we don't yet have a direct createProduct API
        console.warn('Product creation via offline sync not fully implemented');
        
        if (operation.data) {
          // Use the product identifier creation as a partial solution
          // In a real implementation, we would need a proper product creation API
          try {
            if ('platformId' in operation.data && 'productId' in operation.data) {
              const response = await inventoryApi.createProductIdentifier(operation.data);
              
              return {
                success: response.success,
                operationId: operation.id,
                error: response.success ? undefined : response.error,
                data: response.data
              };
            }
          } catch (error) {
            console.error('Error creating product identifier:', error);
          }
        }
        
        return {
          success: false,
          operationId: operation.id,
          error: 'Product creation not fully implemented for offline sync'
        };
        
      case OfflineOperationType.UPDATE:
        if ('productId' in operation.data && 'updates' in operation.data) {
          const { productId, updates } = operation.data as { productId: string; updates: any };
          
          // Use inventory update as a fallback since we don't have a direct product update API
          try {
            // First try to get the inventory ID for this product
            const inventoryResponse = await inventoryApi.getByProductId(productId);
            
            if (inventoryResponse.success && inventoryResponse.data) {
              const inventoryId = inventoryResponse.data.id;
              // Update the inventory record
              const response = await inventoryApi.update(inventoryId, updates);
              
              return {
                success: response.success,
                operationId: operation.id,
                error: response.success ? undefined : response.error,
                data: response.data
              };
            }
          } catch (error) {
            console.error('Error updating product data:', error);
          }
        }
        
        return {
          success: false,
          operationId: operation.id,
          error: 'Product update not fully implemented for offline sync'
        };
        
      case OfflineOperationType.DELETE:
        if ('productId' in operation.data) {
          const { productId } = operation.data as { productId: string };
          
          // We don't have a direct product deletion API
          // For now, log that this operation needs proper implementation
          console.warn(`Product deletion not implemented: ${productId}`);
          
          // Return a placeholder result
          return {
            success: false,
            operationId: operation.id,
            error: 'Product deletion not implemented in API'
          };
        }
        
        return {
          success: false,
          operationId: operation.id,
          error: 'Invalid data format for product deletion'
        };
        
      default:
        return {
          success: false,
          operationId: operation.id,
          error: `Unsupported operation type: ${operation.type} for product`
        };
    }
  }
  
  /**
   * Force a synchronization of pending operations
   * Useful for manual sync buttons/actions
   */
  public forceSynchronization = async (): Promise<boolean> => {
    if (!this.offlineStorage.isOnline()) {
      return false;
    }
    
    const operations = this.offlineStorage.getPendingOperations();
    if (operations.length === 0) {
      return true;
    }
    
    const results = await this.processPendingOperations(operations);
    return results.every(r => r.success);
  };
} 