import React, { useState, useEffect, useCallback } from 'react';
import { webSocketService } from '@/shared/lib/services/websocket';
import { useWebSocketEvent } from '@/shared/lib/hooks/websocket';
import { useSupabaseRealtime } from '@/shared/lib/hooks/websocket';

interface WebSocketStatusMonitorProps {
  showDebugInfo?: boolean;
}

const WebSocketStatusMonitor: React.FC<WebSocketStatusMonitorProps> = ({
  showDebugInfo = false
}) => {
  const [status, setStatus] = useState<string>('Connecting...');
  const [lastEvent, setLastEvent] = useState<string | null>(null);
  const [lastEventTime, setLastEventTime] = useState<Date | null>(null);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [connectionAttempts, setConnectionAttempts] = useState<number>(0);
  
  // Subscribe to WebSocket status changes
  useEffect(() => {
    const handleStateChange = (state: any) => {
      setIsConnected(state.isConnected);
      setConnectionAttempts(state.reconnectAttempts);
      if (state.lastHeartbeat) {
        setLastEventTime(new Date(state.lastHeartbeat));
        setLastEvent('heartbeat');
      }
    };
    
    // Initial state
    const initialState = webSocketService.getState();
    handleStateChange(initialState);
    
    // Register for updates
    webSocketService.onStateChange(handleStateChange);
    
    return () => {
      // Cleanup if needed
    };
  }, []);
  
  // Handle connection status changes
  const handleConnectionStatus = useCallback((message: any) => {
    if (message.type === 'connection_status') {
      setStatus(message.payload.status);
      setIsConnected(message.payload.status === 'connected');
      setLastEvent('connection_status');
      setLastEventTime(new Date());
    }
  }, []);
  
  // Handle heartbeat events
  const handleHeartbeat = useCallback((message: any) => {
    if (message.type === 'heartbeat') {
      setStatus('Connected (heartbeat received)');
      setIsConnected(true);
      setLastEvent('heartbeat');
      setLastEventTime(new Date());
    }
  }, []);
  
  // Connection acknowledgment
  const handleConnectionAck = useCallback((message: any) => {
    setStatus('Connected');
    setIsConnected(true);
    setLastEvent('connection_ack');
    setLastEventTime(new Date());
  }, []);
  
  // Subscribe to WebSocket events
  useWebSocketEvent('connection_status', handleConnectionStatus);
  useWebSocketEvent('heartbeat', handleHeartbeat);
  useWebSocketEvent('connection_ack', handleConnectionAck);
  
  // Test Supabase Realtime connection
  const handleRealtimeEvent = useCallback((payload: any) => {
    setStatus('Supabase Realtime connected');
    setIsConnected(true);
    setLastEvent('supabase_realtime');
    setLastEventTime(new Date());
  }, []);
  
  // Subscribe to a test channel to verify Supabase Realtime connection
  useSupabaseRealtime('orders', 'INSERT', handleRealtimeEvent, 'public', undefined, false);
  
  // Show connection status after a delay
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!isConnected && !lastEventTime) {
        setStatus('No connection established');
      }
    }, 5000);
    
    return () => clearTimeout(timer);
  }, [isConnected, lastEventTime]);
  
  // Manual connect function
  const handleConnect = () => {
    webSocketService.connect();
    setStatus('Connecting...');
  };
  
  // Manual disconnect function
  const handleDisconnect = () => {
    webSocketService.disconnect();
    setStatus('Disconnected manually');
    setIsConnected(false);
  };
  
  // Test message function
  const handleSendTest = () => {
    webSocketService.send({
      type: 'heartbeat',
      payload: { timestamp: new Date().toISOString() }
    });
    setStatus('Test message sent');
  };
  
  return (
    <div className="fixed bottom-4 right-4 z-50 bg-white shadow-lg rounded-lg p-3 text-sm border border-gray-200">
      <div className="flex flex-col">
        <div className="flex items-center space-x-2 mb-2">
          <div 
            className={`w-3 h-3 rounded-full ${
              isConnected ? 'bg-green-500' : 'bg-red-500'
            }`}
          />
          <div className="font-medium">{status}</div>
        </div>
        
        {lastEventTime && (
          <div className="text-xs text-gray-500 mb-2">
            Last event: {lastEvent} at {lastEventTime.toLocaleTimeString()}
          </div>
        )}
        
        {connectionAttempts > 0 && (
          <div className="text-xs text-gray-500 mb-2">
            Connection attempts: {connectionAttempts}
          </div>
        )}
        
        {showDebugInfo && (
          <div className="flex space-x-2 mt-1">
            <button 
              onClick={handleConnect}
              disabled={isConnected}
              className={`px-2 py-1 text-xs font-medium rounded ${
                isConnected 
                  ? 'bg-gray-200 cursor-not-allowed' 
                  : 'bg-blue-100 text-blue-800 hover:bg-blue-200'
              }`}
            >
              Connect
            </button>
            
            <button 
              onClick={handleDisconnect}
              disabled={!isConnected}
              className={`px-2 py-1 text-xs font-medium rounded ${
                !isConnected 
                  ? 'bg-gray-200 cursor-not-allowed' 
                  : 'bg-red-100 text-red-800 hover:bg-red-200'
              }`}
            >
              Disconnect
            </button>
            
            <button 
              onClick={handleSendTest}
              disabled={!isConnected}
              className={`px-2 py-1 text-xs font-medium rounded ${
                !isConnected 
                  ? 'bg-gray-200 cursor-not-allowed' 
                  : 'bg-green-100 text-green-800 hover:bg-green-200'
              }`}
            >
              Test
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default WebSocketStatusMonitor; 