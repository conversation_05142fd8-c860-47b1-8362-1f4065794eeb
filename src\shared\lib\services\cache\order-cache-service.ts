import { fetchOrders, FetchOrdersResponse, OrderFilters, SearchParams, PaginationParams } from '../../services/order/live-order-service';
import { CacheStorage, CacheConfig, CacheEntry } from './cache-storage';
import { CacheKeyManager } from './cache-key-manager';
import { CacheWebSocketHandler } from './cache-websocket-handler';
import { initializeCacheWebSocketHandlers } from './index';

// Conflict resolution strategies
export type ConflictResolution = 'server-wins' | 'client-wins' | 'merge';

class OrderCacheService {
  private storage: CacheStorage;
  private wsHandler: CacheWebSocketHandler;
  private isInitialized = false;
  private pendingRequests = new Map<string, Promise<FetchOrdersResponse>>();
  private cleanupInterval?: NodeJS.Timeout;
  private wsHandlersCleanup?: () => void;

  constructor(config: Partial<CacheConfig> = {}) {
    const fullConfig: CacheConfig = {
      maxEntries: 100,
      defaultTTL: 5 * 60 * 1000, // 5 minutes
      staleWhileRevalidate: 30 * 1000, // 30 seconds
      ...config,
    };

    this.storage = new CacheStorage(fullConfig);
    this.wsHandler = new CacheWebSocketHandler(this.storage);
    this.initialize();
  }

  /**
   * Initialize cache service and set up WebSocket listeners
   */
  private initialize(): void {
    if (this.isInitialized) return;

    // Set up WebSocket listeners using the centralized handler
    const { cleanup } = initializeCacheWebSocketHandlers(this.wsHandler);
    this.wsHandlersCleanup = cleanup;

    // Periodic cleanup of expired entries
    this.cleanupInterval = setInterval(() => {
      const cleanedCount = this.storage.cleanupExpired();
      if (cleanedCount > 0) {
        console.log(`Cleaned up ${cleanedCount} expired cache entries`);
      }
    }, 60000); // Every minute

    this.isInitialized = true;
    console.log('OrderCacheService initialized');
  }

  /**
   * Get orders from cache or fetch from API
   */
  public async getOrders(
    filters: OrderFilters = {},
    search: SearchParams = {},
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<FetchOrdersResponse> {
    const cacheKey = CacheKeyManager.generateKey(filters, search, pagination);
    const cached = this.storage.get(cacheKey);

    // Check if we have valid cached data
    if (cached && this.storage.isValid(cached)) {
      console.log('Cache hit:', cacheKey);
      return {
        orders: [...cached.orders], // Return copy to prevent mutations
        totalCount: cached.totalCount,
        currentPage: pagination.page || 1,
        totalPages: Math.ceil(cached.totalCount / (pagination.limit || 20)),
        hasNextPage: (pagination.page || 1) * (pagination.limit || 20) < cached.totalCount,
        hasPreviousPage: (pagination.page || 1) > 1,
        hasMore: (pagination.page || 1) * (pagination.limit || 20) < cached.totalCount,
      };
    }

    // Check if we have stale data that can be served while revalidating
    if (cached && this.storage.isStaleButRevalidatable(cached)) {
      console.log('Serving stale data while revalidating:', cacheKey);
      
      // Trigger background refresh
      this.refreshCacheEntry(cacheKey, filters, search, pagination).catch(error => {
        console.error('Background cache refresh failed:', error);
      });

      return {
        orders: [...cached.orders],
        totalCount: cached.totalCount,
        currentPage: pagination.page || 1,
        totalPages: Math.ceil(cached.totalCount / (pagination.limit || 20)),
        hasNextPage: (pagination.page || 1) * (pagination.limit || 20) < cached.totalCount,
        hasPreviousPage: (pagination.page || 1) > 1,
        hasMore: (pagination.page || 1) * (pagination.limit || 20) < cached.totalCount,
      };
    }

    // Check if we already have a pending request for this key
    const pendingRequest = this.pendingRequests.get(cacheKey);
    if (pendingRequest) {
      console.log('Using pending request:', cacheKey);
      return pendingRequest;
    }

    // Fetch fresh data
    console.log('Cache miss, fetching fresh data:', cacheKey);
    const fetchPromise = this.fetchAndCache(cacheKey, filters, search, pagination);
    this.pendingRequests.set(cacheKey, fetchPromise);

    try {
      const result = await fetchPromise;
      return result;
    } finally {
      this.pendingRequests.delete(cacheKey);
    }
  }

  /**
   * Fetch data from API and cache it
   */
  private async fetchAndCache(
    cacheKey: string,
    filters: OrderFilters,
    search: SearchParams,
    pagination: PaginationParams
  ): Promise<FetchOrdersResponse> {
    try {
      const response = await fetchOrders(pagination, filters, search);
      
      // Cache the response
      const entry = this.storage.createEntry(
        response.orders,
        response.totalCount,
        filters,
        search,
        pagination
      );

      this.storage.set(cacheKey, entry);
      
      return {
        orders: [...response.orders], // Return copy
        totalCount: response.totalCount,
        currentPage: response.currentPage,
        totalPages: response.totalPages,
        hasNextPage: response.hasNextPage,
        hasPreviousPage: response.hasPreviousPage,
        hasMore: response.hasMore,
      };
    } catch (error) {
      console.error('Failed to fetch and cache orders:', error);
      throw error;
    }
  }

  /**
   * Refresh a specific cache entry in the background
   */
  private async refreshCacheEntry(
    cacheKey: string,
    filters: OrderFilters,
    search: SearchParams,
    pagination: PaginationParams
  ): Promise<void> {
    try {
      const response = await fetchOrders(pagination, filters, search);
      
      const entry = this.storage.createEntry(
        response.orders,
        response.totalCount,
        filters,
        search,
        pagination
      );

      this.storage.set(cacheKey, entry);
      console.log('Cache entry refreshed:', cacheKey);
    } catch (error) {
      console.error('Failed to refresh cache entry:', error);
    }
  }

  /**
   * Invalidate cache entries based on user actions
   */
  public invalidateOnUserAction(orderId: string, action: 'save' | 'edit' | 'delete'): void {
    this.wsHandler.handleUserAction(orderId, action);
  }

  /**
   * Refresh cache on user login/logout
   */
  public refreshOnAuthChange(): void {
    console.log('Refreshing cache due to authentication change');
    this.invalidateAll();
  }

  /**
   * Manual cache refresh
   */
  public async refreshAll(): Promise<void> {
    console.log('Manual cache refresh initiated');
    
    const refreshPromises: Promise<void>[] = [];
    
    for (const [key, entry] of this.storage.entries()) {
      const refreshPromise = this.refreshCacheEntry(
        key,
        entry.filters,
        entry.search,
        entry.pagination
      );
      refreshPromises.push(refreshPromise);
    }
    
    await Promise.allSettled(refreshPromises);
    console.log('Manual cache refresh completed');
  }

  /**
   * Invalidate all cache entries
   */
  public invalidateAll(): void {
    this.storage.clear();
    this.pendingRequests.clear();
    console.log('All cache entries invalidated');
  }

  /**
   * Invalidate cache entries matching specific filters
   */
  public invalidateByFilters(filters: Partial<OrderFilters>): void {
    const keysToDelete: string[] = [];
    
    for (const [key, entry] of this.storage.entries()) {
      const cacheKey = { filters: entry.filters, search: entry.search, pagination: entry.pagination };
      
      if (CacheKeyManager.matchesFilters(cacheKey, filters)) {
        keysToDelete.push(key);
      }
    }
    
    keysToDelete.forEach(key => {
      this.storage.delete(key);
      console.log('Invalidated cache entry:', key);
    });
  }

  /**
   * Get cache statistics
   */
  public getCacheStats(): {
    totalEntries: number;
    totalSize: number;
    oldestEntry: Date | null;
    newestEntry: Date | null;
  } {
    return this.storage.getStats();
  }

  /**
   * Destroy the cache service
   */
  public destroy(): void {
    this.storage.clear();
    this.pendingRequests.clear();
    
    // Clear cleanup interval
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = undefined;
    }
    
    // Clean up WebSocket listeners
    if (this.wsHandlersCleanup) {
      this.wsHandlersCleanup();
      this.wsHandlersCleanup = undefined;
    }
    
    this.isInitialized = false;
    console.log('OrderCacheService destroyed');
  }
}

// Export singleton instance
export const orderCacheService = new OrderCacheService();

// Clean up singleton on process exit (for tests)
if (typeof process !== 'undefined' && typeof process.on === 'function') {
  process.on('exit', () => {
    orderCacheService.destroy();
  });
}

// Export class and types for testing
export { OrderCacheService };
export type { CacheEntry, CacheConfig }; 