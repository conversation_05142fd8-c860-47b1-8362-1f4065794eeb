// API Response type
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

// Bulk API specific types
interface BulkOrderOperation {
  action: 'mark-shipped' | 'add-notes' | 'cancel' | 'mark-urgent' | 'mark-problem' | 'update-status';
  orderIds: string[];
  data?: any;
}

interface BulkDeliveryOperation {
  action: 'mark-for-resend' | 'mark-for-refund' | 'update-status';
  deliveryIds: string[];
  data?: any;
}


// Bulk Operations API Functions
export const bulkApi = {
  // Bulk order operations
  async processOrderOperation(operation: BulkOrderOperation): Promise<ApiResponse<{
    processed: number;
    failed: number;
    results: Array<{
      orderId: string;
      success: boolean;
      error?: string;
    }>;
  }>> {
    const response = await fetch('/api/bulk/orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(operation),
    });
    return response.json();
  },

  // Bulk delivery operations
  async processDeliveryOperation(operation: BulkDeliveryOperation): Promise<ApiResponse<{
    processed: number;
    failed: number;
    results: Array<{
      deliveryId: string;
      success: boolean;
      error?: string;
    }>;
  }>> {
    const response = await fetch('/api/bulk/deliveries', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(operation),
    });
    return response.json();
  },

  // Specific bulk order actions
  orders: {
    // Mark multiple orders as shipped
    async markShipped(orderIds: string[], trackingNumbers?: { [orderId: string]: string }): Promise<ApiResponse<{
      processed: number;
      failed: number;
      results: Array<{ orderId: string; success: boolean; error?: string }>;
    }>> {
      const operation: BulkOrderOperation = {
        action: 'mark-shipped',
        orderIds,
        data: { trackingNumbers }
      };
      return bulkApi.processOrderOperation(operation);
    },

    // Add notes to multiple orders
    async addNotes(orderIds: string[], notes: string): Promise<ApiResponse<{
      processed: number;
      failed: number;
      results: Array<{ orderId: string; success: boolean; error?: string }>;
    }>> {
      const operation: BulkOrderOperation = {
        action: 'add-notes',
        orderIds,
        data: { notes }
      };
      return bulkApi.processOrderOperation(operation);
    },

    // Cancel multiple orders
    async cancel(orderIds: string[], reason?: string): Promise<ApiResponse<{
      processed: number;
      failed: number;
      results: Array<{ orderId: string; success: boolean; error?: string }>;
    }>> {
      const operation: BulkOrderOperation = {
        action: 'cancel',
        orderIds,
        data: { reason }
      };
      return bulkApi.processOrderOperation(operation);
    },

    // Mark multiple orders as urgent
    async markUrgent(orderIds: string[]): Promise<ApiResponse<{
      processed: number;
      failed: number;
      results: Array<{ orderId: string; success: boolean; error?: string }>;
    }>> {
      const operation: BulkOrderOperation = {
        action: 'mark-urgent',
        orderIds
      };
      return bulkApi.processOrderOperation(operation);
    },

    // Mark multiple orders as problem
    async markProblem(orderIds: string[]): Promise<ApiResponse<{
      processed: number;
      failed: number;
      results: Array<{ orderId: string; success: boolean; error?: string }>;
    }>> {
      const operation: BulkOrderOperation = {
        action: 'mark-problem',
        orderIds
      };
      return bulkApi.processOrderOperation(operation);
    }
  },

  // Specific bulk delivery actions
  deliveries: {
    // Mark multiple deliveries for resend
    async markForResend(deliveryIds: string[], reason?: string): Promise<ApiResponse<{
      processed: number;
      failed: number;
      results: Array<{ deliveryId: string; success: boolean; error?: string }>;
    }>> {
      const operation: BulkDeliveryOperation = {
        action: 'mark-for-resend',
        deliveryIds,
        data: { reason }
      };
      return bulkApi.processDeliveryOperation(operation);
    },

    // Mark multiple deliveries for refund
    async markForRefund(deliveryIds: string[], reason?: string): Promise<ApiResponse<{
      processed: number;
      failed: number;
      results: Array<{ deliveryId: string; success: boolean; error?: string }>;
    }>> {
      const operation: BulkDeliveryOperation = {
        action: 'mark-for-refund',
        deliveryIds,
        data: { reason }
      };
      return bulkApi.processDeliveryOperation(operation);
    },

    // Update status for multiple deliveries
    async updateStatus(deliveryIds: string[], status: string, notes?: string): Promise<ApiResponse<{
      processed: number;
      failed: number;
      results: Array<{ deliveryId: string; success: boolean; error?: string }>;
    }>> {
      const operation: BulkDeliveryOperation = {
        action: 'update-status',
        deliveryIds,
        data: { status, notes }
      };
      return bulkApi.processDeliveryOperation(operation);
    }
  },

  // Bulk import/export operations
  import: {
    // Import orders from CSV/Excel
    async orders(file: File, options?: {
      skipFirstRow?: boolean;
      columnMapping?: { [csvColumn: string]: string };
      validateOnly?: boolean;
    }): Promise<ApiResponse<{
      imported: number;
      failed: number;
      errors: Array<{
        row: number;
        field: string;
        message: string;
      }>;
      preview?: Array<{ [key: string]: any }>;
    }>> {
      const formData = new FormData();
      formData.append('file', file);
      if (options) {
        formData.append('options', JSON.stringify(options));
      }

      const response = await fetch('/api/bulk/import/orders', {
        method: 'POST',
        body: formData,
      });
      return response.json();
    },

    // Import products from CSV/Excel
    async products(file: File, options?: {
      skipFirstRow?: boolean;
      columnMapping?: { [csvColumn: string]: string };
      validateOnly?: boolean;
    }): Promise<ApiResponse<{
      imported: number;
      failed: number;
      errors: Array<{
        row: number;
        field: string;
        message: string;
      }>;
      preview?: Array<{ [key: string]: any }>;
    }>> {
      const formData = new FormData();
      formData.append('file', file);
      if (options) {
        formData.append('options', JSON.stringify(options));
      }

      const response = await fetch('/api/bulk/import/products', {
        method: 'POST',
        body: formData,
      });
      return response.json();
    },

    // Import inventory updates from CSV/Excel
    async inventory(file: File, options?: {
      skipFirstRow?: boolean;
      columnMapping?: { [csvColumn: string]: string };
      validateOnly?: boolean;
    }): Promise<ApiResponse<{
      updated: number;
      failed: number;
      errors: Array<{
        row: number;
        field: string;
        message: string;
      }>;
      preview?: Array<{ [key: string]: any }>;
    }>> {
      const formData = new FormData();
      formData.append('file', file);
      if (options) {
        formData.append('options', JSON.stringify(options));
      }

      const response = await fetch('/api/bulk/import/inventory', {
        method: 'POST',
        body: formData,
      });
      return response.json();
    }
  },

  // Export operations
  export: {
    // Export orders to CSV/Excel
    async orders(params: {
      format: 'csv' | 'xlsx';
      filters?: any;
      fields?: string[];
      dateFrom?: string;
      dateTo?: string;
    }): Promise<Blob> {
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          if (typeof value === 'object') {
            queryParams.append(key, JSON.stringify(value));
          } else {
            queryParams.append(key, String(value));
          }
        }
      });

      const response = await fetch(`/api/bulk/export/orders?${queryParams}`);
      return response.blob();
    },

    // Export deliveries to CSV/Excel
    async deliveries(params: {
      format: 'csv' | 'xlsx';
      filters?: any;
      fields?: string[];
      dateFrom?: string;
      dateTo?: string;
    }): Promise<Blob> {
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          if (typeof value === 'object') {
            queryParams.append(key, JSON.stringify(value));
          } else {
            queryParams.append(key, String(value));
          }
        }
      });

      const response = await fetch(`/api/bulk/export/deliveries?${queryParams}`);
      return response.blob();
    },

    // Export inventory to CSV/Excel
    async inventory(params: {
      format: 'csv' | 'xlsx';
      filters?: any;
      fields?: string[];
      includeMovements?: boolean;
    }): Promise<Blob> {
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          if (typeof value === 'object') {
            queryParams.append(key, JSON.stringify(value));
          } else {
            queryParams.append(key, String(value));
          }
        }
      });

      const response = await fetch(`/api/bulk/export/inventory?${queryParams}`);
      return response.blob();
    }
  },

  // Get bulk operation status
  async getOperationStatus(operationId: string): Promise<ApiResponse<{
    id: string;
    type: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    progress: number;
    total: number;
    processed: number;
    failed: number;
    errors: Array<{ message: string; details?: any }>;
    createdAt: string;
    completedAt: string | null;
  }>> {
    const response = await fetch(`/api/bulk/operations/${operationId}`);
    return response.json();
  },

  // Get bulk operation history
  async getOperationHistory(params?: {
    limit?: number;
    page?: number;
    type?: string;
    status?: string;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<ApiResponse<Array<{
    id: string;
    type: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    total: number;
    processed: number;
    failed: number;
    createdBy: string;
    createdByName: string;
    createdAt: string;
    completedAt: string | null;
  }>>> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, String(value));
        }
      });
    }

    const response = await fetch(`/api/bulk/operations?${queryParams}`);
    return response.json();
  }
};

export default bulkApi; 