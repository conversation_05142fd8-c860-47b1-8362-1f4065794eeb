import React, { memo } from 'react';
import { AllOrdersViewItem } from '@/types';
import OrderRow from './OrderRow';

interface OrdersTableBodyProps {
  orders: AllOrdersViewItem[];
  selectedOrderId?: string | null;
  onRowClick: (orderId: string) => void;
}

// Custom comparison function to optimize re-renders
const arePropsEqual = (prevProps: OrdersTableBodyProps, nextProps: OrdersTableBodyProps): boolean => {
  // Always re-render if the orders array reference changes
  if (prevProps.orders !== nextProps.orders) return false;
  
  // Always re-render if selected order changes
  if (prevProps.selectedOrderId !== nextProps.selectedOrderId) return false;
  
  // Check if orders content has changed
  if (prevProps.orders.length !== nextProps.orders.length) return false;
  
  // Check if any order has changed - compare essential fields
  for (let i = 0; i < prevProps.orders.length; i++) {
    const prevOrder = prevProps.orders[i];
    const nextOrder = nextProps.orders[i];
    
    if (prevOrder.id !== nextOrder.id) return false;
    if (prevOrder.status !== nextOrder.status) return false;
    if (prevOrder.totalAmount !== nextOrder.totalAmount) return false;
    if (prevOrder.trackingNumber !== nextOrder.trackingNumber) return false;
    if (prevOrder.isProblem !== nextOrder.isProblem) return false;
  }
  
  return true;
};

const OrdersTableBodyComponent: React.FC<OrdersTableBodyProps> = ({ 
  orders, 
  selectedOrderId, 
  onRowClick 
}) => {
  // Log render for debugging
  if (process.env.NODE_ENV === 'development') {
    console.log(`OrdersTableBody rendering with ${orders.length} orders`);
  }
  
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Order Details
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Order Date
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Channel
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Customer
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Amount
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Flags
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Tracking
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {orders.map((order) => (
            <OrderRow
              key={`${order.id}-${Date.now()}`} // Force re-render on each update
              order={order}
              isSelected={selectedOrderId === order.id}
              onRowClick={onRowClick}
            />
          ))}
        </tbody>
      </table>
    </div>
  );
};

// Export memoized component
const OrdersTableBody = memo(OrdersTableBodyComponent, arePropsEqual);
export default OrdersTableBody; 