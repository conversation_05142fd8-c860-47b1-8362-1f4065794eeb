import { IconType } from 'react-icons';
import {
  FaQuestionCircle,
  FaPaperPlane,
  FaTrash,
  FaWrench,
  FaFacebook,
  FaInstagram,
  FaUps,
  FaFedex,
  FaUsps,
} from 'react-icons/fa';
import { SiWalmart, SiShopee, SiEbay, SiAmazon, SiTiktok, SiShopify, SiDhl } from 'react-icons/si';
import { TbWorldWww } from 'react-icons/tb';

// Define the type for our map for strict type-checking
export type PlatformIconMap = {
  [key: string]: IconType;
};

export type ActionIconMap = PlatformIconMap;

// The map itself. Add new platforms here in the future.
const platformIconMap: PlatformIconMap = {
  amazon: SiAmazon,
  walmart: SiWalmart,
  shopify: SiShopify,
  website: TbWorldWww,
  shopee: SiShopee,
  ebay: SiEbay,
  tiktok: SiTiktok,
  facebook: FaFacebook,
  instagram: FaInstagram,
};

// Carrier specific icons
const carrierIconMap: { [key: string]: IconType } = {
  ups: FaUps,
  fedex: FaFedex,
  usps: FaUsps,
  dhl: SiDhl,
};

const ActionIcons: ActionIconMap = {  // Action Icons
  send: FaPaperPlane,
  trash: FaTrash,
  repair: FaWrench,
  resend: FaPaperPlane, // Alias for send
  cancel: FaTrash,      // Alias for trash
  // Add other platforms as needed
};

// A default icon to use when a platform is not found
const DefaultIcon = FaQuestionCircle;

export const iconMap = {
  platformIconMap,
  carrierIconMap,
  ActionIcons,
  DefaultIcon,
};