@tailwind base;
@tailwind components;
@tailwind utilities;

/*
  Non-Critical CSS: These styles will be loaded asynchronously.
*/

/* Custom Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Custom Component Styles */
.sidebar-hover-trigger {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  width: 12px;
  z-index: 49; /* Just below the sidebar's z-index of 50 */
}

/* Apply bg-main-bg to html as well for consistent background */
html {
  @apply bg-main-bg;
}
body {
   background-color: transparent; /* Body can be transparent if html has the bg */
}

/* Animation for the row fading out from Open Orders */
@keyframes simpleFadeOutCollapse {
  from {
    opacity: 1;
    /* height, line-height, padding will be their natural values */
  }
  to {
    opacity: 0;
    height: 0;
    padding-top: 0;
    padding-bottom: 0;
    margin-top: 0;
    margin-bottom: 0;
    line-height: 0;
    border-top-width: 0;
    border-bottom-width: 0;
    font-size: 0; /* Helps ensure text inside collapses */
  }
}
.order-animate-out {
  animation: simpleFadeOutCollapse 1.2s ease-out forwards;
  overflow: hidden; /* To make height animation work cleanly */
}

/* Animation for the row appearing in Packed Orders */
@keyframes fadeInHighlight {
  0% { opacity: 0; transform: translateY(10px) scale(0.98); background-color: #e0f2fe; /* light blue for highlight */ }
  50% { opacity: 1; transform: translateY(0) scale(1); background-color: #e0f2fe; }
  100% { opacity: 1; transform: translateY(0) scale(1); background-color: transparent; }
}
.order-appearing {
  animation: fadeInHighlight 0.8s ease-out forwards;
}

/* CSS for KpiCard progress arc animation */
.progress-arc {
  transition: stroke-dashoffset 1.2s ease-out;
  /* transition-delay: 0.1s; /* Optional: small delay before animation starts */
}

/* Override browser autofill styles */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px white inset !important; /* Force white background */
    -webkit-text-fill-color: #111827 !important; /* Force dark text (Tailwind gray-900) */
    /* transition: background-color 5000s ease-in-out 0s; */ /* Alternative trick */
}
/* For Firefox (less common to override, but good to be aware) */
input:-moz-autofill {
  filter: none; /* May not work for background, box-shadow is more reliable if needed for FF*/
}

/* Order Detail Panel Animations */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out forwards;
}

.animate-slide-up {
  animation: slide-up 0.4s ease-out forwards;
}

/* Hide scrollbar while maintaining scroll functionality */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;     /* Firefox */
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;             /* Safari and Chrome */
}

/* Dynamic Sidebar Hover Effects */
.sidebar-wrapper {
  pointer-events: auto;
}

.sidebar-hover-area {
  pointer-events: auto;
  z-index: 49; /* Below sidebar but above main content */
}

.sidebar-container {
  pointer-events: auto;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Enhanced hover transitions for nav items */
.nav-item {
  transform-origin: center;
  will-change: transform, background-color, box-shadow;
}

.nav-item:hover {
  transform: translateX(4px) scale(1.02);
}

/* Smooth scale transitions */
.scale-102 {
  transform: scale(1.02);
}
