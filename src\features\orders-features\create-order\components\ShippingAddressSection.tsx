import React from 'react';
import { CreateOrderFormData } from '@/types';

interface ShippingAddressSectionProps {
  formData: CreateOrderFormData;
  onChange: (updates: Partial<CreateOrderFormData>) => void;
}

const ShippingAddressSection: React.FC<ShippingAddressSectionProps> = ({ formData, onChange }) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Shipping Address</h3>
      <input
        type="text"
        placeholder="Street Address"
        value={formData.shipping_street}
        onChange={(e) => onChange({ shipping_street: e.target.value })}
        className="w-full px-3 py-2 border rounded-md"
        required
      />
      <div className="grid grid-cols-3 gap-4">
        <input
          type="text"
          placeholder="City"
          value={formData.shipping_city}
          onChange={(e) => onChange({ shipping_city: e.target.value })}
          className="px-3 py-2 border rounded-md"
          required
        />
        <input
          type="text"
          placeholder="State"
          value={formData.shipping_state}
          onChange={(e) => onChange({ shipping_state: e.target.value })}
          className="px-3 py-2 border rounded-md"
          required
        />
        <input
          type="text"
          placeholder="ZIP Code"
          value={formData.shipping_zip_code}
          onChange={(e) => onChange({ shipping_zip_code: e.target.value })}
          className="px-3 py-2 border rounded-md"
          required
        />
      </div>
    </div>
  );
};

export default ShippingAddressSection; 