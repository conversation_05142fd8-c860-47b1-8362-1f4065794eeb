import * as React from 'react';
import { useRef, useState, useMemo, useCallback } from 'react';
import <PERSON> from 'papaparse';
import { FulfillOrderItem, OrderStatus } from '@/types';
import { ChannelAccordion } from './ChannelAccordion';
import { groupOrdersOptimized } from '@/shared/lib/services/order-grouping-service';
import { Icon } from '@/shared/ui/core/Icon';
import { updateOrdersToShipped } from '@/shared/lib/services/fulfill/fulfill-service';
import { FaChevronDown } from 'react-icons/fa';
import { cn } from '@/shared/lib/utils/core/cn';
import { showCsvValidationError, showCsvParsingError } from '@/shared/lib/services/fulfill/csv-notification-service';
import { processCsvFile } from '@/shared/lib/services/fulfill/csv-processing-service';

interface LabelStationProps {
  orders: FulfillOrderItem[];
  onStatusChange: (orderId: string, newStatus: OrderStatus, trackingId?: string) => void;
  isUpdating: boolean;
  onCsvDataLoaded: (data: { order_id: string; tracking_number: string; carrier_id?: string; carrier_name?: string }[]) => void;
  onDataRefresh: () => void;
  onOpenTrackingModal: (orders: FulfillOrderItem[]) => void;
}

export const LabelStation: React.FC<LabelStationProps> = ({
  orders,
  onStatusChange,
  isUpdating,
  onCsvDataLoaded,
  onDataRefresh,
  onOpenTrackingModal,
}) => {
  const uploadInputRef = useRef<HTMLInputElement>(null);
  const [selectedOrderIds, setSelectedOrderIds] = useState(new Set<string>());
  const [isConfirming, setIsConfirming] = useState<string | null>(null);
  const [collapsedPlatforms, setCollapsedPlatforms] = useState(new Set<string>());

  // Task 3.7: Use optimized grouping function to get both groupings in single pass
  const { channelGroups, buyerGroups } = useMemo(() => groupOrdersOptimized(orders), [orders]);

  // Task 3.8: Optimize buyer groups calculation - replace nested forEach with efficient for...of loops
  const buyerGroupsBySubChannel = useMemo(() => {
    const map = new Map<string, typeof buyerGroups>();
    
    // Task 3.8: Use for...of instead of forEach for better performance with nested loops
    for (const channelGroup of channelGroups) {
      for (const subChannel of channelGroup.subChannels) {
        const key = `${channelGroup.channel}-${subChannel.name}`;
        
        // Task 3.8: Use Set for O(1) order ID lookups instead of nested some() operations
        const subChannelOrderIds = new Set(subChannel.orders.map(order => order.id));
        
        // Single-pass filtering with pre-built Set for efficiency
        const filteredBuyerGroups = buyerGroups
          .map(buyerGroup => {
            const matchingOrders = buyerGroup.orders.filter(order => 
              subChannelOrderIds.has(order.id)
            );
            return matchingOrders.length > 0 ? { ...buyerGroup, orders: matchingOrders } : null;
          })
          .filter(Boolean) as typeof buyerGroups;
        
        map.set(key, filteredBuyerGroups);
      }
    }
    
    return map;
  }, [channelGroups, buyerGroups]);

  // Task 3.8: Optimize CSV export data creation - use direct mapping instead of array operations
  const csvExportData = useMemo(() => {
    // Use Array.from for better performance with large datasets
    return orders.map(order => ({
      order_id: order.id,
      order_number: order.order_number || '',  // Ensure order_number is included
      tracking_number: '', // Empty for user to fill
    }));
  }, [orders]);

  // Task 3.8: Optimize channel data calculation - single pass with Set operations
  const channelData = useMemo(() => {
    return channelGroups.map(channelGroup => {
      // Single pass through subChannels with optimized Set operations
      let totalOrdersInChannel = 0;
      const channelOrderIds = new Set<string>();
      
      // Task 3.8: Use for...of instead of forEach for nested loops
      for (const subChannel of channelGroup.subChannels) {
        totalOrdersInChannel += subChannel.orders.length;
        for (const order of subChannel.orders) {
          channelOrderIds.add(order.id);
        }
      }

      return {
        ...channelGroup,
        totalOrdersInChannel,
        channelOrderIds
      };
    });
  }, [channelGroups]);

  // Task 3.8: Optimize selected orders filtering - use Set.has() for O(1) lookups
  const selectedOrdersByChannel = useMemo(() => {
    const selectedArray = Array.from(selectedOrderIds);
    const result = {} as Record<string, string[]>;
    
    // Task 3.8: Use for...of instead of reduce for better performance
    for (const channelGroup of channelData) {
      result[channelGroup.channel] = selectedArray.filter(id => 
        channelGroup.channelOrderIds.has(id)
      );
    }
    
    return result;
  }, [channelData, selectedOrderIds]);

  const handleTogglePlatformCollapse = (platformName: string) => {
    setCollapsedPlatforms(prev => {
      const newSet = new Set(prev);
      if (newSet.has(platformName)) {
        newSet.delete(platformName);
      } else {
        newSet.add(platformName);
      }
      return newSet;
    });
  };

  const handleToggleSelection = (orderId: string) => {
    setSelectedOrderIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(orderId)) {
        newSet.delete(orderId);
      } else {
        newSet.add(orderId);
      }
      return newSet;
    });
  };

  const handleConfirmSelected = async (channelName: string, orderIdsToConfirm: string[]) => {
    setIsConfirming(channelName);
    try {
      await updateOrdersToShipped(orderIdsToConfirm, 'shipped');
      // Deselect confirmed orders
      setSelectedOrderIds(prev => {
        const newSet = new Set(prev);
        orderIdsToConfirm.forEach(id => newSet.delete(id));
        return newSet;
      });
      onDataRefresh(); // Refresh all data
    } catch (error) {
      console.error(`Failed to confirm orders for ${channelName}:`, error);
      // You might want to set an error state here
    } finally {
      setIsConfirming(null);
    }
  };

  const handleDownloadCSV = useCallback(() => {
    const csv = Papa.unparse(csvExportData);
    
    // Create a Blob from the CSV string
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    
    // Create a link element
    const link = document.createElement('a');
    
    // Set the link's href to a URL representing the blob's data
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    
    // Set the filename for the download
    const date = new Date().toISOString().slice(0, 10);
    link.setAttribute('download', `packed-orders-for-delivery-${date}.csv`);
    
    // Append the link to the body (required for Firefox)
    document.body.appendChild(link);
    
    // Programmatically click the link to trigger the download
    link.click();
    
    // Remove the link from the document
    document.body.removeChild(link);
  }, [csvExportData]);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      processCsvFile(file).then(parsedData => {
        if (parsedData) {
          onCsvDataLoaded(parsedData);
        }
      });
    }
  };

  // Show empty state when no packed orders are available
  if (orders.length === 0) {
    return (
      <div className="h-full overflow-y-auto">
        <div className="max-w-6xl mx-auto p-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
            <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">No Packed Orders Yet</h2>
            <p className="text-gray-600">Packed orders will appear here, ready for label confirmation.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto">
      <div className="max-w-7xl mx-auto p-6">
        {/* Header with bulk actions */}
        <div className="flex justify-end items-center mb-6 space-x-2">
          <button
            onClick={handleDownloadCSV}
            className="px-4 py-2 bg-white border border-gray-300 text-gray-700 text-sm font-medium rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Download Orders CSV
            <span className="block text-xs text-gray-500 mt-1">
              {orders.length} orders with order numbers and IDs
            </span>
          </button>
          <button
            onClick={() => uploadInputRef.current?.click()}
            disabled={isUpdating}
            className="relative px-4 py-2 bg-indigo-600 border border-transparent text-white text-sm font-medium rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isUpdating ? 'Uploading...' : 'Upload Tracking Info'}
            <span className="block text-xs text-white/80 mt-1">
              CSV should include order numbers
            </span>
            {isUpdating && (
              <div className="absolute inset-0 flex items-center justify-center bg-indigo-600 bg-opacity-50">
                <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
            )}
          </button>
          <input
            type="file"
            ref={uploadInputRef}
            className="hidden"
            accept=".csv"
            onChange={handleFileUpload}
          />
        </div>
        <div className="flex justify-end">
          <p className="text-xs text-gray-500 mt-1">
            CSV must contain columns: `order_id` and `tracking_number`. Order numbers are preserved for better identification. Carrier will be auto-detected.
          </p>
        </div>

        <div className="space-y-8">
          {channelData.map(channelGroup => {
            // Use pre-calculated values from memoized channelData
            const { totalOrdersInChannel } = channelGroup;
            const selectedInChannel = selectedOrdersByChannel[channelGroup.channel] || [];
            const isCollapsed = collapsedPlatforms.has(channelGroup.channel);

            return (
              <div key={channelGroup.channel} className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div className="flex items-center justify-between">
                  <button 
                    onClick={() => handleTogglePlatformCollapse(channelGroup.channel)}
                    className="flex items-center flex-grow text-left"
                  >
                    <Icon platform={channelGroup.channel} variant="circle" className="h-8 w-8 mr-3" />
                    <h2 className="text-xl font-bold text-gray-800">
                      {channelGroup.channel}
                    </h2>
                    <span className="text-gray-500 ml-2">({totalOrdersInChannel} orders)</span>
                    <FaChevronDown className={cn('ml-4 text-gray-400 transition-transform duration-300', isCollapsed && '-rotate-90')} />
                  </button>
                  <button
                    onClick={() => handleConfirmSelected(channelGroup.channel, selectedInChannel)}
                    disabled={selectedInChannel.length === 0 || isConfirming === channelGroup.channel || !selectedInChannel.every(orderId => {
                      const order = orders.find(o => o.id === orderId);
                      return order?.status === 'ready_to_ship';
                    })}
                    className="relative px-4 py-2 bg-green-600 text-white text-sm font-semibold rounded-full shadow hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isConfirming === channelGroup.channel ? 'Confirming...' : 'Confirm'}
                    {selectedInChannel.length > 0 && (
                      <span className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-green-800 text-xs font-bold text-white">
                        {selectedInChannel.length}
                      </span>
                    )}
                  </button>
                </div>
                
                {!isCollapsed && (
                  <div className="mt-4 pt-4 border-t border-gray-100">
                    {channelGroup.subChannels.map(subChannel => {
                      // Task 3.7: Use pre-calculated buyer groups instead of calling groupPackedOrdersByBuyer in render
                      const subChannelKey = `${channelGroup.channel}-${subChannel.name}`;
                      const subChannelBuyerGroups = buyerGroupsBySubChannel.get(subChannelKey) || [];
                      
                      return (
                        <div key={subChannel.name} className="mb-6 ml-4 pl-4">
                          <div className="flex items-center mb-3">
                            <span className="inline-block px-3 py-1 rounded-full text-sm font-semibold bg-orange-100 text-orange-800">{subChannel.name}</span>
                            <span className="text-gray-500 ml-2">({subChannel.orders.length} orders)</span>
                          </div>
                          <ChannelAccordion
                            buyerGroups={subChannelBuyerGroups}
                            onStatusChange={onStatusChange}
                            isUpdating={isUpdating}
                            isConfirming={isConfirming}
                            onDataRefresh={onDataRefresh}
                            onOpenTrackingModal={onOpenTrackingModal}
                            selectedOrderIds={selectedOrderIds}
                            onToggleSelection={handleToggleSelection}
                          />
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            )
          })}
        </div>
      </div>
    </div>
  );
};