import { useState, useEffect, useCallback, useMemo } from 'react';
import { UserRole } from '@/types';
// Import from utils/orders instead of directly from service
import { fetchOrdersData as fetchOrdersDataUtil } from '@/shared/lib/utils/domain/orders/order-utils';
import { FetchOrdersResponse } from '@/shared/lib/services/order/live-order-service';
import { updateUrlWithFilters, getFiltersFromUrl, subscribeToUrlChanges, ExtendedOrderFilters } from '@/shared/lib/utils/data/url-params';

// Add debounce utility
const debounce = (fn: Function, ms = 300) => {
  let timeoutId: ReturnType<typeof setTimeout>;
  return function(...args: any[]) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn(...args), ms);
  };
};

interface UseOrdersTableProps {
  filters: ExtendedOrderFilters;
  userRole?: UserRole | undefined;
  onFiltersChange?: (filters: ExtendedOrderFilters) => void;
}

interface UseOrdersTableReturn {
  ordersData: FetchOrdersResponse | null;
  loading: boolean;
  error: string | null;
  effectiveFilters: ExtendedOrderFilters;
  handlePageChange: (page: number) => void;
  handleRetry: () => void;
  fetchOrdersData: (filters: ExtendedOrderFilters) => Promise<void>; // Return Promise for chaining
}

export const useOrdersTable = ({ 
  filters, 
  //userRole, 
  onFiltersChange,
}: UseOrdersTableProps): UseOrdersTableReturn => {
  const [ordersData, setOrdersData] = useState<FetchOrdersResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Merge filters from props with URL filters
  const urlFilters = getFiltersFromUrl();
  
  // Include ALL filter dependencies to make filtering work properly
  const effectiveFilters = useMemo(() => {
    return { ...urlFilters, ...filters };
  }, [
    filters.search,
    filters.page,
    filters.status?.length,
    filters.platform?.length,
    filters.channel?.length,
    filters.isUrgent,
    filters.isProblem,
    filters.hasNotes,
    filters.isResent,
    filters.dateFrom,
    filters.dateTo,
  ]);

  // Use the shared utility function - return Promise for proper handling
  const fetchOrdersDataWrapped = useCallback(async (filtersToUse: ExtendedOrderFilters): Promise<void> => {
    try {
      await fetchOrdersDataUtil(
        filtersToUse,
        setLoading,
        setError,
        setOrdersData
      );
      return; // Return void Promise
    } catch (err) {
      // Error is already handled in the utility
      return Promise.reject(err);
    }
  }, []);

  // Create debounced version of fetch function
  const debouncedFetch = useMemo(() => 
    debounce((filters: ExtendedOrderFilters) => {
      fetchOrdersDataWrapped(filters);
      updateUrlWithFilters(filters, true);
    }, 350), 
    [fetchOrdersDataWrapped]
  );

  // Stable filters for URL change handler
  const stableFilters = useMemo(() => filters, [
    filters.search,
    filters.page,
    filters.status?.length,
    filters.platform?.length,
    filters.channel?.length,
    filters.isUrgent,
    filters.isProblem,
    filters.hasNotes,
    filters.isResent,
    filters.dateFrom,
    filters.dateTo,
  ]);

  // URL change handler
  const handleUrlChange = useCallback((urlFilters: ExtendedOrderFilters) => {
    const newFilters = { ...stableFilters, ...urlFilters };
    fetchOrdersDataWrapped(newFilters);
    
    // Notify parent of filter changes
    if (onFiltersChange) {
      onFiltersChange(newFilters);
    }
  }, [stableFilters, fetchOrdersDataWrapped, onFiltersChange]);

  // Subscribe to URL changes (back/forward navigation)
  useEffect(() => {
    const unsubscribeFromUrl = subscribeToUrlChanges(handleUrlChange);
    return () => unsubscribeFromUrl();
  }, [handleUrlChange]);

  // React to filter changes with improved debouncing
  useEffect(() => {
    // Use longer debounce for search, shorter for pagination, immediate for others
    const isSearchChange = effectiveFilters.search !== undefined;
    
    if (isSearchChange) {
      debouncedFetch(effectiveFilters);
    } else {
      fetchOrdersDataWrapped(effectiveFilters);
      updateUrlWithFilters(effectiveFilters, true);
    }
    
    // No need for cleanup here as it's handled by the debounce function
  }, [
    effectiveFilters.search,
    effectiveFilters.page,
    effectiveFilters.status?.length,
    effectiveFilters.platform?.length,
    effectiveFilters.channel?.length,
    effectiveFilters.isUrgent,
    effectiveFilters.isProblem,
    effectiveFilters.hasNotes,
    effectiveFilters.isResent,
    effectiveFilters.dateFrom,
    effectiveFilters.dateTo,
    fetchOrdersDataWrapped,
    debouncedFetch
  ]);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    const newFilters = { ...effectiveFilters, page };
    
    // Notify parent component of filter change
    if (onFiltersChange) {
      onFiltersChange(newFilters);
    }
    
    // Update URL and fetch data
    updateUrlWithFilters(newFilters);
    fetchOrdersDataWrapped(newFilters);
  }, [effectiveFilters, onFiltersChange, fetchOrdersDataWrapped]);

  // Handle retry
  const handleRetry = useCallback(() => {
    fetchOrdersDataWrapped(effectiveFilters);
  }, [fetchOrdersDataWrapped, effectiveFilters]);

  return {
    ordersData,
    loading,
    error,
    effectiveFilters,
    handlePageChange,
    handleRetry,
    fetchOrdersData: fetchOrdersDataWrapped, // Expose the function for WebSocket integration
  };
}; 