import React, { memo } from 'react';
import Modal from '@/shared/ui/overlay/Modal';
import ProductForm from './ProductForm';
import { Product } from '@/types';
import { ProductFormData } from '@/shared/lib/hooks/inventory/products/useProductForm';

interface ProductFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialData?: Partial<ProductFormData>;
  onSuccess?: (product: Product) => void;
  title?: string;
  submitButtonText?: string;
}

const ProductFormModal: React.FC<ProductFormModalProps> = ({
  isOpen,
  onClose,
  initialData,
  onSuccess,
  title = 'Add New Product',
  submitButtonText = 'Save Product'
}) => {
  const handleSuccess = (product: Product) => {
    if (onSuccess) {
      onSuccess(product);
    }
    onClose();
  };

  return (
    <Modal 
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      size="xl"
      showDefaultFooter={false}
    >
      <ProductForm
        initialData={initialData}
        onSuccess={handleSuccess}
        onCancel={onClose}
        submitButtonText={submitButtonText}
      />
    </Modal>
  );
};

// Custom comparison function for memoization
const areEqual = (prevProps: ProductFormModalProps, nextProps: ProductFormModalProps) => {
  // Always re-render when open state changes
  if (prevProps.isOpen !== nextProps.isOpen) {
    return false;
  }
  
  // Skip comparison of other props when modal is closed
  if (!prevProps.isOpen && !nextProps.isOpen) {
    return true;
  }
  
  // Compare function references
  if (
    prevProps.onClose !== nextProps.onClose ||
    prevProps.onSuccess !== nextProps.onSuccess
  ) {
    return false;
  }
  
  // Compare string props
  if (
    prevProps.title !== nextProps.title ||
    prevProps.submitButtonText !== nextProps.submitButtonText
  ) {
    return false;
  }
  
  // Compare initialData if present
  if (
    (prevProps.initialData && !nextProps.initialData) ||
    (!prevProps.initialData && nextProps.initialData)
  ) {
    return false;
  }
  
  if (prevProps.initialData && nextProps.initialData) {
    // Check if any of the properties have changed
    // Use JSON.stringify for a fast deep comparison of objects
    if (JSON.stringify(prevProps.initialData) !== JSON.stringify(nextProps.initialData)) {
      return false;
    }
  }
  
  return true;
};

// Export memoized component with custom comparison function
export default memo(ProductFormModal, areEqual); 