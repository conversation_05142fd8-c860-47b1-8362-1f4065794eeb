import React, { useMemo } from 'react';
import { AllOrdersStatus, AllOrdersViewItem } from '@/types';

// Status colors mapping for consistent styling
export const STATUS_COLORS: Record<AllOrdersStatus, string> = {
  'open': 'bg-blue-100 text-blue-800',
  'packed': 'bg-purple-100 text-purple-800',
  'ready_to_ship': 'bg-indigo-100 text-indigo-800',
  'shipped': 'bg-yellow-100 text-yellow-800',
  'completed': 'bg-green-100 text-green-800',
  'cancelled': 'bg-red-100 text-red-800',
  'refund': 'bg-orange-100 text-orange-800',
  'on_hold': 'bg-yellow-100 text-yellow-800',
  'reshipment_scheduled': 'bg-purple-100 text-purple-800',
};

// Cache for formatted currencies
const currencyFormatter = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
});

// Create a cache for formatted currency values
const currencyCache: Record<string, string> = {};

// Currency formatter with caching
export const formatCurrency = (amount: number): string => {
  const key = amount.toString();
  if (!currencyCache[key]) {
    currencyCache[key] = currencyFormatter.format(amount);
  }
  return currencyCache[key];
};

// Date format cache
const dateCache: Record<string, string> = {};

// Date formatter with caching
export const formatDate = (dateString: string): string => {
  if (!dateCache[dateString]) {
    dateCache[dateString] = new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  }
  return dateCache[dateString];
};

// Status priority for sorting
export const getStatusPriority = (status: AllOrdersStatus): number => {
  const priorityMap: Record<AllOrdersStatus, number> = {
    'open': 1,
    'packed': 2,
    'ready_to_ship': 3,
    'shipped': 4, 
    'completed': 5,
    'on_hold': 6,
    'cancelled': 7,
    'refund': 8,
    'reshipment_scheduled': 9,
  };
  return priorityMap[status] || 10;
};

// Status badge cache
const statusBadgeCache: Record<string, React.ReactNode> = {};

// Status badge creation with caching
export const createStatusBadge = (status: AllOrdersStatus): React.ReactNode => {
  if (!statusBadgeCache[status]) {
    const displayStatus = status.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');

    statusBadgeCache[status] = React.createElement('span', {
      className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${STATUS_COLORS[status]}`
    }, displayStatus);
  }
  
  return statusBadgeCache[status];
};

// Flag badge cache key generator
const getFlagCacheKey = (order: AllOrdersViewItem): string => {
  return `${order.isUrgent}-${order.isProblem}-${order.isResent}-${order.hasNotes}`;
};

// Flag badge cache
const flagBadgeCache: Record<string, React.ReactNode[]> = {};

// Flag badges creation with caching
export const createFlagBadges = (order: AllOrdersViewItem): React.ReactNode[] => {
  const cacheKey = getFlagCacheKey(order);
  
  if (!flagBadgeCache[cacheKey]) {
    const badges: React.ReactNode[] = [];

    if (order.isUrgent) {
      badges.push(
        React.createElement('span', {
          key: 'urgent',
          className: 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800'
        }, '🚨 Urgent')
      );
    }

    if (order.isProblem) {
      badges.push(
        React.createElement('span', {
          key: 'problem',
          className: 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800'
        }, '⚠️ Problem')
      );
    }

    if (order.isResent) {
      badges.push(
        React.createElement('span', {
          key: 'resent',
          className: 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800'
        }, '🔄 Resent')
      );
    }

    if (order.hasNotes) {
      badges.push(
        React.createElement('span', {
          key: 'notes',
          className: 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800'
        }, '📝 Notes')
      );
    }
    
    flagBadgeCache[cacheKey] = badges;
  }

  return flagBadgeCache[cacheKey];
};

// Optimized sort for orders by priority
export const sortOrdersByPriority = (orders: AllOrdersViewItem[]): AllOrdersViewItem[] => {
  if (!orders || orders.length === 0) return [];
  
  return [...orders].sort((a: AllOrdersViewItem, b: AllOrdersViewItem) => {
    const aPriority = getStatusPriority(a.status);
    const bPriority = getStatusPriority(b.status);
    
    if (aPriority !== bPriority) {
      return aPriority - bPriority;
    }
    
    // Secondary sort by date (newest first)
    return new Date(b.orderDate).getTime() - new Date(a.orderDate).getTime();
  });
}; 