import React, { useMemo } from 'react';
import { cn } from '@/shared/lib/utils/core/cn';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  className?: string;
}

export const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  hasNextPage,
  hasPreviousPage,
  className
}) => {
  // Memoize page number calculations to avoid recalculation on every render
  const visiblePages = useMemo(() => {
    const delta = 2; // Number of pages to show on each side of current page
    const range: number[] = [];
    const rangeWithDots: number[] = [];

    // Always include first page
    range.push(1);

    // Add pages around current page
    for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
      range.push(i);
    }

    // Always include last page if there are multiple pages
    if (totalPages > 1) {
      range.push(totalPages);
    }

    // Remove duplicates and sort
    const uniquePages = [...new Set(range)].sort((a, b) => a - b);

    // Add ellipsis where there are gaps
    let previous = 0;
    uniquePages.forEach(page => {
      if (page - previous > 1) {
        rangeWithDots.push(-1); // -1 represents ellipsis
      }
      rangeWithDots.push(page);
      previous = page;
    });

    return rangeWithDots;
  }, [currentPage, totalPages]);

  if (totalPages <= 1) {
    return null; // Don't show pagination if there's only one page
  }

  return (
    <div className={cn('flex items-center justify-between', className)}>
      {/* Previous Button */}
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={!hasPreviousPage}
        className={cn(
          'px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150',
          hasPreviousPage
            ? 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-500'
            : 'text-gray-400 bg-gray-100 border border-gray-300 cursor-not-allowed'
        )}
      >
        Previous
      </button>

      {/* Page Numbers */}
      <div className="flex items-center space-x-1">
        {visiblePages.map((page, index) => {
          if (page === -1) {
            return (
              <span key={`ellipsis-${index}`} className="px-3 py-2 text-gray-400">
                ...
              </span>
            );
          }

          return (
            <button
              key={page}
              onClick={() => onPageChange(page)}
              className={cn(
                'px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150',
                page === currentPage
                  ? 'text-white bg-blue-600 border border-blue-600'
                  : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-500'
              )}
            >
              {page}
            </button>
          );
        })}
      </div>

      {/* Next Button */}
      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={!hasNextPage}
        className={cn(
          'px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150',
          hasNextPage
            ? 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-500'
            : 'text-gray-400 bg-gray-100 border border-gray-300 cursor-not-allowed'
        )}
      >
        Next
      </button>
    </div>
  );
}; 