import { Order, OrderStatus } from '@/types';
import { ordersApi } from '../../../api/orders/ordersAPI';
import { deliveriesApi } from '../../../api/deliveries/deliveriesAPI';

// Update order status for fulfill workflow: open → packed → shipped
export const updateOrderStatus = async (orderId: string, newStatus: OrderStatus, userId: string): Promise<void> => {
  if (!userId) {
    throw new Error('User ID is required for tracking order status changes');
  }

  try {
    // For 'packed' status, use the bulk pack API which is working correctly
    if (newStatus === 'packed') {
      await bulkPackOrders([orderId], userId);
      return;
    }
    
    // For other statuses, use the update method with status in the data
    const response = await ordersApi.update(orderId, {
      status: newStatus,
      notes: `Status updated to ${newStatus} by ${userId}`
    });
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to update order status');
    }
  } catch (error) {
    console.error('Failed to update order status:', error);
    throw error;
  }
};

// Bulk pack orders for ProductAccordion batch actions
export const bulkPackOrders = async (orderIds: string[], userId: string): Promise<{ success: number; failed: number }> => {
  if (!userId) {
    throw new Error('User ID is required for tracking bulk pack operations');
  }

  try {
    const response = await ordersApi.bulkPack({
      orderIds,
      userId
    });
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to bulk pack orders');
    }
    
    return response.data;
  } catch (error) {
    console.error('Failed to bulk pack orders:', error);
    return { success: 0, failed: orderIds.length };
  }
};

// Update tracking for shipping station
export const updateOrderTracking = async (orderId: string, trackingNumber: string, userId: string): Promise<void> => {
  if (!userId) {
    throw new Error('User ID is required for tracking order tracking updates');
  }

  try {
    // Use the update method with tracking number in the data
    const response = await ordersApi.update(orderId, {
      status: 'shipped',
      trackingNumber: trackingNumber,
      notes: `Tracking number ${trackingNumber} added by ${userId}`
    });
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to update tracking number');
    }
  } catch (error) {
    console.error('Failed to update tracking number:', error);
    throw error;
  }
};

// Get fulfill dashboard stats for BatchProgressBar
export const getFulfillStats = async (): Promise<{ readyToPack: number; packed: number; shipped: number; total: number }> => {
  try {
    const response = await ordersApi.getFulfillStats();
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to get fulfill stats');
    }
    
    return response.data;
  } catch (error) {
    console.error('Failed to get fulfill stats:', error);
    return {
      readyToPack: 0,
      packed: 0,
      shipped: 0,
      total: 0
    };
  }
};

// Get orders for packing station (status = 'open')
export const getPackingStationOrders = async (params = {}) => {
  try {
    const response = await ordersApi.getPackingStationOrders(params);
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to get packing station orders');
    }
    
    return response.data;
  } catch (error) {
    console.error('Failed to get packing station orders:', error);
    return [];
  }
};

// Get orders for label station (status = 'packed')
export const getLabelStationOrders = async (params = {}) => {
  try {
    const response = await ordersApi.getLabelStationOrders(params);
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to get label station orders');
    }
    
    return response.data;
  } catch (error) {
    console.error('Failed to get label station orders:', error);
    return [];
  }
};

// **NEW** - Get packing station summary (from packing_station_summary view)
export const getPackingStationSummary = async () => {
  try {
    const response = await ordersApi.getPackingStationSummary();
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to get packing station summary');
    }
    
    return response.data;
  } catch (error) {
    console.error('Failed to get packing station summary:', error);
    return [];
  }
};

// **NEW** - Get fulfill packing list (from fulfill_packing_list view)
export const getFulfillPackingList = async () => {
  try {
    const response = await ordersApi.getFulfillPackingList();
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to get fulfill packing list');
    }
    
    return response.data;
  } catch (error) {
    console.error('Failed to get fulfill packing list:', error);
    return [];
  }
};

export const updateOrdersFromCSV = async (
  orders: Order[],
  csvData: { OrderID: string; TrackingID: string; Status: string }[],
  userId: string
): Promise<Order[]> => {
  if (!userId) {
    throw new Error('User ID is required for tracking CSV updates');
  }

  let updatedOrders = [...orders];

  for (const row of csvData) {
    await updateOrderTracking(row.OrderID, row.TrackingID, userId);
    await updateOrderStatus(row.OrderID, row.Status as OrderStatus, userId);
  }

  return updatedOrders;
};

// Update orders to packed status in bulk
export const updateOrdersToPacked = async (orderIds: string[], userId: string): Promise<void> => {
  if (!userId) {
    throw new Error('User ID is required for tracking order status changes to packed');
  }

  // Use the bulkPackOrders function directly
  await bulkPackOrders(orderIds, userId);
};

export const updateOrdersToShipped = async (orderIds: string[], userId: string): Promise<void> => {
  if (!userId) {
    throw new Error('User ID is required for tracking order status changes to shipped');
  }

  // Update each order's status to 'shipped'
  for (const orderId of orderIds) {
    await updateOrderStatus(orderId, 'shipped', userId);
  }
};

// Assign delivery numbers to a batch of orders
export const assignDeliveryNumbers = async (orderIds: string[]): Promise<{ success: boolean; data?: any; message?: string }> => {
  try {
    const response = await deliveriesApi.assignDeliveryNumber(orderIds);
    if (!response.success) {
      throw new Error(response.message || 'Failed to assign delivery numbers');
    }
    return { success: true, data: response.data };
  } catch (error) {
    console.error('Failed to assign delivery numbers:', error);
    const message = error instanceof Error ? error.message : 'An unknown error occurred';
    return { success: false, message };
  }
};

export const bulkAssignTrackingNumbers = async (
  payload: { order_id: string; tracking_number: string; carrier_id: string; new_status?: string; order_number?: string }[]
): Promise<{ success: boolean; data?: any; message?: string }> => {
  if (!payload || payload.length === 0) {
    return { 
      success: false, 
      message: 'No orders provided for tracking assignment' 
    };
  }

  // Validate all required fields are present in each item
  const invalidItems = payload.filter(item => 
    !item.order_id || !item.tracking_number || !item.carrier_id
  );
  
  if (invalidItems.length > 0) {
    return { 
      success: false, 
      message: 'Missing required fields in some orders' 
    };
  }

  try {
    // Log the request for debugging
    console.log('Processing tracking assignment for orders:', 
      payload.map(item => ({ 
        id: item.order_id, 
        order_number: item.order_number || '',
        tracking: item.tracking_number,
        new_status: item.new_status || ''
      }))
    );
    
    const response = await deliveriesApi.bulkAssignTracking(payload);
    
    if (!response.success) {
      console.error('API returned error:', response.message);
      throw new Error(response.message || 'Failed to assign tracking numbers');
    }
    
    return { 
      success: true, 
      data: response.data, 
      message: 'Successfully assigned tracking numbers' 
    };
  } catch (error) {
    console.error('Failed to assign tracking numbers:', error);
    const message = error instanceof Error ? error.message : 'An unknown error occurred';
    return { success: false, message };
  }
}; 