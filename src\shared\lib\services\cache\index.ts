// Exported types
export type { <PERSON>acheEntry, CacheConfig, BaseCacheEntry } from './cache-storage';

// Direct exports from files
export { CacheKeyManager } from './cache-key-manager';
export { CacheStorage } from './cache-storage';
export { orderCacheService } from './order-cache-service';
export { inventoryCacheService } from './inventory-cache-service';
export { CacheWebSocketHandler } from './cache-websocket-handler';

// Import for internal use only (not exported)
import { webSocketService } from '../websocket/websocket-service';

/**
 * Initializes WebSocket handlers for cache invalidation with proper component ID tracking
 * @param cacheWebSocketHandler The cache WebSocket handler instance
 * @returns An object containing unsubscribe functions for cleanup
 */
export function initializeCacheWebSocketHandlers(cacheWebSocketHandler: any): { cleanup: () => void } {
  console.log('Initializing cache WebSocket handlers with component ID tracking');
  
  // Register all handlers with unique component IDs to prevent duplication
  const componentId = 'cache-invalidation-service';
  
  // Subscribe to all relevant events
  const unsubscribeOrderUpdate = webSocketService.subscribeWithId(
    'order_update', 
    componentId, 
    cacheWebSocketHandler.handleOrderUpdate.bind(cacheWebSocketHandler)
  );
  
  const unsubscribeOrderInsert = webSocketService.subscribeWithId(
    'order_insert', 
    componentId, 
    cacheWebSocketHandler.handleOrderInsert.bind(cacheWebSocketHandler)
  );
  
  const unsubscribeOrderDelete = webSocketService.subscribeWithId(
    'order_delete', 
    componentId, 
    cacheWebSocketHandler.handleOrderDelete.bind(cacheWebSocketHandler)
  );
  
  // Also subscribe to legacy upper-case event name for backward compatibility
  const unsubscribeLegacyUpdate = webSocketService.subscribeWithId(
    'ORDER_UPDATE', 
    componentId, 
    cacheWebSocketHandler.handleOrderUpdate.bind(cacheWebSocketHandler)
  );
  
  // Subscribe to inventory events
  const unsubscribeInventoryUpdate = webSocketService.subscribeWithId(
    'inventory_update', 
    componentId, 
    cacheWebSocketHandler.handleInventoryUpdate?.bind(cacheWebSocketHandler)
  );
  
  const unsubscribeInventoryInsert = webSocketService.subscribeWithId(
    'inventory_insert', 
    componentId, 
    cacheWebSocketHandler.handleInventoryInsert?.bind(cacheWebSocketHandler)
  );
  
  const unsubscribeInventoryDelete = webSocketService.subscribeWithId(
    'inventory_delete', 
    componentId, 
    cacheWebSocketHandler.handleInventoryDelete?.bind(cacheWebSocketHandler)
  );
  
  // Return a cleanup function that unsubscribes from all events
  return {
    cleanup: () => {
      console.log('Cleaning up cache WebSocket handlers');
      unsubscribeOrderUpdate();
      unsubscribeOrderInsert();
      unsubscribeOrderDelete();
      unsubscribeLegacyUpdate();
      // Clean up inventory handlers if they exist
      if (unsubscribeInventoryUpdate) unsubscribeInventoryUpdate();
      if (unsubscribeInventoryInsert) unsubscribeInventoryInsert();
      if (unsubscribeInventoryDelete) unsubscribeInventoryDelete();
    }
  };
} 