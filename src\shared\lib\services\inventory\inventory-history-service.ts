import { supabase } from '../../../../../supabase/supabase_client/client';

/**
 * Interface for stock movement query parameters
 */
export interface StockMovementParams {
  productId?: string;
  inventoryId?: string;
  fromDate?: string;
  toDate?: string;
  reasonCategory?: string[];
  type?: string[];
  page?: number;
  pageSize?: number;
}

/**
 * Service for managing inventory movement history
 */
export class InventoryHistoryService {
  /**
   * Get stock movement history with filtering and pagination
   */
  async getStockMovements(params: StockMovementParams = {}) {
    try {
      // Base query - use direct field access instead of foreign key relationships
      let query = supabase
        .from('inventory_movements')
        .select('*', { count: 'exact' });

      // Apply filters
      if (params.productId) {
        query = query.eq('product_id', params.productId);
      }

      if (params.inventoryId) {
        query = query.eq('inventory_id', params.inventoryId);
      }

      if (params.fromDate) {
        query = query.gte('timestamp', params.fromDate);
      }

      if (params.toDate) {
        query = query.lte('timestamp', params.toDate);
      }

      if (params.reasonCategory && params.reasonCategory.length > 0) {
        query = query.in('reason', params.reasonCategory);
      }

      if (params.type && params.type.length > 0) {
        query = query.in('type', params.type);
      }

      // Apply sorting
      query = query.order('timestamp', { ascending: false });

      // Apply pagination
      if (params.page !== undefined && params.pageSize !== undefined) {
        const from = (params.page - 1) * params.pageSize;
        const to = from + params.pageSize - 1;
        query = query.range(from, to);
      }

      const { data, error, count } = await query;

      if (error) throw error;

      // If no data, return empty array
      if (!data || data.length === 0) {
        return { data: [], count: 0 };
      }

      // Get product information separately if needed
      let productInfo: Record<string, { name: string; sku: string }> = {};
      
      if (data.some(item => item.product_id)) {
        const productIds = [...new Set(data.map(item => item.product_id).filter(Boolean))];
        
        if (productIds.length > 0) {
          const { data: products } = await supabase
            .from('products')
            .select('id, name, sku')
            .in('id', productIds);
            
          if (products) {
            productInfo = products.reduce((acc, product) => {
              acc[product.id] = { name: product.name, sku: product.sku };
              return acc;
            }, {} as Record<string, { name: string; sku: string }>);
          }
        }
      }

      // Format the data for consumption
      const formattedMovements = data.map(movement => {
        const product = movement.product_id ? productInfo[movement.product_id] : null;
        
        // Use default values to prevent errors
        return {
          id: movement.id,
          inventoryId: movement.inventory_id,
          productId: movement.product_id,
          productName: product?.name || 'Unknown Product',
          sku: product?.sku || 'N/A',
          type: movement.type,
          quantity: movement.quantity,
          previousStock: movement.previous_stock,
          newStock: movement.new_stock,
          reason: movement.reason,
          reasonCategory: movement.reason,
          notes: movement.notes,
          timestamp: movement.timestamp,
          userId: movement.user_id,
          userName: movement.user_name || 'System',
          orderId: movement.order_id
        };
      });

      return { data: formattedMovements, count: count || 0 };
    } catch (err) {
      console.error('Error fetching stock movements:', err);
      return { data: [], count: 0 };
    }
  }

  /**
   * Get stock movement summary (aggregated data)
   */
  async getMovementSummary(productId: string, period: 'week' | 'month' | 'quarter' | 'year' = 'month') {
    // Calculate date ranges based on period
    const now = new Date();
    let fromDate: string;
    
    switch (period) {
      case 'week':
        const lastWeek = new Date(now);
        lastWeek.setDate(lastWeek.getDate() - 7);
        fromDate = lastWeek.toISOString();
        break;
      case 'month':
        const lastMonth = new Date(now);
        lastMonth.setMonth(lastMonth.getMonth() - 1);
        fromDate = lastMonth.toISOString();
        break;
      case 'quarter':
        const lastQuarter = new Date(now);
        lastQuarter.setMonth(lastQuarter.getMonth() - 3);
        fromDate = lastQuarter.toISOString();
        break;
      case 'year':
        const lastYear = new Date(now);
        lastYear.setFullYear(lastYear.getFullYear() - 1);
        fromDate = lastYear.toISOString();
        break;
      default:
        const defaultMonth = new Date(now);
        defaultMonth.setMonth(defaultMonth.getMonth() - 1);
        fromDate = defaultMonth.toISOString();
    }

    // Get all movements for the product within the time range
    const { data, error } = await supabase
      .from('inventory_movements')
      .select('*')
      .eq('product_id', productId)
      .gte('timestamp', fromDate)
      .order('timestamp');

    if (error) throw error;

    // Process movement data to calculate summaries
    let totalIncrease = 0;
    let totalDecrease = 0;
    let totalSales = 0;
    let totalDamaged = 0;
    let totalAdjustments = 0;
    let totalCounts = 0;
    let movementsByDay = new Map<string, number>();

    data?.forEach(movement => {
      // Calculate totals by movement type
      if (movement.type === 'increase') {
        totalIncrease += movement.quantity;
      } else if (movement.type === 'decrease') {
        totalDecrease += movement.quantity;
      }

      // Calculate totals by reason category
      if (movement.reason_category === 'sales') {
        totalSales += movement.quantity;
      } else if (movement.reason_category === 'damaged_goods') {
        totalDamaged += movement.quantity;
      } else if (movement.reason_category === 'adjustment') {
        totalAdjustments += movement.quantity;
      } else if (movement.reason_category === 'inventory_count') {
        totalCounts += movement.quantity;
      }

      // Aggregate movements by day
      const day = movement.timestamp.split('T')[0];
      const currentDay = movementsByDay.get(day) || 0;
      const change = movement.type === 'increase' ? movement.quantity : -movement.quantity;
      movementsByDay.set(day, currentDay + change);
    });

    // Format the daily movements for charts
    const dailyMovements = Array.from(movementsByDay.entries()).map(([date, quantity]) => ({
      date,
      quantity
    }));

    return {
      totalMovements: data?.length || 0,
      totalIncrease,
      totalDecrease,
      totalSales,
      totalDamaged,
      totalAdjustments,
      totalCounts,
      netChange: totalIncrease - totalDecrease,
      dailyMovements: dailyMovements.sort((a, b) => a.date.localeCompare(b.date))
    };
  }
}

// Export as singleton
export default new InventoryHistoryService(); 