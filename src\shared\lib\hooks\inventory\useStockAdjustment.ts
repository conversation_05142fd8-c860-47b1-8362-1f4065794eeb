import { useState, useCallback } from 'react';
import { InventoryMovement, InventoryMovementType, InventoryView, InventoryReasonCategory } from '@/types';
import { useAuth } from '@/app/providers/AuthContext';
import inventoryStockService from '@/shared/lib/services/inventory/inventory-stock-service';
import { useBulkStockAdjustment } from './useBulkStockAdjustment';
import type { StockUpdateFormData } from '@/features/inventory-features/stock-update';

interface UseStockAdjustmentProps {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
  trackHistory?: boolean; // Whether to track adjustment history
  onOptimisticUpdate?: (productId: string, adjustmentType: InventoryMovementType, quantity: number) => void; // For optimistic updates
}

interface UseStockAdjustmentReturn {
  isModalOpen: boolean;
  isUpdating: boolean;
  selectedProductId: string | null;
  selectedProductName: string | null;
  currentStock: number | null;
  error: Error | null;
  movements: InventoryMovement[]; // Add movements list
  openModal: (productId: string, productName: string, currentStock: number) => void;
  closeModal: () => void;
  updateStock: (data: StockUpdateFormData) => Promise<void>;
  fetchMovements: (productId: string) => Promise<void>; // Add method to fetch movements
}

/**
 * Hook for managing stock adjustment functionality for a single product
 * This hook now uses useBulkStockAdjustment internally to reduce duplication
 */
export function useStockAdjustment({
  onSuccess,
  onError,
  trackHistory = true,
  onOptimisticUpdate
}: UseStockAdjustmentProps = {}): UseStockAdjustmentReturn {
  // State for tracking history and product details
  const [movements, setMovements] = useState<InventoryMovement[]>([]);
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);
  const [selectedProductName, setSelectedProductName] = useState<string | null>(null);
  const [currentStock, setCurrentStock] = useState<number | null>(null);
  
  // Get current user from auth context
  const { user, profile } = useAuth();

  // Use the bulk stock adjustment hook for the actual modal and stock updates
  const bulkStockAdjustment = useBulkStockAdjustment({
    onSuccess,
    onError,
    onOptimisticUpdate
  });

  /**
   * Open the stock adjustment modal for a specific product
   */
  const openModal = useCallback((productId: string, productName: string, stock: number) => {
    setSelectedProductId(productId);
    setSelectedProductName(productName);
    setCurrentStock(stock);
    
    // Create a product object in the format expected by useBulkStockAdjustment
    const product: InventoryView = {
      inventory_id: `inv-${productId}`, // Generate a placeholder inventory ID
      product_id: productId,
      name: productName,
      sku: '', // Placeholder, not used in the bulk update process
      product_type: null, // Optional field in InventoryView
      status: 'active', // Using 'active' as default
      current_stock: stock,
      available_stock: stock, // Set to same as current_stock
      reserved_stock: 0, // Default to 0
      minimum_threshold: null, // Optional field
      needs_reorder: null // Optional field
    };
    
    // Use the bulk stock adjustment hook to open the modal
    bulkStockAdjustment.openModal([product]);
    
    // Fetch movement history if tracking is enabled
    if (trackHistory) {
      fetchMovements(productId).catch(console.error);
    }
  }, [bulkStockAdjustment, trackHistory]);

  /**
   * Close the stock adjustment modal
   */
  const closeModal = useCallback(() => {
    bulkStockAdjustment.closeModal();
    
    // Clear the selected product after the modal animation
    setTimeout(() => {
      setSelectedProductId(null);
      setSelectedProductName(null);
      setCurrentStock(null);
    }, 300);
  }, [bulkStockAdjustment]);

  /**
   * Fetch movement history for a product
   */
  const fetchMovements = useCallback(async (productId: string) => {
    try {
      if (!productId) return;
      
      // Use the inventory service to get movements instead of directly calling API
      const movementData = await inventoryStockService.getProductMovements(productId, {
        limit: 10,
        sortBy: 'timestamp',
        sortDirection: 'desc'
      });
      
      setMovements(movementData);
    } catch (err) {
      console.error('Error fetching movements:', err);
      // Don't set error state here as it's not critical
    }
  }, []);

  /**
   * Handle stock update submission using the format expected by the StockUpdateModal
   * This adapts the single-product API to the bulk API
   */
  const updateStock = useCallback(async (data: StockUpdateFormData) => {
    // Get user information for tracking
    const userId = user?.id || 'unknown';
    const userName = profile?.full_name || user?.email || 'Unknown User';

    // Add optimistic movement update for history tracking
    let optimisticMovementId: string | null = null;
    
    if (currentStock !== null) {
      const newStock = data.adjustmentType === 'increase' 
        ? currentStock + data.quantity 
        : data.adjustmentType === 'decrease' 
          ? currentStock - data.quantity 
          : data.quantity;
          
      const optimisticMovement: InventoryMovement = {
        id: `temp-${Date.now()}`,
        inventory_id: 'inv-1',
        product_id: data.productId,
        type: data.adjustmentType,
        quantity: data.quantity,
        previous_stock: currentStock,
        new_stock: newStock,
        reason: data.reason,
        reason_category: data.reasonCategory as InventoryReasonCategory,
        notes: data.notes || null,
        order_id: null,
        user_id: userId,
        user_name: userName,
        timestamp: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: null
      };

      optimisticMovementId = optimisticMovement.id;
      setMovements(prev => [optimisticMovement, ...prev]);
    }

    try {
      // Convert single-product format to bulk format
      await bulkStockAdjustment.updateStock({
        productIds: [data.productId],
        adjustmentType: data.adjustmentType,
        quantity: data.quantity,
        reason: data.reason,
        reasonCategory: data.reasonCategory as InventoryReasonCategory,
        notes: data.notes
      });
    } catch (err) {
      // If there's an error, we should roll back the optimistic update
      if (optimisticMovementId) {
        setMovements(prev => prev.filter(movement => movement.id !== optimisticMovementId));
      }
      
      // Re-throw the error for the component to handle
      throw err;
    }
  }, [bulkStockAdjustment, currentStock, user, profile]);

  return {
    // Pass through properties from the bulk hook
    isModalOpen: bulkStockAdjustment.isModalOpen,
    isUpdating: bulkStockAdjustment.isUpdating,
    error: bulkStockAdjustment.error,
    
    // Keep the original properties for product details
    selectedProductId,
    selectedProductName,
    currentStock,
    movements,
    
    // Methods
    openModal,
    closeModal,
    updateStock,
    fetchMovements
  };
} 