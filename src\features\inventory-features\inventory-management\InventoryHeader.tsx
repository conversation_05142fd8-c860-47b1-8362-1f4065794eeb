import React, { memo, useState } from 'react';
import { cn } from '@/shared/lib/utils/core/cn';
import Dropdown from '@/shared/ui/input/Dropdown';
import type { DropdownOption } from '@/types';

interface InventoryHeaderProps {
  productCount: number;
  searchTerm: string;
  onSearchChange: (term: string) => void;
  onNewProduct: () => void;
  onOpenFilterModal: () => void;
  activeFilterCount: number;
  onStockStatusFilterChange?: (status: string) => void;
  stockStatusFilter: string;
}

// Custom comparison function for the memoized component
const areEqual = (prevProps: InventoryHeaderProps, nextProps: InventoryHeaderProps) => {
  // Compare primitive values
  if (
    prevProps.productCount !== nextProps.productCount ||
    prevProps.searchTerm !== nextProps.searchTerm ||
    prevProps.activeFilterCount !== nextProps.activeFilterCount ||
    prevProps.stockStatusFilter !== nextProps.stockStatusFilter
  ) {
    return false;
  }

  // Compare function references
  if (
    prevProps.onSearchChange !== nextProps.onSearchChange ||
    prevProps.onNewProduct !== nextProps.onNewProduct ||
    prevProps.onOpenFilterModal !== nextProps.onOpenFilterModal ||
    prevProps.onStockStatusFilterChange !== nextProps.onStockStatusFilterChange
  ) {
    return false;
  }

  // Props are equal if we got here
  return true;
};

const InventoryHeader: React.FC<InventoryHeaderProps> = ({
  productCount,
  searchTerm,
  onSearchChange,
  onNewProduct,
  onOpenFilterModal,
  activeFilterCount,
  onStockStatusFilterChange,
  stockStatusFilter
}) => {
  // Stock status filter options
  const stockStatusOptions = [
    { value: 'all', label: 'All Stock Levels' },
    { value: 'out_of_stock', label: 'Out of Stock' },
    { value: 'low_stock', label: 'Low Stock' },
    { value: 'needs_reorder', label: 'Needs Reorder' },
    { value: 'good_stock', label: 'Good Stock Level' }
  ];

  // Handle search input change - directly update the parent state
  // The debouncing is now handled in the useInventoryFilters hook
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSearchChange(e.target.value);
  };

  // Clear search input
  const handleClearSearch = () => {
    onSearchChange('');
  };

  // Handle stock status filter change
  const handleStockStatusChange = (value: string) => {
    if (onStockStatusFilterChange) {
      onStockStatusFilterChange(value);
    }
  };

  return (
    <div className="py-4 flex flex-wrap items-center justify-between">
      {/* Product count and actions */}
      <div className="flex items-center mb-4 md:mb-0">
        <h2 className="text-lg font-medium text-gray-700">
          Total Products:
          <span className="ml-2 px-2 py-1 text-sm bg-gray-100 text-gray-700 rounded">
            {productCount}
          </span>
        </h2>
      </div>

      {/* Search and filters */}
      <div className="flex flex-wrap items-center space-x-2 space-y-2 md:space-y-0 w-full md:w-auto">
        <div className="relative w-full md:w-64">
          {/* Search icon */}
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          
          {/* Search input - using searchTerm directly now */}
          <input 
            type="text" 
            placeholder="Search products..." 
            value={searchTerm} 
            onChange={handleSearchInputChange}
            className="pl-10 pr-10 py-2 w-full border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
          />

          {/* Clear button */}
          {searchTerm && (
            <button
              type="button"
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
              onClick={handleClearSearch}
            >
              <svg className="h-5 w-5 text-gray-400 hover:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>

        {/* Stock status filter dropdown */}
        <div className="relative w-full md:w-auto">
          <Dropdown
            options={stockStatusOptions}
            selectedOption={stockStatusOptions.find(option => option.value === stockStatusFilter)}
            onSelect={(option: DropdownOption) => handleStockStatusChange(option.value)}
            placeholder="Stock Status"
            className="w-full md:w-48"
          />
        </div>

        {/* Filter button */}
        <button 
          onClick={onOpenFilterModal} 
          className={cn(
            "px-4 py-2 border rounded-md text-sm font-medium shadow-sm flex items-center",
            activeFilterCount > 0
              ? "bg-blue-50 text-blue-700 border-blue-300 hover:bg-blue-100"
              : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
          )}
        >
          {/* Filter icon */}
          <svg className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
          </svg>
          Filters
          {activeFilterCount > 0 && (
            <span className="ml-1.5 px-2 py-0.5 bg-blue-200 text-blue-800 rounded-full text-xs font-semibold">
              {activeFilterCount}
            </span>
          )}
        </button>
      </div>
    </div>
  );
};

// Export memoized component with custom comparison function
export default memo(InventoryHeader, areEqual);