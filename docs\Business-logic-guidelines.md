# Business Logic Architecture Guidelines

## Core Architecture Principle

The application strictly follows a layered architecture pattern to ensure separation of concerns and maintainability:

```
UI Components → Hooks → Services → API
```

This document outlines the responsibilities, best practices, and examples for each layer.

## 1. Layer Responsibilities

### 1.1 UI Components (`/features/*`, `/ui/*`)

**Purpose**: Present information to users and capture user interactions.

**Responsibilities**:
- Render UI elements and handle layout
- Capture user input events
- Manage local UI state (form values, visibility toggles)
- Call hooks to perform business operations
- Handle UI-specific side effects (animations, focus management)

**Examples**: `StockAdjustmentModal`, `OrdersTable`, `ProductDetailPanel`

### 1.2 Hooks (`/shared/lib/hooks/*`)

**Purpose**: Prepare data for UI consumption and coordinate state management.

**Responsibilities**:
- Provide UI-ready data structures
- Maintain component-related state
- Call appropriate services for data operations
- Handle loading/error states for UI components
- Transform service responses into UI-friendly formats
- Coordinate complex state interactions between components

**Examples**: `useStockAdjustment`, `useOrderData`, `useProductForm`

### 1.3 Services (`/shared/lib/services/*`)

**Purpose**: Implement business logic and domain rules.

**Responsibilities**:
- Execute core business logic and validation rules
- Coordinate complex operations involving multiple API calls
- Transform data between API and domain formats
- Handle service-level caching strategies
- Implement transactional behaviors
- Provide domain-specific utility functions

**Examples**: `inventoryStockService`, `orderService`, `userService`

### 1.4 API Layer (`/shared/api/*`)

**Purpose**: Direct communication with external systems.

**Responsibilities**:
- Make HTTP requests to API endpoints
- Handle API-specific error handling
- Manage authentication headers
- Format requests according to API specifications
- Parse API responses into consistent formats

**Examples**: `inventoryAPI`, `ordersAPI`, `usersAPI`

## 2. Code Examples

### 2.1 UI Component Example

```tsx
// src/features/inventory-features/stock-update/StockAdjustmentModal.tsx
const StockAdjustmentModal: React.FC<StockAdjustmentModalProps> = ({
  isOpen,
  onClose,
  products,
  onSuccess,
  onError,
}) => {
  // Use hooks for state management
  const stockAdjustment = useStockAdjustment({
    onSuccess,
    onError,
  });
  
  // Local UI state
  const [adjustmentType, setAdjustmentType] = React.useState<'increase' | 'decrease' | 'set'>('increase');
  const [quantity, setQuantity] = React.useState<string>('');
  
  // Handle form submission - passes data to hook
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;
    
    const parsedQuantity = parseFloat(quantity);
    
    try {
      await stockAdjustment.updateStock({
        productId: product.product_id || '',
        adjustmentType,
        quantity: parsedQuantity,
        reason,
        reasonCategory: reasonCategory as InventoryReasonCategory,
        notes: notes.trim() || undefined,
      });
      
      handleClose();
    } catch (err) {
      console.error('Error submitting form:', err);
    }
  };
  
  // UI rendering...
}
```

### 2.2 Hook Example

```tsx
// src/shared/lib/hooks/inventory/useStockAdjustment.ts
export function useStockAdjustment({
  onSuccess,
  onError,
}: UseStockAdjustmentProps = {}): UseStockAdjustmentReturn {
  // State management
  const [movements, setMovements] = useState<InventoryMovement[]>([]);
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  // Calls service method, doesn't directly use API
  const fetchMovements = useCallback(async (productId: string) => {
    try {
      const movementData = await inventoryStockService.getProductMovements(productId, {
        limit: 10,
        sortBy: 'timestamp',
        sortDirection: 'desc'
      });
      
      setMovements(movementData);
    } catch (err) {
      console.error('Error fetching movements:', err);
    }
  }, []);

  // Updates stock using the service layer
  const updateStock = useCallback(async (data: StockUpdateFormData) => {
    setIsUpdating(true);
    setError(null);
    
    try {
      // Call service methods, not API directly
      await inventoryStockService.adjustStock(data.productId, {
        quantity: data.quantity,
        reason: data.reason,
        reason_category: data.reasonCategory,
        type: data.adjustmentType,
        notes: data.notes
      });
      
      if (onSuccess) onSuccess();
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to update stock');
      setError(error);
      if (onError) onError(error);
    } finally {
      setIsUpdating(false);
    }
  }, [onSuccess, onError]);
  
  // Return values and methods for UI components
  return {
    isUpdating,
    error,
    movements,
    updateStock,
    fetchMovements
  };
}
```

### 2.3 Service Example

```typescript
// src/shared/lib/services/inventory/inventory-stock-service.ts
export class InventoryStockService {
  // Business logic and validation
  private validateStockAdjustment(type: AdjustmentType, quantity: number) {
    if (type === 'set' && quantity < 0) {
      throw new Error('Cannot set stock to a negative value');
    }
  }

  // Implementation of business logic
  private calculateNewStock(currentStock: number, type: AdjustmentType, quantity: number): number {
    switch (type) {
      case 'increase':
        return currentStock + quantity;
      case 'decrease':
        const newStock = currentStock - quantity;
        if (newStock < 0) {
          throw new Error('Stock level cannot be negative after decrease');
        }
        return newStock;
      case 'set':
        return quantity;
      default:
        throw new Error('Invalid adjustment type');
    }
  }

  // Complex business operation
  async adjustStock(productId: string, adjustment: StockAdjustmentParams) {
    // Validate business rules
    this.validateStockAdjustment(adjustment.type, adjustment.quantity);
    
    // Get current inventory data
    const { data: inventoryData, error: inventoryError } = await supabase
      .from('inventory')
      .select('*')
      .eq('product_id', productId)
      .single();

    if (inventoryError) throw inventoryError;

    // Apply business logic
    const currentStock = inventoryData.current_stock;
    const newStock = this.calculateNewStock(currentStock, adjustment.type, adjustment.quantity);

    // Record transaction history
    await this.recordStockMovement({
      inventoryId: inventoryData.id,
      productId,
      type: adjustment.type,
      quantity: adjustment.quantity,
      previousStock: currentStock,
      newStock,
      reason: adjustment.reason,
      reasonCategory: adjustment.reason_category,
      notes: adjustment.notes
    });

    // Update inventory record
    const { error: updateError } = await supabase
      .from('inventory')
      .update({
        current_stock: newStock,
        updated_at: new Date().toISOString()
      })
      .eq('product_id', productId);

    if (updateError) throw updateError;
    
    return { productId, newStock };
  }
  
  // Fetching related data
  async getProductMovements(productId: string, options = { limit: 10, sortBy: 'timestamp', sortDirection: 'desc' as 'asc' | 'desc' }): Promise<InventoryMovement[]> {
    try {
      const { data, error } = await supabase
        .from('inventory_movements')
        .select('*')
        .eq('product_id', productId)
        .order(options.sortBy, { ascending: options.sortDirection === 'asc' })
        .limit(options.limit);

      if (error) throw error;
      return data as InventoryMovement[];
    } catch (err) {
      console.error('Error fetching product movements:', err);
      return [];
    }
  }
}

export default new InventoryStockService();
```

### 2.4 API Layer Example

```typescript
// src/shared/api/inventory/inventoryAPI.ts
export const inventoryApi = {
  async getInventoryItems(params: InventoryQueryParams): Promise<ApiResponse<InventoryView[]>> {
    try {
      const { data, error } = await supabase
        .from('inventory_view')
        .select('*')
        .order(params.sortBy || 'name', { 
          ascending: params.sortDirection !== 'desc' 
        })
        .range(
          (params.page - 1) * params.limit,
          params.page * params.limit - 1
        );

      if (error) throw error;
      return { success: true, data: data || [] };
    } catch (err) {
      return { 
        success: false, 
        message: err instanceof Error ? err.message : 'Failed to fetch inventory',
        data: []
      };
    }
  }
};
```

## 3. Flow Diagram

```
┌─────────────────┐     ┌────────────────┐     ┌────────────────┐     ┌────────────────┐
│  UI Component   │     │      Hook      │     │     Service    │     │   API Layer    │
│                 │     │                │     │                │     │                │
│  - Rendering    │     │ - State mgmt   │     │ - Business     │     │ - API calls   │
│  - User events  │     │ - Data prep    │     │   logic        │     │ - Data format │
│  - Local state  │─────▶ - Coordinates  │─────▶ - Validation   │─────▶ - Error      │
│  - UI feedback  │     │   operations   │     │ - Domain       │     │   handling    │
│                 │     │                │     │   rules        │     │                │
└─────────────────┘     └────────────────┘     └────────────────┘     └────────────────┘
```

## 4. Common Anti-Patterns

### 4.1 UI Components Should Not:
- ❌ Make direct API calls
- ❌ Contain complex business logic
- ❌ Process raw API data
- ❌ Implement domain validation rules
- ❌ Cache data between components

**Example Anti-Pattern**:
```tsx
// BAD - UI Component making direct API calls
const ProductTable = () => {
  const [products, setProducts] = useState<Product[]>([]);
  
  useEffect(() => {
    // Direct API call in component - WRONG
    const fetchProducts = async () => {
      const { data, error } = await supabase.from('products').select('*');
      if (error) console.error(error);
      else setProducts(data);
    };
    
    fetchProducts();
  }, []);
}
```

### 4.2 Hooks Should Not:
- ❌ Make direct API calls
- ❌ Implement core business logic
- ❌ Contain database-specific code
- ❌ Manage global application state
- ❌ Handle authentication logic

**Example Anti-Pattern**:
```tsx
// BAD - Hook making direct API calls
function useProducts() {
  const [products, setProducts] = useState<Product[]>([]);
  
  const fetchProducts = useCallback(async () => {
    // Direct API call in hook - WRONG
    const { data, error } = await supabase.from('products').select('*');
    if (error) console.error(error);
    else setProducts(data);
  }, []);
  
  return { products, fetchProducts };
}
```

### 4.3 Services Should Not:
- ❌ Manage UI state
- ❌ Contain UI rendering code
- ❌ Reference React hooks or components
- ❌ Handle UI events directly
- ❌ Mix business logic with UI concerns

**Example Anti-Pattern**:
```typescript
// BAD - Service handling UI state
class ProductService {
  // Service managing UI state - WRONG
  loadingState = false;
  
  async getProducts() {
    this.loadingState = true; // UI state in service - WRONG
    const { data } = await api.get('/products');
    this.loadingState = false;
    return data;
  }
}
```

### 4.4 API Layer Should Not:
- ❌ Contain business logic
- ❌ Handle UI state
- ❌ Implement domain validation rules
- ❌ Manage application workflow
- ❌ Hold application state

**Example Anti-Pattern**:
```typescript
// BAD - API layer with business logic
export const productApi = {
  async createProduct(product) {
    // Business validation in API layer - WRONG
    if (product.price <= 0) {
      throw new Error('Product price must be greater than zero');
    }
    
    // More business logic - WRONG
    const taxRate = 0.08;
    product.taxAmount = product.price * taxRate;
    
    return api.post('/products', product);
  }
};
```

## 5. Testing Responsibilities

### 5.1 UI Component Tests
- Test rendering and layout
- Test user interactions
- Test conditional rendering
- Mock hooks, not services or API

### 5.2 Hook Tests
- Test state changes
- Test service method calls
- Test transformation of data
- Mock services, not API

### 5.3 Service Tests
- Test business logic and rules
- Test error handling
- Test data transformations
- Mock API calls

### 5.4 API Tests
- Test request formatting
- Test response parsing
- Test error handling
- Mock HTTP requests

## 6. Best Practices by Layer

### 6.1 UI Component Best Practices
- Use hooks for state management and data fetching
- Keep components focused on rendering and interaction
- Extract complex UI logic to custom hooks
- Use memoization for expensive renders
- Extract reusable UI elements to shared components

### 6.2 Hook Best Practices
- Return consistent data structures
- Handle loading and error states
- Memoize callbacks and derived values
- Use services for business operations
- Keep hooks focused on a single concern

```tsx
// GOOD - Hook using service layer
function useProductInventory(productId: string) {
  const [inventory, setInventory] = useState<Inventory | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  const loadInventory = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Using service instead of direct API call
      const data = await inventoryService.getProductInventory(productId);
      setInventory(data);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to load inventory'));
    } finally {
      setIsLoading(false);
    }
  }, [productId]);
  
  useEffect(() => {
    loadInventory();
  }, [loadInventory]);
  
  return { inventory, isLoading, error, refresh: loadInventory };
}
```

### 6.3 Service Best Practices
- Implement domain validation logic
- Handle complex business operations
- Make services stateless when possible
- Document business rules in the code
- Use dependency injection for external dependencies

```typescript
// GOOD - Service implementing business logic
class OrderService {
  constructor(
    private userService: UserService,
    private inventoryService: InventoryService,
    private paymentService: PaymentService
  ) {}
  
  async placeOrder(orderData: OrderData): Promise<Order> {
    // Validate business rules
    this.validateOrder(orderData);
    
    // Check inventory
    const hasStock = await this.inventoryService.checkStock(
      orderData.items.map(item => ({
        productId: item.productId,
        quantity: item.quantity
      }))
    );
    
    if (!hasStock) {
      throw new Error('Some items are out of stock');
    }
    
    // Process payment
    const paymentResult = await this.paymentService.processPayment({
      amount: this.calculateTotal(orderData),
      paymentMethod: orderData.paymentMethod
    });
    
    if (!paymentResult.success) {
      throw new Error(`Payment failed: ${paymentResult.message}`);
    }
    
    // Create order record
    const order = await this.createOrderRecord({
      ...orderData,
      paymentId: paymentResult.paymentId,
      status: 'confirmed'
    });
    
    // Update inventory
    await this.inventoryService.reduceStock(
      orderData.items.map(item => ({
        productId: item.productId,
        quantity: item.quantity
      }))
    );
    
    return order;
  }
  
  private validateOrder(orderData: OrderData): void {
    if (!orderData.items || orderData.items.length === 0) {
      throw new Error('Order must contain at least one item');
    }
    
    if (!orderData.shippingAddress) {
      throw new Error('Shipping address is required');
    }
    
    // More validation rules...
  }
}
```

### 6.4 API Layer Best Practices
- Provide consistent response structures
- Handle network errors gracefully
- Implement request retries for transient failures
- Support cancellation for long-running requests
- Log API errors with appropriate detail

```typescript
// GOOD - API layer with proper error handling and consistent response
export const productApi = {
  async getProducts(params?: ProductQueryParams): Promise<ApiResponse<Product[]>> {
    try {
      const response = await axios.get('/products', { params });
      return { 
        success: true, 
        data: response.data,
        pagination: {
          page: response.headers['x-page'],
          pageSize: response.headers['x-page-size'],
          total: response.headers['x-total']
        }
      };
    } catch (error) {
      // Log error for monitoring
      logger.error('Failed to fetch products', error);
      
      // Return consistent error response
      return { 
        success: false, 
        message: error.response?.data?.message || 'Failed to fetch products',
        data: [],
        code: error.response?.status || 'UNKNOWN'
      };
    }
  }
};
```

## 7. When to Break the Rules

While the architectural pattern should be followed as a rule, there are some legitimate exceptions:

### 7.1 Performance Optimizations
In rare cases where performance is critical, some steps may be skipped or combined. These exceptions should be:
- Clearly documented
- Proven with benchmarks
- Limited in scope
- Reviewed by the team

### 7.2 Simple CRUD Operations
For very simple CRUD operations with minimal logic, the service layer might be thin, but should still exist for consistency and future extension.

### 7.3 Specialized Components
Some specialized UI components (like charts, editors) might need direct access to data or APIs. These exceptions should be:
- Isolated in specific files
- Not part of the main application flow
- Documented with clear reasoning

## 8. Ensuring Architectural Compliance

To ensure the architecture is followed:

### 8.1 Code Reviews
- Check for proper layer separation
- Verify business logic is in services
- Ensure UI components don't access APIs directly
- Confirm hooks use services, not APIs

### 8.2 Linting Rules
- Set up ESLint rules to enforce import restrictions
- Prevent UI components from importing API modules
- Prevent hooks from importing API modules
- Prevent services from importing React

### 8.3 Documentation
- Document architectural decisions
- Update this guide as patterns evolve
- Reference this document in code reviews

## 9. Real-World Examples

### Example 1: Stock Adjustment Workflow

**UI Component (`StockAdjustmentModal.tsx`):**
- Renders form for stock adjustment
- Captures user inputs
- Uses `useStockAdjustment` hook for state and operations

**Hook (`useStockAdjustment.ts`):**
- Maintains loading/error state
- Calls `inventoryStockService.adjustStock()`
- Formats data for UI consumption

**Service (`inventory-stock-service.ts`):**
- Implements validation logic
- Calculates new stock values
- Records stock movement history
- Updates inventory records

**API (Supabase client):**
- Executes database operations
- Handles database-specific errors
- Returns raw data for service to process

### Example 2: Order Processing Workflow

**UI Component (`OrderForm.tsx`):**
- Renders order form and product selection
- Captures shipping/payment details
- Uses `useOrderCreation` hook

**Hook (`useOrderCreation.ts`):**
- Maintains form state and validation
- Calls `orderService.createOrder()`
- Handles success/error UI states

**Service (`order-service.ts`):**
- Validates order data
- Checks inventory availability
- Processes payment
- Creates order record
- Updates inventory

**API Layer:**
- Makes API calls to order endpoints
- Handles API-specific error formats
- Returns structured responses

## Summary

The layered architecture pattern (`UI Components → Hooks → Services → API`) ensures:

1. **Separation of concerns** - Each layer has a clear, specific responsibility
2. **Testability** - Components and business logic can be tested independently
3. **Maintainability** - Changes in one layer have minimal impact on others
4. **Reusability** - Services can be used by multiple hooks, hooks by multiple components

Following these guidelines ensures the codebase remains organized, maintainable, and scalable as it grows. 