// Helper functions for date calculations in delivery tracking

/**
 * Calculate the number of days between two dates
 */
export const calculateDaysBetween = (startDate: string | Date, endDate: string | Date): number => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  const timeDifference = end.getTime() - start.getTime();
  return Math.floor(timeDifference / (1000 * 60 * 60 * 24));
};

/**
 * Calculate days since a given date
 */
export const calculateDaysSince = (date: string | Date): number => {
  const targetDate = new Date(date);
  const now = new Date();
  
  return calculateDaysBetween(targetDate, now);
};

/**
 * Calculate days until a future date
 */
export const calculateDaysUntil = (date: string | Date): number => {
  const targetDate = new Date(date);
  const now = new Date();
  
  const days = calculateDaysBetween(now, targetDate);
  return days > 0 ? days : 0; // Don't return negative values for past dates
};

/**
 * Format a date string for display
 */
export const formatDisplayDate = (date: string | Date): string => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

/**
 * Format a date and time string for display
 */
export const formatDisplayDateTime = (date: string | Date): string => {
  return new Date(date).toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });
};

/**
 * Check if a date is overdue (past the expected delivery date)
 */
export const isOverdue = (expectedDate: string | Date): boolean => {
  const expected = new Date(expectedDate);
  const now = new Date();
  
  return now > expected;
};

/**
 * Check if a delivery is considered delayed (within 1 day of expected delivery but not delivered)
 */
export const isNearDeadline = (expectedDate: string | Date, withinDays: number = 1): boolean => {
  const daysUntil = calculateDaysUntil(expectedDate);
  return daysUntil <= withinDays && daysUntil >= 0;
}; 