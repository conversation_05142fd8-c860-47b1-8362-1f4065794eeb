{"importMap": "./import_map.json", "tasks": {"start": "deno run --allow-net --allow-env --allow-read index.ts", "test": "deno test --allow-net --allow-env --allow-read"}, "compilerOptions": {"allowJs": true, "lib": ["deno.window"], "strict": true}, "lint": {"files": {"include": ["**/*.ts"]}, "rules": {"tags": ["recommended"]}}, "fmt": {"files": {"include": ["**/*.ts"]}, "options": {"useTabs": false, "lineWidth": 100, "indentWidth": 2, "singleQuote": true, "proseWrap": "preserve"}}}