// Export types from inventory service only to avoid conflicts
export * from './inventory-service';

// Export service instances 
// IMPORTANT: Do NOT export inventory-stock-service from here to avoid circular dependencies
// Import it directly in components/hooks that need it:
// import inventoryStockService from '@/shared/lib/services/inventory/inventory-stock-service';

// Export other services
import inventoryService from './inventory-service';
import inventoryAlertsService from './inventory-alerts-service';
import inventoryHistoryService from './inventory-history-service';
import productService from './product-service';

// Export other service instances
export {
  inventoryService,
  inventoryAlertsService,
  inventoryHistoryService,
  productService
}; 

// Re-export types from inventory-stock-service for convenience
export type { 
  AdjustmentType, 
  StockAdjustmentParams, 
  ThresholdUpdateParams, 
  BulkStockAdjustmentParams 
} from './inventory-stock-service'; 