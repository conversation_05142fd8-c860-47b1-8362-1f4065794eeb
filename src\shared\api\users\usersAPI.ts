import type {
  UserProfile,
  UserRole
} from '../../../types';

// API Response type
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

// Users API specific types
interface UsersListParams {
  page?: number;
  limit?: number;
  search?: string;
  role?: UserRole;
  businessUnitId?: string;
  isActive?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface UserInviteData {
  email: string;
  fullName: string;
  role: UserRole;
  businessUnitId?: string;
}

// Users API Functions
export const usersApi = {
  // List users (master only)
  async list(params: UsersListParams = {}): Promise<ApiResponse<UserProfile[]>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, String(value));
      }
    });

    const response = await fetch(`/api/users?${queryParams}`);
    return response.json();
  },

  // Get current user profile
  async getMe(): Promise<ApiResponse<UserProfile>> {
    const response = await fetch('/api/users/me');
    return response.json();
  },

  // Get user by ID
  async getById(id: string): Promise<ApiResponse<UserProfile>> {
    const response = await fetch(`/api/users/${id}`);
    return response.json();
  },

  // Update user profile
  async update(id: string, data: Partial<UserProfile>): Promise<ApiResponse<UserProfile>> {
    const response = await fetch(`/api/users/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    return response.json();
  },

  // Update current user profile
  async updateMe(data: Partial<UserProfile>): Promise<ApiResponse<UserProfile>> {
    const response = await fetch('/api/users/me', {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    return response.json();
  },

  // Invite new user (master only)
  async invite(data: UserInviteData): Promise<ApiResponse<{ inviteId: string }>> {
    const response = await fetch('/api/users/invite', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    return response.json();
  },

  // Resend invitation (master only)
  async resendInvite(inviteId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`/api/users/invite/${inviteId}/resend`, {
      method: 'POST',
    });
    return response.json();
  },

  // Cancel invitation (master only)
  async cancelInvite(inviteId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`/api/users/invite/${inviteId}`, {
      method: 'DELETE',
    });
    return response.json();
  },

  // Deactivate user (master only)
  async deactivate(id: string): Promise<ApiResponse<UserProfile>> {
    const response = await fetch(`/api/users/${id}/deactivate`, {
      method: 'POST',
    });
    return response.json();
  },

  // Activate user (master only)
  async activate(id: string): Promise<ApiResponse<UserProfile>> {
    const response = await fetch(`/api/users/${id}/activate`, {
      method: 'POST',
    });
    return response.json();
  },

  // Change user role (master only)
  async changeRole(id: string, role: UserRole): Promise<ApiResponse<UserProfile>> {
    const response = await fetch(`/api/users/${id}/role`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ role }),
    });
    return response.json();
  },

  // Update business unit assignment
  async updateBusinessUnit(id: string, businessUnitId: string | null): Promise<ApiResponse<UserProfile>> {
    const response = await fetch(`/api/users/${id}/business-unit`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ businessUnitId }),
    });
    return response.json();
  },

  // Get pending invitations (master only)
  async getPendingInvites(): Promise<ApiResponse<Array<{
    id: string;
    email: string;
    fullName: string;
    role: UserRole;
    businessUnitId: string | null;
    invitedAt: string;
    expiresAt: string;
  }>>> {
    const response = await fetch('/api/users/invites/pending');
    return response.json();
  },

  // Get user activity log
  async getActivityLog(id: string, params?: {
    limit?: number;
    page?: number;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<ApiResponse<Array<{
    id: string;
    action: string;
    details: any;
    ipAddress: string | null;
    userAgent: string | null;
    timestamp: string;
  }>>> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, String(value));
        }
      });
    }

    const response = await fetch(`/api/users/${id}/activity?${queryParams}`);
    return response.json();
  },

  // Refresh user authentication
  async refreshAuth(): Promise<ApiResponse<UserProfile>> {
    const response = await fetch('/api/users/me/refresh', {
      method: 'POST',
    });
    return response.json();
  },

  // Update last login
  async updateLastLogin(): Promise<ApiResponse<void>> {
    const response = await fetch('/api/users/me/last-login', {
      method: 'PATCH',
    });
    return response.json();
  }
};

export default usersApi; 