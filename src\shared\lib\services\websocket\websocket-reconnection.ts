import { WebSocketConfig } from './websocket-config';

export class WebSocketReconnection {
  private config: WebSocketConfig;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private isDestroyed = false;
  private isCancelled = false;

  constructor(config: WebSocketConfig) {
    this.config = config;
  }

  /**
   * Schedules reconnection with exponential backoff and jitter
   */
  public scheduleReconnection(
    currentAttempts: number,
    updateAttemptsCallback: (attempts: number) => void,
    reconnectCallback: () => Promise<void>
  ): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    // Calculate backoff with jitter
    const newAttemptCount = currentAttempts + 1;
    updateAttemptsCallback(newAttemptCount);
    
    // Use exponential backoff with random jitter (between 75% and 100% of calculated delay)
    const calculatedDelay = Math.min(
      this.config.reconnectInterval * Math.pow(1.5, currentAttempts),
      this.config.maxReconnectInterval
    );
    
    const jitterFactor = 0.75 + Math.random() * 0.25;
    const finalDelay = calculatedDelay * jitterFactor;
    
    // Schedule the reconnection
    this.reconnectTimer = setTimeout(async () => {
      if (this.isDestroyed || this.isCancelled) {
        return;
      }
      
      try {
        await reconnectCallback();
      } catch (error) {
        // If reconnection attempt failed, schedule another one if we haven't reached the limit
        if (newAttemptCount < this.config.maxReconnectAttempts) {
          this.scheduleReconnection(
            newAttemptCount,
            updateAttemptsCallback,
            reconnectCallback
          );
        }
      }
    }, finalDelay);
  }

  /**
   * Cancels any pending reconnection
   */
  public cancelReconnection(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  /**
   * Checks if max reconnection attempts have been reached
   */
  public hasReachedMaxAttempts(reconnectAttempts: number): boolean {
    return reconnectAttempts >= this.config.maxReconnectAttempts;
  }

  /**
   * Destroys the reconnection manager
   */
  public destroy(): void {
    this.isDestroyed = true;
    this.cancelReconnection();
  }
} 