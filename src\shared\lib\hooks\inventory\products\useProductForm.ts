import { useState, useCallback, useEffect } from 'react';
import { Product, ProductStatusEnum, BusinessUnit } from '@/types';
import { notificationService } from '@/shared/lib/services/notification/notification-service';

// Import the product type categories mapping
export const PRODUCT_TYPE_CATEGORIES: Record<string, string[]> = {
  // 'Physical': ['Shrimp', 'Seafood', 'Meat', 'Produce', 'Dairy', 'Home Goods', 'Clothing', 'Furniture', 'Beauty'],
  // 'Digital': ['Software', 'E-books', 'Media', 'Digital Content'],
  // 'Service': ['Maintenance', 'Installation', 'Support', 'Subscription'],
  // 'Bundle': ['Gift Set', 'Package Deal', 'Combo Pack'],
  // 'Perishable': ['Frozen Food', 'Fresh Produce', 'Bakery', 'Dairy Products'],
  // 'Electronics': ['Computers', 'Mobile Devices', 'Appliances', 'Accessories'],
  // 'Clothing': ['Apparel', 'Footwear', 'Accessories', 'Specialty'],
  // 'Furniture': ['Indoor', 'Outdoor', 'Office', 'Storage'],
  // 'Food': ['Processed', 'Prepared', 'Gourmet', 'Specialty'],
  'Raw Material': ['Components', 'Ingredients', 'Building Materials','Shrimp']
};

// Define required fields for the product form
export const REQUIRED_FIELDS: (keyof ProductFormData)[] = [
  'name',
  'sku', 
  'product_type',
  'status',
  'unit_price'  // Making unit price required for the business
];

// Helper function to determine if a field is required
export const isFieldRequired = (field: keyof ProductFormData): boolean => {
  return REQUIRED_FIELDS.includes(field);
};

export interface ProductFormData {
  id?: string;
  name: string;
  sku: string;
  description: string;
  category: string;
  product_type: string;
  unit_cost: number | null;
  unit_price: number | null;
  weight: number | null;
  dimensions_length: number | null;
  dimensions_width: number | null;
  dimensions_height: number | null;
  status: ProductStatusEnum;
  business_unit_id: string | null;
  initial_stock?: number | null;
}

interface ProductFormErrors {
  name?: string;
  sku?: string;
  description?: string;
  category?: string;
  product_type?: string;
  unit_cost?: string;
  unit_price?: string;
  weight?: string;
  dimensions_length?: string;
  dimensions_width?: string;
  dimensions_height?: string;
  status?: string;
  business_unit_id?: string;
  initial_stock?: string;
}

interface UseProductFormProps {
  initialData?: Partial<ProductFormData>;
  onSuccess?: (product: Product) => void;
  onError?: (error: Error) => void;
}

interface UseProductFormReturn {
  formData: ProductFormData;
  errors: ProductFormErrors;
  isSubmitting: boolean;
  submitError: string | null;
  businessUnits: BusinessUnit[];
  isLoading: boolean;
  handleChange: (field: keyof ProductFormData, value: any) => void;
  handleSubmit: () => Promise<void>;
  resetForm: () => void;
  validateForm: (fieldToValidate?: keyof ProductFormData) => boolean;
}

const defaultFormData: ProductFormData = {
  name: '',
  sku: '',
  description: '',
  category: '',
  product_type: '',
  unit_cost: null,
  unit_price: null,
  weight: null,
  dimensions_length: null,
  dimensions_width: null,
  dimensions_height: null,
  status: 'active' as ProductStatusEnum,
  business_unit_id: null,
  initial_stock: null
};

/**
 * Hook for managing product form state, validation, and submission
 */
export function useProductForm({
  initialData = {},
  onSuccess,
  onError
}: UseProductFormProps = {}): UseProductFormReturn {
  // Form state
  const [formData, setFormData] = useState<ProductFormData>({
    ...defaultFormData,
    ...initialData
  });
  
  // Validation state
  const [errors, setErrors] = useState<ProductFormErrors>({});
  
  // Submission state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  
  // Business units for dropdown
  const [businessUnits, setBusinessUnits] = useState<BusinessUnit[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  // Fetch business units on mount
  useEffect(() => {
    const fetchBusinessUnits = async () => {
      setIsLoading(true);
      try {
        // In a real implementation, this would be an API call via service layer
        // const businessUnitData = await inventoryService.getBusinessUnits();
        // setBusinessUnits(businessUnitData);
        
        // Mock data
        setTimeout(() => {
          const mockBusinessUnits: BusinessUnit[] = [
            {
              id: 'bu-001',
              name: 'Shrimp Products Division',
              description: 'All shrimp-related products',
              is_active: true,
              created_at: new Date().toISOString(),
              updated_at: null
            },
            {
              id: 'bu-002',
              name: 'Dropship Operations',
              description: 'Third-party dropshipping products',
              is_active: true,
              created_at: new Date().toISOString(),
              updated_at: null
            },
            {
              id: 'bu-003',
              name: 'Direct Sales',
              description: 'Products sold directly to customers',
              is_active: true,
              created_at: new Date().toISOString(),
              updated_at: null
            }
          ];
          
          setBusinessUnits(mockBusinessUnits);
          setIsLoading(false);
        }, 500);
      } catch (err) {
        console.error('Error fetching business units:', err);
        setIsLoading(false);
      }
    };
    
    fetchBusinessUnits();
  }, []);
  
  // Validate form data - enhanced with field-specific validation
  const validateForm = useCallback((fieldToValidate?: keyof ProductFormData): boolean => {
    const newErrors: ProductFormErrors = { ...errors };
    
    // Helper function to validate a single field
    const validateField = (field: keyof ProductFormData) => {
      // Clear previous error for this field
      if (field in newErrors) {
        delete newErrors[field as keyof ProductFormErrors];
      }
      
      switch (field) {
        case 'name':
          if (!formData.name.trim()) {
            newErrors.name = 'Product name is required';
          } else if (formData.name.length < 3) {
            newErrors.name = 'Product name must be at least 3 characters';
          } else if (formData.name.length > 100) {
            newErrors.name = 'Product name cannot exceed 100 characters';
          }
          break;
          
        case 'sku':
          if (!formData.sku.trim()) {
            newErrors.sku = 'SKU is required';
          } else if (!/^[A-Za-z0-9-_]+$/.test(formData.sku)) {
            newErrors.sku = 'SKU can only contain letters, numbers, hyphens, and underscores';
          } else if (formData.sku.length > 50) {
            newErrors.sku = 'SKU cannot exceed 50 characters';
          }
          break;
          
        case 'product_type':
          if (!formData.product_type.trim()) {
            newErrors.product_type = 'Product type is required';
          } else if (!Object.keys(PRODUCT_TYPE_CATEGORIES).includes(formData.product_type)) {
            newErrors.product_type = 'Invalid product type selected';
          }
          break;
          
        case 'category':
          if (formData.category && formData.product_type) {
            const validCategories = PRODUCT_TYPE_CATEGORIES[formData.product_type] || [];
            if (!validCategories.includes(formData.category)) {
              newErrors.category = `Category must be valid for product type "${formData.product_type}"`;
            }
          }
          break;
          
        case 'status':
          if (!formData.status) {
            newErrors.status = 'Status is required';
          }
          break;
          
        case 'description':
          if (formData.description && formData.description.length > 1000) {
            newErrors.description = 'Description cannot exceed 1000 characters';
          }
          break;
          
        case 'unit_cost':
          if (formData.unit_cost !== null) {
            if (formData.unit_cost < 0) {
              newErrors.unit_cost = 'Unit cost cannot be negative';
            } else if (isNaN(formData.unit_cost)) {
              newErrors.unit_cost = 'Unit cost must be a valid number';
            }
          }
          break;
          
        case 'unit_price':
          if (formData.unit_price === null) {
            newErrors.unit_price = 'Unit price is required';
          } else if (formData.unit_price < 0) {
            newErrors.unit_price = 'Unit price cannot be negative';
          } else if (isNaN(formData.unit_price)) {
            newErrors.unit_price = 'Unit price must be a valid number';
          }
          break;
          
        case 'weight':
          if (formData.weight !== null) {
            if (formData.weight < 0) {
              newErrors.weight = 'Weight cannot be negative';
            } else if (isNaN(formData.weight)) {
              newErrors.weight = 'Weight must be a valid number';
            }
          }
          break;
          
        case 'dimensions_length':
        case 'dimensions_width':
        case 'dimensions_height':
          const dimension = formData[field];
          if (dimension !== null) {
            if (dimension < 0) {
              newErrors[field as keyof ProductFormErrors] = `${field.replace('dimensions_', '').charAt(0).toUpperCase() + field.replace('dimensions_', '').slice(1)} cannot be negative`;
            } else if (isNaN(dimension)) {
              newErrors[field as keyof ProductFormErrors] = `${field.replace('dimensions_', '').charAt(0).toUpperCase() + field.replace('dimensions_', '').slice(1)} must be a valid number`;
            }
          }
          break;
          
        case 'initial_stock':
          if (formData.initial_stock !== null && formData.initial_stock !== undefined) {
            if (formData.initial_stock < 0) {
              newErrors.initial_stock = 'Initial stock cannot be negative';
            } else if (isNaN(formData.initial_stock)) {
              newErrors.initial_stock = 'Initial stock must be a valid number';
            } else if (!Number.isInteger(formData.initial_stock)) {
              newErrors.initial_stock = 'Initial stock must be a whole number';
            }
          }
          break;
      }
    };
    
    // If a specific field is passed, only validate that field
    if (fieldToValidate) {
      validateField(fieldToValidate);
    } else {
      // Otherwise validate all fields
      (Object.keys(formData) as Array<keyof ProductFormData>).forEach(validateField);
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData, errors]);
  
  // Handle form field changes with real-time validation
  const handleChange = useCallback((field: keyof ProductFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Validate the field after a short delay for better UX
    setTimeout(() => {
      validateForm(field);
    }, 300);
  }, [validateForm]);
  
  // Handle form submission
  const handleSubmit = useCallback(async () => {
    // Validate all fields before submission
    if (!validateForm()) {
      // Scroll to the first error if validation fails
      const firstErrorField = document.querySelector('.border-red-300');
      if (firstErrorField) {
        firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      return;
    }
    
    setIsSubmitting(true);
    setSubmitError(null);
    
    try {
      // Prepare data for submission
      const productData = { ...formData };
      const initialStock = productData.initial_stock;
      delete productData.initial_stock;
      
      // In a real implementation, this would use the service layer
      // const savedProduct = await inventoryService.createOrUpdateProduct(productData);
      // const productId = savedProduct.id;
      
      // If initial stock is set, also update inventory using stock service
      // if (initialStock !== null && initialStock !== undefined && initialStock > 0) {
      //   await inventoryStockService.adjustStock(productId, {
      //     quantity: initialStock,
      //     type: 'set',
      //     reason: 'Initial setup',
      //     reason_category: 'initial_setup'
      //   });
      // }
      
      // For development, use a mock response with a delay
      await new Promise<void>((resolve) => {
        setTimeout(() => {
          console.log('Product submitted:', productData);
          
          const mockProduct: Product = {
            id: productData.id || `prod-${Date.now()}`,
            name: productData.name,
            sku: productData.sku,
            description: productData.description || null,
            category: productData.category || null,
            product_type: productData.product_type || null,
            unit_cost: productData.unit_cost || null,
            unit_price: productData.unit_price || null,
            weight: productData.weight || null,
            dimensions_length: productData.dimensions_length || null,
            dimensions_width: productData.dimensions_width || null,
            dimensions_height: productData.dimensions_height || null,
            status: productData.status || null,
            business_unit_id: productData.business_unit_id || null,
            created_at: new Date().toISOString(),
            updated_at: null
          };
          
          if (initialStock !== null && initialStock !== undefined && initialStock > 0) {
            console.log(`Setting initial stock to ${initialStock} for product ${mockProduct.id}`);
          }
          
          // Show success notification
          const isUpdate = !!productData.id;
          notificationService.success(
            `Product "${productData.name}" was ${isUpdate ? 'updated' : 'created'} successfully.`,
            isUpdate ? 'Product Updated' : 'Product Created'
          );
          
          if (onSuccess) {
            onSuccess(mockProduct);
          }
          
          resolve();
        }, 1000);
      });
    } catch (err) {
      console.error('Error submitting product:', err);
      const error = err instanceof Error ? err : new Error('Failed to save product');
      setSubmitError(error.message);
      
      // Show error notification
      notificationService.error(
        error.message || 'An unexpected error occurred while saving the product.',
        'Product Save Error'
      );
      
      if (onError) {
        onError(error);
      }
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, validateForm, onSuccess, onError]);
  
  // Reset form to initial values
  const resetForm = useCallback(() => {
    setFormData({
      ...defaultFormData,
      ...initialData
    });
    setErrors({});
    setSubmitError(null);
  }, [initialData]);
  
  return {
    formData,
    errors,
    isSubmitting,
    submitError,
    businessUnits,
    isLoading,
    handleChange,
    handleSubmit,
    resetForm,
    validateForm
  };
} 