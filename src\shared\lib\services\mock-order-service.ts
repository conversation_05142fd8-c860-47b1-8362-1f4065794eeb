import { 
  AllOrdersDetail, 
  AllOrdersViewItem, 
  AllOrdersStatus, 
  PlatformKey 
} from '@/types';
import { 
  ALL_ORDERS_MOCK_DATA, 
  ALL_ORDERS_VIEW_ITEMS 
} from '@/shared/lib/mock-data/all-orders';

// Filter interface for orders
export interface OrderFilters {
  searchTerm?: string;
  status?: AllOrdersStatus[];
  platform?: PlatformKey[];
  channel?: string[];
  dateRange?: {
    start?: string;
    end?: string;
  };
  isUrgent?: boolean;
  isProblem?: boolean;
  isResent?: boolean;
  hasNotes?: boolean;
  page?: number;
  pageSize?: number;
}

// Response interface for paginated orders
export interface OrdersResponse {
  orders: AllOrdersViewItem[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// Simulated API delay
const simulateDelay = (ms: number = 300): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Get orders with filtering and pagination
export const getOrders = async (filters: OrderFilters = {}): Promise<OrdersResponse> => {
  await simulateDelay();
  
  let filteredOrders = [...ALL_ORDERS_VIEW_ITEMS];
  
  // Apply search filter
  if (filters.searchTerm) {
    const searchLower = filters.searchTerm.toLowerCase();
    
    // Handle special search syntax
    if (searchLower.startsWith('status:')) {
      const statusValue = searchLower.replace('status:', '');
      filteredOrders = filteredOrders.filter(order => 
        order.status.toLowerCase() === statusValue
      );
    } else if (searchLower.startsWith('urgent:')) {
      const urgentValue = searchLower.replace('urgent:', '') === 'true';
      filteredOrders = filteredOrders.filter(order => order.isUrgent === urgentValue);
    } else {
      // Normal text search
      filteredOrders = filteredOrders.filter(order => 
        order.orderNumber.toLowerCase().includes(searchLower) ||
        order.customerName.toLowerCase().includes(searchLower) ||
        order.channel.toLowerCase().includes(searchLower) ||
        order.trackingNumber?.toLowerCase().includes(searchLower)
      );
    }
  }
  
  // Apply status filter
  if (filters.status && filters.status.length > 0) {
    filteredOrders = filteredOrders.filter(order => 
      filters.status!.includes(order.status)
    );
  }
  
  // Apply platform filter
  if (filters.platform && filters.platform.length > 0) {
    filteredOrders = filteredOrders.filter(order => 
      filters.platform!.includes(order.platform)
    );
  }
  
  // Apply channel filter
  if (filters.channel && filters.channel.length > 0) {
    filteredOrders = filteredOrders.filter(order => 
      filters.channel!.includes(order.channel)
    );
  }
  
  // Apply date range filter
  if (filters.dateRange && (filters.dateRange.start || filters.dateRange.end)) {
    filteredOrders = filteredOrders.filter(order => {
      const orderDate = new Date(order.orderDate);
      
      // Check start date if provided
      if (filters.dateRange!.start) {
        const startDate = new Date(filters.dateRange!.start);
        if (orderDate < startDate) return false;
      }
      
      // Check end date if provided
      if (filters.dateRange!.end) {
        const endDate = new Date(filters.dateRange!.end);
        // Set end date to end of day for inclusive filtering
        endDate.setHours(23, 59, 59, 999);
        if (orderDate > endDate) return false;
      }
      
      return true;
    });
  }
  
  // Apply flag filters
  if (filters.isUrgent !== undefined) {
    filteredOrders = filteredOrders.filter(order => order.isUrgent === filters.isUrgent);
  }
  
  if (filters.isProblem !== undefined) {
    filteredOrders = filteredOrders.filter(order => order.isProblem === filters.isProblem);
  }
  
  if (filters.isResent !== undefined) {
    filteredOrders = filteredOrders.filter(order => order.isResent === filters.isResent);
  }
  
  if (filters.hasNotes !== undefined) {
    filteredOrders = filteredOrders.filter(order => order.hasNotes === filters.hasNotes);
  }
  
  // Sort by order date (newest first)
  filteredOrders.sort((a, b) => new Date(b.orderDate).getTime() - new Date(a.orderDate).getTime());
  
  // Apply pagination
  const page = filters.page || 1;
  const pageSize = filters.pageSize || 25;
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedOrders = filteredOrders.slice(startIndex, endIndex);
  
  const totalCount = filteredOrders.length;
  const totalPages = Math.ceil(totalCount / pageSize);
  
  return {
    orders: paginatedOrders,
    totalCount,
    currentPage: page,
    totalPages,
    hasNextPage: page < totalPages,
    hasPreviousPage: page > 1
  };
};

// Get individual order by ID
export const getOrderById = async (orderId: string): Promise<AllOrdersDetail | null> => {
  await simulateDelay();
  
  const order = ALL_ORDERS_MOCK_DATA.find(order => order.id === orderId);
  return order || null;
};

// Update order status
export const updateOrderStatus = async (orderId: string, newStatus: AllOrdersStatus): Promise<AllOrdersDetail | null> => {
  await simulateDelay();
  
  const orderIndex = ALL_ORDERS_MOCK_DATA.findIndex(order => order.id === orderId);
  if (orderIndex === -1) return null;
  
  // Update the order status and timestamp
  ALL_ORDERS_MOCK_DATA[orderIndex] = {
    ...ALL_ORDERS_MOCK_DATA[orderIndex],
    status: newStatus,
    updatedAt: new Date().toISOString(),
    // Add specific timestamp based on status
    ...(newStatus === 'Packed' && { packedAt: new Date().toISOString() }),
    ...(newStatus === 'Shipped' && { shippedAt: new Date().toISOString() }),
    ...(newStatus === 'Completed' && { deliveredAt: new Date().toISOString() })
  };
  
  // Also update the view item
  const viewItemIndex = ALL_ORDERS_VIEW_ITEMS.findIndex(order => order.id === orderId);
  if (viewItemIndex !== -1) {
    ALL_ORDERS_VIEW_ITEMS[viewItemIndex] = {
      ...ALL_ORDERS_VIEW_ITEMS[viewItemIndex],
      status: newStatus
    };
  }
  
  return ALL_ORDERS_MOCK_DATA[orderIndex];
};

// Add note to order
export const addOrderNote = async (orderId: string, content: string, author: string): Promise<AllOrdersDetail | null> => {
  await simulateDelay();
  
  const orderIndex = ALL_ORDERS_MOCK_DATA.findIndex(order => order.id === orderId);
  if (orderIndex === -1) return null;
  
  const newNote = {
    id: `note_${Date.now()}`,
    author,
    timestamp: new Date().toISOString(),
    content,
    type: 'internal' as const
  };
  
  // Update the order with new note
  ALL_ORDERS_MOCK_DATA[orderIndex] = {
    ...ALL_ORDERS_MOCK_DATA[orderIndex],
    notes: [...ALL_ORDERS_MOCK_DATA[orderIndex].notes, newNote],
    hasNotes: true,
    updatedAt: new Date().toISOString()
  };
  
  // Update the view item
  const viewItemIndex = ALL_ORDERS_VIEW_ITEMS.findIndex(order => order.id === orderId);
  if (viewItemIndex !== -1) {
    ALL_ORDERS_VIEW_ITEMS[viewItemIndex] = {
      ...ALL_ORDERS_VIEW_ITEMS[viewItemIndex],
      hasNotes: true
    };
  }
  
  return ALL_ORDERS_MOCK_DATA[orderIndex];
};

// Cancel order
export const cancelOrder = async (orderId: string, reason: string): Promise<AllOrdersDetail | null> => {
  await simulateDelay();
  
  const orderIndex = ALL_ORDERS_MOCK_DATA.findIndex(order => order.id === orderId);
  if (orderIndex === -1) return null;
  
  // Add cancellation note
  const cancellationNote = {
    id: `note_cancel_${Date.now()}`,
    author: 'System',
    timestamp: new Date().toISOString(),
    content: `Order cancelled: ${reason}`,
    type: 'system' as const
  };
  
  // Update the order
  ALL_ORDERS_MOCK_DATA[orderIndex] = {
    ...ALL_ORDERS_MOCK_DATA[orderIndex],
    status: 'Cancelled',
    notes: [...ALL_ORDERS_MOCK_DATA[orderIndex].notes, cancellationNote],
    hasNotes: true,
    updatedAt: new Date().toISOString()
  };
  
  // Update the view item
  const viewItemIndex = ALL_ORDERS_VIEW_ITEMS.findIndex(order => order.id === orderId);
  if (viewItemIndex !== -1) {
    ALL_ORDERS_VIEW_ITEMS[viewItemIndex] = {
      ...ALL_ORDERS_VIEW_ITEMS[viewItemIndex],
      status: 'Cancelled',
      hasNotes: true
    };
  }
  
  return ALL_ORDERS_MOCK_DATA[orderIndex];
};

// Resend order confirmation
export const resendOrderConfirmation = async (orderId: string): Promise<AllOrdersDetail | null> => {
  await simulateDelay();
  
  const orderIndex = ALL_ORDERS_MOCK_DATA.findIndex(order => order.id === orderId);
  if (orderIndex === -1) return null;
  
  // Add resend note
  const resendNote = {
    id: `note_resend_${Date.now()}`,
    author: 'System',
    timestamp: new Date().toISOString(),
    content: 'Order confirmation resent to customer',
    type: 'system' as const
  };
  
  // Update the order
  ALL_ORDERS_MOCK_DATA[orderIndex] = {
    ...ALL_ORDERS_MOCK_DATA[orderIndex],
    notes: [...ALL_ORDERS_MOCK_DATA[orderIndex].notes, resendNote],
    hasNotes: true,
    isResent: true,
    updatedAt: new Date().toISOString()
  };
  
  // Update the view item
  const viewItemIndex = ALL_ORDERS_VIEW_ITEMS.findIndex(order => order.id === orderId);
  if (viewItemIndex !== -1) {
    ALL_ORDERS_VIEW_ITEMS[viewItemIndex] = {
      ...ALL_ORDERS_VIEW_ITEMS[viewItemIndex],
      hasNotes: true,
      isResent: true
    };
  }
  
  return ALL_ORDERS_MOCK_DATA[orderIndex];
};

// Process refund
export const processRefund = async (orderId: string, amount: number, reason: string): Promise<AllOrdersDetail | null> => {
  await simulateDelay();
  
  const orderIndex = ALL_ORDERS_MOCK_DATA.findIndex(order => order.id === orderId);
  if (orderIndex === -1) return null;
  
  // Add refund note
  const refundNote = {
    id: `note_refund_${Date.now()}`,
    author: 'Support Team',
    timestamp: new Date().toISOString(),
    content: `Refund processed: $${amount.toFixed(2)} - ${reason}`,
    type: 'internal' as const
  };
  
  // Update the order
  ALL_ORDERS_MOCK_DATA[orderIndex] = {
    ...ALL_ORDERS_MOCK_DATA[orderIndex],
    status: 'Refunded',
    notes: [...ALL_ORDERS_MOCK_DATA[orderIndex].notes, refundNote],
    hasNotes: true,
    updatedAt: new Date().toISOString()
  };
  
  // Update the view item
  const viewItemIndex = ALL_ORDERS_VIEW_ITEMS.findIndex(order => order.id === orderId);
  if (viewItemIndex !== -1) {
    ALL_ORDERS_VIEW_ITEMS[viewItemIndex] = {
      ...ALL_ORDERS_VIEW_ITEMS[viewItemIndex],
      status: 'Refunded',
      hasNotes: true
    };
  }
  
  return ALL_ORDERS_MOCK_DATA[orderIndex];
};

// Mark order as resent by order number (for delivery page bulk actions)
export const markOrderAsResent = async (orderNumber: string): Promise<AllOrdersDetail | null> => {
  await simulateDelay();
  
  const orderIndex = ALL_ORDERS_MOCK_DATA.findIndex(order => order.orderNumber === orderNumber);
  if (orderIndex === -1) return null;
  
  // Add resend note
  const resendNote = {
    id: `note_resend_${Date.now()}`,
    author: 'System',
    timestamp: new Date().toISOString(),
    content: 'Order marked as resent from delivery tracking page',
    type: 'system' as const
  };
  
  // Update the order
  ALL_ORDERS_MOCK_DATA[orderIndex] = {
    ...ALL_ORDERS_MOCK_DATA[orderIndex],
    notes: [...ALL_ORDERS_MOCK_DATA[orderIndex].notes, resendNote],
    hasNotes: true,
    isResent: true,
    updatedAt: new Date().toISOString()
  };
  
  // Update the view item
  const viewItemIndex = ALL_ORDERS_VIEW_ITEMS.findIndex(order => order.orderNumber === orderNumber);
  if (viewItemIndex !== -1) {
    ALL_ORDERS_VIEW_ITEMS[viewItemIndex] = {
      ...ALL_ORDERS_VIEW_ITEMS[viewItemIndex],
      hasNotes: true,
      isResent: true
    };
  }
  
  return ALL_ORDERS_MOCK_DATA[orderIndex];
};

// Mark order for refund by order number (for delivery page bulk actions)
export const markOrderForRefund = async (orderNumber: string): Promise<AllOrdersDetail | null> => {
  await simulateDelay();
  
  const orderIndex = ALL_ORDERS_MOCK_DATA.findIndex(order => order.orderNumber === orderNumber);
  if (orderIndex === -1) return null;
  
  // Add refund note
  const refundNote = {
    id: `note_refund_${Date.now()}`,
    author: 'System',
    timestamp: new Date().toISOString(),
    content: 'Order marked for refund from delivery tracking page',
    type: 'system' as const
  };
  
  // Update the order status and add note
  ALL_ORDERS_MOCK_DATA[orderIndex] = {
    ...ALL_ORDERS_MOCK_DATA[orderIndex],
    status: 'Refunded',
    notes: [...ALL_ORDERS_MOCK_DATA[orderIndex].notes, refundNote],
    hasNotes: true,
    isProblem: true, // Flag as problem since it requires refund
    updatedAt: new Date().toISOString()
  };
  
  // Update the view item
  const viewItemIndex = ALL_ORDERS_VIEW_ITEMS.findIndex(order => order.orderNumber === orderNumber);
  if (viewItemIndex !== -1) {
    ALL_ORDERS_VIEW_ITEMS[viewItemIndex] = {
      ...ALL_ORDERS_VIEW_ITEMS[viewItemIndex],
      status: 'Refunded',
      hasNotes: true,
      isProblem: true
    };
  }
  
  return ALL_ORDERS_MOCK_DATA[orderIndex];
};

// Get available filter options
export const getFilterOptions = async () => {
  await simulateDelay();
  
  const statuses = [...new Set(ALL_ORDERS_VIEW_ITEMS.map(order => order.status))];
  const platforms = [...new Set(ALL_ORDERS_VIEW_ITEMS.map(order => order.platform))];
  const channels = [...new Set(ALL_ORDERS_VIEW_ITEMS.map(order => order.channel))];
  
  return {
    statuses,
    platforms,
    channels
  };
}; 