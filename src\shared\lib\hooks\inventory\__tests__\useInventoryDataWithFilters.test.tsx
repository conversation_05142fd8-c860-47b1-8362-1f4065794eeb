import React from 'react';
import { renderHook, act, waitFor } from '@testing-library/react';
import { useInventoryDataWithFilters } from '../useInventoryDataWithFilters';

// Mock the services before importing them
jest.mock('@/shared/lib/services/inventory', () => ({
  inventoryService: {
    getInventoryList: jest.fn().mockResolvedValue({
      data: [
        { product_id: '1', name: 'Product 1', current_stock: 10, product_type: 'Type A' },
        { product_id: '2', name: 'Product 2', current_stock: 5, product_type: 'Type B' },
      ],
      count: 2
    })
  }
}));

jest.mock('@/shared/lib/services/cache', () => ({
  inventoryCacheService: {
    getCachedInventoryList: jest.fn().mockReturnValue(null),
    cacheInventoryList: jest.fn()
  }
}));

// Import the mocked services
import { inventoryService } from '@/shared/lib/services/inventory';
import { inventoryCacheService } from '@/shared/lib/services/cache';

// Set up fake timer for debounce testing
jest.useFakeTimers();

describe('useInventoryDataWithFilters', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  it('should fetch products on initial render', async () => {
    const { result } = renderHook(() => useInventoryDataWithFilters());
    
    // Initial state should show loading
    expect(result.current.loading).toBe(true);
    
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });
    
    // After loading, should have products
    expect(result.current.products).toHaveLength(2);
    expect(inventoryService.getInventoryList).toHaveBeenCalledTimes(1);
  });
  
  it('should update search term and fetch filtered products', async () => {
    const { result } = renderHook(() => useInventoryDataWithFilters());
    
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });
    
    // Change search term
    act(() => {
      result.current.handleSearchChange('Product 1');
    });
    
    // Wait for debounce
    act(() => {
      jest.advanceTimersByTime(350);
    });
    
    await waitFor(() => {
      expect(inventoryService.getInventoryList).toHaveBeenCalledWith(
        expect.objectContaining({ search: 'Product 1' })
      );
    });
  });
  
  it('should apply filters and reset to page 1', async () => {
    const { result } = renderHook(() => useInventoryDataWithFilters());
    
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });
    
    // Change page to 2
    act(() => {
      result.current.setCurrentPage(2);
    });
    
    await waitFor(() => {
      expect(result.current.currentPage).toBe(2);
    });
    
    // Apply filters
    act(() => {
      result.current.handleApplyFilters([
        { id: '1', type: 'productType', value: 'Type A', label: 'Type A' }
      ]);
    });
    
    await waitFor(() => {
      expect(result.current.currentPage).toBe(1);
    });
    
    // Page should reset to 1 and filter should be applied
    expect(result.current.activeFilters).toHaveLength(1);
    expect(inventoryService.getInventoryList).toHaveBeenCalledWith(
      expect.objectContaining({ 
        productType: ['Type A'],
        page: 1
      })
    );
  });
  
  it('should handle stock status filter changes', async () => {
    const { result } = renderHook(() => useInventoryDataWithFilters());
    
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });
    
    // Change stock status filter
    act(() => {
      result.current.handleStockStatusFilterChange('low_stock');
    });
    
    await waitFor(() => {
      expect(result.current.stockStatusFilter).toBe('low_stock');
    });
    
    // Should add filter chip and call service with parameter
    expect(result.current.activeFilters).toHaveLength(1);
    expect(result.current.activeFilters[0].type).toBe('stockStatus');
    expect(inventoryService.getInventoryList).toHaveBeenCalledWith(
      expect.objectContaining({ lowStock: true })
    );
  });
  
  it('should clear all filters', async () => {
    const { result } = renderHook(() => useInventoryDataWithFilters());
    
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });
    
    // Apply filters
    act(() => {
      result.current.handleSearchChange('test');
      result.current.handleStockStatusFilterChange('low_stock');
    });
    
    act(() => {
      jest.advanceTimersByTime(350);
    });
    
    await waitFor(() => {
      expect(result.current.activeFilters.length).toBeGreaterThan(0);
    });
    
    expect(result.current.searchTerm).toBe('test');
    
    // Clear filters
    act(() => {
      result.current.handleClearAllFilters();
    });
    
    await waitFor(() => {
      expect(result.current.activeFilters).toHaveLength(0);
    });
    
    // All filters should be cleared
    expect(result.current.searchTerm).toBe('');
    expect(result.current.stockStatusFilter).toBe('all');
    expect(inventoryService.getInventoryList).toHaveBeenCalledWith(
      expect.not.objectContaining({ 
        search: 'test',
        lowStock: true
      })
    );
  });
  
  it('should handle removing individual filters', async () => {
    const { result } = renderHook(() => useInventoryDataWithFilters());
    
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });
    
    // Apply filters
    act(() => {
      result.current.handleApplyFilters([
        { id: '1', type: 'productType', value: 'Type A', label: 'Type A' },
        { id: '2', type: 'status', value: 'active', label: 'Active' }
      ]);
    });
    
    await waitFor(() => {
      expect(result.current.activeFilters).toHaveLength(2);
    });
    
    // Remove one filter
    act(() => {
      result.current.handleRemoveFilter('1');
    });
    
    await waitFor(() => {
      expect(result.current.activeFilters).toHaveLength(1);
    });
    
    // Should have one filter left
    expect(result.current.activeFilters[0].id).toBe('2');
    expect(inventoryService.getInventoryList).toHaveBeenCalledWith(
      expect.objectContaining({ 
        status: ['active']
      })
    );
  });
}); 