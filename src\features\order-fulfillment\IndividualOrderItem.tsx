import * as React from 'react';
import { memo, useMemo, useCallback } from 'react';
import { FulfillOrderItem } from '@/types';
import { cn } from '@/shared/lib/utils/core/cn';

interface IndividualOrderItemProps {
  order: FulfillOrderItem;
  hideChannelBadge?: boolean;
  isSelected: boolean;
  onToggleSelection: (orderId: string) => void;
  isGroupSelectable: boolean;
}

// Task 3.9: Memoize IndividualOrderItem to prevent unnecessary re-renders
export const IndividualOrderItem = memo<IndividualOrderItemProps>(({ 
  order, 
  hideChannelBadge, 
  isSelected, 
  onToggleSelection, 
  isGroupSelectable 
}) => {
  const { order_number, store, items, shipping_fee_paid, tracking_number } = order;

  // Task 3.9: Memoize expensive calculations to prevent recalculation on every render
  const totalQuantity = useMemo(() => {
    let total = 0;
    for (const item of items) {
      total += item.quantity;
    }
    return total;
  }, [items]);
  
  // Task 3.9: Memoize date calculation for overdue status
  const isOverdue = useMemo(() => {
    return new Date(order.order_date) < new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // Example: older than 7 days
  }, [order.order_date]);

  // Add a condition for ready_to_ship status
  const isReadyToShip = order.status === 'ready_to_ship';

  // Task 3.9: Memoize click handler to prevent recreation
  const handleClick = useCallback(() => {
    // Don't allow selection if ready to ship
    if (isGroupSelectable && !isReadyToShip) {
      onToggleSelection(order.id);
    }
  }, [isGroupSelectable, onToggleSelection, order.id, isReadyToShip]);

  // Task 3.9: Memoize className calculation
  const containerClassName = useMemo(() => cn(
    'p-3 rounded-lg border transition-all duration-150 relative',
    isReadyToShip
      ? 'bg-green-50 border-green-300 shadow-sm opacity-85'
      : isSelected && isGroupSelectable
        ? 'bg-blue-50 border-blue-300 shadow-sm' 
        : 'bg-white border-gray-200',
    isGroupSelectable && !isReadyToShip
      ? 'cursor-pointer hover:bg-gray-50 hover:border-gray-300'
      : isReadyToShip ? '' : 'opacity-60 cursor-not-allowed'
  ), [isSelected, isGroupSelectable, isReadyToShip]);

  return (
    <div
      onClick={handleClick}
      className={containerClassName}
    >
      {isReadyToShip}
      <div className="flex justify-between items-center">
        <div className="flex-1 min-w-0">
          <div className="flex items-center">
            {!hideChannelBadge && (
              <span className="inline-block mr-2 px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">{store}</span>
            )}
            <p className="text-sm font-medium text-gray-900 truncate">#{order_number}</p>
          </div>
          <div className="mt-2 flex items-center space-x-2">
            {shipping_fee_paid && <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">Priority Shipping</span>}
            {isOverdue && <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">Overdue</span>}
          </div>
        </div>
        <div className="flex-shrink-0 ml-4 text-right">
          <p className="text-sm text-gray-600">
            {tracking_number ? 
              <span className={isReadyToShip ? "font-medium text-green-700" : ""}>
                TRK: {tracking_number}
              </span> 
              : 'No Tracking'}
          </p>
          <p className="text-sm font-semibold text-gray-900">Qty: {totalQuantity}</p>
        </div>
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  // Task 3.9: Custom comparison to prevent unnecessary re-renders
  return (
    prevProps.order.id === nextProps.order.id &&
    prevProps.order.order_number === nextProps.order.order_number &&
    prevProps.order.order_date === nextProps.order.order_date &&
    prevProps.hideChannelBadge === nextProps.hideChannelBadge &&
    prevProps.isSelected === nextProps.isSelected &&
    prevProps.isGroupSelectable === nextProps.isGroupSelectable &&
    prevProps.onToggleSelection === nextProps.onToggleSelection &&
    // Compare items array length and essential properties
    prevProps.order.items.length === nextProps.order.items.length &&
    prevProps.order.items.every((item, index) => 
      item.quantity === nextProps.order.items[index]?.quantity
    )
  );
}); 