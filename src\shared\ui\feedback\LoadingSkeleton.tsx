import React from 'react';
import { cn } from '@/shared/lib/utils/core/cn';

export const PanelLoadingSkeleton: React.FC = () => (
  <div className="p-6 space-y-6 animate-pulse">
    {/* Header */}
    <div className="flex items-center space-x-3">
      <div className="h-8 bg-gray-200 rounded w-1/2"></div>
    </div>
    {/* Status Summary */}
    <div className="bg-gray-50 rounded-lg p-4">
      <div className="flex items-center space-x-4">
        <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
        <div className="flex-1 space-y-2">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    </div>
    {/* Details Grid */}
    <div className="grid grid-cols-2 gap-4">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="space-y-1">
          <div className="h-4 bg-gray-200 rounded w-1/3"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3"></div>
        </div>
      ))}
    </div>
    {/* Destination */}
    <div className="space-y-1">
      <div className="h-4 bg-gray-200 rounded w-1/3"></div>
      <div className="h-4 bg-gray-200 rounded w-2/3"></div>
    </div>
    {/* Timeline */}
    <div>
      <div className="h-5 bg-gray-200 rounded w-1/4 mb-4"></div>
      {[...Array(3)].map((_, i) => (
        <div key={i} className="flex pb-6">
          <div className="relative z-10 flex-shrink-0 w-6 h-6">
            <div className="w-6 h-6 bg-gray-200 rounded-full"></div>
          </div>
          <div className="ml-4 flex-1 space-y-2">
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-3 bg-gray-200 rounded w-1/4 mt-1"></div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

// New component for filter section skeleton loading
export const FilterSectionSkeleton: React.FC = () => (
  <div className="px-4 sm:px-6 py-4 animate-pulse">
    <div className="flex flex-wrap items-center justify-between">
      <div className="flex items-center space-x-4 mb-2 sm:mb-0">
        <div className="h-8 bg-gray-200 rounded-md w-32 sm:w-48"></div>
        <div className="h-8 bg-gray-200 rounded-md w-24 sm:w-32"></div>
      </div>
      <div className="flex items-center space-x-2">
        <div className="h-8 bg-gray-200 rounded-md w-24 sm:w-32"></div>
        <div className="h-8 bg-gray-200 rounded-md w-24 sm:w-32"></div>
      </div>
    </div>
    <div className="mt-3 flex flex-wrap items-center">
      <div className="h-6 bg-gray-200 rounded w-16 mr-2"></div>
      <div className="flex space-x-2">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="h-8 bg-gray-200 rounded-md w-20 sm:w-24"></div>
        ))}
      </div>
    </div>
  </div>
);

interface TableLoadingSkeletonProps {
  rowCount?: number;
  columnCount?: number;
  className?: string;
}

// Loading skeleton for the inventory table (desktop version)
export const TableLoadingSkeleton: React.FC<TableLoadingSkeletonProps> = ({
  rowCount = 5,
  columnCount = 8,
  className
}) => {
  return (
    <div className={cn("flex flex-col", className)}>
      {/* Stock Indicator Legend Skeleton */}
      <div className="mb-4 px-3 sm:px-6 py-3 bg-gray-50 rounded-md animate-pulse">
        <div className="flex flex-col sm:flex-row sm:items-center">
          <div className="h-5 bg-gray-200 rounded w-32 mb-2 sm:mb-0 sm:mr-6"></div>
          <div className="grid grid-cols-2 sm:flex sm:space-x-4 gap-y-2 w-full">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="flex items-center">
                <div className="w-3 h-3 bg-gray-200 rounded-full mr-2"></div>
                <div className="h-4 bg-gray-200 rounded w-16 sm:w-24"></div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Desktop Table Skeleton (hidden on small screens) */}
      <div className="hidden md:block">
        <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div className="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
            <div className="overflow-hidden border border-gray-200 sm:rounded-lg">
              <table className="min-w-full divide-y divide-gray-200">
                {/* Table Header */}
                <thead className="bg-gray-50">
                  <tr>
                    {[...Array(columnCount)].map((_, i) => (
                      <th key={i} scope="col" className="px-6 py-3 text-left">
                        <div className="h-4 bg-gray-200 rounded w-2/3 animate-pulse"></div>
                      </th>
                    ))}
                  </tr>
                </thead>
                {/* Table Body */}
                <tbody className="bg-white divide-y divide-gray-200">
                  {[...Array(rowCount)].map((_, rowIndex) => (
                    <tr key={rowIndex} className="animate-pulse">
                      {/* Checkbox column */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="h-4 w-4 bg-gray-200 rounded"></div>
                      </td>
                      {/* Product name column with indicator */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-gray-200 rounded-full mr-2"></div>
                          <div className="h-4 bg-gray-200 rounded w-32"></div>
                        </div>
                      </td>
                      {/* Other columns */}
                      {[...Array(columnCount - 2)].map((_, colIndex) => (
                        <td key={colIndex} className="px-6 py-4 whitespace-nowrap">
                          <div className="h-4 bg-gray-200 rounded w-16"></div>
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile/Tablet Card Skeleton (shown on small screens) */}
      <div className="md:hidden">
        <div className="grid gap-4 px-2">
          {[...Array(rowCount)].map((_, i) => (
            <div key={i} className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm animate-pulse">
              {/* Header with checkbox and name */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <div className="h-4 w-4 bg-gray-200 rounded"></div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-gray-200 rounded-full mr-2"></div>
                    <div className="h-5 bg-gray-200 rounded w-24 sm:w-32"></div>
                  </div>
                </div>
                <div className="h-5 w-16 bg-gray-200 rounded-full"></div>
              </div>

              {/* Product details grid */}
              <div className="grid grid-cols-2 gap-3">
                {[...Array(6)].map((_, j) => (
                  <div key={j} className="flex flex-col space-y-1">
                    <div className="h-3 bg-gray-200 rounded w-12"></div>
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Pagination Skeleton */}
      <div className="py-4 px-4 sm:px-6 flex justify-center animate-pulse">
        <div className="flex space-x-2">
          <div className="h-8 w-8 bg-gray-200 rounded"></div>
          <div className="h-8 w-8 bg-gray-200 rounded"></div>
          <div className="h-8 w-8 bg-gray-200 rounded"></div>
          <div className="h-8 w-8 bg-gray-200 rounded"></div>
          <div className="h-8 w-8 bg-gray-200 rounded"></div>
        </div>
      </div>
    </div>
  );
}; 