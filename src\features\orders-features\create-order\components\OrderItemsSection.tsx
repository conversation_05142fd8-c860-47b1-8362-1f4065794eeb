import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { CreateOrderFormData, ProductIdentifier } from '@/types';

interface OrderItemsSectionProps {
  formData: CreateOrderFormData;
  productIdentifiers: ProductIdentifier[];
  onUpdateFormData: (updates: Partial<CreateOrderFormData>) => void;
  onAddItem: () => void;
  onRemoveItem: (index: number) => void;
}

const OrderItemsSection: React.FC<OrderItemsSectionProps> = ({
  formData,
  productIdentifiers,
  onUpdateFormData,
  onAddItem,
  onRemoveItem
}) => {
  const [searchTerms, setSearchTerms] = useState<string[]>([]);
  const [searchResults, setSearchResults] = useState<ProductIdentifier[][]>([]);

  // Task 3.5: Optimize product lookup with Map for O(1) access instead of O(n) array.find()
  const productIdentifiersById = useMemo(() => {
    const map = new Map<string, ProductIdentifier>();
    productIdentifiers.forEach(product => {
      map.set(product.id, product);
    });
    return map;
  }, [productIdentifiers]);

  // Initialize search terms and results arrays when order items change
  useEffect(() => {
    // Initialize search terms array to match order items length
    const initialSearchTerms = formData.order_items.map((item) => {
      // Preserve search terms for items with IDs
      const productInfo = item.product_id ? productIdentifiersById.get(item.product_id) : undefined;
      return productInfo ? productInfo.platform_identifier : '';
    });
    
    setSearchTerms(initialSearchTerms);
    setSearchResults(Array(formData.order_items.length).fill([]));
  }, [formData.order_items, productIdentifiersById]);

  const updateOrderItem = useCallback((index: number, product_id: string) => {
    const updatedItems = [...formData.order_items];
    updatedItems[index] = { product_id };
    onUpdateFormData({ order_items: updatedItems });
  }, [formData.order_items, onUpdateFormData]);

  const getProductInfo = useCallback((product_id: string) => {
    // Task 3.5: Use Map.get() for O(1) product lookup instead of array.find() O(n)
    return productIdentifiersById.get(product_id);
  }, [productIdentifiersById]);

  const handleSearch = useCallback((index: number, searchTerm: string) => {
    setSearchTerms(prev => {
      const newSearchTerms = [...prev];
      newSearchTerms[index] = searchTerm;
      return newSearchTerms;
    });

    if (searchTerm.length >= 2) {
      const filtered = productIdentifiers.filter(pi => 
        (!formData.platform_id || pi.platform_id === formData.platform_id) &&
        pi.platform_identifier.toLowerCase().includes(searchTerm.toLowerCase())
      );
      
      setSearchResults(prev => {
        const newResults = [...prev];
        newResults[index] = filtered.slice(0, 10); // Limit to 10 results
        return newResults;
      });
    } else {
      setSearchResults(prev => {
        const newResults = [...prev];
        newResults[index] = [];
        return newResults;
      });
    }
  }, [productIdentifiers, formData.platform_id]);

  const selectProduct = useCallback((index: number, productIdentifier: ProductIdentifier) => {
    updateOrderItem(index, productIdentifier.id);
    
    setSearchTerms(prev => {
      const newSearchTerms = [...prev];
      newSearchTerms[index] = productIdentifier.platform_identifier;
      return newSearchTerms;
    });
    
    setSearchResults(prev => {
      const newResults = [...prev];
      newResults[index] = [];
      return newResults;
    });
  }, [updateOrderItem]);

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Order Items</h3>
        <button
          type="button"
          onClick={onAddItem}
          className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          + Add Item
        </button>
      </div>
      
      {formData.order_items.map((item, index) => {
        const productInfo = getProductInfo(item.product_id);
        
        return (
          <div key={index} className="relative p-4 border rounded-md bg-gray-50">
            {formData.order_items.length > 1 && (
              <button
                type="button"
                onClick={() => onRemoveItem(index)}
                className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full text-xs hover:bg-red-600"
              >
                ×
              </button>
            )}
            
            {/* Product Search - Full Width */}
            <div className="mb-4 relative">
              <label className="block text-sm font-medium text-gray-700 mb-2">Product SKU/ASIN</label>
              <input
                type="text"
                value={searchTerms[index] || ''}
                onChange={(e) => handleSearch(index, e.target.value)}
                placeholder="Type to search SKU/ASIN..."
                className="w-full px-3 py-2 border rounded-md"
                required
              />
              
              {/* Search Results Dropdown */}
              {searchResults[index] && searchResults[index].length > 0 && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                  {searchResults[index].map((productIdentifier) => (
                    <div
                      key={productIdentifier.id}
                      onClick={() => selectProduct(index, productIdentifier)}
                      className="px-3 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
                    >
                      <div className="font-medium">{productIdentifier.platform_identifier} - {productIdentifier.code_name} </div>
                      <div className="text-sm text-gray-500">Pack: {productIdentifier.pack_size}</div>
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            {/* Product Details Grid */}
            {productInfo && (
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Code Name</label>
                  <input
                    type="text"
                    value={productInfo.code_name || ''}
                    className="w-full px-3 py-2 border rounded-md bg-gray-100"
                    readOnly
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">SKU/ASIN</label>
                  <input
                    type="text"
                    value={productInfo.platform_identifier}
                    className="w-full px-3 py-2 border rounded-md bg-gray-100"
                    readOnly
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Pack Size</label>
                  <input
                    type="text"
                    value={productInfo.pack_size}
                    className="w-full px-3 py-2 border rounded-md bg-gray-100"
                    readOnly
                  />
                </div>
              </div>
            )}
          </div>
        );
      })}
      
      {/* Order Summary */}
      <div className="bg-blue-50 p-4 rounded-md">
        <div className="flex justify-between text-sm">
          <span>Total Products:</span>
          <span className="font-medium">{formData.order_items.filter(item => item.product_id).length}</span>
        </div>
      </div>
    </div>
  );
};

export default OrderItemsSection; 