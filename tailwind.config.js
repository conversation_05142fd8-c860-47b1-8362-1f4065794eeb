/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'sidebar-bg': '#1F2937',
        'sidebar-text': '#D1D5DB',
        'sidebar-hover-bg': '#374151',
        'main-bg': '#F9FAFB',
        'card-bg': '#FFFFFF',
        'primary': '#3B82F6',
        'primary-dark': '#2563EB',
        'login-button-bg': '#6366F1',
        'login-button-hover-bg': '#4F46E5',
      },
      scale: {
        '102': '1.02',
      },
      zIndex: {
        '49': '49',
      },
      keyframes: {
        'slide-out-left': {
          '0%': { transform: 'translateX(0)', opacity: '1', backgroundColor: 'transparent' },
          '20%': { transform: 'translateX(0)', opacity: '1', backgroundColor: '#d1fae5' },
          '100%': { transform: 'translateX(-100%)', opacity: '0', backgroundColor: '#d1fae5' }
        },
        'slide-in-right': {
          '0%': { transform: 'translateX(-10%)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' }
        },
        'fade-in-down': {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' }
        }
      },
      animation: {
        'slide-out-left': 'slide-out-left 0.8s ease-in-out forwards',
        'slide-in-right': 'slide-in-right 0.4s ease-out forwards',
        'fade-in-down': 'fade-in-down 0.5s ease-out forwards'
      }
    },
  },
  plugins: [],
}
