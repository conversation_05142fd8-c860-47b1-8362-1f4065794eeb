import { getCarriers } from './carrier-service';
import { Carrier } from '@/types';

/**
 * Normalizes a carrier name string for consistent comparison
 * @param carrierName Name of the carrier to normalize
 * @returns Normalized carrier name (lowercase, trimmed)
 */
export const normalizeCarrierName = (carrierName: string): string => {
  return carrierName.toLowerCase().trim();
};

/**
 * Service for detecting carriers based on tracking number patterns
 */
class CarrierParserService {
  private carrierPatterns = [
    { carrier: 'usps', pattern: /^9\d{21}$/, length: 22 },
    { carrier: 'fedex', pattern: /^\d{12}$/, length: 12 },
    { carrier: 'ups', pattern: /^1Z[A-Z0-9]{16}$/i, length: 18 },
    { carrier: 'dhl', pattern: /^\d{10}$/, length: 10 },
  ];
  
  private carrierCache: Map<string, Carrier> | null = null;
  private carrierLookup: Map<string, string> | null = null;
  
  /**
   * Initialize the carrier cache for ID lookups
   */
  private async ensureCarrierCache(): Promise<void> {
    if (this.carrierCache === null) {
      const carriers = await getCarriers();
      this.carrierCache = new Map();
      this.carrierLookup = new Map();
      
      carriers.forEach(carrier => {
        // Store by ID for quick retrieval
        this.carrierCache!.set(carrier.id, carrier);
        
        // Store normalized name -> ID mapping for lookups
        const normalizedName = normalizeCarrierName(carrier.name);
        this.carrierLookup!.set(normalizedName, carrier.id);
        
        // Store normalized code -> ID mapping as alternative lookup
        if (carrier.code) {
          const normalizedCode = normalizeCarrierName(carrier.code);
          this.carrierLookup!.set(normalizedCode, carrier.id);
        }
      });
    }
  }
  
  /**
   * Detect carrier based on tracking number format
   * @param trackingNumber The tracking number to analyze
   * @returns Detected carrier name or null if unknown format
   */
  public detectCarrierFromTracking(trackingNumber: string): string | null {
    if (!trackingNumber) return null;
    
    // Clean the tracking number
    const cleaned = trackingNumber.trim().replace(/\s+/g, '');
    
    // Find matching carrier pattern
    for (const { carrier, pattern, length } of this.carrierPatterns) {
      if ((length && cleaned.length === length) || pattern.test(cleaned)) {
        return carrier;
      }
    }
    
    return null;
  }
  
  /**
   * Get carrier ID from carrier name
   * @param carrierName The name of the carrier
   * @returns Carrier ID or null if not found
   */
  public async getCarrierIdByName(carrierName: string): Promise<string | null> {
    if (!carrierName) return null;
    
    await this.ensureCarrierCache();
    
    const normalizedName = normalizeCarrierName(carrierName);
    return this.carrierLookup!.get(normalizedName) || null;
  }
  
  /**
   * Get carrier details by ID
   * @param carrierId The carrier ID
   * @returns Carrier object or null if not found
   */
  public async getCarrierById(carrierId: string): Promise<Carrier | null> {
    if (!carrierId) return null;
    
    await this.ensureCarrierCache();
    
    return this.carrierCache!.get(carrierId) || null;
  }
  
  /**
   * Process tracking data to ensure it has a valid carrier ID
   * @param trackingData Object with tracking info that may have carrier_name instead of carrier_id
   * @returns Promise with the processed data including carrier_id
   */
  public async processTrackingData(
    trackingData: {
      order_id: string;
      tracking_number: string;
      carrier_id?: string;
      carrier_name?: string;
      order_number?: string;
    }
  ): Promise<{
    order_id: string;
    tracking_number: string;
    carrier_id: string;
    order_number?: string;
    processed: boolean;
  } | null> {
    if (!trackingData.tracking_number || !trackingData.order_id) {
      return null;
    }
    
    // If carrier_id is already provided, just validate it exists
    if (trackingData.carrier_id) {
      const carrier = await this.getCarrierById(trackingData.carrier_id);
      if (carrier) {
        return {
          order_id: trackingData.order_id,
          tracking_number: trackingData.tracking_number,
          carrier_id: trackingData.carrier_id,
          order_number: trackingData.order_number,
          processed: false
        };
      }
    }
    
    // Try to get carrier ID from provided name
    if (trackingData.carrier_name) {
      const carrierId = await this.getCarrierIdByName(trackingData.carrier_name);
      if (carrierId) {
        return {
          order_id: trackingData.order_id,
          tracking_number: trackingData.tracking_number,
          carrier_id: carrierId,
          order_number: trackingData.order_number,
          processed: true
        };
      }
    }
    
    // Try to detect carrier from tracking number
    const detectedCarrierName = this.detectCarrierFromTracking(trackingData.tracking_number);
    if (detectedCarrierName) {
      const carrierId = await this.getCarrierIdByName(detectedCarrierName);
      if (carrierId) {
        return {
          order_id: trackingData.order_id,
          tracking_number: trackingData.tracking_number,
          carrier_id: carrierId,
          order_number: trackingData.order_number,
          processed: true
        };
      }
    }
    
    // Failed to resolve carrier ID
    return null;
  }
  
  /**
   * Process batch of tracking data to ensure all have valid carrier IDs
   * @param trackingDataArray Array of tracking data objects 
   * @returns Processed data with validation results
   */
  public async processBatchTrackingData(
    trackingDataArray: Array<{
      order_id: string;
      tracking_number: string;
      carrier_id?: string;
      carrier_name?: string;
      order_number?: string;
    }>
  ): Promise<{
    validItems: Array<{
      order_id: string;
      tracking_number: string;
      carrier_id: string;
      order_number?: string;
    }>;
    invalidItems: Array<{
      order_id: string;
      tracking_number: string;
      error: string;
      order_number?: string;
    }>;
    detectedCount: number;
  }> {
    const validItems: Array<{
      order_id: string;
      tracking_number: string;
      carrier_id: string;
      order_number?: string;
    }> = [];
    
    const invalidItems: Array<{
      order_id: string;
      tracking_number: string;
      error: string;
      order_number?: string;
    }> = [];
    
    let detectedCount = 0;
    
    for (const item of trackingDataArray) {
      const processed = await this.processTrackingData(item);
      
      if (processed) {
        validItems.push({
          order_id: processed.order_id,
          tracking_number: processed.tracking_number,
          carrier_id: processed.carrier_id,
          order_number: processed.order_number
        });
        
        if (processed.processed) {
          detectedCount++;
        }
      } else {
        invalidItems.push({
          order_id: item.order_id,
          tracking_number: item.tracking_number || '',
          error: 'Unable to determine carrier',
          order_number: item.order_number
        });
      }
    }
    
    return {
      validItems,
      invalidItems,
      detectedCount
    };
  }
  
  /**
   * Reset the carrier cache (useful for testing)
   */
  public resetCache(): void {
    this.carrierCache = null;
    this.carrierLookup = null;
  }
}

// Export singleton instance
export const carrierParserService = new CarrierParserService(); 