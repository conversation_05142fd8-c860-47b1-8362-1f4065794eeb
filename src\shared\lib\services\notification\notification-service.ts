import { BehaviorSubject, Observable } from 'rxjs';

// Define the notification types
export type NotificationType = 'success' | 'error' | 'warning' | 'info';

// Define the notification interface
export interface Notification {
  id: string;
  type: NotificationType;
  message: string;
  title?: string;
  autoClose?: boolean;
  duration?: number;
}

class NotificationService {
  private notifications: BehaviorSubject<Notification[]>;
  
  constructor() {
    this.notifications = new BehaviorSubject<Notification[]>([]);
  }
  
  // Get all notifications as an observable
  public getNotifications(): Observable<Notification[]> {
    return this.notifications.asObservable();
  }
  
  // Add a new notification
  public addNotification(notification: Omit<Notification, 'id'>): string {
    const id = this.generateId();
    const newNotification: Notification = {
      id,
      autoClose: true,
      duration: 5000, // Default to 5 seconds
      ...notification,
    };
    
    const currentNotifications = this.notifications.getValue();
    this.notifications.next([...currentNotifications, newNotification]);
    
    // Auto-remove notification after duration if autoClose is true
    if (newNotification.autoClose) {
      setTimeout(() => {
        this.removeNotification(id);
      }, newNotification.duration);
    }
    
    return id;
  }
  
  // Show success notification
  public success(message: string, title?: string, options: Partial<Omit<Notification, 'id' | 'type' | 'message' | 'title'>> = {}): string {
    return this.addNotification({
      type: 'success',
      message,
      title,
      ...options
    });
  }
  
  // Show error notification
  public error(message: string, title?: string, options: Partial<Omit<Notification, 'id' | 'type' | 'message' | 'title'>> = {}): string {
    return this.addNotification({
      type: 'error',
      message,
      title,
      ...options
    });
  }
  
  // Show warning notification
  public warning(message: string, title?: string, options: Partial<Omit<Notification, 'id' | 'type' | 'message' | 'title'>> = {}): string {
    return this.addNotification({
      type: 'warning',
      message,
      title,
      ...options
    });
  }
  
  // Show info notification
  public info(message: string, title?: string, options: Partial<Omit<Notification, 'id' | 'type' | 'message' | 'title'>> = {}): string {
    return this.addNotification({
      type: 'info',
      message,
      title,
      ...options
    });
  }
  
  // Remove a notification by id
  public removeNotification(id: string): void {
    const currentNotifications = this.notifications.getValue();
    this.notifications.next(
      currentNotifications.filter(notification => notification.id !== id)
    );
  }
  
  // Clear all notifications
  public clearNotifications(): void {
    this.notifications.next([]);
  }
  
  // Generate a unique id
  private generateId(): string {
    return Math.random().toString(36).substring(2, 9);
  }
}

// Export singleton instance
export const notificationService = new NotificationService(); 