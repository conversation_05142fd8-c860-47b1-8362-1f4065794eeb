import React, { Profiler, useCallback, lazy, Suspense, useState, useRef, useEffect } from 'react';
import LoadingSpinner from '@/shared/ui/feedback/LoadingSpinner';
import { useOrdersPage } from '@/shared/lib/hooks/orders/useOrdersPage';
import { createProfilerCallback } from '@/shared/lib/utils/performance/profiler-utils';
import { useWebSocketEvent, useSupabaseRealtime } from '@/shared/lib/hooks/websocket';

// Dynamic imports for feature components
const OrdersTable = lazy(() => import('@/features/orders-features/orders-table/OrdersTable'));
const OrderDetailPanel = lazy(() => import('@/features/orders-features/order-detail-panel/OrderDetailPanel'));
const OrdersHeader = lazy(() => import('@/features/orders-features/orders-header/OrdersHeader'));
const FilterModal = lazy(() => import('@/features/orders-features/orders-filter-modal/FilterModal'));
const FilterChips = lazy(() => import('@/features/orders-features/orders-filter-chips/FilterChips'));
const BulkCreateOrderModal = lazy(() => import('@/features/orders-features/bulk-create-order/BulkCreateOrderModal'));

// Simple loading component for Suspense fallback
const FeatureLoading = () => (
  <div className="flex justify-center items-center p-8">
    <LoadingSpinner size="md" message="Loading component..." />
  </div>
);

const AllOrdersPage: React.FC = () => {
  return (
    <Profiler id="AllOrdersPage" onRender={createProfilerCallback('AllOrdersPage')}>
      <AllOrdersPageContent />
    </Profiler>
  );
};

const AllOrdersPageContent: React.FC = () => {
  const {
    profile,
    isFilterModalOpen,
    searchTerm,
    filters,
    activeFiltersCount,
    combinedFilters,
    userRole,
    panelOrderId,
    isPanelOpen,
    setIsFilterModalOpen,
    handleSearchChange,
    handleApplyFilters,
    handleRemoveFilter,
    handleClearAllFilters,
    openPanel,
    closePanel,
    refreshOrders,
    setFilters
  } = useOrdersPage();
  
  // Simple isRefreshing state for loading indicator
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Track if component is mounted
  const isMountedRef = useRef(true);
  
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);
  
  // Simple refresh function that shows loading state
  const handleRefresh = useCallback(() => {
    if (!isMountedRef.current) return;
    
    setIsRefreshing(true);
    refreshOrders({ skipCache: true }); // Skip cache on WebSocket refreshes
    
    // Reset loading state after a short delay
    setTimeout(() => {
      if (isMountedRef.current) {
        setIsRefreshing(false);
      }
    }, 500);
  }, [refreshOrders]);
  
  // Add WebSocket event listener for order changes
  const [lastWebSocketEvent, setLastWebSocketEvent] = useState<any>(null);
  
  useWebSocketEvent('order_changes', (message) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Orders page received WebSocket event:', message);
    }
    if (isMountedRef.current) {
      setLastWebSocketEvent({
        type: message.type,
        timestamp: new Date().toISOString(),
        payload: message.payload
      });
      handleRefresh(); // Use refresh function with loading spinner
    }
  }, [handleRefresh]);

  // Add Supabase Realtime subscription for orders table
  const handleRealtimeUpdate = useCallback((payload: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`Orders page received Supabase Realtime event:`, payload);
    }
    if (isMountedRef.current) {
      handleRefresh(); // Use refresh function with loading spinner
    }
  }, [handleRefresh]);
  
  // Subscribe to Supabase Realtime for orders table
  useSupabaseRealtime('orders', '*', handleRealtimeUpdate, 'public', undefined, false);
  
  // Clean up on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Add state for Bulk Create Orders modal
  const [isBulkCreateOrderOpen, setIsBulkCreateOrderOpen] = useState(false);

  // Memoize event handlers with useCallback to prevent unnecessary re-renders
  const handleOpenBulkCreateOrder = useCallback(() => {
    setIsBulkCreateOrderOpen(true);
  }, []);

  const handleCloseBulkCreateOrder = useCallback(() => {
    setIsBulkCreateOrderOpen(false);
  }, []);

  const handleOpenFilterModal = useCallback(() => {
    setIsFilterModalOpen(true);
  }, [setIsFilterModalOpen]);

  const handleCloseFilterModal = useCallback(() => {
    setIsFilterModalOpen(false);
  }, [setIsFilterModalOpen]);

  return (
    <div className="flex min-h-screen bg-gray-50">
      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Page Header */}
        <header className="flex-shrink-0 bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-6 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">All Orders</h1>
                <p className="text-gray-600 mt-1">Manage and track all your orders</p>
              </div>
              <div className="flex space-x-3">
                <button
                  onClick={handleOpenBulkCreateOrder}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors duration-150"
                >
                  Bulk Create Orders
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Content Area */}
        <main className="flex-1">
          {/* Orders Header (Search & Filters) */}
          <div className="flex-shrink-0 bg-white border-b border-gray-100">
            <div className="max-w-7xl mx-auto px-6">
              <Suspense fallback={<FeatureLoading />}>
                <OrdersHeader
                  searchTerm={searchTerm}
                  onSearchChange={handleSearchChange}
                  onFiltersClick={handleOpenFilterModal}
                  onFiltersChange={setFilters}
                  activeFiltersCount={activeFiltersCount}
                />
              </Suspense>
            </div>
          </div>

          {/* Active Filter Chips */}
          <div className="flex-shrink-0 bg-gray-50 border-b border-gray-200">
            <div className="max-w-7xl mx-auto px-6">
              <Suspense fallback={<FeatureLoading />}>
                <FilterChips
                  filters={filters as any}
                  onRemoveFilter={handleRemoveFilter as (filterType: string, value?: string) => void}
                  onClearAll={handleClearAllFilters}
                />
              </Suspense>
            </div>
          </div>

          {/* Orders Table */}
          <div className="max-w-7xl mx-auto px-6 py-6 relative">
            {/* Simple loading overlay */}
            {isRefreshing && (
              <div className="absolute inset-0 bg-white bg-opacity-70 z-10 flex items-center justify-center">
                <div className="bg-white p-4 rounded-lg shadow-md">
                  <LoadingSpinner size="md" message="Refreshing data..." />
                </div>
              </div>
            )}
            <Suspense fallback={<FeatureLoading />}>
              <OrdersTable
                filters={combinedFilters}
                onRowClick={openPanel}
                selectedOrderId={panelOrderId}
                onFiltersChange={setFilters}
                userRole={userRole}
              />
            </Suspense>
          </div>
        </main>
      </div>

      {/* Order Detail Panel */}
      <Suspense fallback={<FeatureLoading />}>
        <OrderDetailPanel
          orderId={panelOrderId}
          isOpen={isPanelOpen}
          onClose={closePanel}
          onOrderUpdate={handleRefresh}
        />
      </Suspense>

      {/* Filter Modal */}
      <Suspense fallback={<FeatureLoading />}>
        <FilterModal
          isOpen={isFilterModalOpen}
          onClose={handleCloseFilterModal}
          onApplyFilters={handleApplyFilters}
          currentFilters={filters as any}
          userRole={profile?.role}
        />
      </Suspense>
      
      {/* Bulk Create Order Modal */}
      <Suspense fallback={<FeatureLoading />}>
        <BulkCreateOrderModal
          isOpen={isBulkCreateOrderOpen}
          onClose={handleCloseBulkCreateOrder}
          onSuccess={handleRefresh}
        />
      </Suspense>

      {/* WebSocket debug - only visible when debugging */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 right-4 z-50 space-y-4">
          {/* WebSocket Event Debug */}
          {lastWebSocketEvent && (
            <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200 max-w-md">
              <h3 className="text-lg font-medium mb-2">Last WebSocket Event</h3>
              <div className="bg-gray-50 p-3 rounded text-xs font-mono overflow-auto max-h-40">
                <p><span className="font-medium">Type:</span> {lastWebSocketEvent.type}</p>
                <p><span className="font-medium">Time:</span> {new Date(lastWebSocketEvent.timestamp).toLocaleTimeString()}</p>
                <details>
                  <summary className="cursor-pointer hover:text-blue-600">View Payload</summary>
                  <pre className="mt-2 whitespace-pre-wrap">
                    {JSON.stringify(lastWebSocketEvent.payload, null, 2)}
                  </pre>
                </details>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AllOrdersPage;