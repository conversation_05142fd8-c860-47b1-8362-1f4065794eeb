import React from 'react';
import { useNavigate } from 'react-router-dom';
import SalesChart from '@/features/report-features/channel-performance/SalesOverTime';
import LowStockAlertsCard from '@/features/inventory-features/inventory-alerts';
import {
  SHRIMP_DASHBOARD_METRICS,
  SHRIMP_LOW_STOCK_ALERTS,
  SHRIMP_TOP_SELLERS,
} from '@/shared/lib/mock-data/shrimp-dashboard';
import IntelligenceChart from '@/features/report-features/intelligence-chart';
import ShrimpTopSellersCard from '@/features/report-features/shrimp-top-sellers/index';
import { SHRIMP_INTELLIGENCE_CHART_DATA, SHRIMP_TOP_SELLERS_DATA } from '@/shared/lib/mock-data/shrimp-dashboard'; // Adjust path if necessary
import DashboardHeader from '@/features/dashboard-header';

const ShrimpDashboard: React.FC = () => {
  const navigate = useNavigate();

  const handleViewChange = (view: string) => {
    switch (view) {
      case 'all_units':
        navigate('/dashboard-center');
        break;
      case 'shrimp_products':
        navigate('/dashboard-shrimp');
        break;
      case 'dropship_products':
        navigate('/dashboard-dropship');
        break;
      default:
        navigate('/dashboard-center');
    }
  };

  return (
    <>
      <DashboardHeader onViewChange={handleViewChange} selectedView="shrimp_products" />
      <main className="flex-1 overflow-x-hidden overflow-y-auto p-6 space-y-6">
        {/* Shrimp Dashboard Metrics Section */}
        <section className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Total Gross Sales */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Total Gross Sales</h3>
            <p className="text-3xl font-bold text-gray-900">{SHRIMP_DASHBOARD_METRICS.totalGrossSales}</p>
          </div>
          
          {/* Total Orders */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Total Orders</h3>
            <p className="text-3xl font-bold text-gray-900">{SHRIMP_DASHBOARD_METRICS.totalOrders}</p>
          </div>
          
          {/* Avg. Profit Margin */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Avg. Profit Margin</h3>
            <p className="text-3xl font-bold text-green-600">{SHRIMP_DASHBOARD_METRICS.avgProfitMargin}</p>
          </div>
          
          {/* Active Products */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Active Products</h3>
            <p className="text-3xl font-bold text-gray-900">{SHRIMP_DASHBOARD_METRICS.activeProducts}</p>
          </div>
        </section>

        {/* Intelligence Chart and Top Sellers Pie Chart Section */}
        <section className="grid grid-cols-1 lg:grid-cols-3 gap-6 items-stretch">
          <div className="lg:col-span-2">
            <IntelligenceChart data={SHRIMP_INTELLIGENCE_CHART_DATA} />
          </div>
          <div className="lg:col-span-1">
            <ShrimpTopSellersCard data={SHRIMP_TOP_SELLERS_DATA} />
          </div>
        </section>

        {/* Sales Over Time and Top Sellers Section */}
        <section className="grid grid-cols-1 lg:grid-cols-3 gap-6 items-stretch">
          <div className="lg:col-span-2">
            <SalesChart />
          </div>
          <div className="lg:col-span-1">
            <div className="bg-white p-6 rounded-lg shadow h-full">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Top Selling Shrimp Products</h2>
              <div className="space-y-4">
                {SHRIMP_TOP_SELLERS.map((product, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-900 text-sm">{product.name}</h3>
                      <p className="text-xs text-gray-500">{product.units} units</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">{product.sales}</p>
                      <p className="text-xs text-green-600">{product.growth}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Low Stock Alerts Section */}
        <section>
          <LowStockAlertsCard alerts={SHRIMP_LOW_STOCK_ALERTS} />
        </section>
      </main>
    </>
  );
};

export default ShrimpDashboard;