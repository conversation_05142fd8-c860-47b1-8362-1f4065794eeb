import React, { memo, useCallback, useState, useEffect, lazy, Suspense } from 'react';
import { OrderNote, CustomerHistoryItem } from '@/types';
import ResendConfirmationModal from '@/features/orders-features/order-actions/ResendConfirmationModal';
import AddNoteModal from '@/features/orders-features/order-actions/AddNoteModal';
import RefundModal from '@/features/orders-features/order-actions/RefundModal';
import CancelOrderModal from '@/features/orders-features/order-actions/CancelOrderModal';

// Imported hooks
import { useOrderData } from '@/shared/lib/hooks/orders/useOrderData';
import { useOrderActions } from '@/shared/lib/hooks/orders/useOrderActions';
import { useOrderFormatting } from '@/shared/lib/hooks/orders/useOrderFormatting';
import { useOrderPanel } from '@/shared/lib/hooks/orders/useOrderPanel';

// PERFORMANCE: Lazy load heavy components to reduce initial bundle size
const OrderDetailHeader = lazy(() => import('./components/OrderDetailHeader'));
const OrderBasicInfo = lazy(() => import('./components/OrderBasicInfo'));
const OrderItemsList = lazy(() => import('./components/OrderItemsList'));
const OrderActions = lazy(() => import('./components/OrderActions'));

interface OrderDetailPanelProps {
  orderId: string | null;
  isOpen: boolean;
  onClose: () => void;
  onGoToFulfill?: (orderId: string) => void;
  onOrderUpdate?: () => void;
}

// PERFORMANCE: Loading skeleton component
const LoadingSkeleton = memo(() => (
  <div className="animate-pulse">
    <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
    <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
    <div className="h-4 bg-gray-200 rounded w-2/3"></div>
  </div>
));

const OrderDetailPanelComponent: React.FC<OrderDetailPanelProps> = ({
  orderId,
  isOpen,
  onClose,
  onGoToFulfill,
  onOrderUpdate
}) => {
  // PERFORMANCE: Modal states for order actions
  const [isResendModalOpen, setIsResendModalOpen] = useState(false);
  const [isAddNoteModalOpen, setIsAddNoteModalOpen] = useState(false);
  const [isRefundModalOpen, setIsRefundModalOpen] = useState(false);
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);

  // Use hooks correctly
  const { selectedOrder, orderLoading, orderError, fetchOrder } = useOrderData();
  const { getStatusBadge, formatCurrency, formatDate } = useOrderFormatting();
  const { handleGoToFulfill } = useOrderPanel({ isOpen, onClose, onGoToFulfill });
  const orderActions = useOrderActions();

  // PERFORMANCE: Debounced fetch to prevent rapid API calls
  useEffect(() => {
    if (!orderId || !isOpen) return;
    
    const timeoutId = setTimeout(() => {
      fetchOrder(orderId);
    }, 50); // 50ms debounce

    return () => clearTimeout(timeoutId);
  }, [orderId, isOpen, fetchOrder]);

  // PERFORMANCE: Memoize action handlers with useCallback
  const handleResendConfirmation = useCallback(() => setIsResendModalOpen(true), []);
  const handleAddNote = useCallback(() => setIsAddNoteModalOpen(true), []);
  const handleProcessRefund = useCallback(() => setIsRefundModalOpen(true), []);
  const handleCancelOrder = useCallback(() => setIsCancelModalOpen(true), []);

  // PERFORMANCE: Memoize close handlers
  const closeResendModal = useCallback(() => setIsResendModalOpen(false), []);
  const closeAddNoteModal = useCallback(() => setIsAddNoteModalOpen(false), []);
  const closeRefundModal = useCallback(() => setIsRefundModalOpen(false), []);
  const closeCancelModal = useCallback(() => setIsCancelModalOpen(false), []);

  // PERFORMANCE: Memoize action confirmations
  const handleResendConfirm = useCallback(async () => {
    if (!selectedOrder) return;
    try {
      await orderActions.resendConfirmation(selectedOrder.id);
      setIsResendModalOpen(false);
      if (onOrderUpdate) onOrderUpdate();
    } catch (error) {
      console.error('Failed to resend confirmation:', error);
    }
  }, [selectedOrder, orderActions, onOrderUpdate]);

  const handleAddNoteConfirm = useCallback(async (content: string) => {
    if (!selectedOrder) return;
    try {
      await orderActions.addNote(selectedOrder.id, content);
      setIsAddNoteModalOpen(false);
      if (onOrderUpdate) onOrderUpdate();
    } catch (error) {
      console.error('Failed to add note:', error);
    }
  }, [selectedOrder, orderActions, onOrderUpdate]);

  const handleRefundConfirm = useCallback(async (amount: number, reason: string) => {
    if (!selectedOrder) return;
    try {
      await orderActions.refund(selectedOrder.id, amount, reason);
      setIsRefundModalOpen(false);
      if (onOrderUpdate) onOrderUpdate();
    } catch (error) {
      console.error('Failed to process refund:', error);
    }
  }, [selectedOrder, orderActions, onOrderUpdate]);

  const handleCancelConfirm = useCallback(async (reason: string) => {
    if (!selectedOrder) return;
    try {
      await orderActions.cancel(selectedOrder.id, reason);
      setIsCancelModalOpen(false);
      if (onOrderUpdate) onOrderUpdate();
    } catch (error) {
      console.error('Failed to cancel order:', error);
    }
  }, [selectedOrder, orderActions, onOrderUpdate]);

  // PERFORMANCE: Early return for closed panel - minimal DOM
  if (!isOpen) {
    return null;
  }

  return (
    <>
      {/* PERFORMANCE: Backdrop with will-change for GPU acceleration */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-20 z-40 transition-opacity duration-300 ease-in-out will-change-transform"
        onClick={onClose}
      />
      
      {/* PERFORMANCE: Panel with hardware acceleration */}
      <div className={`
        fixed right-0 top-0 h-full w-1/2 bg-white shadow-2xl z-50 
        transform transition-all duration-500 ease-out will-change-transform
        ${isOpen ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
      `}>
        {/* PERFORMANCE: Suspense boundaries for lazy-loaded components */}
        <Suspense fallback={<LoadingSkeleton />}>
          <OrderDetailHeader onClose={onClose} />
        </Suspense>

        {/* PERFORMANCE: Virtualized content area with contain layout */}
        <div className="h-full overflow-y-auto pb-40 scrollbar-hide" style={{ contain: 'layout style paint' }}>
          {orderLoading && (
            <div className="flex items-center justify-center h-64 animate-fade-in">
              <div className="flex items-center space-x-3">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <div className="text-gray-500">Loading order details...</div>
              </div>
            </div>
          )}

          {orderError && (
            <div className="flex items-center justify-center h-64 animate-fade-in">
              <div className="text-red-500">{orderError}</div>
            </div>
          )}

          {selectedOrder && (
            <div className="p-6 space-y-6 animate-slide-up">
              {/* PERFORMANCE: Lazy-loaded components with Suspense */}
              <Suspense fallback={<LoadingSkeleton />}>
                <OrderBasicInfo
                  order={selectedOrder}
                  getStatusBadge={getStatusBadge}
                  formatCurrency={formatCurrency}
                  formatDate={formatDate}
                />
              </Suspense>

              {/* Customer Information - Simple section, no lazy loading needed */}
              <section>
                <h4 className="text-lg font-medium text-gray-900 mb-3">Customer Information</h4>
                <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                  <div>
                    <span className="font-medium text-gray-500">Name:</span>
                    <p className="text-gray-900">{selectedOrder.customer.name}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-500">Email:</span>
                    <p className="text-gray-900">{selectedOrder.customer.email}</p>
                  </div>
                  {selectedOrder.customer.phone && (
                    <div>
                      <span className="font-medium text-gray-500">Phone:</span>
                      <p className="text-gray-900">{selectedOrder.customer.phone}</p>
                    </div>
                  )}
                  <div>
                    <span className="font-medium text-gray-500">Address:</span>
                    <p className="text-gray-900">
                      {selectedOrder.customer.address.street}<br />
                      {selectedOrder.customer.address.city}, {selectedOrder.customer.address.state},{selectedOrder.customer.address.zipCode}<br />
                      {selectedOrder.customer.address.country}
                    </p>
                  </div>
                </div>
              </section>

              {/* PERFORMANCE: Lazy-loaded order items with virtualization */}
              <Suspense fallback={<LoadingSkeleton />}>
                <OrderItemsList
                  order={selectedOrder}
                  formatCurrency={formatCurrency}
                />
              </Suspense>

              {/* Shipping Information - Simple section */}
              <section>
                <h4 className="text-lg font-medium text-gray-900 mb-3">Shipping Information</h4>
                <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                  <div>
                    <span className="font-medium text-gray-500">Method:</span>
                    <p className="text-gray-900">{selectedOrder.shippingMethod}</p>
                  </div>
                  {selectedOrder.expectedDelivery && (
                    <div>
                      <span className="font-medium text-gray-500">Expected Delivery:</span>
                      <p className="text-gray-900">{formatDate(selectedOrder.expectedDelivery)}</p>
                    </div>
                  )}
                  <div>
                    <span className="font-medium text-gray-500">Shipping Address:</span>
                    <p className="text-gray-900">
                      {selectedOrder.shippingAddress.street}<br />
                      {selectedOrder.shippingAddress.city}, {selectedOrder.shippingAddress.state}, {selectedOrder.shippingAddress.zipCode}<br />
                      {selectedOrder.shippingAddress.country}
                    </p>
                  </div>
                </div>
              </section>

              {/* PERFORMANCE: Conditional rendering for optional sections */}
              {selectedOrder.notes.length > 0 && (
                <section>
                  <h4 className="text-lg font-medium text-gray-900 mb-3">Order Notes</h4>
                  <div className="space-y-3 max-h-48 overflow-y-auto">
                    {selectedOrder.notes.map((note: OrderNote) => (
                      <div key={note.id} className="bg-gray-50 rounded-lg p-4">
                        <p className="text-gray-700">{note.content}</p>
                      </div>
                    ))}
                  </div>
                </section>
              )}

              {selectedOrder.customerHistory.length > 0 && (
                <section>
                  <h4 className="text-lg font-medium text-gray-900 mb-3">Customer History</h4>
                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    {selectedOrder.customerHistory.map((historyItem: CustomerHistoryItem) => (
                      <div key={historyItem.orderId} className="bg-gray-50 rounded-lg p-3">
                        <div className="flex justify-between items-center">
                          <div>
                            <span className="font-medium text-gray-900">{historyItem.orderNumber}</span>
                            <span className="text-sm text-gray-500 ml-2">{formatDate(historyItem.date)}</span>
                          </div>
                          <div className="text-right">
                            <p className="font-medium text-gray-900">{formatCurrency(historyItem.totalAmount)}</p>
                            <p className="text-sm text-gray-500">{historyItem.status}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </section>
              )}
            </div>
          )}
        </div>

        {/* PERFORMANCE: Lazy-loaded actions section */}
        {selectedOrder && (
          <Suspense fallback={<div className="h-32 bg-gray-100 animate-pulse"></div>}>
            <OrderActions
              order={selectedOrder}
              onResendConfirmation={handleResendConfirmation}
              onAddNote={handleAddNote}
              onProcessRefund={handleProcessRefund}
              onCancelOrder={handleCancelOrder}
              onGoToFulfill={handleGoToFulfill}
              onClose={onClose}
            />
          </Suspense>
        )}
      </div>

      {/* PERFORMANCE: Conditional modal rendering to reduce DOM size */}
      {selectedOrder && isResendModalOpen && (
        <ResendConfirmationModal
          isOpen={isResendModalOpen}
          onClose={closeResendModal}
          onConfirm={handleResendConfirm}
          orderNumber={selectedOrder.orderNumber}
          customerEmail={selectedOrder.customer.email}
          loading={orderActions.loading}
        />
      )}

      {selectedOrder && isAddNoteModalOpen && (
        <AddNoteModal
          isOpen={isAddNoteModalOpen}
          onClose={closeAddNoteModal}
          onConfirm={handleAddNoteConfirm}
          orderNumber={selectedOrder.orderNumber}
          loading={orderActions.loading}
        />
      )}

      {selectedOrder && isRefundModalOpen && (
        <RefundModal
          isOpen={isRefundModalOpen}
          onClose={closeRefundModal}
          onConfirm={handleRefundConfirm}
          orderNumber={selectedOrder.orderNumber}
          totalAmount={selectedOrder.totalAmount}
          loading={orderActions.loading}
        />
      )}

      {selectedOrder && isCancelModalOpen && (
        <CancelOrderModal
          isOpen={isCancelModalOpen}
          onClose={closeCancelModal}
          onConfirm={handleCancelConfirm}
          orderNumber={selectedOrder.orderNumber}
          loading={orderActions.loading}
        />
      )}
    </>
  );
};

// PERFORMANCE: Ultra-aggressive memoization with minimal prop comparison
const OrderDetailPanel = memo(OrderDetailPanelComponent, (prevProps, nextProps) => {
  // Only re-render if essential props change
  return (
    prevProps.orderId === nextProps.orderId &&
    prevProps.isOpen === nextProps.isOpen &&
    prevProps.onClose === nextProps.onClose &&
    prevProps.onGoToFulfill === nextProps.onGoToFulfill &&
    prevProps.onOrderUpdate === nextProps.onOrderUpdate
  );
});

OrderDetailPanel.displayName = 'OrderDetailPanel';

export default OrderDetailPanel; 