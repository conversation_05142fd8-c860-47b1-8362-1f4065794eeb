
import React, { memo, useMemo } from 'react';
import { DeliveryOrderItem } from '@/types/index';

interface DeliveryInfoCardProps {
  title: string;
  orders: DeliveryOrderItem[];
  icon?: React.FC<React.SVGProps<SVGSVGElement>>; // Type from 'react' module still used
  cardClassName?: string;
  titleClassName?: string;
}

// Move these utility functions outside component to prevent recreation
  const getStatusColor = (status?: string) => {
    switch (status?.toLowerCase()) {
      case 'in transit':
        return 'text-blue-600 bg-blue-100';
      case 'out for delivery':
      return 'text-orange-600 bg-orange-100';
      case 'delivered':
        return 'text-green-600 bg-green-100';
      case 'damaged':
      return 'text-amber-600 bg-amber-100';
      case 'lost':
        return 'text-red-600 bg-red-100';
    default:
        return 'text-gray-600 bg-gray-100';
    }
  };
  
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };
  
   const formatUpdateDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
     return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
  };

const DeliveryInfoCard: React.FC<DeliveryInfoCardProps> = ({ 
    title, 
    orders, 
    icon: IconComponent, 
    cardClassName = '',
    titleClassName = 'text-gray-800'
}) => {
  // Memoized empty message to prevent recalculation
  const emptyMessage = useMemo(() => 
    `No orders in ${title.toLowerCase()}.`, 
    [title]
  );


  return (
    <div className={`bg-card-bg p-6 rounded-lg shadow w-full ${cardClassName}`}>
      <div className="flex items-center mb-4">
        {IconComponent && <IconComponent className={`w-6 h-6 mr-2 ${titleClassName}`} />}
        <h3 className={`text-xl font-semibold ${titleClassName}`}>{title}</h3>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order Date</th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tracking ID</th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Destination</th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {orders.map((order: DeliveryOrderItem) => (
              <tr key={order.id}>
                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-700">{order.id}</td>
                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-700">{order.name}</td>
                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-700">{formatDate(order.date)}</td>
                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-700">
                  <a 
                    href={`#`} // Placeholder - replace with actual tracking link provider logic
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-primary hover:text-primary-dark hover:underline"
                    title={`Track order ${order.trackingId}`}
                  >
                    {order.trackingId}
                  </a>
                </td>
                <td className="px-4 py-3 whitespace-nowrap text-sm">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(order.status)}`}>
                    {order.status || 'Unknown'}
                  </span>
                </td>
                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-700">{order.destinationState}</td>
                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-700">{formatUpdateDate(order.updateDate)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {orders.length === 0 && (
        <p className="text-center text-gray-500 py-4">{emptyMessage}</p>
      )}
    </div>
  );
};

export default memo(DeliveryInfoCard);