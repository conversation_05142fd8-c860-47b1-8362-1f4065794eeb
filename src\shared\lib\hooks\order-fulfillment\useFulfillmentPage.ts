import { useState, useEffect, useCallback } from 'react';
import { OrderStatus } from '@/types';
import { FulfillOrderItem } from '@/types';
import { PackingStationSummaryItem, FulfillPackingListItem } from '@/shared/api/orders/ordersAPI_types';
import { 
  updateOrderStatus, 
  updateOrderTracking,
  getFulfillPackingList,
  getPackingStationSummary,
  bulkAssignTrackingNumbers
} from '@/shared/lib/services/fulfill/fulfill-service';
import { useAuth } from '@/app/providers/AuthProvider';
import { useOrderDataProcessing } from './useOrderDataProcessing';
import { processAndAssignTrackingData } from '@/shared/lib/services/fulfill/csv-processing-service';

export interface UseFulfillmentPageReturn {
  // Data
  packingList: FulfillPackingListItem[];
  packingSummary: PackingStationSummaryItem[];
  productBatches: any[];
  packedOrders: FulfillOrderItem[];
  openOrderCount: number;
  readyToShipCount: number;
  
  // UI State
  activeView: 'packing' | 'label';
  setActiveView: (view: 'packing' | 'label') => void;
  isLoading: boolean;
  error: string | null;
  isUploading: boolean;
  ordersForTracking: FulfillOrderItem[] | null;
  
  // Actions
  refreshFulfillData: () => Promise<void>;
  handleStatusChange: (orderId: string, newStatus: OrderStatus, userId?: string, trackingId?: string) => Promise<void>;
  handleCsvDataLoaded: (data: { order_id: string; tracking_number: string; carrier_id?: string; carrier_name?: string }[]) => Promise<void>;
  handleOpenTrackingModal: (orders: FulfillOrderItem[]) => void;
  handleCloseTrackingModal: () => void;
  handleAssignmentSuccess: () => void;
  clearError: () => void;
}

export function useFulfillmentPage(): UseFulfillmentPageReturn {
  const { user } = useAuth();
  const [packingList, setPackingList] = useState<FulfillPackingListItem[]>([]);
  const [packingSummary, setPackingSummary] = useState<PackingStationSummaryItem[]>([]);
  const [activeView, setActiveView] = useState<'packing' | 'label'>('packing');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [ordersForTracking, setOrdersForTracking] = useState<FulfillOrderItem[] | null>(null);

  // Use our data processing hook for computed values
  const { productBatches, packedOrders, openOrderCount, readyToShipCount } = useOrderDataProcessing(packingList);

  // Load initial data
  useEffect(() => {
    const loadFulfillData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        // Load real data from API - only use fulfill_packing_list since it has everything we need
        const [
          packingListData,
          packingSummaryData
        ] = await Promise.all([
          getFulfillPackingList(),   // detailed packing list view - contains all order info
          getPackingStationSummary() // product summary view - for stock info
        ]);

        // Set the packing list and summary data
        setPackingList(packingListData);
        setPackingSummary(packingSummaryData);
        
      } catch (err) {
        setError('Failed to load fulfill data. Please try again.');
        console.error('Error loading fulfill data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadFulfillData();
  }, []);

  const refreshFulfillData = useCallback(async () => {
    try {
      const [
        updatedPackingList,
        updatedPackingSummary
      ] = await Promise.all([
        getFulfillPackingList(),
        getPackingStationSummary()
      ]);

      setPackingList(updatedPackingList);
      setPackingSummary(updatedPackingSummary);
    } catch (error) {
      console.error('Failed to refresh fulfill data:', error);
      setError('Failed to refresh data. Please try again.');
    }
  }, []);

  const handleStatusChange = useCallback(async (orderId: string, newStatus: OrderStatus, userId?: string, trackingId?: string) => {
    try {
      // Get a valid userId - use provided, user.id, or default UUID
      const validUserId = userId || user?.id || '00000000-0000-0000-0000-000000000000';
      
      // Call real API
      await updateOrderStatus(orderId, newStatus, validUserId);
      if (trackingId) {
        await updateOrderTracking(orderId, trackingId, validUserId);
      }

      // Reload the fulfill data to get updated state
      await refreshFulfillData();

    } catch (error) {
      setError('Failed to update order status. Please try again.');
      console.error('Error updating order status:', error);
    }
  }, [user, refreshFulfillData]);

  const handleCsvDataLoaded = useCallback(async (data: { order_id: string; tracking_number: string; carrier_id?: string; carrier_name?: string }[]) => {
    setIsUploading(true);
    setError(null);
    try {
      // Process the tracking data with our new service
      const result = await processAndAssignTrackingData(data);
      
      // If successful, refresh the data to show the changes
      if (result.success) {
        await refreshFulfillData();
      }
    } catch (err) {
      const message = err instanceof Error ? err.message : 'An unknown error occurred during CSV processing.';
      setError(message);
      console.error('Error processing CSV upload:', err);
    } finally {
      setIsUploading(false);
    }
  }, [refreshFulfillData]);

  const handleOpenTrackingModal = useCallback((orders: FulfillOrderItem[]) => {
    setOrdersForTracking(orders);
  }, []);

  const handleCloseTrackingModal = useCallback(() => {
    setOrdersForTracking(null);
  }, []);

  const handleAssignmentSuccess = useCallback(() => {
    handleCloseTrackingModal();
    refreshFulfillData();
  }, [handleCloseTrackingModal, refreshFulfillData]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // Data
    packingList,
    packingSummary,
    productBatches,
    packedOrders,
    openOrderCount,
    readyToShipCount,
    
    // UI State
    activeView,
    setActiveView,
    isLoading,
    error,
    isUploading,
    ordersForTracking,
    
    // Actions
    refreshFulfillData,
    handleStatusChange,
    handleCsvDataLoaded,
    handleOpenTrackingModal,
    handleCloseTrackingModal,
    handleAssignmentSuccess,
    clearError
  };
} 