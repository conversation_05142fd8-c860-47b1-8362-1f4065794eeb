import { AllOrdersDetail } from '@/types';
import { 
  getOrders,
  fetchOrderById,
  FetchOrdersResponse
} from '@/shared/lib/services/order/live-order-service';
import { sortOrdersByPriority } from '@/shared/lib/utils/domain/orders/orders-table-helpers';

/**
 * Shared utilities for order hooks to prevent circular dependencies
 */

// Simple cache implementation
interface Cache {
  data: Record<string, { 
    response: FetchOrdersResponse;
    timestamp: number;
  }>;
  orderDetails: Record<string, {
    order: AllOrdersDetail;
    timestamp: number;
  }>;
  ttl: number; // Time to live in milliseconds
}

// Initialize cache with shorter TTL for more frequent refreshes
const cache: Cache = {
  data: {},
  orderDetails: {},
  ttl: 30 * 1000 // 30 seconds (reduced from 2 minutes)
};

// Generate cache key from filters
const generateCacheKey = (filters: any): string => {
  const { page = 1, limit = 20, search = '', ...rest } = filters;
  return JSON.stringify({ page, limit, search, ...rest });
};

// Check if cache is valid
const isCacheValid = (timestamp: number): boolean => {
  return Date.now() - timestamp < cache.ttl;
};

// Common fetch orders function that can be used by multiple hooks
export const fetchOrdersData = async (
  filtersToUse: any, 
  setLoading?: (loading: boolean) => void,
  setError?: (error: string | null) => void,
  setOrdersData?: (data: FetchOrdersResponse) => void
) => {
  try {
    if (setLoading) setLoading(true);
    if (setError) setError(null);

    // Generate cache key
    const cacheKey = generateCacheKey(filtersToUse);
    
    // Skip cache on WebSocket refresh to ensure fresh data
    const skipCache = filtersToUse.skipCache === true;
    
    // Check cache first (unless skipCache is true)
    const cachedData = cache.data[cacheKey];
    if (!skipCache && cachedData && isCacheValid(cachedData.timestamp)) {
      if (setOrdersData) setOrdersData(cachedData.response);
      if (setLoading) setLoading(false);
      return cachedData.response;
    }

    const page = filtersToUse.page || 1;
    const limit = filtersToUse.limit || 20;
    const search = filtersToUse.search || '';

    // Extract only OrderFilters from ExtendedOrderFilters
    const { page: _, limit: __, search: ___, skipCache: ____, ...orderFilters } = filtersToUse;

    // API call with optimized parameters
    const response = await getOrders({
      ...orderFilters,
      page,
      pageSize: limit,
      searchTerm: search
    });
    
    // Sort orders by business logic priority
    const sortedOrders = sortOrdersByPriority(response.orders);

    // Metadata computation
    const totalPages = Math.ceil(response.totalCount / limit);
    const enhancedResponse = {
      ...response,
      orders: sortedOrders,
      currentPage: page,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1
    };
    
    // Update cache
    cache.data[cacheKey] = {
      response: enhancedResponse,
      timestamp: Date.now()
    };
    
    if (setOrdersData) setOrdersData(enhancedResponse);
    return enhancedResponse;
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Failed to load orders';
    if (setError) setError(errorMessage);
    console.error('Error fetching orders:', err);
    throw err;
  } finally {
    if (setLoading) setLoading(false);
  }
};

// Common fetch single order function
export const fetchOrderDetailsData = async (
  orderId: string,
  setOrderLoading?: (loading: boolean) => void,
  setOrderError?: (error: string | null) => void,
  setSelectedOrder?: (order: AllOrdersDetail | null) => void
) => {
  try {
    if (setOrderLoading) setOrderLoading(true);
    if (setOrderError) setOrderError(null);
    
    // Check cache first
    const cachedOrder = cache.orderDetails[orderId];
    if (cachedOrder && isCacheValid(cachedOrder.timestamp)) {
      if (setSelectedOrder) setSelectedOrder(cachedOrder.order);
      if (setOrderLoading) setOrderLoading(false);
      return cachedOrder.order;
    }
    
    const order = await fetchOrderById(orderId);
    
    // Update cache
    if (order) {
      cache.orderDetails[orderId] = {
        order,
        timestamp: Date.now()
      };
    }
    
    if (setSelectedOrder) setSelectedOrder(order);
    return order;
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Failed to fetch order';
    if (setOrderError) setOrderError(errorMessage);
    console.error('Error fetching order:', err);
    throw err;
  } finally {
    if (setOrderLoading) setOrderLoading(false);
  }
}; 