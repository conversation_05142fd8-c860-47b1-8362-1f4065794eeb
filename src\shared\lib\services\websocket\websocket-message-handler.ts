import { WebSocketMessage, WebSocketState, WebSocketEventHandler, WebSocketStateHandler } from './websocket-config';

export class WebSocketMessageHandler {
  private eventListeners: Map<string, Set<WebSocketEventHandler>> = new Map();
  private stateListeners: Set<WebSocketStateHandler> = new Set();
  private isDestroyed = false;

  /**
   * Register an event handler for a specific event type
   */
  public on(eventType: string, handler: WebSocketEventHandler): void {
    if (this.isDestroyed) {
      return;
    }

    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set());
    }

    this.eventListeners.get(eventType)!.add(handler);
  }

  /**
   * Unregister an event handler
   */
  public off(eventType: string, handler: WebSocketEventHandler): void {
    if (!this.eventListeners.has(eventType)) {
      return;
    }

    this.eventListeners.get(eventType)!.delete(handler);

    // Clean up empty listener sets
    if (this.eventListeners.get(eventType)!.size === 0) {
      this.eventListeners.delete(eventType);
    }
  }

  /**
   * Register a state change handler
   */
  public onStateChange(handler: WebSocketStateHandler): void {
    if (this.isDestroyed) {
      return;
    }

    this.stateListeners.add(handler);
  }

  /**
   * Handles incoming WebSocket messages and dispatches to subscribers
   */
  public handleIncomingMessage(message: WebSocketMessage): void {
    if (!message || !message.type) {
      return; // Ignore invalid messages
    }
    
    // Get handlers for this message type
    const handlers = this.eventListeners.get(message.type);
    if (!handlers || handlers.size === 0) {
      return; // No handlers registered for this event
    }
    
    // Call all handlers for this event type
    handlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error(`Error in WebSocket message handler for ${message.type}:`, error);
      }
    });
  }

  /**
   * Notify all state change handlers of a state update
   */
  public notifyStateChange(state: WebSocketState): void {
    if (this.isDestroyed) {
      return;
    }

    this.stateListeners.forEach(handler => {
      try {
        handler(state);
      } catch (error) {
        console.error('Error in state change handler');
      }
    });
  }

  /**
   * Get all registered event types
   */
  public getRegisteredEventTypes(): string[] {
    return Array.from(this.eventListeners.keys());
  }

  /**
   * Destroy the message handler
   */
  public destroy(): void {
    this.isDestroyed = true;
    this.eventListeners.clear();
    this.stateListeners.clear();
  }
} 