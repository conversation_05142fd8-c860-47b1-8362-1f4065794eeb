import { renderHook, act } from '@testing-library/react';
import { useStockAdjustment } from '../useStockAdjustment';
import inventoryStockService from '@/shared/lib/services/inventory/inventory-stock-service';
import { InventoryReasonCategory, InventoryMovementType } from '@/types';

// Mock the inventory stock service
jest.mock('@/shared/lib/services/inventory', () => ({
  inventoryStockService: {
    adjustStock: jest.fn(),
    getProductMovements: jest.fn()
  }
}));

// Mock the auth context
jest.mock('@/app/providers/AuthContext', () => ({
  useAuth: () => ({
    user: { id: 'test-user-id' },
    profile: { full_name: 'Test User' }
  })
}));

// Mock the bulk stock adjustment hook
jest.mock('../useBulkStockAdjustment', () => ({
  useBulkStockAdjustment: () => ({
    isModalOpen: false,
    isUpdating: false,
    error: null,
    openModal: jest.fn(),
    closeModal: jest.fn(),
    updateStock: jest.fn().mockImplementation(async (data) => {
      // This mock will be overridden in specific tests
      return Promise.resolve();
    })
  })
}));

describe('useStockAdjustment Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default mock implementation for getProductMovements
    (inventoryStockService.getProductMovements as jest.Mock).mockResolvedValue([]);
  });

  test('should fetch movements when opening modal', async () => {
    const { result } = renderHook(() => useStockAdjustment());
    
    // Call openModal
    act(() => {
      result.current.openModal('product-1', 'Test Product', 10);
    });
    
    // Verify product details are set
    expect(result.current.selectedProductId).toBe('product-1');
    expect(result.current.selectedProductName).toBe('Test Product');
    expect(result.current.currentStock).toBe(10);
    
    // Verify movements were fetched
    expect(inventoryStockService.getProductMovements).toHaveBeenCalledWith(
      'product-1',
      expect.objectContaining({
        limit: 10,
        sortBy: 'timestamp',
        sortDirection: 'desc'
      })
    );
  });

  test('should add optimistic movement when updating stock', async () => {
    // Setup mock for bulk update
    const mockUpdateStock = jest.fn().mockResolvedValue({});
    jest.mock('../useBulkStockAdjustment', () => ({
      useBulkStockAdjustment: () => ({
        isModalOpen: false,
        isUpdating: false,
        error: null,
        openModal: jest.fn(),
        closeModal: jest.fn(),
        updateStock: mockUpdateStock
      })
    }));
    
    const { result } = renderHook(() => useStockAdjustment());
    
    // Set up initial state
    act(() => {
      result.current.openModal('product-1', 'Test Product', 10);
    });
    
    // Update stock
    await act(async () => {
      await result.current.updateStock({
        productId: 'product-1',
        adjustmentType: 'increase',
        quantity: 5,
        reason: 'Test update',
        reasonCategory: 'inventory_count' as InventoryReasonCategory,
        notes: 'Test notes'
      });
    });
    
    // Verify optimistic movement was added
    expect(result.current.movements[0]).toMatchObject({
      product_id: 'product-1',
      type: 'increase',
      quantity: 5,
      previous_stock: 10,
      new_stock: 15,
      reason: 'Test update',
      reason_category: 'inventory_count'
    });
  });

  test('should handle server errors during stock update', async () => {
    // Setup mock for bulk update to throw error
    const mockError = new Error('Server error');
    const mockUpdateStock = jest.fn().mockRejectedValue(mockError);
    const mockOnError = jest.fn();
    
    // Override the mock implementation
    require('../useBulkStockAdjustment').useBulkStockAdjustment = () => ({
      isModalOpen: false,
      isUpdating: false,
      error: null,
      openModal: jest.fn(),
      closeModal: jest.fn(),
      updateStock: mockUpdateStock
    });
    
    const { result } = renderHook(() => useStockAdjustment({
      onError: mockOnError
    }));
    
    // Set up initial state
    act(() => {
      result.current.openModal('product-1', 'Test Product', 10);
    });
    
    // Attempt to update stock
    await act(async () => {
      try {
        await result.current.updateStock({
          productId: 'product-1',
          adjustmentType: 'increase',
          quantity: 5,
          reason: 'Test update',
          reasonCategory: 'inventory_count' as InventoryReasonCategory
        });
      } catch (err) {
        // Expected to throw
      }
    });
    
    // Verify error callback was called
    expect(mockOnError).toHaveBeenCalledWith(mockError);
    
    // Verify optimistic movement was still added (needs to be rolled back in UI)
    expect(result.current.movements[0]).toMatchObject({
      product_id: 'product-1',
      type: 'increase',
      quantity: 5
    });
  });

  test('should correctly sync between single and bulk operations', async () => {
    // Setup spy for bulk update
    const mockBulkUpdate = jest.fn().mockResolvedValue({});
    
    // Override the mock implementation
    require('../useBulkStockAdjustment').useBulkStockAdjustment = () => ({
      isModalOpen: false,
      isUpdating: false,
      error: null,
      openModal: jest.fn(),
      closeModal: jest.fn(),
      updateStock: mockBulkUpdate
    });
    
    const { result } = renderHook(() => useStockAdjustment());
    
    // Set up initial state
    act(() => {
      result.current.openModal('product-1', 'Test Product', 10);
    });
    
    // Update stock with single product
    await act(async () => {
      await result.current.updateStock({
        productId: 'product-1',
        adjustmentType: 'increase',
        quantity: 5,
        reason: 'Test update',
        reasonCategory: 'inventory_count' as InventoryReasonCategory
      });
    });
    
    // Verify bulk update was called with correct parameters
    expect(mockBulkUpdate).toHaveBeenCalledWith({
      productIds: ['product-1'],
      adjustmentType: 'increase',
      quantity: 5,
      reason: 'Test update',
      reasonCategory: 'inventory_count',
      notes: undefined
    });
  });

  test('should handle optimistic updates with onOptimisticUpdate callback', async () => {
    // Setup mock callback
    const mockOptimisticUpdate = jest.fn();
    
    const { result } = renderHook(() => useStockAdjustment({
      onOptimisticUpdate: mockOptimisticUpdate
    }));
    
    // Set up initial state
    act(() => {
      result.current.openModal('product-1', 'Test Product', 10);
    });
    
    // Update stock
    await act(async () => {
      await result.current.updateStock({
        productId: 'product-1',
        adjustmentType: 'increase',
        quantity: 5,
        reason: 'Test update',
        reasonCategory: 'inventory_count' as InventoryReasonCategory
      });
    });
    
    // Verify optimistic update callback was called
    expect(mockOptimisticUpdate).toHaveBeenCalledWith(
      'product-1',
      'increase' as InventoryMovementType,
      5
    );
  });
}); 