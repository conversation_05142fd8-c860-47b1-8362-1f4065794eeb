// @ts-nocheck
// deno-lint-ignore-file

import { serve } from "https://deno.land/std@0.177.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.12.0"
import { corsHeaders } from "../_shared/cors.ts"

// Type definitions for safe error handling
type ErrorWithMessage = { message: string };
function isErrorWithMessage(error: unknown): error is ErrorWithMessage {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof (error as Record<string, unknown>).message === 'string'
  );
}

function getErrorMessage(error: unknown): string {
  if (isErrorWithMessage(error)) return error.message;
  return String(error);
}

interface OrderItem {
  product_identifier: string;
  quantity: number;
  subtotal_item: number; // Price per item
}

interface BulkOrderRequest {
  order_number: string;
  platform: string;
  channel: string;
  customer_email: string;
  customer_name: string;
  shipping_street: string;
  shipping_city: string;
  shipping_state: string;
  shipping_zip_code: string;
  shipping_country: string;
  order_items: OrderItem[];
  _row_indices: number[];
}

interface BulkOrderResponse {
  successful: {
    order_id: string;
    order_number: string;
  }[];
  failed: {
    row: number;
    reason: string;
    data: any;
  }[];
}

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Parse the request body
    const { orders } = await req.json();
    if (!Array.isArray(orders) || orders.length === 0) {
      throw new Error('Invalid request: orders array is required');
    }

    console.log(`Processing ${orders.length} orders`);

    // Create Supabase client using SERVICE_ROLE key for privileged operations
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      { global: { headers: { Authorization: req.headers.get('Authorization') || '' } } }
    );

    // Call the database function to process all orders in a single call
    const { data, error } = await supabaseClient.rpc('bulk_create_order', {
      p_orders: orders
    });

    if (error) {
      console.error('Database function error:', error);
      throw new Error(`Failed to process orders: ${error.message}`);
    }

    console.log(`Processed orders successfully:`, 
      data?.successful?.length || 0, 'successful,', 
      data?.failed?.length || 0, 'failed');

    // Return the response with proper CORS headers
    return new Response(
      JSON.stringify(data),
      { 
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    );
  } catch (err) {
    const errorMessage = getErrorMessage(err);
    console.error('Bulk order creation error:', errorMessage);
    
    return new Response(
      JSON.stringify({ 
        error: errorMessage,
        successful: [],
        failed: [{
          row: -1,
          reason: errorMessage,
          data: { message: 'General processing error' }
        }]
      }),
      { 
        status: 400, 
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    );
  }
}); 