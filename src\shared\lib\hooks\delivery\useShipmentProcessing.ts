import { useMemo } from 'react';
import { DeliveryDetails, DeliveryStatusEnum, DeliveryEvent } from '@/types';
import {
  TruckIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  ArrowUturnLeftIcon,
  TagIcon,
  FaceFrownIcon
} from '@heroicons/react/24/outline';
import {
  CheckCircleIcon as CheckCircleIconSolid,
  TruckIcon as TruckIconSolid,
  ClockIcon as ClockIconSolid,
  XCircleIcon as XCircleIconSolid,
  ArrowUturnLeftIcon as ArrowUturnLeftIconSolid,
  TagIcon as TagIconSolid
} from '@heroicons/react/24/solid';

// Helper function to get status icon and color
const getStatusIcon = (status: DeliveryStatusEnum) => {
  switch (status) {
    case 'delivered':
      return { icon: CheckCircleIcon, color: 'text-green-600' };
    case 'label_created':
      return { icon: TagIcon, color: 'text-indigo-600' };
    case 'in_transit':
      return { icon: TruckIcon, color: 'text-blue-600' };
    case 'out_for_delivery':
      return { icon: TruckIcon, color: 'text-purple-600' };
    case 'delayed':
      return { icon: ClockIcon, color: 'text-yellow-600' };
    case 'lost':
      return { icon: XCircleIcon, color: 'text-red-600' };
    case 'exception':
      return { icon: ExclamationTriangleIcon, color: 'text-orange-600' };
    case 'returned':
      return { icon: ArrowUturnLeftIcon, color: 'text-gray-600' };
    default:
      return { icon: TruckIcon, color: 'text-gray-600' };
  }
};

// Helper function to get status styling for timeline events  
const getEventStyling = (isCurrentEvent: boolean, shipmentStatus: DeliveryStatusEnum) => {
  if (!isCurrentEvent) {
    return {
      dotClass: 'relative z-10 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3 h-3 rounded-full border-2 border-gray-400 bg-white',
      icon: null
    };
  }
  
  // Current event styling with larger size, icons, and solid glow animation
  const baseCurrentClass = 'w-6 h-6 rounded-full flex items-center justify-center animate-pulse shadow-lg';

  switch (shipmentStatus) {
    case 'delivered':
      return {
        dotClass: `${baseCurrentClass} bg-green-600`,
        wrapperClass: 'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white flex items-center justify-center',
        glowClass: 'absolute inset-0 rounded-full bg-green-400 opacity-60 animate-ping',
        icon: CheckCircleIconSolid,
        iconClass: 'w-4 h-4 text-white z-10 relative'
      };
    case 'label_created':
      return {
        dotClass: `${baseCurrentClass} bg-indigo-600`,
        wrapperClass: 'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white flex items-center justify-center',
        glowClass: 'absolute inset-0 rounded-full bg-indigo-400 opacity-60 animate-ping',
        icon: TagIconSolid,
        iconClass: 'w-4 h-4 text-white z-10 relative'
      };
    case 'out_for_delivery':
      return {
        dotClass: `${baseCurrentClass} bg-purple-600`,
        wrapperClass: 'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white flex items-center justify-center',
        glowClass: 'absolute inset-0 rounded-full bg-purple-400 opacity-60 animate-ping',
        icon: TruckIconSolid,
        iconClass: 'w-4 h-4 text-white z-10 relative'
      };
    case 'exception':
      return {
        dotClass: `${baseCurrentClass} bg-red-500`,
        wrapperClass: 'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white flex items-center justify-center',
        glowClass: 'absolute inset-0 rounded-full bg-red-300 opacity-60 animate-ping',
        icon: FaceFrownIcon,
        iconClass: 'w-4 h-4 text-white z-10 relative'
      };
    case 'delayed':
      return {
        dotClass: `${baseCurrentClass} bg-yellow-500`,
        wrapperClass: 'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white flex items-center justify-center',
        glowClass: 'absolute inset-0 rounded-full bg-yellow-300 opacity-60 animate-ping',
        icon: ClockIconSolid,
        iconClass: 'w-4 h-4 text-white z-10 relative'
      };
    case 'lost':
      return {
        dotClass: `${baseCurrentClass} bg-red-600`,
        wrapperClass: 'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white flex items-center justify-center',
        glowClass: 'absolute inset-0 rounded-full bg-red-400 opacity-60 animate-ping',
        icon: XCircleIconSolid,
        iconClass: 'w-4 h-4 text-white z-10 relative'
      };
    case 'returned':
      return {
        dotClass: `${baseCurrentClass} bg-gray-600`,
        wrapperClass: 'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white flex items-center justify-center',
        glowClass: 'absolute inset-0 rounded-full bg-gray-400 opacity-60 animate-ping',
        icon: ArrowUturnLeftIconSolid,
        iconClass: 'w-4 h-4 text-white z-10 relative'
      };
    default: // IN_TRANSIT
      return {
        dotClass: `${baseCurrentClass} bg-blue-600`,
        wrapperClass: 'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white flex items-center justify-center',
        glowClass: 'absolute inset-0 rounded-full bg-blue-400 opacity-60 animate-ping',
        icon: TruckIconSolid,
        iconClass: 'w-4 h-4 text-white z-10 relative'
      };
  }
};

// Refactored from a custom hook to a pure calculation function to avoid breaking rules of hooks.
const calculateTimeMetrics = (shipment: DeliveryDetails) => {
  const now = new Date();
  const shipDate = new Date(shipment.ship_date);
  const estimatedDelivery = shipment.estimated_delivery ? new Date(shipment.estimated_delivery) : now;
  
  const daysAfterFirstEvent = Math.floor((now.getTime() - shipDate.getTime()) / (1000 * 60 * 60 * 24));
  const daysInTransit = Math.floor((now.getTime() - shipDate.getTime()) / (1000 * 60 * 60 * 24));
  const daysToDelivery = Math.floor((estimatedDelivery.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
  
  return {
    daysAfterFirstEvent,
    daysInTransit,
    daysToDelivery: daysToDelivery > 0 ? daysToDelivery : 0,
    totalTransitTime: daysInTransit
  };
};

const capitalizeStatus = (status: string = '') => {
  return status.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
};

const formatDate = (dateStr: string | null) => {
  if (!dateStr) return 'N/A';
  return new Date(dateStr).toLocaleDateString();
};

interface ProcessedEvent extends DeliveryEvent {
  date: string;
  time: string;
}

interface UseShipmentProcessingProps {
  shipment: DeliveryDetails | undefined;
  showFullHistory: boolean;
}

interface UseShipmentProcessingReturn {
  statusIcon: { icon: React.ComponentType<any>; color: string };
  timeMetrics: ReturnType<typeof calculateTimeMetrics> | null;
  processedEvents: ProcessedEvent[];
  visibleEvents: ProcessedEvent[];
  getEventStyling: typeof getEventStyling;
  capitalizeStatus: typeof capitalizeStatus;
  formatDate: typeof formatDate;
}

export const useShipmentProcessing = ({ 
  shipment, 
  showFullHistory 
}: UseShipmentProcessingProps): UseShipmentProcessingReturn => {

  // Guard clause to prevent processing if shipment data is not yet available
  if (!shipment) {
    return {
      statusIcon: { icon: TruckIcon, color: 'text-gray-600' },
      timeMetrics: null,
      processedEvents: [],
      visibleEvents: [],
      getEventStyling,
      capitalizeStatus,
      formatDate
    };
  }

  const statusIcon = useMemo(() => {
    return getStatusIcon(shipment.status);
  }, [shipment.status]);

  const timeMetrics = useMemo(() => {
    return calculateTimeMetrics(shipment);
  }, [shipment]);

  const processedEvents = useMemo(() => {
    if (!shipment.events) return [];
    
    return shipment.events
      .sort((a, b) => new Date(b.event_timestamp).getTime() - new Date(a.event_timestamp).getTime())
      .map((event) => {
        const eventDate = new Date(event.event_timestamp);
        return {
          ...event,
          date: eventDate.toLocaleDateString('en-US', { year: 'numeric', month: '2-digit', day: '2-digit' }),
          time: eventDate.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true }),
        };
      });
  }, [shipment.events]);

  const visibleEvents = useMemo(() => {
    return showFullHistory ? processedEvents : processedEvents.slice(0, 3);
  }, [processedEvents, showFullHistory]);

  return {
    statusIcon,
    timeMetrics,
    processedEvents,
    visibleEvents,
    getEventStyling,
    capitalizeStatus,
    formatDate
  };
}; 