import { ApiResponse } from '@/shared/api/inventory/inventoryAPI-types';

/**
 * Options for configuring optimistic updates
 */
export interface OptimisticUpdateOptions<T, U = any> {
  /** Initial data state before update */
  initialData: T;
  
  /** Function to generate the optimistically updated data */
  getOptimisticData: (initialData: T, params: U) => T;
  
  /** API function to call for the actual update */
  apiCall: (params: U) => Promise<ApiResponse<any>>;
  
  /** Parameters to pass to the API call */
  params: U;
  
  /** Optional callback for when the update is successful */
  onSuccess?: (response: ApiResponse<any>) => void;
  
  /** Optional callback for when the update fails */
  onError?: (error: any, rollbackData: T) => void;
  
  /** Optional function for custom error handling */
  handleError?: (error: any) => void;
}

/**
 * Handles optimistic updates for a better user experience
 * 
 * @param options Configuration options for the optimistic update
 * @returns A promise that resolves with the API response
 */
export async function performOptimisticUpdate<T, U = any>(
  options: OptimisticUpdateOptions<T, U>
): Promise<ApiResponse<any>> {
  const { 
    initialData,
    getOptimisticData,
    apiCall,
    params,
    onSuccess,
    onError,
    handleError
  } = options;
  
  // Create optimistically updated data
  const optimisticData = getOptimisticData(initialData, params);
  
  try {
    // Perform the actual API call
    const response = await apiCall(params);
    
    if (!response.success) {
      // API call returned an error
      console.error('Optimistic update failed:', response.message, response.error);
      
      // Call error callback with initial data for rollback
      if (onError) {
        onError(response.error, initialData);
      }
      
      return response;
    }
    
    // Call success callback
    if (onSuccess) {
      onSuccess(response);
    }
    
    return response;
  } catch (error) {
    // Unexpected error during API call
    console.error('Error during optimistic update:', error);
    
    // Call error callback with initial data for rollback
    if (onError) {
      onError(error, initialData);
    }
    
    // Use custom error handler if provided
    if (handleError) {
      handleError(error);
    }
    
    // Return error response
    return {
      data: null,
      totalCount: 0,
      success: false,
      message: error instanceof Error ? error.message : 'An unexpected error occurred',
      error: {
        code: 'OPTIMISTIC_UPDATE_FAILED',
        message: 'Failed to perform update',
        originalError: error
      }
    };
  }
} 