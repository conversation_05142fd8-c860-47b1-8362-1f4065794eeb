import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import fs from 'fs';
import path from 'path';

describe('Chunk dependencies', () => {
  const DIST_DIR = path.resolve('dist/assets');
  let files = [];
  let htmlContent = '';

  beforeAll(() => {
    // This test should only run after a build
    if (!fs.existsSync(DIST_DIR)) {
      console.warn('dist directory not found. These tests require a build.');
      return;
    }
    
    // Get all JS files in the dist directory
    files = fs.readdirSync(DIST_DIR).filter(file => file.endsWith('.js'));
    
    // Read the index.html to check the loading order
    if (fs.existsSync('dist/index.html')) {
      htmlContent = fs.readFileSync('dist/index.html', 'utf-8');
    }
  });

  it('should have vendor-react-core chunk', () => {
    // Skip test if no build is available
    if (files.length === 0) {
      return;
    }
    
    const reactCoreChunk = files.find(file => file.includes('vendor-react-core'));
    expect(reactCoreChunk).toBeDefined();
  });
  
  it('should have vendor-react-dependent chunk', () => {
    // Skip test if no build is available
    if (files.length === 0) {
      return;
    }
    
    const reactDependentChunk = files.find(file => file.includes('vendor-react-dependent'));
    expect(reactDependentChunk).toBeDefined();
  });

  it('should load React core before React-dependent libraries', () => {
    // Skip test if no build is available
    if (htmlContent === '') {
      return;
    }
    
    const reactCoreChunk = files.find(file => file.includes('vendor-react-core'));
    const reactDependentChunk = files.find(file => file.includes('vendor-react-dependent'));
    
    if (!reactCoreChunk || !reactDependentChunk) {
      return;
    }
    
    // Check that reactCore appears before reactDependent in the HTML
    const reactCoreIndex = htmlContent.indexOf(reactCoreChunk);
    const reactDependentIndex = htmlContent.indexOf(reactDependentChunk);
    
    expect(reactCoreIndex).toBeGreaterThan(-1);
    expect(reactDependentIndex).toBeGreaterThan(-1);
    expect(reactCoreIndex).toBeLessThan(reactDependentIndex);
  });
  
  it('should ensure zustand and tanstack libraries are in the react-dependent chunk', () => {
    // Skip test if no build is available
    if (files.length === 0) {
      return;
    }
    
    const reactDependentChunk = files.find(file => file.includes('vendor-react-dependent'));
    
    if (!reactDependentChunk) {
      return;
    }
    
    // Read the content of the chunk file
    const chunkContent = fs.readFileSync(path.join(DIST_DIR, reactDependentChunk), 'utf-8');
    
    // Check that the chunk contains zustand and/or @tanstack code
    const containsZustand = chunkContent.includes('zustand') || chunkContent.includes('createStore');
    const containsTanstack = chunkContent.includes('@tanstack') || chunkContent.includes('createQuery');
    
    // It should contain at least one of these libraries
    expect(containsZustand || containsTanstack).toBeTruthy();
  });
}); 