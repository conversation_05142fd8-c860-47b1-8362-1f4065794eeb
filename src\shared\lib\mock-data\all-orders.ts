import { AllOrdersDetail, AllOrdersViewItem, PlatformKey, AllOrdersStatus } from '@/types';

// Helper function to generate dates
const generateDate = (daysAgo: number): string => {
  const date = new Date();
  date.setDate(date.getDate() - daysAgo);
  return date.toISOString();
};

// Mock data for All Orders Detail view
export const MOCK_ALL_ORDERS_DETAIL: AllOrdersDetail[] = [
  {
    id: 'ord_001',
    orderNumber: 'AMZ-001-2024',
    orderDate: generateDate(2),
    platform: 'amazon',
    channel: 'Amz-NHU',
    status: 'open',
    customer: {
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '******-0123',
      address: {
        street: '123 Main St',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
        country: 'USA'
      }
    },
    items: [
      {
        id: 'item_001',
        sku: 'SHRIMP-001',
        productName: 'Premium Shrimp 1lb',
        quantity: 2,
        unitPrice: 15.99,
        totalPrice: 31.98
      }
    ],
    subtotal: 31.98,
    tax: 2.56,
    shipping: 5.99,
    totalAmount: 40.53,
    shippingMethod: 'Standard Shipping',
    shippingAddress: {
      street: '123 Main St',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'USA'
    },
    isUrgent: false,
    isProblem: false,
    isResent: false,
    hasNotes: true,
    notes: [
      {
        id: 'note_001',
        author: 'Support Team',
        timestamp: generateDate(1),
        content: 'Customer requested expedited shipping',
        type: 'internal'
      }
    ],
    linkedOrders: [],
    customerHistory: [
      {
        orderId: 'ord_hist_001',
        orderNumber: 'AMZ-H01-2024',
        date: generateDate(30),
        totalAmount: 25.99,
        status: 'Completed',
        platform: 'amazon'
      }
    ],
    createdAt: generateDate(2),
    updatedAt: generateDate(1)
  },
  {
    id: 'ord_002',
    orderNumber: 'EBY-002-2024',
    orderDate: generateDate(5),
    platform: 'ebay',
    channel: 'ebay-SeamS',
    status: 'Packed',
    customer: {
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      address: {
        street: '456 Oak Ave',
        city: 'Los Angeles',
        state: 'CA',
        zipCode: '90210',
        country: 'USA'
      }
    },
    items: [
      {
        id: 'item_002',
        sku: 'DROP-001',
        productName: 'Dropship Product A',
        quantity: 1,
        unitPrice: 29.99,
        totalPrice: 29.99
      }
    ],
    subtotal: 29.99,
    tax: 2.40,
    shipping: 7.99,
    totalAmount: 40.38,
    shippingMethod: 'Express Shipping',
    trackingNumber: 'TRK123456789',
    shippingAddress: {
      street: '456 Oak Ave',
      city: 'Los Angeles',
      state: 'CA',
      zipCode: '90210',
      country: 'USA'
    },
    isUrgent: true,
    isProblem: false,
    isResent: false,
    hasNotes: false,
    notes: [],
    linkedOrders: [],
    customerHistory: [],
    createdAt: generateDate(5),
    updatedAt: generateDate(2),
    packedAt: generateDate(1)
  },
  {
    id: 'ord_003',
    orderNumber: 'WEB-003-2024',
    orderDate: generateDate(10),
    platform: 'website',
    channel: 'Website-WAS',
    status: 'Shipped',
    customer: {
      name: 'Michael Brown',
      email: '<EMAIL>',
      phone: '******-0456',
      address: {
        street: '789 Pine St',
        city: 'Chicago',
        state: 'IL',
        zipCode: '60601',
        country: 'USA'
      }
    },
    items: [
      {
        id: 'item_003',
        sku: 'SHRIMP-002',
        productName: 'Jumbo Shrimp 2lb',
        quantity: 1,
        unitPrice: 35.99,
        totalPrice: 35.99
      },
      {
        id: 'item_004',
        sku: 'SAUCE-001',
        productName: 'Cocktail Sauce',
        quantity: 2,
        unitPrice: 4.99,
        totalPrice: 9.98
      }
    ],
    subtotal: 45.97,
    tax: 3.68,
    shipping: 0.00,
    totalAmount: 49.65,
    shippingMethod: 'Free Shipping',
    trackingNumber: 'TRK987654321',
    expectedDelivery: generateDate(-2),
    shippingAddress: {
      street: '789 Pine St',
      city: 'Chicago',
      state: 'IL',
      zipCode: '60601',
      country: 'USA'
    },
    isUrgent: false,
    isProblem: false,
    isResent: false,
    hasNotes: true,
    notes: [
      {
        id: 'note_002',
        author: 'Customer',
        timestamp: generateDate(8),
        content: 'Please leave package at door',
        type: 'customer'
      }
    ],
    linkedOrders: [],
    customerHistory: [
      {
        orderId: 'ord_hist_002',
        orderNumber: 'WEB-H02-2024',
        date: generateDate(60),
        totalAmount: 42.99,
        status: 'Completed',
        platform: 'website'
      }
    ],
    createdAt: generateDate(10),
    updatedAt: generateDate(3),
    packedAt: generateDate(4),
    shippedAt: generateDate(3)
  },
  {
    id: 'ord_004',
    orderNumber: 'WAL-004-2024',
    orderDate: generateDate(15),
    platform: 'walmart',
    channel: 'Walmart-SeamS',
    status: 'Completed',
    customer: {
      name: 'Emily Davis',
      email: '<EMAIL>',
      address: {
        street: '321 Elm Dr',
        city: 'Houston',
        state: 'TX',
        zipCode: '77001',
        country: 'USA'
      }
    },
    items: [
      {
        id: 'item_005',
        sku: 'BULK-001',
        productName: 'Bulk Shrimp Package',
        quantity: 1,
        unitPrice: 89.99,
        totalPrice: 89.99
      }
    ],
    subtotal: 89.99,
    tax: 7.20,
    shipping: 12.99,
    totalAmount: 110.18,
    shippingMethod: 'Priority Shipping',
    trackingNumber: 'TRK555666777',
    shippingAddress: {
      street: '321 Elm Dr',
      city: 'Houston',
      state: 'TX',
      zipCode: '77001',
      country: 'USA'
    },
    isUrgent: false,
    isProblem: false,
    isResent: false,
    hasNotes: false,
    notes: [],
    linkedOrders: [],
    customerHistory: [],
    createdAt: generateDate(15),
    updatedAt: generateDate(8),
    packedAt: generateDate(12),
    shippedAt: generateDate(10),
    deliveredAt: generateDate(8)
  },
  {
    id: 'ord_005',
    orderNumber: 'AMZ-005-2024',
    orderDate: generateDate(20),
    platform: 'amazon',
    channel: 'Amz-YASH',
    status: 'Cancelled',
    customer: {
      name: 'Robert Wilson',
      email: '<EMAIL>',
      phone: '******-0789',
      address: {
        street: '654 Maple Ave',
        city: 'Phoenix',
        state: 'AZ',
        zipCode: '85001',
        country: 'USA'
      }
    },
    items: [
      {
        id: 'item_006',
        sku: 'SEASONAL-001',
        productName: 'Seasonal Seafood Mix',
        quantity: 3,
        unitPrice: 19.99,
        totalPrice: 59.97
      }
    ],
    subtotal: 59.97,
    tax: 4.80,
    shipping: 8.99,
    totalAmount: 73.76,
    shippingMethod: 'Standard Shipping',
    shippingAddress: {
      street: '654 Maple Ave',
      city: 'Phoenix',
      state: 'AZ',
      zipCode: '85001',
      country: 'USA'
    },
    isUrgent: false,
    isProblem: true,
    isResent: false,
    hasNotes: true,
    notes: [
      {
        id: 'note_003',
        author: 'System',
        timestamp: generateDate(18),
        content: 'Order cancelled due to inventory shortage',
        type: 'system'
      },
      {
        id: 'note_004',
        author: 'Support Team',
        timestamp: generateDate(17),
        content: 'Customer notified of cancellation',
        type: 'internal'
      }
    ],
    linkedOrders: [],
    customerHistory: [],
    createdAt: generateDate(20),
    updatedAt: generateDate(17)
  }
];

// Add 15 more diverse orders to reach the minimum 20 requirement
const additionalOrders: AllOrdersDetail[] = [];

// Generate additional orders with various statuses and conditions
const platforms: PlatformKey[] = ['amazon', 'ebay', 'walmart', 'website', 'shopify'];
const platformChannels: Record<PlatformKey, string[]> = {
  amazon: ['Amz-NHU', 'Amz-YASH', 'Amz-Deosai'],
  ebay: ['ebay-SeamS', 'ebay-Drop', 'ebay-WAS'],
  walmart: ['Walmart-SeamS', 'Walmart-Drop'],
  website: ['Website-WAS', 'Website-SeamS'],
  shopify: ['Shopify-WAS', 'Shopify-SeamS'],
  tiktok: ['TikTok-WAS', 'TikTok-SeamS']
};
const statuses: AllOrdersStatus[] = ['Open', 'Packed', 'Shipped', 'Completed', 'Cancelled', 'Refunded', 'On Hold'];
const customers = [
  { name: 'Lisa Thompson', email: '<EMAIL>' },
  { name: 'David Lee', email: '<EMAIL>' },
  { name: 'Jessica Miller', email: '<EMAIL>' },
  { name: 'Christopher Garcia', email: '<EMAIL>' },
  { name: 'Amanda Martinez', email: '<EMAIL>' },
  { name: 'Ryan Anderson', email: '<EMAIL>' },
  { name: 'Nicole Taylor', email: '<EMAIL>' },
  { name: 'Kevin Thomas', email: '<EMAIL>' },
  { name: 'Michelle White', email: '<EMAIL>' },
  { name: 'Brandon Clark', email: '<EMAIL>' },
  { name: 'Jennifer Lopez', email: '<EMAIL>' },
  { name: 'Daniel Rodriguez', email: '<EMAIL>' },
  { name: 'Ashley Lewis', email: '<EMAIL>' },
  { name: 'Matthew Walker', email: '<EMAIL>' },
  { name: 'Stephanie Hall', email: '<EMAIL>' }
];

for (let i = 6; i <= 20; i++) {
  const customer = customers[i - 6];
  const platform = platforms[Math.floor(Math.random() * platforms.length)];
  const availableChannels = platformChannels[platform];
  const channel = availableChannels[Math.floor(Math.random() * availableChannels.length)];
  const status = statuses[Math.floor(Math.random() * statuses.length)];
  const hasNotes = Math.random() > 0.7;
  const isUrgent = Math.random() > 0.8;
  const isProblem = Math.random() > 0.85;
  const isResent = Math.random() > 0.9;
  const daysAgo = Math.floor(Math.random() * 30) + 1;

  additionalOrders.push({
    id: `ord_${String(i).padStart(3, '0')}`,
    orderNumber: `${platform.toUpperCase().slice(0, 3)}-${String(i).padStart(3, '0')}-2024`,
    orderDate: generateDate(daysAgo),
    platform,
    channel,
    status,
    customer: {
      name: customer.name,
      email: customer.email,
      address: {
        street: `${Math.floor(Math.random() * 999) + 100} Random St`,
        city: 'Sample City',
        state: 'CA',
        zipCode: '90210',
        country: 'USA'
      }
    },
    items: [
      {
        id: `item_${String(i).padStart(3, '0')}`,
        sku: `PROD-${String(i).padStart(3, '0')}`,
        productName: `Product ${i}`,
        quantity: Math.floor(Math.random() * 3) + 1,
        unitPrice: Math.round((Math.random() * 50 + 10) * 100) / 100,
        totalPrice: 0 // Will be calculated below
      }
    ],
    subtotal: 0, // Will be calculated below
    tax: 0,
    shipping: Math.round((Math.random() * 15 + 5) * 100) / 100,
    totalAmount: 0,
    shippingMethod: Math.random() > 0.5 ? 'Standard Shipping' : 'Express Shipping',
    trackingNumber: status === 'Shipped' || status === 'Completed' ? `TRK${Math.random().toString().slice(2, 11)}` : undefined,
    shippingAddress: {
      street: `${Math.floor(Math.random() * 999) + 100} Random St`,
      city: 'Sample City',
      state: 'CA',
      zipCode: '90210',
      country: 'USA'
    },
    isUrgent,
    isProblem,
    isResent,
    hasNotes,
    notes: hasNotes ? [
      {
        id: `note_${String(i).padStart(3, '0')}`,
        author: 'Support Team',
        timestamp: generateDate(Math.floor(daysAgo / 2)),
        content: `Sample note for order ${i}`,
        type: 'internal'
      }
    ] : [],
    linkedOrders: [],
    customerHistory: [],
    createdAt: generateDate(daysAgo),
    updatedAt: generateDate(Math.floor(daysAgo / 2))
  });

  // Calculate totals
  const order = additionalOrders[additionalOrders.length - 1];
  order.items[0].totalPrice = order.items[0].quantity * order.items[0].unitPrice;
  order.subtotal = order.items[0].totalPrice;
  order.tax = Math.round(order.subtotal * 0.08 * 100) / 100;
  order.totalAmount = order.subtotal + order.tax + order.shipping;
}

// Combine all orders
export const ALL_ORDERS_MOCK_DATA = [...MOCK_ALL_ORDERS_DETAIL, ...additionalOrders];

// Create view items for table display
export const ALL_ORDERS_VIEW_ITEMS: AllOrdersViewItem[] = ALL_ORDERS_MOCK_DATA.map(order => ({
  id: order.id,
  orderNumber: order.orderNumber,
  orderDate: order.orderDate,
  platform: order.platform,
  channel: order.channel,
  status: order.status,
  customerName: order.customer.name,
  totalAmount: order.totalAmount,
  itemCount: order.items.length,
  trackingNumber: order.trackingNumber,
  hasNotes: order.hasNotes,
  isUrgent: order.isUrgent,
  isProblem: order.isProblem,
  isResent: order.isResent
})); 