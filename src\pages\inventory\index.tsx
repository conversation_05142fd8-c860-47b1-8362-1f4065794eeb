import PageLayout from '@/shared/ui/layout/PageLayout';
import InventoryFilterSection from '@/features/inventory-features/inventory-management/InventoryFilterSection';
import InventoryPageContent from '@/features/inventory-features/inventory-management/InventoryPageContent';
import { useInventoryDataWithFilters } from '../../shared/lib/hooks/inventory/useInventoryDataWithFilters';
import { useProductDetailPanel } from '../../shared/lib/hooks/inventory/products/useProductDetailPanel';
import { useBulkStockAdjustment } from '../../shared/lib/hooks/inventory/useBulkStockAdjustment';
import { useStockAdjustment } from '../../shared/lib/hooks/inventory/useStockAdjustment';
import { useProductFormManagement } from '../../shared/lib/hooks/inventory/products/useProductFormManagement';
import { InventoryMovementType } from '../../types/index';
import ProductDetailPanel from '../../features/inventory-features/inventory-detail/ProductDetailPanel';
import { Notifications } from '@/shared/ui/feedback/Notifications';
import { notificationService } from '@/shared/lib/services/notification/notification-service';
import { inventoryService } from '../../shared/lib/services/inventory';
import { optimisticUpdateAdapter, bulkStockUpdateAdapter } from '../../shared/lib/utils/domain/inventory/inventory-adapters';
import InventoryModals from '../../features/inventory-features/inventory-management/InventoryModals';
import { BulkStockUpdateFormData } from '@/features/inventory-features/stock-update';

const InventoryPage: React.FC = () => {
  // Use the consolidated hook for inventory data and filtering
  const {
    products,
    loading,
    isInitialLoading,
    error,
    totalCount,
    fetchProducts,
    selectProduct,
    selectAllProducts,
    selectedProductIds,
    isAllSelected,
    isSomeSelected,
    clearSelection,
    currentPage,
    totalPages,
    setCurrentPage,
    hasNextPage,
    hasPreviousPage,
    sortConfig,
    handleSort,
    updateProductStock,
    // Filter state and handlers from the consolidated hook
    searchTerm,
    activeFilters,
    isFilterModalOpen,
    stockStatusFilter,
    availableProductTypes,
    handleSearchChange,
    handleOpenFilterModal,
    handleCloseFilterModal,
    handleApplyFilters,
    handleRemoveFilter,
    handleStockStatusFilterChange,
    handleClearAllFilters
  } = useInventoryDataWithFilters();

  // Create an adapter wrapper for updateProductStock to use the optimisticUpdateAdapter
  const handleOptimisticUpdate = (
    productId: string, 
    adjustmentType: InventoryMovementType, 
    quantity: number
  ) => {
    const [id, type, qty] = optimisticUpdateAdapter(productId, adjustmentType, quantity);
    updateProductStock(id, type, qty);
  };

  // Use the product detail panel hook
  const {
    selectedProductId,
    isDetailPanelOpen,
    closeDetailPanel,
    handleProductClick
  } = useProductDetailPanel();

  // Use the product form management hook
  const {
    isProductFormModalOpen,
    productToEdit,
    productFormTitle,
    productFormButtonText,
    handleNewProduct,
    handleEditProduct,
    handleCloseProductFormModal,
    handleProductSaved
  } = useProductFormManagement({
    onSuccess: () => fetchProducts({}),
    onDetailPanelClose: closeDetailPanel,
    isDetailPanelOpen
  });

  // Use the stock adjustment hook for individual products
  const {
    isModalOpen: isStockUpdateModalOpen,
    selectedProductId: stockUpdateProductId,
    selectedProductName: stockUpdateProductName,
    currentStock: stockUpdateCurrentStock,
    openModal: openStockUpdateModal,
    closeModal: closeStockUpdateModal,
    updateStock
  } = useStockAdjustment({
    onSuccess: () => {
      // Refresh the product list after a successful update
      fetchProducts({});
    },
    // Use the adapter function for optimistic updates
    onOptimisticUpdate: handleOptimisticUpdate
  });

  // Use the bulk stock adjustment hook
  const {
    isModalOpen: isBulkStockUpdateModalOpen,
    selectedProducts: bulkSelectedProducts,
    openModal: openBulkStockUpdateModal,
    closeModal: closeBulkStockUpdateModal,
    updateStock: bulkUpdateStock
  } = useBulkStockAdjustment({
    onSuccess: () => {
      // Clear selection and refresh the product list after a successful update
      clearSelection();
      fetchProducts({});
    },
    // Use the adapter function for optimistic updates
    onOptimisticUpdate: handleOptimisticUpdate
  });

  // Create an adapter wrapper for bulk stock updates
  const handleBulkUpdateStock = async (formData: BulkStockUpdateFormData) => {
    const adjustmentData = bulkStockUpdateAdapter(formData);
    return await bulkUpdateStock(adjustmentData);
  };

  const handleDiscontinueProduct = async (productId: string, reason: string, notes: string) => {
    try {
      // Get the inventory ID for this product using the service layer
      const inventoryResponse = await inventoryService.getByProductId(productId);
      
      if (!inventoryResponse || !inventoryResponse.inventory_id) {
        throw new Error('Failed to fetch inventory data');
      }
      
      // Update the product status to discontinued
      const updateData = {
        status: 'discontinued' as const,
        notes: `Discontinued: ${reason}. ${notes}`
      };
      
      // Update the product through the inventory service
      const response = await inventoryService.update(inventoryResponse.inventory_id, {
        notes: updateData.notes
      });
      
      if (!response) {
        throw new Error('Failed to discontinue product');
      }
      
      // Show success notification
      notificationService.success(
        'The product has been successfully discontinued', 
        'Product Discontinued'
      );
      
      // Refresh the product list
      fetchProducts({});
    } catch (err) {
      console.error('Error discontinuing product:', err);
      
      // Show error notification
      notificationService.error(
        'Unable to discontinue the product. Please try again.',
        'Error Discontinuing Product'
      );
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Adapter function to convert checkbox event to boolean for selectAllProducts
  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    selectAllProducts(event.target.checked);
  };

  // Handler for retrying data load
  const handleRetryLoad = () => {
    fetchProducts({});
  };

  // Handle refreshing the data after a product update
  const handleProductUpdate = () => {
    fetchProducts({});
  };

  // Handler for opening the stock update modal
  const handleOpenStockUpdateModal = (productId: string) => {
    const product = products.find(p => p.product_id === productId);
    if (product) {
      openStockUpdateModal(
        productId,
        product.name || 'Unknown Product',
        product.current_stock || 0
      );
    }
  };

  // Handler for opening the bulk stock update modal
  const handleOpenBulkStockUpdateModal = () => {
    // Filter products to get only the selected ones
    const selectedProducts = products
      .filter(product => product.product_id && selectedProductIds.has(product.product_id));
    
    if (selectedProducts.length > 0) {
      openBulkStockUpdateModal(selectedProducts);
    }
  };

  // Render header actions for the PageLayout
  const renderHeaderActions = () => (
    <button
      onClick={handleNewProduct}
      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
    >
      Add New Product
    </button>
  );

  return (
    <PageLayout
      title="Inventory Management"
      description="Manage and track your product inventory"
      headerActions={renderHeaderActions()}
    >
      {/* Notifications component */}
      <Notifications />
      
      {/* Search and Filter Section */}
      <InventoryFilterSection
        isInitialLoading={isInitialLoading}
        productCount={totalCount}
        searchTerm={searchTerm}
        onSearchChange={handleSearchChange}
        onNewProduct={handleNewProduct}
        onOpenFilterModal={handleOpenFilterModal}
        activeFilterCount={activeFilters.length}
        stockStatusFilter={stockStatusFilter}
        onStockStatusFilterChange={handleStockStatusFilterChange}
      />
      
      {/* Content Area */}
      <main className="flex-1 overflow-y-auto">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 py-4 sm:py-6">
        <InventoryPageContent
            isInitialLoading={isInitialLoading}
            loading={loading}
            error={error}
            products={products}
            totalCount={totalCount}
            activeFilters={activeFilters}
            onRemoveFilter={handleRemoveFilter}
            onClearAllFilters={handleClearAllFilters}
            selectedProductIds={selectedProductIds}
            isAllSelected={isAllSelected}
            isSomeSelected={isSomeSelected}
            onSelectProduct={selectProduct}
            onSelectAll={handleSelectAll}
            clearSelection={clearSelection}
            currentPage={currentPage}
            totalPages={totalPages}
            hasNextPage={hasNextPage}
            hasPreviousPage={hasPreviousPage}
            onPageChange={handlePageChange}
            sortConfig={{
              column: sortConfig.column,
              direction: sortConfig.direction
            }}
            onSort={handleSort}
            onProductClick={handleProductClick}
            onRetryLoad={handleRetryLoad}
            onAddNewProduct={handleNewProduct}
            onOpenBulkStockUpdateModal={handleOpenBulkStockUpdateModal}
        />
        </div>
      </main>
      
      {/* Product Detail Panel */}
      <ProductDetailPanel
        productId={selectedProductId}
        isOpen={isDetailPanelOpen}
        onClose={closeDetailPanel}
        onUpdateStock={handleOpenStockUpdateModal}
        onProductUpdate={handleProductUpdate}
        onEditProduct={handleEditProduct}
        onDiscontinueProduct={handleDiscontinueProduct}
      />
      
      {/* All Modals */}
      <InventoryModals
        // Filter modal props
        isFilterModalOpen={isFilterModalOpen}
        onCloseFilterModal={handleCloseFilterModal}
        onApplyFilters={handleApplyFilters}
        currentFilters={activeFilters}
        availableProductTypes={availableProductTypes}
        
        // Product form modal props
        isProductFormModalOpen={isProductFormModalOpen}
        onCloseProductFormModal={handleCloseProductFormModal}
        onProductSaved={handleProductSaved}
        productFormTitle={productFormTitle}
        productFormButtonText={productFormButtonText}
        productToEdit={productToEdit}
        
        // Stock update modal props
        isStockUpdateModalOpen={isStockUpdateModalOpen}
        stockUpdateProductId={stockUpdateProductId}
        stockUpdateProductName={stockUpdateProductName}
        stockUpdateCurrentStock={stockUpdateCurrentStock}
        onCloseStockUpdateModal={closeStockUpdateModal}
        onUpdateStock={updateStock}
        
        // Bulk stock update modal props
        isBulkStockUpdateModalOpen={isBulkStockUpdateModalOpen}
        bulkSelectedProducts={bulkSelectedProducts}
        onCloseBulkStockUpdateModal={closeBulkStockUpdateModal}
        onBulkUpdateStock={handleBulkUpdateStock}
      />
    </PageLayout>
  );
};

export default InventoryPage;