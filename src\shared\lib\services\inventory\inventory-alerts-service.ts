import { supabase } from '../../../../../supabase/supabase_client/client';
import type { Platform, ProductIdentifier } from '@/types';
import { productApi } from '@/shared/api/inventory/productAPI';

/**
 * Alert filter parameters
 */
export interface AlertFilterParams {
  isResolved?: boolean;
  severity?: string;
  alertType?: string;
}

/**
 * Service for managing inventory alerts and product identifiers
 */
export class InventoryAlertsService {
  /**
   * Get inventory alerts with filtering
   */
  async getAlerts(params?: AlertFilterParams) {
    try {
      let query = supabase
        .from('inventory_alerts')
        .select('*', { count: 'exact' });
      
      if (params?.isResolved !== undefined) {
        query = query.eq('is_resolved', params.isResolved);
      }
      
      if (params?.severity) {
        query = query.eq('severity', params.severity);
      }
      
      if (params?.alertType) {
        query = query.eq('alert_type', params.alertType);
      }
      
      const { data, error, count } = await query;
      
      if (error) throw error;
      
      return { 
        data: data || [],
        count: count || 0
      };
    } catch (err) {
      console.error('Error fetching alerts:', err);
      return { data: [], count: 0 };
    }
  }

  /**
   * Resolve an inventory alert
   */
  async resolveAlert(alertId: string, notes?: string) {
    try {
      const { error } = await supabase
        .from('inventory_alerts')
        .update({
          is_resolved: true,
          resolved_at: new Date().toISOString(),
          resolution_notes: notes || null
        })
        .eq('id', alertId);
      
      if (error) throw error;
    } catch (err) {
      console.error('Error resolving alert:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      throw new Error(`Failed to resolve alert: ${errorMessage}`);
    }
  }

  /**
   * Get available platforms
   */
  async getPlatforms() {
    try {
      const response = await productApi.getPlatforms();
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch platforms');
      }
      
      return { 
        data: response.data || [],
        count: response.totalCount || 0
      };
    } catch (err) {
      console.error('Error fetching platforms:', err);
      return { data: [], count: 0 };
    }
  }

  /**
   * Get product identifiers for a product
   */
  async getProductIdentifiers(productId: string) {
    try {
      const response = await productApi.getProductIdentifiers(productId);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch product identifiers');
      }
      
      return { 
        data: response.data || [],
        count: response.totalCount || 0
      };
    } catch (err) {
      console.error('Error fetching product identifiers:', err);
      return { data: [], count: 0 };
    }
  }

  /**
   * Create a new product identifier
   */
  async createProductIdentifier(data: {
    product_id: string;
    platform_id: string;
    platform_identifier: string;
    code_name?: string | null;
    pack_size: number;
  }) {
    try {
      // Validate required fields
      if (!data.product_id || !data.platform_id || !data.platform_identifier) {
        throw new Error('Missing required fields for product identifier');
      }
      
      // Create a compatible object for the API
      const createParams = {
        product_id: data.product_id,
        platform_id: data.platform_id,
        platform_identifier: data.platform_identifier,
        // Convert null to undefined to match the API's expected type
        code_name: data.code_name || undefined,
        pack_size: data.pack_size
      };
      
      const response = await productApi.createProductIdentifier(createParams);
      
      if (!response.success || !response.data) {
        throw new Error(response.message || 'Failed to create product identifier');
      }
      
      return response.data;
    } catch (err) {
      console.error('Error creating product identifier:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      throw new Error(`Failed to create product identifier: ${errorMessage}`);
    }
  }

  /**
   * Update a product identifier
   */
  async updateProductIdentifier(id: string, data: {
    platform_identifier?: string;
    code_name?: string | null;
    pack_size?: number;
  }) {
    try {
      // Validate ID
      if (!id) {
        throw new Error('Product identifier ID is required');
      }
      
      // Create a compatible object for the API
      const updateParams = {
        platform_identifier: data.platform_identifier,
        // Convert null to undefined to match the API's expected type
        code_name: data.code_name || undefined,
        pack_size: data.pack_size
      };
      
      const response = await productApi.updateProductIdentifier(id, updateParams);
      
      if (!response.success || !response.data) {
        throw new Error(response.message || 'Failed to update product identifier');
      }
      
      return response.data;
    } catch (err) {
      console.error('Error updating product identifier:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      throw new Error(`Failed to update product identifier: ${errorMessage}`);
    }
  }

  /**
   * Delete a product identifier
   */
  async deleteProductIdentifier(id: string) {
    try {
      // Validate ID
      if (!id) {
        throw new Error('Product identifier ID is required');
      }
      
      const response = await productApi.deleteProductIdentifier(id);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to delete product identifier');
      }
    } catch (err) {
      console.error('Error deleting product identifier:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      throw new Error(`Failed to delete product identifier: ${errorMessage}`);
    }
  }
}

// Export as singleton
export default new InventoryAlertsService(); 