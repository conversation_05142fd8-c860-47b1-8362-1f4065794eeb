import * as React from 'react';
import { useRef, useEffect, Suspense, useState, lazy } from 'react';
import {  Routes, Route, Navigate, useNavigate, Outlet, useLocation } from 'react-router-dom';
import '@/index.css';
import Sidebar from '@/shared/ui/layout/Sidebar';
import LoadingSpinner from '@/shared/ui/feedback/LoadingSpinner';
import ErrorMessage from '@/shared/ui/feedback/ErrorMessage';
import ProfilerDashboard from '@/shared/ui/data/ProfilerDashboard';
import { Notifications } from '@/shared/ui/feedback/Notifications';
import { StagewiseToolbar } from '@stagewise/toolbar-react';
import { ReactPlugin } from '@stagewise-plugins/react';
import { AuthProvider, useAuth } from './providers/AuthProvider';
import { preloadCriticalRoutes, routeTracker } from '@/shared/lib/utils/performance/route-preloading';

// Lazy-loaded route components for better performance and code splitting
const DashboardView = lazy(() => import('@/pages/dashboard'));
const DeliveryPage = lazy(() => import('@/pages/delivery'));
const InventoryPage = lazy(() => import('@/pages/inventory'));
const ReportsView = lazy(() => import('@/pages/reports'));
const LoginPage = lazy(() => import('@/pages/login'));
const AuthCallback = lazy(() => import('@/pages/auth/callback'));
const OrdersByChannelPage = lazy(() => import('@/pages/orders'));
const FulfillPage = lazy(() => import('@/pages/fulfill'));
const ShrimpDashboard = lazy(() => import('@/pages/dashboard/sub-dashboard/shrimp'));
const DropshipDashboard = lazy(() => import('@/pages/dashboard/sub-dashboard/dropship'));
const StaffDashboard = lazy(() => import('@/pages/dashboard/sub-dashboard/staff'));
const UserManagementPage = lazy(() => import('@/pages/user-management'));

// Loading fallback component for Suspense
const PageLoadingFallback: React.FC = () => (
  <div className="flex items-center justify-center min-h-[400px] bg-main-bg">
    <LoadingSpinner size="lg" message="Loading page..." />
  </div>
);

// Error boundary for lazy loading failures
class LazyLoadErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ComponentType<{ error: Error; retry: () => void }> },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: { children: React.ReactNode; fallback?: React.ComponentType<{ error: Error; retry: () => void }> }) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Lazy loading error:', error, errorInfo);
  }

  retry = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback;
      if (FallbackComponent && this.state.error) {
        return <FallbackComponent error={this.state.error} retry={this.retry} />;
      }
      return (
        <div className="flex items-center justify-center min-h-[400px] bg-main-bg">
          <ErrorMessage 
            message="Failed to load page. Please try refreshing." 
            onRetry={this.retry}
            variant="error"
          />
        </div>
      );
    }

    return this.props.children;
  }
}

// Wrapper component for lazy-loaded routes with error boundary and suspense
const LazyRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <LazyLoadErrorBoundary>
    <Suspense fallback={<PageLoadingFallback />}>
      {children}
    </Suspense>
  </LazyLoadErrorBoundary>
);

const AuthenticatedLayout: React.FC = () => {
  // Track route changes for preloading optimization
  const location = useLocation();
  
  // Record route visit when location changes
  useEffect(() => {
    routeTracker.recordRouteVisit(location.pathname);
  }, [location.pathname]);
  
  return (
    <div className="min-h-screen bg-main-bg font-sans">
      <Sidebar />
      {/* Main content area - full width since sidebar is now fixed/overlay */}
      <div className="min-h-screen flex flex-col">
        <Outlet /> {/* Nested routes will render here */}
      </div>
      {/* Development-only Profiler Dashboard */}
      <ProfilerDashboard />
    </div>
  );
};

// Component that handles authentication logic
const AppContent: React.FC = () => {
  const { user, isLoading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const toolbarInitialized = useRef(false);
  const [toolbarReady, setToolbarReady] = useState(false);
  const preloadInitiated = useRef(false);

  useEffect(() => {
    if (!isLoading) {
      // Skip redirect logic for auth callback page - let the AuthCallback component handle it
      if (location.pathname === '/auth/callback') {
        return;
      }
      
      if (user && location.pathname === '/login') {
        navigate('/dashboard-center', { replace: true });
      } else if (!user && location.pathname !== '/login') {
        navigate('/login', { replace: true });
      }
    }
  }, [user, isLoading, navigate, location.pathname]);

  // Initialize toolbar only once and after a delay to ensure DOM is ready
  useEffect(() => {
    if (!toolbarInitialized.current) {
      toolbarInitialized.current = true;
      
      // Wait for the DOM to be fully loaded and rendered
      const timer = setTimeout(() => {
        setToolbarReady(true);
      }, 1000); // 1 second delay
      
      return () => clearTimeout(timer);
    }
  }, []);
  
  // Preload critical routes after authentication
  useEffect(() => {
    // Only start preloading once authentication is complete and we haven't already initiated preloading
    if (!isLoading && !preloadInitiated.current) {
      preloadInitiated.current = true;
      
      // Wait a moment after initial render to start preloading
      // This allows the initial route to load first
      setTimeout(() => {
        // Get recent routes for priority boosting
        const recentRoutes = routeTracker.getRecentRoutes();
        
        // Preload critical routes based on user role and navigation history
        preloadCriticalRoutes(
          user ? { role: user.role } : undefined,
          recentRoutes
        );
        
        if (process.env.NODE_ENV === 'development') {
          console.debug('🔄 Initiated route preloading');
        }
      }, 2000); // 2 second delay after authentication
    }
  }, [isLoading, user]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-main-bg">
        <LoadingSpinner size="lg" message="Authenticating..." />
      </div>
    );
  }

  return (
    <>
      {toolbarInitialized.current && toolbarReady && (
        <StagewiseToolbar config={{ plugins: [ReactPlugin] }} />
      )}
      <Notifications />
      <Routes>
        <Route 
          path="/login" 
          element={
            <LazyRoute>
              <LoginPage />
            </LazyRoute>
          } 
        />
        <Route 
          path="/auth/callback" 
          element={
            <LazyRoute>
              <AuthCallback />
            </LazyRoute>
          } 
        />
        
        {user ? (
          <Route element={<AuthenticatedLayout />}>
            <Route path="/dashboard" element={<Navigate to="/dashboard-center" replace />} />
            <Route 
              path="/dashboard-center" 
              element={
                <LazyRoute>
                  <DashboardView />
                </LazyRoute>
              } 
            />
            <Route 
              path="/dashboard-shrimp" 
              element={
                <LazyRoute>
                  <ShrimpDashboard />
                </LazyRoute>
              } 
            />
            <Route 
              path="/dashboard-dropship" 
              element={
                <LazyRoute>
                  <DropshipDashboard />
                </LazyRoute>
              } 
            />
            <Route 
              path="/dashboard-staff" 
              element={
                <LazyRoute>
                  <StaffDashboard />
                </LazyRoute>
              } 
            />
            <Route 
              path="/orders" 
              element={
                <LazyRoute>
                  <OrdersByChannelPage />
                </LazyRoute>
              } 
            />
            <Route 
              path="/fulfill" 
              element={
                <LazyRoute>
                  <FulfillPage />
                </LazyRoute>
              } 
            />
            <Route 
              path="/delivery" 
              element={
                <LazyRoute>
                  <DeliveryPage />
                </LazyRoute>
              } 
            /> 
            <Route 
              path="/inventory" 
              element={
                <LazyRoute>
                  <InventoryPage />
                </LazyRoute>
              } 
            />
            <Route 
              path="/reports" 
              element={
                <LazyRoute>
                  <ReportsView />
                </LazyRoute>
              } 
            />
            <Route 
              path="/user-management" 
              element={
                <LazyRoute>
                  <UserManagementPage />
                </LazyRoute>
              } 
            />
            
            {/* Redirects for authenticated users */}
            <Route path="/" element={<Navigate to="/dashboard-center" replace />} />
            {/* Fallback for any other authenticated route attempt */}
            <Route path="*" element={<Navigate to="/dashboard-center" replace />} />
          </Route>
        ) : (
           // If not authenticated, all paths not explicitly /login redirect to /login
          <Route path="*" element={<Navigate to="/login" replace />} />
        )}
      </Routes>
    </>
  );
};

// Main App component with AuthProvider wrapper
const App: React.FC = () => {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
};

export default App;
