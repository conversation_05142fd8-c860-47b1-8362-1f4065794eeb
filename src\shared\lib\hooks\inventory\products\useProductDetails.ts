import { useState, useEffect, useCallback } from 'react';
import { InventoryView, Product, Inventory, BusinessUnit } from '@/types';
import { inventoryService, productService } from '@/shared/lib/services/inventory';

// Product details with additional information
export interface DetailedProduct extends Product {
  business_unit_name?: string;
  inventory?: Inventory;
  identifiers?: Array<{
    platform_name: string;
    platform_identifier: string;
    code_name: string | null;
  }>;
}

export interface UseProductDetailsReturn {
  detailedProductData: DetailedProduct | null;
  isLoading: boolean;
  error: string | null;
  businessUnits: BusinessUnit[];
}

export const useProductDetails = (product: InventoryView): UseProductDetailsReturn => {
  const [detailedProductData, setDetailedProductData] = useState<DetailedProduct | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [businessUnits, setBusinessUnits] = useState<BusinessUnit[]>([]);

  // Fetch business units
  useEffect(() => {
    const fetchBusinessUnits = async () => {
      try {
        // For now, using mock data
        setBusinessUnits([
          { id: '1', name: 'Shrimp Products', description: null, is_active: true, created_at: null, updated_at: null },
          { id: '2', name: 'Dropship Products', description: null, is_active: true, created_at: null, updated_at: null },
          { id: '3', name: 'General Merchandise', description: null, is_active: true, created_at: null, updated_at: null }
        ]);
      } catch (err) {
        console.error('Error fetching business units:', err);
      }
    };

    fetchBusinessUnits();
  }, []);

  // Fetch detailed product data
  useEffect(() => {
    if (!product.product_id) return;

    const fetchDetailedProductData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch product data using the service
        const inventoryData = await inventoryService.getByProductId(product.product_id!);
        
        // Fetch platform identifiers using the service
        const identifiersResult = await productService.getProductIdentifiers(product.product_id!);
        
        // Get business unit name from the fetched business units
        const businessUnit = businessUnits.find(bu => 
          bu.id === (inventoryData?.product?.business_unit_id || '')
        );
        
        // Combine the data with safe access to nested properties
        const detailedData: DetailedProduct = {
          ...(inventoryData?.product || {}),
          business_unit_name: businessUnit?.name || 'Unassigned',
          inventory: inventoryData,
          identifiers: identifiersResult.success && identifiersResult.data ? 
            identifiersResult.data.map(id => ({
              platform_name: id.platform_name || 'Unknown',
              platform_identifier: id.platform_identifier || '',
              code_name: id.code_name
            })) : []
        };
        
        setDetailedProductData(detailedData);
      } catch (err) {
        console.error('Error fetching detailed product data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load product details');
      } finally {
        setIsLoading(false);
      }
    };

    fetchDetailedProductData();
  }, [product.product_id, businessUnits]);

  return {
    detailedProductData,
    isLoading,
    error,
    businessUnits
  };
};

export default useProductDetails; 