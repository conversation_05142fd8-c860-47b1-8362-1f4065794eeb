import React, { useCallback, useState, useEffect, lazy, Suspense } from 'react';
import { InventoryView, ProductStatusEnum } from '@/types';
import { PanelLoadingSkeleton } from '@/shared/ui/feedback/LoadingSkeleton';
import { XMarkIcon, ExclamationCircleIcon, ArrowPathIcon } from '@heroicons/react/24/outline';
import { inventoryService } from '@/shared/lib/services/inventory';

// Lazy loaded components
const ProductBasicInfo = lazy(() => import('@/features/inventory-features/inventory-detail/components/ProductBasicInfo'));
const ProductStockHistory = lazy(() => import('@/features/inventory-features/inventory-detail/components/ProductStockHistory'));
const ProductActions = lazy(() => import('@/features/inventory-features/inventory-detail/components/ProductActions'));
const ProductPlatformIdentifiers = lazy(() => import('@/features/inventory-features/inventory-detail/components/ProductPlatformIdentifiers'));

interface ProductDetailPanelProps {
  productId: string | null;
  isOpen: boolean;
  onClose: () => void;
  onUpdateStock?: (productId: string) => void;
  onProductUpdate?: () => void;
  onEditProduct?: (productId: string) => void;
  onDiscontinueProduct?: (productId: string, reason: string, notes: string) => Promise<void>;
}

/**
 * Transform API inventory data to InventoryView format
 */
const transformInventoryData = (data: any): InventoryView => {
  // Return a default object if data or product is missing
  if (!data || !data.product) {
    return {
      inventory_id: '',
      product_id: '',
      name: 'Unknown Product',
      sku: '',
      product_type: null,
      status: null,
      current_stock: 0,
      available_stock: 0,
      reserved_stock: 0,
      minimum_threshold: null,
      needs_reorder: false
    };
  }

  return {
    inventory_id: data.id,
    product_id: data.product_id,
    name: data.product.name,
    sku: data.product.sku,
    product_type: data.product.product_type || null,
    status: data.product.status || null,
    current_stock: data.current_stock,
    available_stock: data.available_stock,
    reserved_stock: data.reserved_stock,
    minimum_threshold: data.minimum_threshold,
    // Calculate needs_reorder based on current_stock and reorder_point or minimum_threshold
    needs_reorder: data.current_stock !== null && 
      ((data.reorder_point !== null && data.current_stock <= data.reorder_point) || 
       (data.minimum_threshold !== null && data.current_stock <= data.minimum_threshold * 1.5))
  };
};

const ProductDetailPanel: React.FC<ProductDetailPanelProps> = ({
  productId,
  isOpen,
  onClose,
  onUpdateStock,
  // onProductUpdate,
  onEditProduct,
  onDiscontinueProduct
}) => {
  // State for loading product data
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [loadingState, setLoadingState] = useState<'initial' | 'refreshing' | 'error' | 'success'>('initial');
  const [productData, setProductData] = useState<InventoryView | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  
  const maxRetries = 3;
  
  const retry = useCallback(() => {
    if (retryCount < maxRetries) {
      setRetryCount(prev => prev + 1);
    }
  }, [retryCount]);

  // Fetch product data when panel opens with a specific productId
  useEffect(() => {
    if (!productId || !isOpen) {
      return;
    }

    const fetchProductData = async () => {
      // Set appropriate loading state based on whether we already have data
      setLoadingState(productData ? 'refreshing' : 'initial');
      setIsLoading(true);
      setError(null);

      try {
        // console.log(`Fetching product data for ID: ${productId} (attempt ${retryCount + 1})`);
        
        // Use the service to fetch product data
        const response = await inventoryService.getByProductId(productId);
        
        if (!response) {
          throw new Error('Failed to fetch product data');
        }

        // Transform the service response to match our component's data structure
        const transformedData = transformInventoryData(response);
        setProductData(transformedData);
        setIsLoading(false);
        setLoadingState('success');
        setRetryCount(0); // Reset retry count on success
      } catch (err) {
        console.error('Error fetching product data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load product data');
        setIsLoading(false);
        setLoadingState('error');
      }
    };

    fetchProductData();
  }, [productId, isOpen, retryCount]);

  // Reset state when panel closes
  useEffect(() => {
    if (!isOpen) {
      // Optional: Keep the data for a short time for better UX if panel reopens quickly
      // with the same product
      setTimeout(() => {
        if (!isOpen) {
          setProductData(null);
          setLoadingState('initial');
          setRetryCount(0);
        }
      }, 300);
    }
  }, [isOpen]);

  // Handle retry loading
  const handleRetryLoad = useCallback(() => {
    retry();
  }, [retry]);

  // PERFORMANCE: Early return if panel is closed
  if (!isOpen) {
    return null;
  }

  return (
    <>
      {/* Backdrop overlay with animation */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-30 z-40 transition-opacity duration-300 ease-in-out"
        onClick={onClose}
      />
      
      {/* Panel with slide-in animation */}
      <div 
        className={`
          fixed right-0 top-0 h-full w-full md:w-2/3 lg:w-1/2 bg-white shadow-xl z-50 
          transform transition-all duration-500 ease-out will-change-transform
          ${isOpen ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
          flex flex-col
        `}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">
            {isLoading && loadingState === 'initial' ? (
              <div className="h-8 bg-gray-200 rounded w-48 animate-pulse"></div>
            ) : productData?.name || 'Product Details'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors"
            aria-label="Close panel"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Content area with scrolling */}
        <div className="flex-1 h-full overflow-y-auto pb-40 scrollbar-hide" style={{ contain: 'content' }}>
          <div className="p-6 space-y-8">
            {/* Loading, error or content states */}
            {isLoading && loadingState === 'initial' ? (
              <PanelLoadingSkeleton />
            ) : error ? (
              <div className="flex flex-col items-center justify-center p-8 bg-red-50 rounded-lg">
                <ExclamationCircleIcon className="h-12 w-12 text-red-400 mb-3" />
                <h3 className="text-lg font-medium text-red-800 mb-2">Failed to load product details</h3>
                <p className="text-sm text-red-600 mb-4">{error}</p>
                <button
                  onClick={handleRetryLoad}
                  disabled={retryCount >= maxRetries}
                  className={`
                    inline-flex items-center px-4 py-2 rounded-md text-sm font-medium
                    ${retryCount >= maxRetries
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-red-100 text-red-700 hover:bg-red-200'}
                  `}
                >
                  <ArrowPathIcon className="h-4 w-4 mr-2" />
                  {retryCount >= maxRetries ? 'Max retries reached' : 'Retry'}
                </button>
                {retryCount > 0 && (
                  <p className="text-xs text-gray-500 mt-2">
                    Retry attempt {retryCount} of {maxRetries}
                  </p>
                )}
              </div>
            ) : productData ? (
              <div className={`space-y-8 ${loadingState === 'refreshing' ? 'opacity-50' : ''}`}>
                {/* Actions */}
                <Suspense fallback={<div className="h-12 bg-gray-100 animate-pulse rounded"></div>}>
                  <ProductActions
                    productId={productData.product_id!}
                    productName={productData.name!}
                    productStatus={productData.status as ProductStatusEnum}
                    onUpdateStock={onUpdateStock ? onUpdateStock : () => {}}
                    onEditProduct={onEditProduct}
                    onDiscontinueProduct={onDiscontinueProduct}
                  />
                </Suspense>
                
                {/* Basic Info */}
                <Suspense fallback={<PanelLoadingSkeleton />}>
                  <ProductBasicInfo product={productData} />
                </Suspense>
                
                {/* Platform Identifiers - only show if productId is available */}
                {productData.product_id && (
                  <Suspense fallback={<PanelLoadingSkeleton />}>
                    <ProductPlatformIdentifiers productId={productData.product_id} />
                  </Suspense>
                )}
                
                {/* Stock History - only show if productId is available */}
                {productData.product_id && (
                  <Suspense fallback={<PanelLoadingSkeleton />}>
                    <ProductStockHistory productId={productData.product_id} />
                  </Suspense>
                )}
              </div>
            ) : (
              <div className="p-8 text-center text-gray-500">
                No product data available
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default ProductDetailPanel; 