import React, { memo } from 'react';

interface OrderDetailHeaderProps {
  onClose: () => void;
}

const OrderDetailHeader: React.FC<OrderDetailHeaderProps> = ({ onClose }) => {
  return (
    <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900">Order Details</h2>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 transition-colors duration-150"
        >
          <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  );
};

// PERFORMANCE: Simple props comparison for header
export default memo(OrderDetailHeader, (prevProps, nextProps) => 
  prevProps.onClose === nextProps.onClose
); 