import inventoryStockService from '@/shared/lib/services/inventory/inventory-stock-service';
import { AdjustmentType } from '@/shared/lib/utils/domain/inventory/stock-form-utils';
import { supabase } from '../../../../../../supabase/supabase_client/client';
import { InventoryReasonCategory } from '@/types';

// Mock Supabase client
jest.mock('@/shared/lib/utils/api/supabase/client', () => ({
  supabase: {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    single: jest.fn().mockReturnThis()
  }
}));

describe('Inventory Stock Service', () => {
  // Mock data for tests
  const mockInventoryItem = {
    id: '1',
    product_id: 'prod-1',
    current_stock: 10,
    reserved_stock: 2,
    available_stock: 8,
    last_counted_at: '2023-01-01T00:00:00Z',
    last_restocked_at: '2023-01-01T00:00:00Z'
  };

  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default mock implementation for select
    (supabase.from as jest.Mock).mockImplementation(() => ({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: mockInventoryItem,
            error: null
          })
        })
      }),
      update: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { ...mockInventoryItem, current_stock: 15 },
              error: null
            })
          })
        })
      }),
      insert: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            error: null
          })
        })
      })
    }));
    
    // Mock Date.now for consistent timestamps
    jest.spyOn(Date.prototype, 'toISOString').mockReturnValue('2023-01-15T12:00:00Z');
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('adjustStock', () => {
    test('should increase stock correctly', async () => {
      // Call the service to increase stock
      const adjustmentParams = {
        quantity: 5,
        reason: 'New shipment arrived',
        reason_category: 'new_shipment' as InventoryReasonCategory,
        type: 'increase' as AdjustmentType,
        notes: 'Test notes'
      };
      
      const result = await inventoryStockService.adjustStock('1', adjustmentParams);
      
      // Verify Supabase was called correctly
      expect(supabase.from).toHaveBeenCalledWith('inventory');
      expect(supabase.from).toHaveBeenCalledWith('inventory_movements');
      
      // Verify update was called with correct values
      const updateCall = (supabase.from as jest.Mock).mock.calls.find(
        call => call[0] === 'inventory'
      );
      expect(updateCall).toBeTruthy();
      
      // Verify inventory movement was recorded
      const insertCall = (supabase.from as jest.Mock).mock.calls.find(
        call => call[0] === 'inventory_movements'
      );
      expect(insertCall).toBeTruthy();
      
      // Verify result
      expect(result.current_stock).toBe(15); // 10 + 5
    });

    test('should decrease stock correctly', async () => {
      // Call the service to decrease stock
      const adjustmentParams = {
        quantity: 3,
        reason: 'Damaged items removed',
        reason_category: 'damaged' as InventoryReasonCategory,
        type: 'decrease' as AdjustmentType,
        notes: 'Test notes'
      };
      
      // Mock the updated inventory with decreased stock
      (supabase.from as jest.Mock).mockImplementation(() => ({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockInventoryItem,
              error: null
            })
          })
        }),
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            select: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: { ...mockInventoryItem, current_stock: 7 }, // 10 - 3
                error: null
              })
            })
          })
        }),
        insert: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              error: null
            })
          })
        })
      }));
      
      const result = await inventoryStockService.adjustStock('1', adjustmentParams);
      
      // Verify result
      expect(result.current_stock).toBe(7); // 10 - 3
    });

    test('should set stock to specific value', async () => {
      // Call the service to set stock
      const adjustmentParams = {
        quantity: 20,
        reason: 'Inventory count adjustment',
        reason_category: 'inventory_count' as InventoryReasonCategory,
        type: 'set' as AdjustmentType,
        notes: 'Test notes'
      };
      
      // Mock the updated inventory with set stock
      (supabase.from as jest.Mock).mockImplementation(() => ({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockInventoryItem,
              error: null
            })
          })
        }),
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            select: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: { ...mockInventoryItem, current_stock: 20 },
                error: null
              })
            })
          })
        }),
        insert: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              error: null
            })
          })
        })
      }));
      
      const result = await inventoryStockService.adjustStock('1', adjustmentParams);
      
      // Verify result
      expect(result.current_stock).toBe(20);
    });

    test('should throw error when trying to set negative stock', async () => {
      // Call the service to set negative stock
      const adjustmentParams = {
        quantity: -5,
        reason: 'Invalid adjustment',
        reason_category: 'inventory_count' as InventoryReasonCategory,
        type: 'set' as AdjustmentType,
        notes: 'Test notes'
      };
      
      // Expect the service to throw an error
      await expect(inventoryStockService.adjustStock('1', adjustmentParams))
        .rejects
        .toThrow('Cannot set stock to a negative value');
    });

    test('should throw error when decreasing below zero', async () => {
      // Call the service to decrease more than available
      const adjustmentParams = {
        quantity: 15, // Current stock is 10
        reason: 'Too much decrease',
        reason_category: 'damaged' as InventoryReasonCategory,
        type: 'decrease' as AdjustmentType,
        notes: 'Test notes'
      };
      
      // Expect the service to throw an error
      await expect(inventoryStockService.adjustStock('1', adjustmentParams))
        .rejects
        .toThrow('Stock level cannot be negative after decrease');
    });

    test('should update timestamps based on reason category', async () => {
      // Call the service with reason category that updates last_counted_at
      const adjustmentParams = {
        quantity: 12,
        reason: 'Inventory count',
        reason_category: 'inventory_count' as InventoryReasonCategory,
        type: 'set' as AdjustmentType,
        notes: 'Test notes'
      };
      
      // Setup spy to capture the update call arguments
      const updateSpy = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { 
                ...mockInventoryItem, 
                current_stock: 12,
                last_counted_at: '2023-01-15T12:00:00Z' // Should be updated
              },
              error: null
            })
          })
        })
      });
      
      (supabase.from as jest.Mock).mockImplementation((table) => {
        if (table === 'inventory') {
          return {
            select: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: mockInventoryItem,
                  error: null
                })
              })
            }),
            update: updateSpy
          };
        }
        return {
          insert: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                error: null
              })
            })
          })
        };
      });
      
      await inventoryStockService.adjustStock('1', adjustmentParams);
      
      // Verify update was called with timestamp updates
      expect(updateSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          last_counted_at: '2023-01-15T12:00:00Z'
        })
      );
    });

    // NEW TEST: Handle zero quantity adjustment
    test('should handle zero quantity adjustment correctly', async () => {
      // Call the service with zero quantity
      const adjustmentParams = {
        quantity: 0,
        reason: 'Zero adjustment test',
        reason_category: 'inventory_count' as InventoryReasonCategory,
        type: 'increase' as AdjustmentType,
        notes: 'Test notes'
      };
      
      // Expect the service to throw an error for zero quantity
      await expect(inventoryStockService.adjustStock('1', adjustmentParams))
        .rejects
        .toThrow('Quantity must be greater than zero');
    });

    // NEW TEST: Handle decimal quantities
    test('should handle decimal quantities correctly', async () => {
      // Setup mock inventory with decimal stock
      const mockDecimalInventory = {
        ...mockInventoryItem,
        current_stock: 10.5
      };
      
      // Mock the updated inventory with decimal stock
      (supabase.from as jest.Mock).mockImplementation(() => ({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockDecimalInventory,
              error: null
            })
          })
        }),
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            select: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: { ...mockDecimalInventory, current_stock: 12.0 }, // 10.5 + 1.5
                error: null
              })
            })
          })
        }),
        insert: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              error: null
            })
          })
        })
      }));
      
      // Call the service with decimal quantity
      const result = await inventoryStockService.adjustStock('1', {
        quantity: 1.5,
        reason: 'Adding partial units',
        reason_category: 'inventory_count' as InventoryReasonCategory,
        type: 'increase' as AdjustmentType,
        notes: 'Test notes'
      });
      
      // Verify result handles decimal correctly
      expect(result.current_stock).toBe(12.0); // 10.5 + 1.5 = 12.0
    });
  });

  describe('reserveStock', () => {
    test('should reserve stock correctly', async () => {
      // Setup spy to capture the update call arguments
      const updateSpy = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { 
                ...mockInventoryItem,
                reserved_stock: 4, // 2 + 2
                available_stock: 6  // 10 - 4
              },
              error: null
            })
          })
        })
      });
      
      (supabase.from as jest.Mock).mockImplementation((table) => {
        if (table === 'inventory') {
          return {
            select: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: mockInventoryItem,
                  error: null
                })
              })
            }),
            update: updateSpy
          };
        }
        return {
          insert: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                error: null
              })
            })
          })
        };
      });
      
      // Call the service to reserve stock
      await inventoryStockService.reserveStock('prod-1', 2, 'order-123');
      
      // Verify update was called with correct values
      expect(updateSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          reserved_stock: 4, // 2 + 2
          available_stock: 6  // 10 - 4
        })
      );
      
      // Verify inventory movement was recorded
      expect(supabase.from).toHaveBeenCalledWith('inventory_movements');
    });

    test('should throw error when trying to reserve more than available', async () => {
      // Setup inventory with low available stock
      const lowStockInventory = {
        ...mockInventoryItem,
        current_stock: 5,
        reserved_stock: 4,
        available_stock: 1
      };
      
      (supabase.from as jest.Mock).mockImplementation(() => ({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: lowStockInventory,
              error: null
            })
          })
        })
      }));
      
      // Call the service to reserve more than available
      await expect(inventoryStockService.reserveStock('prod-1', 2, 'order-123'))
        .rejects
        .toThrow('Not enough available stock to reserve');
    });

    // NEW TEST: Test concurrent reservations
    test('should handle concurrent stock reservations correctly', async () => {
      // Setup tracking variables for concurrent operations
      let currentReservedStock = 2; // Initial reserved stock
      let currentAvailableStock = 8; // Initial available stock
      
      // Mock implementation that updates values on each call
      (supabase.from as jest.Mock).mockImplementation((table) => {
        if (table === 'inventory') {
          return {
            select: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                single: jest.fn().mockImplementation(() => {
                  // Return current state when queried
                  return Promise.resolve({
                    data: {
                      ...mockInventoryItem,
                      reserved_stock: currentReservedStock,
                      available_stock: currentAvailableStock
                    },
                    error: null
                  });
                })
              })
            }),
            update: jest.fn().mockImplementation((updateData) => {
              // Update the mock state to simulate concurrent operations
              currentReservedStock = updateData.reserved_stock;
              currentAvailableStock = updateData.available_stock;
              
              return {
                eq: jest.fn().mockReturnValue({
                  select: jest.fn().mockReturnValue({
                    single: jest.fn().mockImplementation(() => {
                      // Return updated state after update
                      return Promise.resolve({
                        data: {
                          ...mockInventoryItem,
                          reserved_stock: currentReservedStock,
                          available_stock: currentAvailableStock
                        },
                        error: null
                      });
                    })
                  })
                })
              };
            })
          };
        }
        return {
          insert: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                error: null
              })
            })
          })
        };
      });
      
      // Make first reservation
      await inventoryStockService.reserveStock('prod-1', 2, 'order-123');
      // At this point: reserved = 4, available = 6
      
      // Make second reservation
      await inventoryStockService.reserveStock('prod-1', 3, 'order-456');
      // At this point: reserved = 7, available = 3
      
      // Verify final state is correct (2 + 2 + 3 = 7 reserved, 10 - 7 = 3 available)
      expect(currentReservedStock).toBe(7);
      expect(currentAvailableStock).toBe(3);
    });
  });
}); 