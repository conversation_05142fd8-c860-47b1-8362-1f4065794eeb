import { useState, useCallback } from 'react';
import { ProductFormData } from './useProductForm';
import inventoryService from '@/shared/lib/services/inventory/inventory-service';
import { notificationService } from '@/shared/lib/services/notification/notification-service';

interface UseProductFormManagementProps {
  onSuccess: () => void;
  onDetailPanelClose?: () => void;
  isDetailPanelOpen?: boolean;
}

interface UseProductFormManagementReturn {
  isProductFormModalOpen: boolean;
  productToEdit: Partial<ProductFormData> | null;
  productFormTitle: string;
  productFormButtonText: string;
  handleNewProduct: () => void;
  handleEditProduct: (productId: string) => Promise<void>;
  handleCloseProductFormModal: () => void;
  handleProductSaved: () => void;
}

/**
 * Custom hook for managing the product form modal state and actions
 * Handles opening/closing the modal and editing product data
 */
export function useProductFormManagement({
  onSuccess,
  onDetailPanelClose,
  isDetailPanelOpen
}: UseProductFormManagementProps): UseProductFormManagementReturn {
  // State for the product form modal
  const [isProductFormModalOpen, setIsProductFormModalOpen] = useState(false);
  const [productToEdit, setProductToEdit] = useState<Partial<ProductFormData> | null>(null);
  const [productFormTitle, setProductFormTitle] = useState('Add New Product');
  const [productFormButtonText, setProductFormButtonText] = useState('Create Product');

  const handleNewProduct = useCallback(() => {
    // Reset any editing state
    setProductToEdit(null);
    setProductFormTitle('Add New Product');
    setProductFormButtonText('Create Product');
    setIsProductFormModalOpen(true);
  }, []);

  const handleEditProduct = useCallback(async (productId: string) => {
    try {
      // Fetch the product data from the service layer
      const productData = await inventoryService.getByProductId(productId);
      
      if (!productData || !productData.product) {
        throw new Error('Failed to fetch product data');
      }
      
      // Format product data for the form
      const product = productData.product;
      const inventory = productData;
      
      // Set up the form data
      setProductToEdit({
        id: product.id,
        name: product.name,
        sku: product.sku,
        description: product.description || '',
        status: product.status || 'active',
        product_type: product.product_type || '',
        business_unit_id: product.business_unit_id || '',
        unit_cost: product.unit_cost || 0,
        unit_price: product.unit_price || 0,
        weight: product.weight || 0,
        dimensions_length: product.dimensions_length || 0,
        dimensions_width: product.dimensions_width || 0,
        dimensions_height: product.dimensions_height || 0,
        initial_stock: inventory.current_stock,
      });
      
      // Set modal titles
      setProductFormTitle('Edit Product');
      setProductFormButtonText('Update Product');
      
      // Open the form modal immediately
      setIsProductFormModalOpen(true);
    } catch (error) {
      console.error('Error fetching product for edit:', error);
      notificationService.error('Failed to load product data for editing');
    }
  }, []);

  const handleCloseProductFormModal = useCallback(() => {
    setIsProductFormModalOpen(false);
    // Clear the product to edit after a delay to avoid UI flickering
    setTimeout(() => {
      setProductToEdit(null);
    }, 300);
  }, []);

  const handleProductSaved = useCallback(() => {
    // Call the success callback provided by the parent
    onSuccess();
    
    // Close the detail panel if we were editing a product
    if (productToEdit && isDetailPanelOpen && onDetailPanelClose) {
      onDetailPanelClose();
    }
  }, [onSuccess, productToEdit, isDetailPanelOpen, onDetailPanelClose]);

  return {
    isProductFormModalOpen,
    productToEdit,
    productFormTitle,
    productFormButtonText,
    handleNewProduct,
    handleEditProduct,
    handleCloseProductFormModal,
    handleProductSaved
  };
} 