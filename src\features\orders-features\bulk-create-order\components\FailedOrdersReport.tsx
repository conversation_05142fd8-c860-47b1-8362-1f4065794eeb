import React from 'react';

interface FailedOrder {
  row: number;
  reason: string;
  data: Record<string, any>;
}

interface FailedOrdersReportProps {
  failedOrders: FailedOrder[];
  onDownload: () => void;
}

const FailedOrdersReport: React.FC<FailedOrdersReportProps> = ({ failedOrders, onDownload }) => {
  return (
    <div className="mt-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-medium text-red-600">
          {failedOrders.length} Order{failedOrders.length !== 1 ? 's' : ''} Failed
        </h3>
        <button
          onClick={onDownload}
          className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
        >
          <span className="mr-1">Download Failed Orders</span>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
        </button>
      </div>
      
      <div className="max-h-60 overflow-y-auto border border-gray-200 rounded-md">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50 sticky top-0">
            <tr>
              <th scope="col" className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider text-left">
                Row
              </th>
              <th scope="col" className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider text-left">
                Error
              </th>
              <th scope="col" className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider text-left">
                Order #
              </th>
              <th scope="col" className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider text-left">
                Customer
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {failedOrders.map((order, index) => (
              <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                  {order.row}
                </td>
                <td className="px-4 py-2 text-sm text-red-600 max-w-xs truncate">
                  {order.reason}
                </td>
                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                  {order.data?.order_number || 'N/A'}
                </td>
                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                  {order.data?.customer_name || order.data?.customer_email || 'N/A'}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      <div className="mt-4 bg-yellow-50 p-3 rounded-md border border-yellow-100">
        <p className="text-xs text-yellow-800">
          <span className="font-medium">Tip:</span> Download the failed orders report, fix the errors, and re-upload the CSV.
        </p>
      </div>
    </div>
  );
};

export default FailedOrdersReport; 