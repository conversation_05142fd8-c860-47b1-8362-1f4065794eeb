import React from 'react';
import { InventoryView, ProductStatusEnum } from '@/types';
import { TagIcon, CubeIcon, ScaleIcon, CurrencyDollarIcon, ArchiveBoxIcon, BuildingOfficeIcon } from '@heroicons/react/24/outline';
import { PanelLoadingSkeleton } from '@/shared/ui/feedback/LoadingSkeleton';
import { useProductDetails } from '@/shared/lib/hooks/inventory/products';

interface ProductBasicInfoProps {
  product: InventoryView;
}

const ProductBasicInfo: React.FC<ProductBasicInfoProps> = ({ product }) => {
  const { detailedProductData, isLoading, error } = useProductDetails(product);

  const getStatusStyle = (status: ProductStatusEnum | null) => {
    if (!status) return 'bg-gray-100 text-gray-800';
    
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'new':
        return 'bg-blue-100 text-blue-800';
      case 'issue':
        return 'bg-yellow-100 text-yellow-800';
      case 'discontinued':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };
  
  const formatCurrency = (value: number | null) => {
    if (value === null) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value);
  };

  if (isLoading) {
    return <PanelLoadingSkeleton />;
  }

  if (error) {
    return (
      <div className="bg-red-50 p-4 rounded-md text-red-700">
        {error}
      </div>
    );
  }

  if (!detailedProductData) {
    return <div className="text-gray-500">No detailed product information available</div>;
  }

  return (
    <div className="space-y-6">
      {/* Basic Product Information */}
      <section>
        <h4 className="text-lg font-medium text-gray-900 mb-3">Product Information</h4>
        <div className="bg-gray-50 rounded-lg p-4 space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <h2 className="text-xl font-semibold text-gray-900">{detailedProductData.name}</h2>
              <div className="flex items-center mt-1">
                <TagIcon className="h-4 w-4 text-gray-500 mr-1" />
                <span className="text-sm text-gray-600">{detailedProductData.sku}</span>
              </div>
            </div>
            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusStyle(detailedProductData.status)}`}>
              {detailedProductData.status || 'unknown'}
            </span>
          </div>
          
          {detailedProductData.description && (
            <div className="mt-2">
              <h5 className="text-sm font-medium text-gray-700 mb-1">Description</h5>
              <p className="text-sm text-gray-600">{detailedProductData.description}</p>
            </div>
          )}
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
            <div>
              <h5 className="text-sm font-medium text-gray-700 mb-2">Classification</h5>
              <div className="space-y-2">
                <div className="flex items-start">
                  <CubeIcon className="h-5 w-5 text-gray-400 mr-2 mt-0.5" />
                  <div>
                    <span className="text-xs text-gray-500">Category</span>
                    <p className="text-sm text-gray-900">{detailedProductData.category || 'N/A'}</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <ArchiveBoxIcon className="h-5 w-5 text-gray-400 mr-2 mt-0.5" />
                  <div>
                    <span className="text-xs text-gray-500">Product Type</span>
                    <p className="text-sm text-gray-900">{detailedProductData.product_type}</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <BuildingOfficeIcon className="h-5 w-5 text-gray-400 mr-2 mt-0.5" />
                  <div>
                    <span className="text-xs text-gray-500">Business Unit</span>
                    <p className="text-sm text-gray-900">{detailedProductData.business_unit_name || 'N/A'}</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div>
              <h5 className="text-sm font-medium text-gray-700 mb-2">Inventory</h5>
              <div className="space-y-2">
                <div className="flex items-start">
                  <ScaleIcon className="h-5 w-5 text-gray-400 mr-2 mt-0.5" />
                  <div>
                    <span className="text-xs text-gray-500">Current Stock</span>
                    <p className="text-sm text-gray-900">
                      {detailedProductData.inventory?.current_stock !== undefined ? 
                        detailedProductData.inventory.current_stock : 'N/A'}
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <CurrencyDollarIcon className="h-5 w-5 text-gray-400 mr-2 mt-0.5" />
                  <div>
                    <span className="text-xs text-gray-500">Unit Price</span>
                    <p className="text-sm text-gray-900">{formatCurrency(detailedProductData.unit_price)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* <div className="mt-4">
            <h5 className="text-sm font-medium text-gray-700 mb-2">Platform Identifiers</h5>
            <div className="bg-white rounded-md border border-gray-200 overflow-hidden">
              {detailedProductData.identifiers && detailedProductData.identifiers.length > 0 ? (
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Platform</th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Identifier</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {detailedProductData.identifiers.map((identifier, index) => (
                      <tr key={index}>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-700">{identifier.platform_name}</td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-700">{identifier.platform_identifier}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <div className="p-3 text-sm text-gray-500 italic">No platform identifiers found</div>
              )}
            </div>
          </div> */}
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
            <div>
              <h5 className="text-sm font-medium text-gray-700 mb-2">Creation Info</h5>
              <div className="space-y-2">
                <div>
                  <span className="text-xs text-gray-500">Created</span>
                  <p className="text-sm text-gray-900">{formatDate(detailedProductData.created_at)}</p>
                </div>
                <div>
                  <span className="text-xs text-gray-500">Last Updated</span>
                  <p className="text-sm text-gray-900">{formatDate(detailedProductData.updated_at)}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ProductBasicInfo; 