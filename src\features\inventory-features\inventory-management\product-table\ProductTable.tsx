import React, { useState, useCallback, memo, useMemo } from 'react';
import { InventoryView, ProductStatusEnum, ActiveFilter } from '@/types';
import { Pagination } from '@/shared/ui/navigation';
import { TableLoadingSkeleton } from '@/shared/ui/feedback/LoadingSkeleton';
import { cn } from '@/shared/lib/utils/core/cn';
import ErrorMessage from '@/shared/ui/feedback/ErrorMessage';
import { ChevronUpIcon, ChevronDownIcon, EyeIcon } from '@heroicons/react/24/solid';

export interface SortConfig {
  column: keyof InventoryView | null;
  direction: 'asc' | 'desc';
}

// Static loading skeleton component for initial page load
export const ProductTableSkeleton: React.FC = () => <TableLoadingSkeleton data-testid="table-loading-indicator" />;

interface ProductTableProps {
  products: InventoryView[];
  loading: boolean;
  error: Error | null;
  onSelectProduct: (productId: string) => void;
  onSelectAll: (event: React.ChangeEvent<HTMLInputElement>) => void;
  isAllSelected: boolean;
  isSomeSelected: boolean;
  selectedProductIds: Set<string>;
  // Pagination props
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  // Sorting props
  onSort: (column: keyof InventoryView) => void;
  sortConfig: SortConfig;
  // Detail panel prop
  onProductClick?: (productId: string) => void;
  // Optional callbacks
  onRetryLoad?: () => void;
  onAddNewProduct?: () => void;
  onClearFilters?: () => void;
  // Active filters
  activeFilters?: ActiveFilter[];
}

// Custom comparison function for memoization
const areEqual = (prevProps: ProductTableProps, nextProps: ProductTableProps) => {
  // Check if function references changed
  if (
    prevProps.onSelectProduct !== nextProps.onSelectProduct ||
    prevProps.onSelectAll !== nextProps.onSelectAll ||
    prevProps.onPageChange !== nextProps.onPageChange ||
    prevProps.onSort !== nextProps.onSort ||
    prevProps.onRetryLoad !== nextProps.onRetryLoad ||
    prevProps.onAddNewProduct !== nextProps.onAddNewProduct ||
    prevProps.onClearFilters !== nextProps.onClearFilters
  ) {
    return false;
  }

  // Check if UI state booleans changed
  if (
    prevProps.isAllSelected !== nextProps.isAllSelected ||
    prevProps.isSomeSelected !== nextProps.isSomeSelected ||
    prevProps.loading !== nextProps.loading ||
    prevProps.hasNextPage !== nextProps.hasNextPage ||
    prevProps.hasPreviousPage !== nextProps.hasPreviousPage
  ) {
    return false;
  }

  // Check if pagination state changed
  if (
    prevProps.currentPage !== nextProps.currentPage ||
    prevProps.totalPages !== nextProps.totalPages
  ) {
    return false;
  }

  // Check if sorting configuration changed
  if (
    prevProps.sortConfig.column !== nextProps.sortConfig.column ||
    prevProps.sortConfig.direction !== nextProps.sortConfig.direction
  ) {
    return false;
  }

  // Check if products array reference or error changed
  if (
    prevProps.products !== nextProps.products ||
    prevProps.error !== nextProps.error ||
    prevProps.activeFilters !== nextProps.activeFilters
  ) {
    return false;
  }

  // Check if selection state changed
  if (prevProps.selectedProductIds !== nextProps.selectedProductIds) {
    return false;
  }

  // If we got here, props are equal
  return true;
};

const ProductTable: React.FC<ProductTableProps> = ({
  products,
  loading,
  error,
  onSelectProduct,
  onSelectAll,
  isAllSelected,
  isSomeSelected,
  selectedProductIds,
  currentPage,
  totalPages,
  onPageChange,
  hasNextPage,
  hasPreviousPage,
  onSort,
  sortConfig,
  onProductClick,
  onRetryLoad,
  onAddNewProduct,
  onClearFilters,
  activeFilters = []
}) => {
  // State for stock indicator legend visibility
  const [isLegendVisible, setIsLegendVisible] = useState(true);

  // Toggle legend visibility
  const toggleLegend = useCallback(() => {
    setIsLegendVisible(prev => !prev);
  }, []);

  // Get sort direction icon for column
  const getSortIcon = useCallback((column: keyof InventoryView) => {
    if (sortConfig.column !== column) {
      return null;
    }
    return sortConfig.direction === 'asc' ? 
      <ChevronUpIcon className="h-4 w-4" /> : 
      <ChevronDownIcon className="h-4 w-4" />;
  }, [sortConfig]);

  // Get status badge style
  const getStatusBadge = useCallback((status: ProductStatusEnum | null) => {
    if (!status) return <span className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">Unknown</span>;
    
    switch (status) {
      case 'active':
        return <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Active</span>;
      case 'discontinued':
        return <span className="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">Discontinued</span>;
      case 'issue':
        return <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">Pending</span>;
      default:
        return <span className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">{status}</span>;
    }
  }, []);

  // Get stock indicator style
  const getStockIndicator = useCallback((product: InventoryView) => {
    const { current_stock, minimum_threshold, needs_reorder } = product;
    
    if (current_stock === null || current_stock === undefined) {
      return <div className="w-3 h-3 rounded-full bg-gray-300" title="No stock data" />;
    }
    
    if (current_stock <= 0) {
      return <div className="w-3 h-3 rounded-full bg-red-500" title="Out of stock" />;
    }
    
    if (minimum_threshold !== null && current_stock <= minimum_threshold) {
      return <div className="w-3 h-3 rounded-full bg-orange-500" title="Below minimum threshold" />;
    }
    
    if (needs_reorder) {
      return <div className="w-3 h-3 rounded-full bg-yellow-500" title="Needs reordering" />;
    }
    
    return <div className="w-3 h-3 rounded-full bg-green-500" title="Stock OK" />;
  }, []);

  // Determine if a row should be highlighted based on sorting
  const getRowHighlight = (product: InventoryView) => {
    // If we're sorting by a column, highlight rows with min/max values
    if (sortConfig.column && product[sortConfig.column] !== null) {
      // Implementation would depend on specific business rules
      // For example, highlight rows with low stock or highest inventory
      return '';
    }
    return '';
  };

  // Render table header with sort functionality
  const renderTableHeader = () => {
    const getColumnHeaderClass = (column: keyof InventoryView) => {
      return cn(
        "px-6 py-3 text-left text-xs font-medium uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors duration-150",
        sortConfig.column === column ? "text-blue-600 bg-blue-50" : "text-gray-500"
      );
    };

    return (
      <thead className="bg-gray-50">
        <tr>
          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            <input 
              type="checkbox" 
              onChange={onSelectAll} 
              checked={isAllSelected}
              ref={el => {
                if (el) {
                  el.indeterminate = isSomeSelected && !isAllSelected;
                }
              }}
              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
          </th>
          <th 
            scope="col" 
            className={getColumnHeaderClass('name')}
            onClick={() => onSort('name')}
          >
            Name {getSortIcon('name')}
          </th>
          <th 
            scope="col" 
            className={getColumnHeaderClass('sku')}
            onClick={() => onSort('sku')}
          >
            SKU {getSortIcon('sku')}
          </th>
          <th 
            scope="col" 
            className={getColumnHeaderClass('product_type')}
            onClick={() => onSort('product_type')}
          >
            Type {getSortIcon('product_type')}
          </th>
          <th 
            scope="col" 
            className={getColumnHeaderClass('status')}
            onClick={() => onSort('status')}
          >
            Status {getSortIcon('status')}
          </th>
          <th 
            scope="col" 
            className={getColumnHeaderClass('current_stock')}
            onClick={() => onSort('current_stock')}
          >
            Current Stock {getSortIcon('current_stock')}
          </th>
          <th 
            scope="col" 
            className={getColumnHeaderClass('available_stock')}
            onClick={() => onSort('available_stock')}
          >
            Available {getSortIcon('available_stock')}
          </th>
          <th 
            scope="col" 
            className={getColumnHeaderClass('reserved_stock')}
            onClick={() => onSort('reserved_stock')}
          >
            Reserved {getSortIcon('reserved_stock')}
          </th>
        </tr>
      </thead>
    );
  };

  // First let's locate the renderTableRow function and wrap it with useMemo
  const renderTableRows = useMemo(() => {
    if (products.length === 0) {
      return null;
    }

    return products.map(product => {
      // Get product ID with fallback
      const productId = product.product_id || product.inventory_id || '';
      
      return (
        <tr 
          key={productId}
          className={cn(
            "hover:bg-gray-50 transition-colors duration-150",
            getRowHighlight(product)
          )}
          onClick={() => onProductClick?.(productId)}
        >
          <td className="px-6 py-4 whitespace-nowrap" onClick={(e) => e.stopPropagation()}>
            <input
              type="checkbox"
              checked={selectedProductIds.has(productId)}
              onChange={() => onSelectProduct(productId)}
              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
          </td>
          <td className="px-6 py-4 whitespace-nowrap">
            <div className="flex items-center">
              {getStockIndicator(product)}
              <span className="ml-2 text-sm font-medium text-gray-900">{product.name || 'Unnamed Product'}</span>
            </div>
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            {product.sku || 'No SKU'}
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            {product.product_type || 'Unspecified'}
          </td>
          <td className="px-6 py-4 whitespace-nowrap">
            {getStatusBadge(product.status)}
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            {product.current_stock !== null && product.current_stock !== undefined 
              ? product.current_stock
              : 'N/A'}
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            {product.available_stock !== null && product.available_stock !== undefined 
              ? product.available_stock
              : 'N/A'}
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            {product.reserved_stock !== null && product.reserved_stock !== undefined 
              ? product.reserved_stock
              : 'N/A'}
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
            <button
              onClick={(e) => {
                e.stopPropagation();
                if (onProductClick) onProductClick(productId);
              }}
              className="text-blue-600 hover:text-blue-900"
              title="View details"
            >
              <EyeIcon className="h-4 w-4" />
            </button>
          </td>
        </tr>
      );
    });
  }, [products, onProductClick, onSelectProduct, selectedProductIds, getStockIndicator, getStatusBadge, getRowHighlight]);

  // Let's find the renderEmptyState function and wrap it with useMemo
  const renderEmptyState = useMemo(() => {
    if (products.length > 0 || loading) {
      return null;
    }

    return (
      <tr>
        <td colSpan={8} className="px-6 py-12">
          <div className="text-center">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No products found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {activeFilters.length > 0 ? (
                <>
                  No products match the current filters.{' '}
                  <button
                    onClick={onClearFilters}
                    className="text-blue-600 hover:text-blue-500"
                  >
                    Clear filters
                  </button>
                </>
              ) : (
                <>
                  Get started by adding a new product to your inventory.{' '}
                  <button
                    onClick={onAddNewProduct}
                    className="text-blue-600 hover:text-blue-500"
                  >
                    Add product
                  </button>
                </>
              )}
            </p>
          </div>
        </td>
      </tr>
    );
  }, [products.length, loading, activeFilters, onClearFilters, onAddNewProduct]);

  // Let's find the renderError function and wrap it with useMemo
  const renderError = useMemo(() => {
    if (!error) {
      return null;
    }

    return (
      <tr>
        <td colSpan={8}>
          <ErrorMessage
            message={error.message || 'An error occurred while loading products.'}
            onRetry={onRetryLoad}
          />
        </td>
      </tr>
    );
  }, [error, onRetryLoad]);

  // Render loading state
  if (loading && products.length === 0) {
    return <TableLoadingSkeleton data-testid="table-loading-indicator" />;
  }

  // Render error state
  if (error) {
    return (
      <ErrorMessage 
        message={error.message || 'Error loading inventory data'}
        variant="error"
        className="max-w-lg"
      />
    );
  }

  // Render empty state
  if (products.length === 0) {
    return (
      <div className="p-8 text-center">
        <p className="text-gray-500 text-lg">No products found.</p>
        <p className="text-gray-400 mt-2">Try adjusting your search or filters.</p>
      </div>
    );
  }

  // Status styling helper
  const getStatusStyle = (status: ProductStatusEnum | null) => {
    switch(status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'new':
        return 'bg-blue-100 text-blue-800';
      case 'discontinued':
        return 'bg-red-100 text-red-800';
      case 'issue':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="flex flex-col">
      {/* Stock Indicator Legend */}
      <div className="px-3 sm:px-6 py-3 bg-gray-50 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <button 
            className="text-sm font-medium text-gray-700 flex items-center"
            onClick={toggleLegend}
          >
            Stock Indicators
            {isLegendVisible ? 
              <ChevronUpIcon className="h-4 w-4 ml-1" /> : 
              <ChevronDownIcon className="h-4 w-4 ml-1" />
            }
          </button>
          
          {loading && products.length > 0 && (
            <div className="flex items-center text-sm text-gray-500" data-testid="table-loading-indicator">
              <div className="mr-2 h-4 w-4 border-2 border-gray-200 border-t-blue-500 rounded-full animate-spin"></div>
              Refreshing...
            </div>
          )}
        </div>
        
        {isLegendVisible && (
          <div className="mt-2 grid grid-cols-2 sm:flex sm:space-x-4 gap-y-2 text-xs text-gray-600">
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-green-500 mr-2" />
              <span>Stock OK</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2" />
              <span>Needs Reorder</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-orange-500 mr-2" />
              <span>Below Threshold</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-red-500 mr-2" />
              <span>Out of Stock</span>
            </div>
          </div>
        )}
      </div>

      {/* Desktop Table (hidden on small screens) */}
      <div className="hidden md:block">
        <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div className="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
            <div className="overflow-hidden border border-gray-200 sm:rounded-lg">
              <table className="min-w-full divide-y divide-gray-200">
                {renderTableHeader()}
                <tbody className="bg-white divide-y divide-gray-200">
                  {renderError}
                  {loading && !products.length ? (
                    <tr>
                      <td colSpan={8}>
                        <div className="py-12">
                          <TableLoadingSkeleton data-testid="table-loading-indicator" />
                        </div>
                      </td>
                    </tr>
                  ) : renderEmptyState || renderTableRows}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile/Tablet Card View (shown on small screens) */}
      <div className="md:hidden">
        <div className="grid gap-4 px-2">
          {products.map((product) => (
            <div 
              key={product.inventory_id} 
              className={cn(
                "bg-white p-4 rounded-lg border shadow-sm cursor-pointer",
                product.current_stock !== null ? (
                  product.current_stock <= 0 ? 'border-red-300 bg-red-50' : 
                  product.needs_reorder ? 'border-yellow-300 bg-yellow-50' :
                  (product.minimum_threshold !== null && product.current_stock < product.minimum_threshold) ? 'border-orange-300 bg-orange-50' : 'border-gray-200'
                ) : 'border-gray-200'
              )}
              onClick={() => product.product_id && onProductClick && onProductClick(product.product_id)}
            >
              {/* Header with checkbox, name and stock indicator */}
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <input 
                    type="checkbox" 
                    checked={selectedProductIds.has(product.product_id || '')}
                    onChange={() => product.product_id && onSelectProduct(product.product_id)}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <div className="flex items-center">
                    {getStockIndicator(product)}
                    <span className="font-medium text-gray-900">{product.name || 'N/A'}</span>
                  </div>
                </div>
                <span className={cn(
                  "px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full",
                  getStatusStyle(product.status)
                )}>
                  {product.status || 'unknown'}
                </span>
              </div>

              {/* Product details */}
              <div className="grid grid-cols-2 gap-1 text-sm">
                <div>
                  <span className="text-gray-500">SKU:</span>
                  <span className="ml-2 text-gray-900">{product.sku || 'N/A'}</span>
                </div>
                <div>
                  <span className="text-gray-500">Type:</span>
                  <span className="ml-2 text-gray-900">{product.product_type || 'N/A'}</span>
                </div>
                <div>
                  <span className="text-gray-500">Current Stock:</span>
                  <span className="ml-2 text-gray-900">
                    {product.current_stock !== null ? product.current_stock : 'N/A'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-500">Available:</span>
                  <span className="ml-2 text-gray-900">
                    {product.available_stock !== null ? product.available_stock : 'N/A'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-500">Reserved:</span>
                  <span className="ml-2 text-gray-900">
                    {product.reserved_stock !== null ? product.reserved_stock : 'N/A'}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Pagination with custom text */}
      <div className="py-4 px-4 sm:px-6">
        {totalPages > 1 && (
          <div className="text-sm text-gray-700 text-center mb-2">
            Page {currentPage} of {totalPages}
          </div>
        )}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={onPageChange}
          hasNextPage={hasNextPage}
          hasPreviousPage={hasPreviousPage}
          className="justify-center"
        />
      </div>
    </div>
  );
};

// Export memoized component with custom comparison
export default memo(ProductTable, areEqual);