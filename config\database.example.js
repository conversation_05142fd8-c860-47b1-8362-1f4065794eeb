// Database Configuration Example
// Copy this file to config/database.js and update with your actual values

export const databaseConfig = {
  // Database Connection
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'washolding_internal',
  username: process.env.DB_USER || 'your_username',
  password: process.env.DB_PASSWORD || 'your_password',
  
  // Connection Pool Settings
  pool: {
    min: 2,
    max: 10,
    acquire: 30000,
    idle: 10000
  },
  
  // API Configuration
  api: {
    baseURL: process.env.API_BASE_URL || 'http://localhost:3001/api',
    timeout: parseInt(process.env.API_TIMEOUT) || 10000,
    retries: 3
  },
  
  // Feature Flags for gradual migration
  features: {
    useMockData: process.env.VITE_USE_MOCK_DATA === 'true',
    enableRealTimeUpdates: process.env.VITE_ENABLE_REAL_TIME_UPDATES === 'true',
    enableAnalytics: process.env.VITE_ENABLE_ANALYTICS === 'true'
  }
};

// Table configurations for optimized queries
export const tableConfig = {
  orders: {
    batchSize: 100,
    cacheTimeout: 300000, // 5 minutes
    indexes: ['status', 'platform', 'created_at']
  },
  
  delivery: {
    batchSize: 50,
    cacheTimeout: 180000, // 3 minutes
    indexes: ['tracking_number', 'status', 'updated_at']
  },
  
  inventory: {
    batchSize: 200,
    cacheTimeout: 600000, // 10 minutes
    indexes: ['sku', 'status', 'quantity']
  }
}; 