import { supabase } from '../../../../../supabase/supabase_client/client';
import { OfflineStorage, OfflineOperationType } from '@/shared/lib/utils/data/offline-storage';
import type {
  InventoryView,
  ProductStatusEnum,
  InventoryReasonCategory,
  InventoryMovementType
} from '@/types';


/**
 * Interface for inventory list parameters
 */
export interface InventoryListParams {
  search?: string;
  status?: ProductStatusEnum[];
  productType?: string[];
  stockCondition?: string[];
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
}

/**
 * Interface for inventory update data
 */
export interface InventoryUpdateData {
  quantityOnHand?: number;
  minimumThreshold?: number;
  maximumThreshold?: number;
  reorderPoint?: number;
  reorderQuantity?: number;
  unitCost?: number;
  notes?: string;
}

/**
 * Interface for stock adjustment parameters
 */
export interface StockAdjustmentParams {
  quantity: number;
  reason: string;
  reason_category: InventoryReasonCategory;
  type: InventoryMovementType;
  notes?: string;
  user_id?: string;
  user_name?: string;
}

/**
 * Interface for bulk threshold update parameters
 */
export interface ThresholdUpdateParams {
  inventoryId: string;
  minimumThreshold?: number;
  maximumThreshold?: number;
  reorderPoint?: number;
}

/**
 * Interface for bulk stock adjustment parameters
 */
export interface BulkStockAdjustmentParams {
  productIds: string[];
  quantity: number;
  reason: string;
  reason_category: InventoryReasonCategory;
  type: InventoryMovementType;
  notes?: string;
}

/**
 * Service class for core inventory operations
 */
export class InventoryService {
  private offlineStorage: OfflineStorage;

  constructor() {
    this.offlineStorage = OfflineStorage.getInstance();
  }

  /**
   * Get filtered inventory list with sorting and pagination
   */
  async getInventoryList(params: InventoryListParams = {}) {
    try {
      let query = supabase.from('inventory_view').select('*', { count: 'exact' });

      // Apply search filter if provided
      if (params.search) {
        query = query.or(`name.ilike.%${params.search}%,sku.ilike.%${params.search}%`);
      }

      // Apply status filter if provided
      if (params.status && params.status.length > 0) {
        query = query.in('status', params.status);
      }

      // Apply product type filter if provided
      if (params.productType && params.productType.length > 0) {
        query = query.in('product_type', params.productType);
      }

      // Apply stock condition filter if provided
      if (params.stockCondition && params.stockCondition.length > 0) {
        // Stock conditions require special handling
        params.stockCondition.forEach(condition => {
          switch(condition) {
            case 'out_of_stock':
              query = query.eq('current_stock', 0);
              break;
            case 'low_stock':
              query = query.lt('current_stock', 5).gt('current_stock', 0); // Example threshold
              break;
            case 'needs_reorder':
              query = query.eq('needs_reorder', true);
              break;
          }
        });
      }

      // Apply sorting if provided
      if (params.sortBy) {
        query = query.order(params.sortBy, { ascending: params.sortDirection === 'asc' });
      }

      // Apply pagination if provided
      if (params.page && params.pageSize) {
        const from = (params.page - 1) * params.pageSize;
        const to = from + params.pageSize - 1;
        query = query.range(from, to);
      }

      const { data, error, count } = await query;

      if (error) throw error;

      // Store the fetched data in offline storage
      if (data && data.length > 0) {
        const inventoryMap: Record<string, InventoryView> = {};
        data.forEach(item => {
          if (item.inventory_id) {
            inventoryMap[item.inventory_id] = item;
          }
        });
        this.offlineStorage.saveBulkOfflineData('inventory_view', inventoryMap);
      }

      return { data: data || [], count: count || 0 };
    } catch (error) {
      console.error('Error in getInventoryList, trying offline data:', error);
      
      // If we're offline or encountered an error, try to get data from offline storage
      if (!navigator.onLine || (error instanceof Error && error.message.includes('network'))) {
        return this.getOfflineInventoryList(params);
      }
      
      throw error;
    }
  }

  /**
   * Get inventory list from offline storage
   */
  private async getOfflineInventoryList(params: InventoryListParams = {}): Promise<{ data: InventoryView[], count: number }> {
    try {
      // Get all inventory data from offline storage
      const allInventory = this.offlineStorage.getAllOfflineData<InventoryView>('inventory_view');
      
      let filteredInventory = Object.values(allInventory);
      
      // Apply search filter if provided
      if (params.search) {
        const searchLower = params.search.toLowerCase();
        filteredInventory = filteredInventory.filter(item => 
          item.name?.toLowerCase().includes(searchLower) || 
          item.sku?.toLowerCase().includes(searchLower)
        );
      }

      // Apply status filter if provided
      if (params.status && params.status.length > 0) {
        filteredInventory = filteredInventory.filter(item => 
          item.status && params.status?.includes(item.status)
        );
      }

      // Apply product type filter if provided
      if (params.productType && params.productType.length > 0) {
        filteredInventory = filteredInventory.filter(item => 
          item.product_type && params.productType?.includes(item.product_type)
        );
      }

      // Apply stock condition filter if provided
      if (params.stockCondition && params.stockCondition.length > 0) {
        filteredInventory = filteredInventory.filter(item => {
          return params.stockCondition?.some(condition => {
            switch(condition) {
              case 'out_of_stock':
                return item.current_stock === 0;
              case 'low_stock':
                return item.current_stock !== null && item.current_stock > 0 && item.current_stock < 5;
              case 'needs_reorder':
                return item.needs_reorder === true;
              default:
                return false;
            }
          });
        });
      }

      // Apply sorting if provided
      if (params.sortBy) {
        filteredInventory.sort((a, b) => {
          const aValue = a[params.sortBy as keyof InventoryView];
          const bValue = b[params.sortBy as keyof InventoryView];
          
          // Handle null values
          if (aValue === null && bValue === null) return 0;
          if (aValue === null) return 1;
          if (bValue === null) return -1;
          
          if (typeof aValue === 'string' && typeof bValue === 'string') {
            return params.sortDirection === 'asc' 
              ? aValue.localeCompare(bValue) 
              : bValue.localeCompare(aValue);
          }
          
          if (typeof aValue === 'number' && typeof bValue === 'number') {
            return params.sortDirection === 'asc' 
              ? aValue - bValue 
              : bValue - aValue;
          }
          
          return 0;
        });
      }

      // Apply pagination if provided
      if (params.page && params.pageSize) {
        const startIdx = (params.page - 1) * params.pageSize;
        const endIdx = startIdx + params.pageSize;
        filteredInventory = filteredInventory.slice(startIdx, endIdx);
      }

      return {
        data: filteredInventory,
        count: filteredInventory.length
      };
    } catch (error) {
      console.error('Error fetching offline inventory data:', error);
      return { data: [], count: 0 };
    }
  }

  /**
   * Get inventory details for a product
   */
  async getByProductId(productId: string) {
    try {
      // Get inventory data
      const { data: inventoryData, error: inventoryError } = await supabase
        .from('inventory')
        .select('*')
        .eq('product_id', productId)
        .single();
        
      if (inventoryError) throw inventoryError;
      
      // Get product data
      const { data: productData, error: productError } = await supabase
        .from('products')
        .select('*')
        .eq('id', productId)
        .single();
      
      if (productError) throw productError;
      
      return {
        ...inventoryData,
        product: productData
      };
    } catch (error) {
      console.error('Error fetching inventory by product ID:', error);
      
      // If offline, try to get data from storage
      if (!navigator.onLine || (error instanceof Error && error.message.includes('network'))) {
        return this.getOfflineProductInventory(productId);
      }
      
      throw error;
    }
  }
  
  /**
   * Get inventory details from offline storage
   */
  private getOfflineProductInventory(productId: string) {
    // This is a simplified version - in a real app, you'd want to have a more robust
    // offline data management system for related entities
    const inventory = this.offlineStorage.getOfflineData<InventoryView>('inventory_view', productId);
    return inventory ? { ...inventory, product: { id: productId, name: inventory.name, sku: inventory.sku } } : null;
  }

  /**
   * Get inventory details by ID
   */
  async getById(id: string) {
    try {
      // Get inventory data
      const { data: inventoryData, error: inventoryError } = await supabase
        .from('inventory')
        .select('*')
        .eq('id', id)
        .single();
        
      if (inventoryError) throw inventoryError;
      
      // Get product data
      const { data: productData, error: productError } = await supabase
        .from('products')
        .select('*')
        .eq('id', inventoryData.product_id)
        .single();
      
      if (productError) throw productError;
      
      return {
        ...inventoryData,
        product: productData
      };
    } catch (error) {
      console.error('Error fetching inventory by ID:', error);
      throw error;
    }
  }

  /**
   * Update inventory details
   */
  async update(id: string, data: InventoryUpdateData) {
    try {
      // Prepare update data
      const updateData: Record<string, any> = {};
      
      if (data.quantityOnHand !== undefined) {
        updateData.current_stock = data.quantityOnHand;
      }
      
      if (data.minimumThreshold !== undefined) {
        updateData.minimum_threshold = data.minimumThreshold;
      }
      
      if (data.maximumThreshold !== undefined) {
        updateData.maximum_threshold = data.maximumThreshold;
      }
      
      if (data.reorderPoint !== undefined) {
        updateData.reorder_point = data.reorderPoint;
      }
      
      if (data.notes !== undefined) {
        updateData.notes = data.notes;
      }
      
      // Always update the timestamp
      updateData.updated_at = new Date().toISOString();
      
      // Update the inventory record
      const { data: updatedData, error } = await supabase
        .from('inventory')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      
      // Store in offline storage
      this.offlineStorage.saveOfflineData('inventory', id, updatedData);
      
      // Queue the operation for offline sync if needed
      this.offlineStorage.addPendingOperation({
        type: OfflineOperationType.UPDATE,
        entityType: 'inventory',
        data: { id, updateData }
      });
      
      return updatedData;
    } catch (error) {
      console.error('Error updating inventory:', error);
      throw error;
    }
  }

  /**
   * Create a new inventory record
   */
  async create(productId: string, data: InventoryUpdateData) {
    try {
      // Check if inventory already exists for this product
      const { data: existingInventory, error: checkError } = await supabase
        .from('inventory')
        .select('id')
        .eq('product_id', productId)
        .maybeSingle();
      
      if (checkError) throw checkError;
      
      if (existingInventory) {
        throw new Error('Inventory record already exists for this product');
      }
      
      // Prepare create data
      const createData = {
        product_id: productId,
        current_stock: data.quantityOnHand || 0,
        available_stock: data.quantityOnHand || 0,
        reserved_stock: 0,
        minimum_threshold: data.minimumThreshold,
        maximum_threshold: data.maximumThreshold,
        reorder_point: data.reorderPoint,
        notes: data.notes,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      // Create the inventory record
      const { data: newInventory, error } = await supabase
        .from('inventory')
        .insert(createData)
        .select()
        .single();
      
      if (error) throw error;
      
      // Store in offline storage
      this.offlineStorage.saveOfflineData('inventory', newInventory.id, newInventory);
      
      // Queue the operation for offline sync if needed
      this.offlineStorage.addPendingOperation({
        type: OfflineOperationType.CREATE,
        entityType: 'inventory',
        data: { productId, createData }
      });
      
      return newInventory;
    } catch (error) {
      console.error('Error creating inventory:', error);
      throw error;
    }
  }

  /**
   * Delete an inventory record
   */
  async delete(id: string) {
    try {
      const { error } = await supabase
        .from('inventory')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
      
      // Queue delete operation for offline sync
      this.offlineStorage.addPendingOperation({
        type: OfflineOperationType.DELETE,
        entityType: 'inventory',
        data: { id }
      });
    } catch (error) {
      console.error('Error deleting inventory:', error);
      throw error;
    }
  }

  /**
   * Get unique product types
   */
  async getProductTypes() {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('product_type')
        .not('product_type', 'is', null);
      
      if (error) throw error;
      
      // Extract unique product types
      const productTypes = [...new Set(data
        .map(item => item.product_type)
        .filter(Boolean)
      )];
      
      return productTypes;
    } catch (error) {
      console.error('Error fetching product types:', error);
      return [];
    }
  }
}

// Export as singleton
export default new InventoryService(); 