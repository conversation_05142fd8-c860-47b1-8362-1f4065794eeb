import React, { useMemo } from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Toolt<PERSON>, Legend } from 'recharts';

interface TopSellersData {
  store: string;
  sales: number;
  color: string;
}

interface ShrimpTopSellersCardProps {
  data: TopSellersData[];
}

const ShrimpTopSellersCard: React.FC<ShrimpTopSellersCardProps> = ({ data }) => {
  // Memoize the formatLegendValue function to prevent recreation on each render
  const formatLegendValue = useMemo(() => {
    return (value: string) => {
      const maxLength = 20;
      if (value.length > maxLength) {
        return (
          <span className="text-sm text-gray-600" title={value}>
            {value.substring(0, maxLength - 3) + '...'}
          </span>
        );
      }
      return <span className="text-sm text-gray-600">{value}</span>;
    };
  }, []);
  
  // Memoize the formatter function for the Tooltip to prevent recreation on each render
  const tooltipFormatter = useMemo(() => {
    return (value: number) => new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(value);
  }, []);

  // Memoize the legend style object to prevent recreation on each render
  const legendWrapperStyle = useMemo(() => ({
    fontSize: '0.875rem',
    lineHeight: '1.25rem',
    paddingLeft: '1rem',
    maxWidth: '40%'
  }), []);
  
  return (
    <Link
      to="/reports"
      className="bg-card-bg p-6 rounded-lg shadow cursor-pointer hover:shadow-lg transition-shadow h-full block"
      aria-label="View shrimp top sellers report"
    >
      <h4 className="text-lg font-semibold text-gray-700 mb-4">Top Shrimp Sellers</h4>
      <div className="w-full h-[330px] relative">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart >
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius="80%"
              fill="#8884d8"
              dataKey="sales"
              nameKey="store"
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip formatter={tooltipFormatter} />
            <Legend 
              layout="vertical" 
              verticalAlign="middle" 
              align="right"
              iconSize={10}
              iconType="circle"
              wrapperStyle={legendWrapperStyle}
              formatter={formatLegendValue}
            />
          </PieChart>
        </ResponsiveContainer>
      </div>
    </Link>
  );
};

export default ShrimpTopSellersCard;