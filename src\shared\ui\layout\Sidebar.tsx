import React, { useState } from 'react';
import { NavLink, useNavigate } from 'react-router-dom';
import { SIDEBAR_TOP_ITEMS, SIDEBAR_BOTTOM_ITEMS } from '@/shared/config';
import { useAuth } from '@/app/providers/AuthProvider';
import { UserRole } from '@/types/index';
import { supabase } from '../../../../supabase/supabase_client/client';

interface SidebarProps {}

// Define role-based access rules
const getRoleBasedNavItems = (role: UserRole | undefined) => {
  if (!role) return []; // No navigation items for logged-out users

  const allItems = SIDEBAR_TOP_ITEMS;

  switch (role) {
    case 'master':
      // Master sees all features
      return allItems;
    
    case 'admin':
      // Ad<PERSON> sees all except Settings and User Management
      return allItems.filter(item => 
        item.label !== 'Settings' && item.label !== 'User Management'
      );
    
    case 'staff':
      // Staff sees only basic operational features
      return allItems.filter(item => 
        ['Orders', 'Fulfill', 'Delivery'].includes(item.label)
      );
    
    default:
      return [];
  }
};

// Loading skeleton component
const LoadingSkeleton: React.FC = () => (
  <div className="space-y-2 animate-pulse">
    {[1, 2, 3, 4, 5].map((i) => (
      <div key={i} className="flex items-center p-4">
        <div className="h-6 w-6 bg-gray-600 rounded mr-4"></div>
        <div className="h-4 bg-gray-600 rounded flex-1"></div>
      </div>
    ))}
  </div>
);

const Sidebar: React.FC<SidebarProps> = () => {
  // Get auth state using useAuth hook
  const { profile, isLoading } = useAuth();
  const navigate = useNavigate();
  
  // New state for hover-reveal functionality
  const [isSidebarVisible, setIsSidebarVisible] = useState(false);
  const [isPinned, setIsPinned] = useState(false);

  // Handle logout with Supabase
  const handleLogout = async () => {
    try {
      await supabase.auth.signOut();
      navigate('/login', { replace: true });
    } catch (error) {
      console.error('Error signing out:', error);
      // Navigate anyway to ensure logout
      navigate('/login', { replace: true });
    }
  };

  // Handle mouse enter - show sidebar when not pinned
  const handleMouseEnter = () => {
    if (!isPinned) {
      setIsSidebarVisible(true);
    }
  };

  // Handle mouse leave - hide sidebar when not pinned
  const handleMouseLeave = () => {
    if (!isPinned) {
      setIsSidebarVisible(false);
    }
  };

  // Handle click to toggle pin state
  const handleSidebarClick = () => {
    setIsPinned(!isPinned);
    if (!isPinned) {
      // When pinning, ensure sidebar is visible
      setIsSidebarVisible(true);
    }
  };

  // Get filtered navigation items based on user role
  const visibleNavItems = getRoleBasedNavItems(profile?.role);

  // Display loading skeleton when auth is loading
  if (isLoading) {
    return (
      <div 
        className="sidebar-wrapper fixed top-0 left-0 z-50"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {/* Invisible hover area - extends beyond sidebar when hidden */}
        <div className="sidebar-hover-area absolute top-0 left-0 w-12 h-screen bg-transparent" />
        
        {/* Main sidebar container with loading state */}
        <div 
          className={`
            sidebar-container fixed top-0 left-0 h-screen w-64 
            bg-sidebar-bg/95 backdrop-blur-sm text-sidebar-text
            transition-all duration-300 ease-in-out
            border-r border-gray-700/50 shadow-2xl
            ${isSidebarVisible || isPinned ? 'translate-x-0 opacity-100' : '-translate-x-full opacity-0'}
          `}
        >
          <div className="flex flex-col h-full p-4">
            {/* Header section */}
            <div className="mb-8 flex items-center justify-center">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gray-600 rounded-full animate-pulse"></div>
                <div className="ml-3 h-6 bg-gray-600 rounded w-24 animate-pulse"></div>
              </div>
            </div>

            {/* Loading skeleton for navigation */}
            <nav className="flex-grow">
              <LoadingSkeleton />
            </nav>
          </div>
        </div>
      </div>
    );
  }

  // Display nothing if user is logged out (profile is null)
  if (!profile) {
    return null;
  }

  return (
    <div 
      className="sidebar-wrapper fixed top-0 left-0 z-50"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Invisible hover area - extends beyond sidebar when hidden */}
      <div className="sidebar-hover-area absolute top-0 left-0 w-12 h-screen bg-transparent" />
      
      {/* Main sidebar container */}
      <div 
        className={`
          sidebar-container fixed top-0 left-0 h-screen w-64 
          bg-sidebar-bg/95 backdrop-blur-sm text-sidebar-text
          transition-all duration-300 ease-in-out
          border-r border-gray-700/50 shadow-2xl
          ${isSidebarVisible || isPinned ? 'translate-x-0 opacity-100' : '-translate-x-full opacity-0'}
          ${isPinned ? 'border-l-4 border-l-blue-500' : ''}
        `}
        onClick={handleSidebarClick}
      >
        <div className="flex flex-col h-full p-4">
          {/* Header section */}
          <div className="mb-8 flex items-center justify-center">
            <NavLink 
              to="/dashboard-center" 
              className="flex items-center hover:opacity-80 transition-opacity duration-150"
              title="Dashboard"
            >
              <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center shadow-lg">
                <span className="text-xl font-bold text-gray-800">W</span>
              </div>
              <span className="ml-3 text-xl font-semibold text-white">WAS-APP</span>
            </NavLink>
          </div>

          {/* Pin indicator */}
          {isPinned && (
            <div className="mb-4 text-center">
              <span className="text-xs text-blue-300 bg-blue-900/30 px-2 py-1 rounded-full">
                Pinned - Click to unpin
              </span>
            </div>
          )}

          {/* User role indicator */}
          <div className="mb-4 text-center">
            <span className="text-xs text-gray-300 bg-gray-800/50 px-2 py-1 rounded-full capitalize">
              {profile.role} • {profile.full_name}
            </span>
          </div>

          {/* Navigation section - filtered by role */}
          <nav className="flex-grow space-y-2">
            {visibleNavItems.map((item) => (
              <NavLink
                key={item.label}
                to={item.path}
                className={({ isActive }) =>
                  `nav-item flex items-center p-4 rounded-lg transition-all duration-200 ease-in-out group
                  ${isActive 
                    ? 'bg-primary text-white shadow-lg transform scale-105' 
                    : 'hover:bg-white/10 hover:text-white hover:shadow-md hover:transform hover:scale-102'
                  }`
                }
                title={item.label}
                onClick={(e) => e.stopPropagation()} // Prevent triggering sidebar pin toggle
              >
                <item.icon className="h-6 w-6 mr-4 group-hover:scale-110 transition-transform duration-200" aria-hidden="true" />
                <span className="font-medium">{item.label}</span>
              </NavLink>
            ))}
          </nav>

          {/* Bottom section */}
          <div className="mt-auto space-y-2">
            {SIDEBAR_BOTTOM_ITEMS.map((item) => {
              if (item.path === '#logout') {
                return (
                  <button
                    key={item.label}
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent triggering sidebar pin toggle
                      handleLogout();
                    }}
                    className="nav-item flex items-center w-full p-4 rounded-lg hover:bg-red-600/20 hover:text-red-300 transition-all duration-200 ease-in-out group"
                    title={item.label}
                  >
                    <item.icon className="h-6 w-6 mr-4 group-hover:scale-110 transition-transform duration-200" aria-hidden="true" />
                    <span className="font-medium">{item.label}</span>
                  </button>
                );
              }
              return (
                <NavLink
                  key={item.label}
                  to={item.path}
                  className={({ isActive }) =>
                    `nav-item flex items-center p-4 rounded-lg transition-all duration-200 ease-in-out group
                    ${isActive 
                      ? 'bg-primary text-white shadow-lg transform scale-105' 
                      : 'hover:bg-white/10 hover:text-white hover:shadow-md hover:transform hover:scale-102'
                    }`
                  }
                  title={item.label}
                  onClick={(e) => e.stopPropagation()} // Prevent triggering sidebar pin toggle
                >
                  <item.icon className="h-6 w-6 mr-4 group-hover:scale-110 transition-transform duration-200" aria-hidden="true" />
                  <span className="font-medium">{item.label}</span>
                </NavLink>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;