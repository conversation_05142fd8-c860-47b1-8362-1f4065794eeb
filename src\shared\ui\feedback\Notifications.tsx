import React, { useEffect, useState } from 'react';
import { notificationService, Notification } from '@/shared/lib/services/notification/notification-service';
import { createPortal } from 'react-dom';
import { cn } from '@/shared/lib/utils/core/cn';
import { FiX, FiAlertCircle, FiCheckCircle, FiInfo, FiAlertTriangle } from 'react-icons/fi';

interface NotificationItemProps {
  notification: Notification;
  onClose: (id: string) => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({ notification, onClose }) => {
  const { id, type, message, title } = notification;

  const bgColor = {
    success: 'bg-green-50 border-green-500',
    error: 'bg-red-50 border-red-500',
    warning: 'bg-yellow-50 border-yellow-500',
    info: 'bg-blue-50 border-blue-500',
  };

  const textColor = {
    success: 'text-green-800',
    error: 'text-red-800',
    warning: 'text-yellow-800',
    info: 'text-blue-800',
  };

  const iconColor = {
    success: 'text-green-500',
    error: 'text-red-500',
    warning: 'text-yellow-500',
    info: 'text-blue-500',
  };

  const Icon = {
    success: FiCheckCircle,
    error: FiAlertCircle,
    warning: FiAlertTriangle,
    info: FiInfo,
  }[type];

  // Process the message to handle newline characters
  const messageLines = message.split('\n');

  return (
    <div
      className={cn(
        'w-full min-w-[320px] max-w-md mx-auto shadow-lg rounded-lg pointer-events-auto overflow-hidden border-l-4',
        bgColor[type]
      )}
      role="alert"
    >
      <div className="p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <Icon className={cn('h-5 w-5', iconColor[type])} aria-hidden="true" />
          </div>
          <div className="ml-3 w-0 flex-1">
            {title && (
              <p className={`text-sm font-medium ${textColor[type]}`}>{title}</p>
            )}
            <div className={`text-sm ${textColor[type]} mt-1`}>
              {messageLines.map((line, index) => (
                <React.Fragment key={index}>
                  {line}
                  {index < messageLines.length - 1 && <br />}
                </React.Fragment>
              ))}
            </div>
          </div>
          <div className="ml-4 flex-shrink-0 flex">
            <button
              className={`inline-flex rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 ${iconColor[type]} p-1`}
              onClick={() => onClose(id)}
            >
              <span className="sr-only">Close</span>
              <FiX className="h-4 w-4" aria-hidden="true" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export const Notifications: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const portalContainer = document.getElementById('notifications-container') || document.body;

  useEffect(() => {
    const subscription = notificationService.getNotifications().subscribe(setNotifications);
    return () => subscription.unsubscribe();
  }, []);
  
  // Create notification container if it doesn't exist
  useEffect(() => {
    if (!document.getElementById('notifications-container')) {
      const container = document.createElement('div');
      container.id = 'notifications-container';
      container.className = 'fixed top-5 left-1/2 transform -translate-x-1/2 z-50 space-y-2';
      document.body.appendChild(container);
    }
  }, []);

  const handleClose = (id: string) => {
    notificationService.removeNotification(id);
  };

  return createPortal(
    <div className="fixed top-5 left-1/2 transform -translate-x-1/2 z-50 space-y-2">
      {notifications.map(notification => (
        <div
          key={notification.id}
          className="transform transition-all duration-300 ease-in-out animate-fade-in-down"
        >
          <NotificationItem 
            notification={notification}
            onClose={handleClose}
          />
        </div>
      ))}
    </div>,
    portalContainer
  );
};

export default Notifications; 