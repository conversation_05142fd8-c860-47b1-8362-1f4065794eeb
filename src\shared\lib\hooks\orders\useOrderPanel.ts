import { useEffect, useCallback, useMemo } from 'react';

interface UseOrderPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onGoToFulfill?: (orderId: string) => void;
}

interface UseOrderPanelReturn {
  handleGoToFulfill: (orderId: string) => void;
}

export const useOrderPanel = ({ 
  isOpen, 
  onClose, 
  onGoToFulfill 
}: UseOrderPanelProps): UseOrderPanelReturn => {
  // Task 3.6: Memoize keyboard event handler to prevent recreation
  const handleEscapeKey = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape' && isOpen) {
      onClose();
    }
  }, [isOpen, onClose]);

  // Handle escape key to close panel
  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleEscapeKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isOpen, handleEscapeKey]);

  // Task 3.6: Memoize go to fulfill handler to prevent recreation
  const handleGoToFulfill = useCallback((orderId: string) => {
    if (onGoToFulfill) {
      onGoToFulfill(orderId);
    }
  }, [onGoToFulfill]);

  // Task 3.6: Memoize return object to prevent recreation
  const returnValue = useMemo(() => ({
    handleGoToFulfill
  }), [handleGoToFulfill]);

  return returnValue;
}; 