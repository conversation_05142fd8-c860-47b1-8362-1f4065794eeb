import <PERSON> from 'papaparse';
import { carrierParserService } from '../carrier/carrier-parser-service';
import { bulkAssignTrackingNumbers } from './fulfill-service';
import { showCsvProcessingNotification, showCsvValidationError, showCsvParsingError } from './csv-notification-service';

// Define the expected CSV columns
export interface CsvTrackingRow {
  order_id: string;
  tracking_number: string;
  order_number?: string;
  carrier_id?: string;
  carrier_name?: string;
}

// Define the validation result
export interface CsvValidationResult {
  isValid: boolean;
  error?: string;
  data?: CsvTrackingRow[];
}

/**
 * Validates that the CSV data has the required columns
 */
export const validateCsvData = (data: any[]): CsvValidationResult => {
  if (!data || data.length === 0) {
    return {
      isValid: false,
      error: 'No data found in CSV file'
    };
  }
  
  // Check first row for required columns
  const firstRow = data[0];
  
  if (!('order_id' in firstRow)) {
    return {
      isValid: false,
      error: 'CSV must contain an "order_id" column'
    };
  }
  
  if (!('tracking_number' in firstRow)) {
    return {
      isValid: false,
      error: 'CSV must contain a "tracking_number" column'
    };
  }
  
  // Both carrier_id and carrier_name are now optional
  // Carrier will be auto-detected if not provided
  
  // Convert any undefined values to empty strings
  const processedData = data.map(row => ({
    order_id: row.order_id || '',
    tracking_number: row.tracking_number || '',
    carrier_id: row.carrier_id || undefined,
    carrier_name: row.carrier_name || undefined
  }));
  
  return {
    isValid: true,
    data: processedData
  };
};

/**
 * Process a CSV file for tracking number assignment
 */
export const processCsvFile = async (file: File): Promise<CsvTrackingRow[] | null> => {
  return new Promise((resolve, reject) => {
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        try {
          const validationResult = validateCsvData(results.data);
          
          if (!validationResult.isValid) {
            showCsvValidationError(validationResult.error || 'Invalid CSV format');
            resolve(null);
            return;
          }
          
          resolve(validationResult.data || []);
        } catch (error) {
          if (error instanceof Error) {
            showCsvParsingError(error);
          } else {
            showCsvValidationError('Unknown error parsing CSV');
          }
          resolve(null);
        }
      },
      error: (error) => {
        showCsvParsingError(error);
        reject(error);
      }
    });
  });
};

/**
 * Process tracking data from CSV and submit for assignment
 */
export const processAndAssignTrackingData = async (data: CsvTrackingRow[]): Promise<{
  success: boolean;
  message?: string;
  processedCount: number;
  failedCount: number;
  detectedCount: number;
  processedOrders?: Array<{
    order_id: string;
    order_number?: string;
    tracking_number: string;
  }>;
}> => {
  try {
    // Process the data to ensure all items have carrier IDs
    const processedData = await carrierParserService.processBatchTrackingData(data);
    
    // If there are any invalid items, show error
    if (processedData.invalidItems.length > 0) {
      const itemCount = processedData.invalidItems.length;
      const message = `${itemCount} item${itemCount !== 1 ? 's' : ''} could not be processed due to missing or invalid carrier information.`;
      
      showCsvProcessingNotification({
        success: false,
        message,
        failedCount: processedData.invalidItems.length
      });
      
      return {
        success: false,
        message,
        processedCount: 0,
        failedCount: processedData.invalidItems.length,
        detectedCount: 0
      };
    }
    
    // If no valid items, show error
    if (processedData.validItems.length === 0) {
      const message = 'No valid tracking data found in CSV.';
      
      showCsvProcessingNotification({
        success: false,
        message
      });
      
      return {
        success: false,
        message,
        processedCount: 0,
        failedCount: data.length,
        detectedCount: 0
      };
    }
    
    // Add ready_to_ship status to each item
    const itemsWithStatus = processedData.validItems.map(item => ({
      ...item,
      new_status: 'ready_to_ship'
    }));
    
    // Submit the valid items for processing with the ready_to_ship status
    const response = await bulkAssignTrackingNumbers(itemsWithStatus);
    
    // Create result message and notification
    const successMessage = `Successfully processed ${processedData.validItems.length} tracking numbers${
      processedData.detectedCount > 0 
        ? ` (auto-detected ${processedData.detectedCount} carriers)`
        : ''
    }.`;
    
    const failMessage = response.message || 'Failed to assign tracking numbers.';
    
    // Create a lookup map for faster order number retrieval
    const orderInfoMap = new Map();
    data.forEach(row => {
      if (row.order_id) {
        orderInfoMap.set(row.order_id, {
          order_number: row.order_number || row.order_id
        });
      }
    });
    
    // Prepare processed orders data for notification
    const processedOrders = processedData.validItems.map(item => {
      const orderInfo = orderInfoMap.get(item.order_id) || {};
      
      return {
        order_id: item.order_id,
        // Use order_number if available, otherwise use order_id
        order_number: orderInfo.order_number || item.order_id,
        tracking_number: item.tracking_number
      };
    });
    
    showCsvProcessingNotification({
      success: response.success,
      message: response.success ? successMessage : failMessage,
      processedCount: processedData.validItems.length,
      data: response.data,
      processedOrders: response.success ? processedOrders : undefined
    });
    
    return {
      success: response.success,
      message: response.success ? successMessage : failMessage,
      processedCount: processedData.validItems.length,
      failedCount: processedData.invalidItems.length,
      detectedCount: processedData.detectedCount,
      processedOrders: response.success ? processedOrders : undefined
    };
  } catch (error) {
    const message = error instanceof Error 
      ? error.message 
      : 'An unknown error occurred during processing';
    
    showCsvProcessingNotification({
      success: false,
      message
    });
    
    return {
      success: false,
      message,
      processedCount: 0,
      failedCount: data.length,
      detectedCount: 0
    };
  }
}; 