import React, { memo, useCallback } from 'react';
import { FiAlertTriangle } from 'react-icons/fi'; // Example icon

interface LowStockItem {
  sku: string;
  stockLeft: number;
}

interface LowStockAlertsCardProps {
  alerts: LowStockItem[];
}

// Custom comparison function for memoization
const areEqual = (prevProps: LowStockAlertsCardProps, nextProps: LowStockAlertsCardProps) => {
  // If alerts array reference has changed
  if (prevProps.alerts !== nextProps.alerts) {
    // First check if lengths are different
    if (prevProps.alerts.length !== nextProps.alerts.length) {
      return false;
    }
    
    // If lengths match, deep compare each item
    for (let i = 0; i < prevProps.alerts.length; i++) {
      const prevAlert = prevProps.alerts[i];
      const nextAlert = nextProps.alerts[i];
      
      if (
        prevAlert.sku !== nextAlert.sku ||
        prevAlert.stockLeft !== nextAlert.stockLeft
      ) {
        return false;
      }
    }
  }
  
  // If we reach here, props are equal
  return true;
};

const LowStockAlertsCard: React.FC<LowStockAlertsCardProps> = ({ alerts }) => {
  // Memoize the stock class calculation function to prevent recreation on each render
  const getStockLeftClass = useCallback((stock: number) => {
    if (stock <= 5) return 'bg-red-100 text-red-700';
    if (stock <= 10) return 'bg-yellow-100 text-yellow-700';
    // For items with stock > 10, you might want a different or no specific class
    return 'bg-gray-100 text-gray-700'; // Default for items not critically low
  }, []);

  return (
    <div className="bg-card-bg p-6 rounded-lg shadow">
      <div className="flex items-center mb-6"> {/* Increased bottom margin for title section */}
        <FiAlertTriangle className="text-yellow-500 mr-3" size={24} /> {/* Slightly more margin for icon */}
        <h4 className="text-xl font-semibold text-gray-800">Low Stock Alerts</h4> {/* Slightly larger title, darker text */}
      </div>
      <div className="overflow-x-auto">
        <table className="w-full table-fixed divide-y divide-gray-200">
          <thead className="bg-gray-100">
            <tr>
              <th scope="col" className="w-1/2 px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">
                SKU
              </th>
              <th scope="col" className="w-1/4 px-4 py-3 text-center text-xs font-medium text-gray-600 uppercase tracking-wider">
                Stock Left
              </th>
              <th scope="col" className="w-1/4 px-4 py-3 text-center text-xs font-medium text-gray-600 uppercase tracking-wider">
                Action
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {alerts.length > 0 ? (
              alerts.map((item) => (
                <tr key={item.sku} className="hover:bg-gray-50 transition-colors duration-150">
                  <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.sku}</td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-center">
                    <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStockLeftClass(item.stockLeft)}`}>
                      {item.stockLeft}
                    </span>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-center">
                    <button 
                      onClick={() => {/* Reorder functionality */}} 
                      className="bg-indigo-500 hover:bg-indigo-600 text-white font-semibold py-1.5 px-3 rounded text-xs transition-colors duration-150 shadow-sm hover:shadow-md"
                    >
                      Reorder
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={3} className="px-6 py-10 whitespace-nowrap text-sm text-gray-500 text-center">
                  No low stock alerts.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

// Export memoized component with custom comparison
export default memo(LowStockAlertsCard, areEqual);