import { useState, useEffect, useMemo, useCallback } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { SALES_CHART_DATA as INITIAL_YEARLY_NET_SALES, salesTypeOptions, yearComparisonOptions } from '@/shared/lib/mock-data/dashboard';
import Dropdown from '@/shared/ui/input/Dropdown';
import { DropdownOption, SalesDataPoint } from '../../../types/index';
import { ClockIcon } from '@/shared/config';

const SalesChart: React.FC = () => {
  const [selectedSalesType, setSelectedSalesType] = useState<DropdownOption | null>(salesTypeOptions[0]);
  const [selectedYearComparison, setSelectedYearComparison] = useState<DropdownOption | null>(yearComparisonOptions[0]);
  
  const [chartDisplayData, setChartDisplayData] = useState<SalesDataPoint[]>([]);
  const [currentPeriodLabel, setCurrentPeriodLabel] = useState<string>('This Year');
  const [previousPeriodLabel, setPreviousPeriodLabel] = useState<string>('Last Year');

  // Memoize currency formatter to avoid recreation on every render
  const formatCurrency = useCallback((value: number) => {
    return new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(value);
  }, []);
  
  // Memoize expensive data generation to avoid recalculation on every render
  const chartData = useMemo(() => {
    const generateDynamicSalesData = (): { data: SalesDataPoint[]; currentLabel: string; previousLabel: string } => {
      let newChartData: SalesDataPoint[] = [];
      let newCurrentLabel = 'This Year';
      let newPreviousLabel = 'Last Year';
      
      const isGrossSales = selectedSalesType?.value === 'gross_sales';
      const grossSalesFactor = 1.15; // Gross sales are 15% higher

      const applyGrossFactor = (value: number) => isGrossSales ? Math.round(value * grossSalesFactor) : value;

      switch (selectedYearComparison?.value) {
        case 'monthly_this_vs_last':
          newCurrentLabel = 'This Month';
          newPreviousLabel = 'Last Month';
          newChartData = Array.from({ length: 30 }, (_, i) => ({
            name: `Day ${i + 1}`,
            currentValue: applyGrossFactor(Math.floor(Math.random() * (2500 - 200 + 1) + 200) + (i * (Math.random()*50 -10))),
            previousValue: applyGrossFactor(Math.floor(Math.random() * (2000 - 150 + 1) + 150) + (i * (Math.random()*40-15))),
          }));
          break;
        case 'quarterly_this_vs_last':
          newCurrentLabel = 'This Quarter';
          newPreviousLabel = 'Last Quarter';
          newChartData = ['Month 1', 'Month 2', 'Month 3'].map((monthName, i) => ({
            name: monthName,
            currentValue: applyGrossFactor(Math.floor(Math.random() * (50000 - 15000 + 1) + 15000) + (i * (Math.random()*5000-1000))),
            previousValue: applyGrossFactor(Math.floor(Math.random() * (45000 - 12000 + 1) + 12000) + (i * (Math.random()*4000-1500))),
          }));
          break;
        case 'yearly_this_vs_last':
        default:
          newCurrentLabel = 'This Year';
          newPreviousLabel = 'Last Year';
          // Use the already updated SALES_CHART_DATA structure from mock-data
          newChartData = INITIAL_YEARLY_NET_SALES.map(d => ({
            name: d.name,
            currentValue: applyGrossFactor(d.currentValue),
            previousValue: applyGrossFactor(d.previousValue),
          }));
          break;
      }
      return { data: newChartData, currentLabel: newCurrentLabel, previousLabel: newPreviousLabel };
    };

    return generateDynamicSalesData();
  }, [selectedSalesType, selectedYearComparison]);

  // Update state when memoized data changes
  useEffect(() => {
    setChartDisplayData(chartData.data);
    setCurrentPeriodLabel(chartData.currentLabel);
    setPreviousPeriodLabel(chartData.previousLabel);
  }, [chartData]);
  
  const totalCurrentPeriod = useMemo(() => {
    let total = 0;
    for (const item of chartDisplayData) {
      total += item.currentValue;
    }
    return total;
  }, [chartDisplayData]);

  const totalPreviousPeriod = useMemo(() => {
    let total = 0;
    for (const item of chartDisplayData) {
      total += item.previousValue;
    }
    return total;
  }, [chartDisplayData]);


  return (
    <div className="bg-card-bg p-6 rounded-lg shadow">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
        <h3 className="text-xl font-semibold text-gray-800 mb-4 md:mb-0">Sales Over Time</h3>
        <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-2 w-full md:w-auto">
          <Dropdown
            options={salesTypeOptions}
            selectedOption={selectedSalesType}
            onSelect={setSelectedSalesType}
            className="w-full sm:w-auto"
          />
          <Dropdown
            options={yearComparisonOptions}
            selectedOption={selectedYearComparison}
            onSelect={setSelectedYearComparison}
            className="w-full sm:w-auto"
          />
          <div className="flex items-center text-sm text-gray-500 ml-auto md:ml-4 pt-2 md:pt-0">
            <ClockIcon className="w-4 h-4 mr-1" />
            10:50 AM {/* This time can be dynamic if needed */}
          </div>
        </div>
      </div>

      <div className="flex flex-col lg:flex-row">
        <div className="w-full lg:w-1/4 pr-0 lg:pr-6 mb-6 lg:mb-0">
          <div className="mb-4">
            <p className="text-xs text-gray-500 uppercase">{currentPeriodLabel}</p>
            <p className="text-3xl font-bold text-gray-800">{formatCurrency(totalCurrentPeriod)}</p>
          </div>
          <div className="mb-6">
            <p className="text-xs text-gray-500 uppercase">{previousPeriodLabel}</p>
            <p className="text-2xl font-semibold text-gray-600">{formatCurrency(totalPreviousPeriod)}</p>
          </div>
        </div>

        <div className="w-full lg:w-3/4 h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartDisplayData} margin={{ top: 5, right: 20, left: 30, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
              <XAxis dataKey="name" tick={{ fontSize: 12, fill: '#6b7280' }} />
              <YAxis 
                tickFormatter={(value) => formatCurrency(value)}
                tick={{ fontSize: 12, fill: '#6b7280' }}
                domain={[0, (dataMax: number) => Math.max(dataMax + dataMax*0.1, 100)]} // Ensure Y-axis starts at 0 and gives some padding
                allowDataOverflow={false}
              />
              <Tooltip formatter={(value: number, name: string) => [formatCurrency(value), name]} />
              <Legend 
                verticalAlign="top" 
                align="right" 
                iconType="circle" 
                wrapperStyle={{paddingBottom: '20px'}}
                formatter={(value, _entry) => <span className="text-gray-600 text-sm">{value}</span>} // `value` here is the `name` prop of Line
              />
              <Line 
                type="monotone" 
                dataKey="currentValue" 
                name={currentPeriodLabel} 
                stroke="#3B82F6" 
                strokeWidth={2} 
                dot={{ r: 4 }} 
                activeDot={{ r: 6 }} 
              />
              <Line 
                type="monotone" 
                dataKey="previousValue" 
                name={previousPeriodLabel} 
                stroke="#9CA3AF" 
                strokeWidth={2} 
                strokeDasharray="5 5" 
                dot={{ r: 4 }} 
                activeDot={{ r: 6 }} 
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default SalesChart;