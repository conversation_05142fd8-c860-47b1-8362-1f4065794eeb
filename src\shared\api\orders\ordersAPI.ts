import type {
  AllOrdersViewItem,
  AllOrdersDetail,
  OrderNote,
  CreateOrderFormData,
  LinkedOrder
} from '../../../types';

// Import Supabase constants from client
import { supabase, supabaseUrl, supabaseAnonKey } from '../../../../supabase/supabase_client/client';

// Import all API types from separate file
import type {
  ApiResponse,
  OrdersListParams,
  OrderUpdateData,
  PackOrderData,
  ShipOrderData,
  BulkPackData,
  FulfillStatsResponse,
  PackingStationSummaryItem,
  FulfillPackingListItem
} from './ordersAPI_types';

// Orders API Functions
export const ordersApi = {
  // Get configuration data for order creation
  async getCreateOrderConfig(): Promise<ApiResponse<{
    platforms: Array<{ id: string; name: string; key: string; is_active: boolean }>;
    channels: Array<{ id: string; name: string; code: string; platform_id: string; is_active: boolean }>;
    productIdentifiers: Array<{
      id: string;
      product_id: string;
      platform_id: string;
      platform_identifier: string;
      code_name: string | null;
      pack_size: number;
    }>;
  }>> {
    try {
      // Use Supabase edge function or direct database calls
      const response = await fetch('/api/v1/orders/config', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API call failed:', error);
      return {
        success: false,
        message: `Failed to fetch configuration: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: {
          platforms: [],
          channels: [],
          productIdentifiers: []
        }
      };
    }
  },

  // List orders with filtering and pagination
  async list(params: OrdersListParams = {}): Promise<ApiResponse<AllOrdersViewItem[]>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, String(value));
      }
    });

    const response = await fetch(`/api/orders?${queryParams}`);
    return response.json();
  },

  // Get detailed order information
  async getById(id: string): Promise<ApiResponse<AllOrdersDetail>> {
    const response = await fetch(`/api/orders/${id}`);
    return response.json();
  },

  // Create new order
  async create(data: CreateOrderFormData): Promise<ApiResponse<AllOrdersDetail>> {
    const response = await fetch('/api/orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    return response.json();
  },

  // Update order
  async update(id: string, data: OrderUpdateData): Promise<ApiResponse<AllOrdersDetail>> {
    const response = await fetch(`/api/orders/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    return response.json();
  },

  // Delete/Cancel order
  async cancel(id: string, reason?: string): Promise<ApiResponse<void>> {
    const response = await fetch(`/api/orders/${id}/cancel`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ reason }),
    });
    return response.json();
  },

  // Get order notes
  async getNotes(orderId: string): Promise<ApiResponse<OrderNote[]>> {
    const response = await fetch(`/api/orders/${orderId}/notes`);
    return response.json();
  },

  // Add order note
  async addNote(orderId: string, note: { content: string; noteType?: string; isImportant?: boolean }): Promise<ApiResponse<OrderNote>> {
    const response = await fetch(`/api/orders/${orderId}/notes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(note),
    });
    return response.json();
  },

  // Get linked orders
  async getLinkedOrders(orderId: string): Promise<ApiResponse<LinkedOrder[]>> {
    const response = await fetch(`/api/orders/${orderId}/linked`);
    return response.json();
  },

  // Pack order (fulfillment)
  async pack(data: PackOrderData): Promise<ApiResponse<AllOrdersDetail>> {
    const response = await fetch(`/api/orders/${data.orderId}/pack`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    return response.json();
  },

  // Ship order (fulfillment)
  async ship(data: ShipOrderData): Promise<ApiResponse<AllOrdersDetail>> {
    const response = await fetch(`/api/orders/${data.orderId}/ship`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    return response.json();
  },

  // Mark as urgent
  async markUrgent(id: string, urgent: boolean = true): Promise<ApiResponse<AllOrdersDetail>> {
    const response = await fetch(`/api/orders/${id}/urgent`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ isUrgent: urgent }),
    });
    return response.json();
  },

  // Mark as problem
  async markProblem(id: string, problem: boolean = true): Promise<ApiResponse<AllOrdersDetail>> {
    const response = await fetch(`/api/orders/${id}/problem`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ isProblem: problem }),
    });
    return response.json();
  },

  // Mark as resent
  async markResent(id: string, resent: boolean = true): Promise<ApiResponse<AllOrdersDetail>> {
    const response = await fetch(`/api/orders/${id}/resent`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ isResent: resent }),
    });
    return response.json();
  },

  // Resend confirmation email
  async resendConfirmation(id: string): Promise<ApiResponse<void>> {
    const response = await fetch(`/api/orders/${id}/resend-confirmation`, {
      method: 'POST',
    });
    return response.json();
  },

  // Process refund
  async refund(id: string, amount?: number, reason?: string): Promise<ApiResponse<void>> {
    const response = await fetch(`/api/orders/${id}/refund`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ amount, reason }),
    });
    return response.json();
  },

  // Bulk pack orders (for ProductAccordion batch actions)
  async bulkPack(data: BulkPackData): Promise<ApiResponse<{ success: number; failed: number }>> {
    // The name of the new Edge Function
    const functionName = 'bulk-pack-orders'; 
    const functionUrl = `${supabaseUrl}/functions/v1/${functionName}`;

    const response = await fetch(functionUrl, { // <-- This now calls the new function
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
        'Authorization': `Bearer ${supabaseAnonKey}`
      },
      body: JSON.stringify(data), // The body contains { orderIds, userId }
    });

    // Check for non-ok response to provide better errors
    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }
    
    return response.json();
  },

  // Get fulfill dashboard stats (for BatchProgressBar.tsx)
  async getFulfillStats(): Promise<ApiResponse<FulfillStatsResponse>> {
    // The name of the Edge Function for fulfill stats
    const functionName = 'get-fulfill-stats';
    const functionUrl = `${supabaseUrl}/functions/v1/${functionName}`;

    const response = await fetch(functionUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
        'Authorization': `Bearer ${supabaseAnonKey}`
      },
    });

    // Check for non-ok response to provide better errors
    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }
    
    return response.json();
  },

  // Get orders for packing station (status = 'open')
  async getPackingStationOrders(params: OrdersListParams = {}): Promise<ApiResponse<AllOrdersViewItem[]>> {
    return this.list({ ...params, status: 'open' });
  },

  // Get orders for label station (status = 'packed')
  async getLabelStationOrders(params: OrdersListParams = {}): Promise<ApiResponse<AllOrdersViewItem[]>> {
    return this.list({ ...params, status: 'packed' });
  },

  // **NEW** - Get packing station summary (from packing_station_summary view)
  async getPackingStationSummary(): Promise<ApiResponse<PackingStationSummaryItem[]>> {
    try {
      // Directly fetch from the packing_station_summary view
      const { data, error } = await supabase
        .from('packing_station_summary')
        .select('*');

      if (error) {
        throw new Error(error.message);
      }

      return {
        success: true,
        data: data || []
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch packing station summary',
        data: []
      };
    }
  },

  // **NEW** - Get fulfill packing list (from fulfill_packing_list view)
  async getFulfillPackingList(): Promise<ApiResponse<FulfillPackingListItem[]>> {
    try {
      // Directly fetch from the fulfill_packing_list view
      const { data, error } = await supabase
        .from('fulfill_packing_list')
        .select('*');

      if (error) {
        throw new Error(error.message);
      }

      return {
        success: true,
        data: data || []
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch fulfill packing list',
        data: []
      };
    }
  }
};

export default ordersApi; 