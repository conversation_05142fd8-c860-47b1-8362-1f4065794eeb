import { useState, useCallback } from 'react';

interface UseProductDetailPanelReturn {
  selectedProductId: string | null;
  isDetailPanelOpen: boolean;
  openDetailPanel: (productId: string) => void;
  closeDetailPanel: () => void;
  handleProductClick: (productId: string) => void;
}

/**
 * Custom hook for managing the Product Detail Panel state
 * Provides functions for opening/closing the panel and selecting products
 */
export function useProductDetailPanel(): UseProductDetailPanelReturn {
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);
  const [isDetailPanelOpen, setIsDetailPanelOpen] = useState(false);

  // Open panel with a specific product
  const openDetailPanel = useCallback((productId: string) => {
    setSelectedProductId(productId);
    setIsDetailPanelOpen(true);
  }, []);

  // Close panel and reset selected product immediately
  const closeDetailPanel = useCallback(() => {
    setIsDetailPanelOpen(false);
    setSelectedProductId(null); // Clear immediately instead of waiting for animation
  }, []);

  // Handle product selection - opens panel with the selected product
  const handleProductClick = useCallback((productId: string) => {
    openDetailPanel(productId);
  }, [openDetailPanel]);

  return {
    selectedProductId,
    isDetailPanelOpen,
    openDetailPanel,
    closeDetailPanel,
    handleProductClick
  };
} 