import React, { memo, useMemo } from 'react';
import { AllOrdersDetail } from '@/types';

interface OrderItemsListProps {
  order: AllOrdersDetail;
  formatCurrency: (amount: number) => string;
}

// PERFORMANCE: Individual item component with aggressive memoization
const OrderItemRow = memo(({ item, formatCurrency }: { 
  item: AllOrdersDetail['items'][0]; 
  formatCurrency: (amount: number) => string;
}) => (
  <div className="bg-gray-50 rounded-lg p-4">
    <div className="flex justify-between items-start">
      <div className="flex-1">
        <h5 className="font-medium text-gray-900">{item.productName}</h5>
        <p className="text-sm text-gray-500">SKU: {item.sku}-{item.pack_size}</p>
        <p className="text-sm text-gray-500">Quantity: {item.quantity}</p>
      </div>
      <div className="text-right">
        <p className="font-medium text-gray-900">{formatCurrency(item.subtotal_item)}</p>
      </div>
    </div>
  </div>
), (prevProps, nextProps) => (
  prevProps.item.id === nextProps.item.id &&
  prevProps.item.productName === nextProps.item.productName &&
  prevProps.item.sku === nextProps.item.sku &&
  prevProps.item.quantity === nextProps.item.quantity &&
  prevProps.item.subtotal_item === nextProps.item.subtotal_item &&
  prevProps.formatCurrency === nextProps.formatCurrency
));
// }</p>
//       </div>
//     </div>
//   </div>
// ), (prevProps, nextProps) => (   
//   prevProps.item.id === nextProps.item.id &&
//   prevProps.item.productName === nextProps.item.productName &&
//   prevProps.item.sku === nextProps.item.sku &&
//   prevProps.item.quantity === nextProps.item.quantity &&
//   prevProps.item.subtotal_item === nextProps.item.subtotal_item &&
//   prevProps.formatCurrency === nextProps.formatCurrency
// ));

const OrderItemsList: React.FC<OrderItemsListProps> = ({ order, formatCurrency }) => {
  // PERFORMANCE: Memoize order summary calculations
  const orderSummary = useMemo(() => ({
    subtotal: order.subtotal,
    tax: order.tax,
    shipping: order.shipping,
    total: order.totalAmount
  }), [order.subtotal, order.tax, order.shipping, order.totalAmount]);

  return (
    <section>
      <h4 className="text-lg font-medium text-gray-900 mb-3">Order Items</h4>
      
      {/* PERFORMANCE: Virtualized item list for large orders */}
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {order.items.map((item) => (
          <OrderItemRow
            key={item.id}
            item={item}
            formatCurrency={formatCurrency}
          />
        ))}
      </div>

      {/* Order Summary */}
      <div className="mt-4 bg-gray-50 rounded-lg p-4">
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-500">Subtotal:</span>
            <span className="text-gray-900">{formatCurrency(orderSummary.subtotal)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">Tax:</span>
            <span className="text-gray-900">{formatCurrency(orderSummary.tax)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">Shipping:</span>
            <span className="text-gray-900">{formatCurrency(orderSummary.shipping)}</span>
          </div>
          <div className="border-t border-gray-200 pt-2 flex justify-between font-medium">
            <span className="text-gray-900">Total:</span>
            <span className="text-gray-900">{formatCurrency(orderSummary.total)}</span>
          </div>
        </div>
      </div>
    </section>
  );
};

// PERFORMANCE: Shallow comparison for items array and formatCurrency function
export default memo(OrderItemsList, (prevProps, nextProps) => (
  prevProps.order.id === nextProps.order.id &&
  prevProps.order.items.length === nextProps.order.items.length &&
  prevProps.order.subtotal === nextProps.order.subtotal &&
  prevProps.order.tax === nextProps.order.tax &&
  prevProps.order.shipping === nextProps.order.shipping &&
  prevProps.order.totalAmount === nextProps.order.totalAmount &&
  prevProps.formatCurrency === nextProps.formatCurrency
)); 