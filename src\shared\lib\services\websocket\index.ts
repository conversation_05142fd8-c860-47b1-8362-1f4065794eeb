// Main service
export { webSocketService, WebSocketService } from './websocket-service';

// Types and configuration
export type {
  WebSocketMessage,
  WebSocketConfig,
  WebSocketState,
  WebSocketEventHandler,
  WebSocketStateHandler,
} from './websocket-config';

export { DEFAULT_WEBSOCKET_CONFIG } from './websocket-config';

// Individual modules (for testing or advanced usage)
export { WebSocketConnection } from './websocket-connection';
export { WebSocketReconnection } from './websocket-reconnection';
export { WebSocketHeartbeat } from './websocket-heartbeat';
export { WebSocketMessageHandler } from './websocket-message-handler';