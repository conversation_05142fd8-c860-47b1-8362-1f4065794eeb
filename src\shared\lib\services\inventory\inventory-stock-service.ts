import { inventoryApi } from '@/shared/api/inventory/inventoryAPI';
import type { InventoryReasonCategory, InventoryMovement } from '@/types';
import { supabase } from '../../../../../supabase/supabase_client/client';

/**
 * Type definitions for stock operations
 */
export type AdjustmentType = 'increase' | 'decrease' | 'set';

/**
 * Interface for stock adjustment parameters
 */
export interface StockAdjustmentParams {
  quantity: number;
  reason: string;
  reason_category: InventoryReasonCategory;
  type: AdjustmentType;
  notes?: string;
  user_id?: string;
  user_name?: string;
}

/**
 * Interface for bulk threshold update parameters
 */
export interface ThresholdUpdateParams {
  inventoryId: string;
  minimumThreshold?: number;
  maximumThreshold?: number;
  reorderPoint?: number;
}

/**
 * Interface for bulk stock adjustment parameters
 */
export interface BulkStockAdjustmentParams {
  productIds: string[];
  quantity: number;
  reason: string;
  reason_category: InventoryReasonCategory;
  type: AdjustmentType;
  notes?: string;
}

/**
 * Service for stock-specific inventory operations
 */
export class InventoryStockService {
  // Common validation
  private validateStockAdjustment(type: AdjustmentType, quantity: number) {
    // First check for negative stock when using 'set' type
    if (type === 'set' && quantity < 0) {
      throw new Error('Cannot set stock to a negative value');
    }
    
    // Then check for zero or negative quantity for all types
    if (quantity <= 0) {
      throw new Error('Quantity must be greater than zero');
    }
  }

  // Calculate new stock based on adjustment type
  private calculateNewStock(currentStock: number, type: AdjustmentType, quantity: number): number {
    switch (type) {
      case 'increase':
        return currentStock + quantity;
      case 'decrease':
        const newStock = currentStock - quantity;
        if (newStock < 0) {
          throw new Error('Stock level cannot be negative after decrease');
        }
        return newStock;
      case 'set':
        return quantity;
      default:
        throw new Error('Invalid adjustment type');
    }
  }

  /**
   * Adjust stock levels
   */
  async adjustStock(id: string, adjustment: StockAdjustmentParams) {
    this.validateStockAdjustment(adjustment.type, adjustment.quantity);
    
    // Get the current inventory record
    const inventoryResponse = await inventoryApi.getById(id);
    
    if (!inventoryResponse.success || !inventoryResponse.data) {
      throw new Error(inventoryResponse.message || 'Failed to fetch inventory data');
    }
    
    const inventoryData = inventoryResponse.data;

    // Calculate new stock level
    const currentStock = inventoryData.current_stock;
    const newStock = this.calculateNewStock(currentStock, adjustment.type, adjustment.quantity);

    // Update timestamps based on reason category
    const timestamps = this.getTimestampUpdates(
      adjustment.reason_category, 
      inventoryData.last_counted_at, 
      inventoryData.last_restocked_at
    );

    // Update the inventory record with stock movement
    const updateData = {
      current_stock: newStock,
      updated_at: new Date().toISOString(),
      last_counted_at: timestamps.lastCountedAt,
      last_restocked_at: timestamps.lastRestockedAt,
      // Include movement data for the API to record
      movement: {
        type: adjustment.type,
        quantity: adjustment.quantity,
        previous_stock: currentStock,
        reason: adjustment.reason,
        reason_category: adjustment.reason_category,
        notes: adjustment.notes,
        user_id: adjustment.user_id,
        user_name: adjustment.user_name
      }
    };

    const response = await inventoryApi.update(id, updateData);
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to update inventory');
    }

    return response.data;
  }

  // Helper to determine timestamp updates
  private getTimestampUpdates(reasonCategory: InventoryReasonCategory, currentCountedAt: string | null, currentRestockedAt: string | null) {
    const now = new Date().toISOString();
    return {
      lastCountedAt: reasonCategory === 'inventory_count' ? now : (currentCountedAt || now),
      lastRestockedAt: ['new_shipment', 'returns', 'initial_setup'].includes(reasonCategory) ? now : (currentRestockedAt || now)
    };
  }

  /**
   * Reserve stock for orders
   */
  async reserveStock(productId: string, quantity: number, orderId: string) {
    // Validate quantity
    if (quantity <= 0) {
      throw new Error('Quantity must be greater than zero');
    }
    
    const response = await inventoryApi.reserveStock(productId, quantity, orderId);
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to reserve stock');
    }
    
    return response.data;
  }

  /**
   * Release reserved stock
   */
  async releaseStock(productId: string, quantity: number, orderId: string) {
    // Validate quantity
    if (quantity <= 0) {
      throw new Error('Quantity must be greater than zero');
    }
    
    const response = await inventoryApi.releaseStock(productId, quantity, orderId);
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to release stock');
    }
    
    return response.data;
  }

  /**
   * Get low stock items
   */
  async getLowStock() {
    const response = await inventoryApi.getLowStock();
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to get low stock items');
    }
    
    return {
      data: response.data,
      count: response.totalCount
    };
  }

  /**
   * Get items needing reorder
   */
  async getNeedingReorder() {
    const response = await inventoryApi.getNeedingReorder();
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to get items needing reorder');
    }
    
    return {
      data: response.data,
      count: response.totalCount
    };
  }

  /**
   * Bulk update inventory thresholds
   */
  async bulkUpdateThresholds(updates: ThresholdUpdateParams[]) {
    const response = await inventoryApi.bulkUpdateThresholds(updates);
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to update inventory thresholds');
    }
  }

  /**
   * Bulk adjust stock
   */
  async bulkAdjustStock(adjustments: BulkStockAdjustmentParams) {
    try {
      // Use Edge Function through the bulkStockUpdateEdge method
      // instead of the non-existent inventoryApi.bulkAdjustStock
      const items = adjustments.productIds.map(productId => ({
        productId,
        quantity: adjustments.quantity,
        type: adjustments.type,
        reason: adjustments.reason,
        reasonCategory: adjustments.reason_category,
        notes: adjustments.notes
      }));
      
      const result = await this.bulkStockUpdateEdge(items, 'system');
      
      return result;
    } catch (err) {
      console.error('Error in bulk stock adjustment:', err);
      throw new Error(err instanceof Error ? err.message : 'Failed to perform bulk stock adjustment');
    }
  }

  /**
   * Perform stock count
   */
  async performStockCount(inventoryId: string, countedStock: number, notes?: string) {
    // Validate count value - can be zero for out of stock
    if (countedStock < 0) {
      throw new Error('Stock count cannot be negative');
    }
    
    // Create adjustment with set type and inventory_count reason
    const adjustment: StockAdjustmentParams = {
      quantity: countedStock,
      type: 'set',
      reason: 'Physical inventory count',
      reason_category: 'inventory_count',
      notes: notes || 'Regular stock count'
    };
    
    return await this.adjustStock(inventoryId, adjustment);
  }

  /**
   * Get product movement history
   */
  async getProductMovements(productId: string, options = { limit: 10, sortBy: 'timestamp', sortDirection: 'desc' as 'asc' | 'desc' }): Promise<InventoryMovement[]> {
    try {
      const params = {
        limit: options.limit,
        sortBy: options.sortBy,
        sortDirection: options.sortDirection
      };
      
      const response = await inventoryApi.getMovements(productId, params);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to get product movements');
      }
      
      return response.data;
    } catch (error) {
      console.error('Error getting product movements:', error);
      return [];
    }
  }
  /**
   * Process bulk stock updates using Edge Function
   * This provides better performance and reliability for large batch operations
   */
  async bulkStockUpdateEdge(items: Array<{
    productId: string;
    quantity: number;
    type: AdjustmentType;
    reason: string;
    reasonCategory: InventoryReasonCategory;
    notes?: string;
  }>, userId: string) {
    // Format the request body for the Edge Function
    const requestBody = {
      items,
      userId
    };

    // Invoke Supabase Edge Function using the JS client – this automatically
    // targets the correct URL (even in local dev) and attaches the auth token.

    const { data: result, error } = await supabase.functions.invoke('bulk-stock-update', {
      body: requestBody
    });

    if (error) {
      throw new Error(error.message || 'Failed to update stock');
    }
    
    // If there were partial failures, throw an error with details
    if (result && !result.success) {
      const errorMessage = result.errors?.length 
        ? `Failed to update ${result.failedCount} products: ${result.errors[0].error}`
        : 'Failed to update stock';
      
      throw new Error(errorMessage);
    }

    return result;
  }
}

// Export as singleton
export default new InventoryStockService(); 