import React, { useMemo } from 'react';
import { AllOrdersStatus } from '@/types';
import { STATUS_COLORS } from '@/shared/lib/utils/domain/orders/orders-table-helpers';

interface UseOrderFormattingReturn {
  getStatusBadge: (status: AllOrdersStatus) => React.ReactNode;
  formatCurrency: (amount: number) => string;
  formatDate: (dateString: string) => string;
}

export const useOrderFormatting = (): UseOrderFormattingReturn => {
  // Task 3.6: Memoize expensive currency formatting function
  const formatCurrency = useMemo(() => (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  }, []);

  // Task 3.6: Memoize expensive date formatting function  
  const formatDate = useMemo(() => (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }, []);

  // Task 3.6: Memoize status configuration to avoid recreation on every render
  const statusConfig = useMemo(() => ({
    'open': { display: 'Open', color: STATUS_COLORS.open },
    'packed': { display: 'Packed', color: STATUS_COLORS.packed },
    'shipped': { display: 'Shipped', color: STATUS_COLORS.shipped },
    'completed': { display: 'Completed', color: STATUS_COLORS.completed },
    'refund': { display: 'Refunded', color: STATUS_COLORS.refund },
    'on_hold': { display: 'On Hold', color: STATUS_COLORS.on_hold },
    'reshipment_scheduled': { display: 'Reshipment Scheduled', color: STATUS_COLORS.reshipment_scheduled },
    'ready_to_ship': { display: 'Ready to Ship', color: STATUS_COLORS.ready_to_ship },
  }), []);

  // Task 3.6: Memoize status badge generation function with status config dependency
  const getStatusBadge = useMemo(() => (status: AllOrdersStatus): React.ReactNode => {
    const config = statusConfig[status.toLowerCase() as keyof typeof statusConfig] || { 
      display: status, 
      color: 'bg-gray-100 text-gray-800' 
    };

    return React.createElement('span', {
      className: `inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${config.color}`
    }, config.display);
  }, [statusConfig]);

  return {
    getStatusBadge,
    formatCurrency,
    formatDate
  };
}; 