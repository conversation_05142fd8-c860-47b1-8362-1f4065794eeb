// Test script to debug chunk assignment - SIMPLIFIED VERSION
console.log("Starting chunk assignment test...");

// Helper functions
const isModuleFrom = (id, pkgName) => {
  return id.includes(`node_modules/${pkgName}`);
};

const isInGroup = (id, pkgNames) => {
  return pkgNames.some(pkg => isModuleFrom(id, pkg));
};

// Define test paths
const paths = {
  reactCore: '/node_modules/react/index.js',
  reactJsx: '/node_modules/react/jsx-runtime.js',
  reactDom: '/node_modules/react-dom/index.js',
  reactRouter: '/node_modules/react-router/index.js',
  remixRouter: '/node_modules/@remix-run/router/index.js',
  reactRouterDom: '/node_modules/react-router-dom/index.js',
  recharts: '/node_modules/recharts/index.js',
  chartJs: '/node_modules/chart.js/auto/index.js',
  reactChartJs: '/node_modules/react-chartjs-2/index.js',
  heroicons: '/node_modules/@heroicons/react/24/outline/index.js',
  clsx: '/node_modules/clsx/index.js',
  featuresOrders: '/src/features/orders-features/orders-table/OrdersTable.tsx',
  pagesDashboard: '/src/pages/dashboard/index.tsx',
  lodash: '/node_modules/lodash/index.js'
};

// Test function with fixed order matching vite.config.ts
function testPath(id) {
  let result = "";
  
  if (id.includes('node_modules')) {
    // 1. React Router (check first)
    if (id.includes('node_modules/react-router') || id.includes('node_modules/@remix-run/router')) {
      result = 'vendor-routing';
    }
    
    // 2. Chart libraries (check before generic react)
    else if (id.includes('node_modules/recharts') || id.includes('node_modules/chart.js') || id.includes('node_modules/react-chartjs-2')) {
      result = 'vendor-charts';
    }
    
    // 3. React DOM
    else if (id.includes('node_modules/react-dom')) {
      result = 'vendor-react-dom';
    }
    
    // 4. React Core
    else if (id.includes('node_modules/react') && !id.includes('react-dom')) {
      result = 'vendor-react-core';
    }
    
    // 5. Supabase
    else if (id.includes('node_modules/@supabase')) {
      result = 'vendor-supabase';
    }
    
    // Default for node_modules
    else {
      result = 'vendor-others';
    }
  }
  
  // Features
  else if (id.includes('/features/orders-features/')) {
    result = 'feature-orders';
  }
  
  // Pages
  else if (id.includes('/pages/dashboard/')) {
    result = 'page-dashboard';
  }
  
  else {
    result = 'no-match';
  }
  
  console.log(`Path: ${id} -> Result: ${result}`);
  return result;
}

console.log("\n=== Testing Module Chunk Assignment ===");

// Run tests
console.log("\n1. React Core");
testPath(paths.reactCore);
testPath(paths.reactJsx);

console.log("\n2. React DOM");
testPath(paths.reactDom);

console.log("\n3. React Router");
testPath(paths.reactRouter);
testPath(paths.remixRouter);
testPath(paths.reactRouterDom);

console.log("\n4. Charts");
testPath(paths.recharts);
testPath(paths.chartJs);
testPath(paths.reactChartJs);

console.log("\n5. Features");
testPath(paths.featuresOrders);

console.log("\n6. Pages");
testPath(paths.pagesDashboard);

console.log("\n7. Others");
testPath(paths.lodash);

console.log("\nTest completed."); 