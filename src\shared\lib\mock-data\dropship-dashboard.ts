// Mock data for Dropship Products Dashboard
import { KpiDataType, SalesDataPoint, DropdownOption } from '@/types/index';
import {
  InboxStackIcon,
  ArchiveBoxArrowDownIcon,
  PaperAirplaneIcon,
  CheckCircleIcon,
} from '@/shared/config';

// Dropship Products Dashboard metrics
export const DROPSHIP_DASHBOARD_METRICS = {
  totalGrossSales: '$67,890',
  totalOrders: '355',
  avgProfitMargin: '28.2%',
  activeSuppliers: '18'
};

// Dropship Products KPI Data
export const DROPSHIP_KPI_DATA: KpiDataType[] = [
  { value: '5', label: 'Open Orders', icon: InboxStackIcon, color: 'text-orange-600', progress: 15 },
  { value: '7', label: 'Packed Orders', icon: ArchiveBoxArrowDownIcon, color: 'text-blue-600', progress: 25 },
  { value: '57', label: 'Ship', icon: PaperAirplaneIcon, color: 'text-green-600', progress: 65 },
  { value: '1', label: 'Delivered order', icon: CheckCircleIcon, color: 'text-purple-600', progress: 8 },
];

// Dropship Products Sales Chart Data
export const DROPSHIP_SALES_CHART_DATA: SalesDataPoint[] = [
  { name: 'January', currentValue: 5200, previousValue: 4100 },
  { name: 'February', currentValue: 7800, previousValue: 5900 },
  { name: 'March', currentValue: 9500, previousValue: 7200 },
  { name: 'April', currentValue: 11200, previousValue: 8800 },
  { name: 'May', currentValue: 13500, previousValue: 10200 },
  { name: 'June', currentValue: 12800, previousValue: 9800 },
  { name: 'July', currentValue: 0, previousValue: 0 },
  { name: 'August', currentValue: 0, previousValue: 0 },
  { name: 'September', currentValue: 0, previousValue: 0 },
  { name: 'October', currentValue: 0, previousValue: 0 },
  { name: 'November', currentValue: 0, previousValue: 0 },
  { name: 'December', currentValue: 0, previousValue: 0 },
];

// Dropship Product Categories Performance
export const DROPSHIP_CATEGORY_PERFORMANCE_DATA = [
  { name: 'Electronics', value: 22000, color: '#F59E0B' },
  { name: 'Home & Garden', value: 18500, color: '#D97706' },
  { name: 'Fashion & Apparel', value: 15200, color: '#FBBF24' },
  { name: 'Health & Beauty', value: 12190, color: '#FCD34D' },
];

// Top Selling Dropship Products
export const DROPSHIP_TOP_SELLERS = [
  { name: 'Wireless Bluetooth Earbuds', sales: '$8,900', units: 89, growth: '+22%' },
  { name: 'LED Desk Lamp', sales: '$6,750', units: 67, growth: '+18%' },
  { name: 'Fitness Tracker Watch', sales: '$5,400', units: 54, growth: '+15%' },
  { name: 'Portable Phone Charger', sales: '$4,200', units: 42, growth: '+12%' },
  { name: 'Yoga Mat Set', sales: '$3,800', units: 38, growth: '+8%' },
];

// Low Stock Dropship Products
export const DROPSHIP_LOW_STOCK_ALERTS = [
  { sku: 'DS-101', productName: 'Wireless Bluetooth Earbuds', stockLeft: 15, minThreshold: 30 },
  { sku: 'DS-205', productName: 'LED Desk Lamp White', stockLeft: 7, minThreshold: 20 },
  { sku: 'DS-312', productName: 'Fitness Tracker Watch Black', stockLeft: 4, minThreshold: 15 },
  { sku: 'DS-418', productName: 'Portable Phone Charger 10000mAh', stockLeft: 2, minThreshold: 12 },
];

// Dropship Supplier Performance
export const DROPSHIP_SUPPLIER_PERFORMANCE = [
  { supplier: 'TechSupply Co.', orders: 89, revenue: '$18,900', rating: 4.8 },
  { supplier: 'HomeGoods Direct', orders: 76, revenue: '$15,200', rating: 4.6 },
  { supplier: 'Fashion Forward', orders: 65, revenue: '$12,800', rating: 4.5 },
  { supplier: 'Beauty Essentials', orders: 54, revenue: '$10,400', rating: 4.7 },
  { supplier: 'Fitness Pro', orders: 43, revenue: '$8,600', rating: 4.4 },
];

// Dropship Order Fulfillment Times
export const DROPSHIP_FULFILLMENT_TIMES = [
  { timeRange: '1-2 days', percentage: 25, orders: 89 },
  { timeRange: '3-5 days', percentage: 45, orders: 160 },
  { timeRange: '6-10 days', percentage: 22, orders: 78 },
  { timeRange: '10+ days', percentage: 8, orders: 28 },
];

// Dropship Product Categories Distribution
export const DROPSHIP_CATEGORY_DISTRIBUTION = [
  { category: 'Electronics', percentage: 32, value: 22000 },
  { category: 'Home & Garden', percentage: 27, value: 18500 },
  { category: 'Fashion & Apparel', percentage: 22, value: 15200 },
  { category: 'Health & Beauty', percentage: 19, value: 12190 },
];

// Dropship Shipping Methods
export const DROPSHIP_SHIPPING_METHODS = [
  { method: 'Standard Shipping', orders: 142, percentage: 40, avgDays: 5 },
  { method: 'Express Shipping', orders: 107, percentage: 30, avgDays: 3 },
  { method: 'Priority Shipping', orders: 71, percentage: 20, avgDays: 2 },
  { method: 'Overnight Shipping', orders: 35, percentage: 10, avgDays: 1 },
];

export const dropshipSalesTypeOptions: DropdownOption[] = [
  { value: 'net_sales', label: 'Net Sales' },
  { value: 'gross_sales', label: 'Gross Sales' },
  { value: 'by_category', label: 'Sales by Category' },
  { value: 'by_supplier', label: 'Sales by Supplier' },
  { value: 'by_fulfillment', label: 'Sales by Fulfillment Time' },
];

export const dropshipYearComparisonOptions: DropdownOption[] = [
  { value: 'yearly_this_vs_last', label: 'Yearly: This Year vs. Last Year' },
  { value: 'monthly_this_vs_last', label: 'Monthly: This Month vs. Last Month' },
  { value: 'quarterly_this_vs_last', label: 'Quarterly: This Quarter vs. Last Quarter' },
  { value: 'supplier_comparison', label: 'Supplier Performance Comparison' },
];