# CSV Processing for Tracking Number Assignment

## Overview

We've enhanced the CSV processing system to support automatic carrier detection from tracking numbers. This document outlines the changes and how to use the new system.

## Key Components

1. **Carrier Parser Service** (`src/shared/lib/services/carrier/carrier-parser-service.ts`)
   - Detects carriers from tracking number patterns
   - Maps carrier names to carrier IDs
   - Normalizes carrier names (lowercase, trim)
   - Caches carrier information for performance

2. **CSV Processing Service** (`src/shared/lib/services/fulfill/csv-processing-service.ts`)
   - Validates CSV files for required columns
   - Auto-detects carriers from tracking numbers
   - Integrates with notification system for user feedback

## CSV Format

The CSV file only needs to contain the following columns:
- `order_id`: The ID of the order
- `tracking_number`: The tracking number to assign

Optionally:
- `order_number`: The order number for better identification (displayed in notifications)

Carrier information is automatically detected from the tracking number format.

You can still include:
- `carrier_name`: The name of the carrier (e.g., "USPS", "FedEx") if auto-detection fails
- `carrier_id`: The exact carrier ID if you know it

## Process Flow

1. User uploads CSV file
2. System validates CSV format
3. For each row:
   - System attempts to detect carrier from tracking number
   - If provided, use `carrier_name` or `carrier_id` as fallback
4. Process valid entries
5. Show notification with results (success/failure)
   - Shows order numbers for successfully processed orders

## Carrier Detection

The system can detect these carriers automatically from tracking number formats:
- USPS: 22-digit number starting with '9'
- FedEx: 12-digit number
- UPS: 18-character alphanumeric starting with '1Z'
- DHL: 10-digit number

## Error Handling

- Invalid CSV format: Shows specific error about missing columns
- Carrier detection failures: Groups and reports items with undetectable carriers

## Notification System

The system shows detailed notifications about processed orders:
- Success: Shows list of order numbers processed (up to 10, with count for remaining)
- Errors: Shows specific error message with details

## CSV Template Download

The system provides a CSV template download with all required columns:
- `order_id` (required): ID of the order
- `order_number` (included): Order number for better identification
- `tracking_number` (to be filled): Tracking number to assign

## Usage Example

```
order_id,tracking_number
123456,9400111202000000000000
789012,1234567890
```

## Future Improvements

- Add more carrier detection patterns
- Support for international carriers
- Batch processing with progress indicators 