import { useState, useCallback, useMemo, useEffect, useRef } from 'react';
import { useDebounce } from 'use-debounce';
import { InventoryView, ActiveFilter, FilterOption } from '@/types';
import { inventoryService, InventoryListParams } from '@/shared/lib/services/inventory';
import { inventoryCacheService } from '@/shared/lib/services/cache';
import { transformFiltersToParams, createStockStatusFilter, extractProductTypeOptions } from '@/shared/lib/utils/domain/inventory/inventory-filters';

export interface SortConfig {
  column: keyof InventoryView | null;
  direction: 'asc' | 'desc';
}

interface UseInventoryDataWithFiltersReturn {
  // Data state
  products: InventoryView[];
  loading: boolean;
  isInitialLoading: boolean;
  error: Error | null;
  totalCount: number;
  fetchProducts: (filters: InventoryListParams) => Promise<void>;
  
  // Selection state
  selectProduct: (productId: string) => void;
  selectAllProducts: (isSelected: boolean) => void;
  selectedProductIds: Set<string>;
  isAllSelected: boolean;
  isSomeSelected: boolean;
  clearSelection: () => void;
  
  // Pagination
  currentPage: number;
  totalPages: number;
  pageSize: number;
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  
  // Sorting
  sortConfig: SortConfig;
  setSortConfig: (config: SortConfig) => void;
  handleSort: (column: keyof InventoryView) => void;
  
  // Optimistic updates
  updateProductStock: (productId: string, adjustmentType: 'increase' | 'decrease' | 'set', quantity: number) => void;
  
  // Offline status
  isOffline: boolean;
  
  // Filter state
  searchTerm: string;
  activeFilters: ActiveFilter[];
  isFilterModalOpen: boolean;
  stockStatusFilter: string;
  availableProductTypes: FilterOption[];
  
  // Filter handlers
  handleSearchChange: (term: string) => void;
  handleOpenFilterModal: () => void;
  handleCloseFilterModal: () => void;
  handleApplyFilters: (newFilters: ActiveFilter[]) => void;
  handleRemoveFilter: (filterId: string) => void;
  handleStockStatusFilterChange: (status: string) => void;
  handleClearAllFilters: () => void;
}

/**
 * Consolidated hook for managing inventory data with filtering capabilities
 * Handles fetching, filtering, sorting, pagination, and selection states with proper memoization
 */
export function useInventoryDataWithFilters(): UseInventoryDataWithFiltersReturn {
  // Data state
  const [products, setProducts] = useState<InventoryView[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [isStaleData, setIsStaleData] = useState<boolean>(false);
  const [isInitialLoading, setIsInitialLoading] = useState<boolean>(true);
  const [isOffline, setIsOffline] = useState<boolean>(!navigator.onLine);
  
  // Selection state
  const [selectedProductIds, setSelectedProductIds] = useState<Set<string>>(new Set());
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  
  // Sorting state
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    column: null,
    direction: 'asc'
  });
  
  // Filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm] = useDebounce(searchTerm, 350);
  const [activeFilters, setActiveFilters] = useState<ActiveFilter[]>([]);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [stockStatusFilter, setStockStatusFilter] = useState<string>('all');
  
  // Current filters ref to avoid unnecessary fetches
  const currentFiltersRef = useRef<InventoryListParams>({});

  // Add online/offline detection
  useEffect(() => {
    const handleOnline = () => setIsOffline(false);
    const handleOffline = () => setIsOffline(true);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Calculate total pages
  const totalPages = useMemo(() => {
    return Math.max(1, Math.ceil(totalCount / pageSize));
  }, [totalCount, pageSize]);

  // Calculate if there's a next or previous page
  const hasNextPage = useMemo(() => {
    return currentPage < totalPages;
  }, [currentPage, totalPages]);

  const hasPreviousPage = useMemo(() => {
    return currentPage > 1;
  }, [currentPage]);

  // Generate product type options for filter modal - memoized
  const availableProductTypes: FilterOption[] = useMemo(() => {
    return extractProductTypeOptions(products);
  }, [products]);

  // Fetch products with memoized callback
  const fetchProducts = useCallback(async (filters: InventoryListParams) => {
    // Update current filters ref for revalidation
    currentFiltersRef.current = { ...filters };
    
    // Always set loading to true for consistent behavior
    setLoading(true);
    setError(null);
    
    try {
      // Add pagination and sorting parameters
      const params: InventoryListParams = {
        ...filters,
        page: currentPage,
        pageSize: pageSize,
      };
      
      // Add sorting if available
      if (sortConfig.column) {
        params.sortBy = sortConfig.column as string;
        params.sortDirection = sortConfig.direction;
      }
      
      // Check cache first
      const cachedData = inventoryCacheService.getCachedInventoryList(params);
      
      if (cachedData) {
        // Use cached data
        setProducts(cachedData.data);
        setTotalCount(cachedData.totalCount);
        
        // If data is stale, revalidate in the background
        if (cachedData.isStale) {
          console.log('Using stale inventory data while revalidating...');
          setIsStaleData(true);
          
          // Background fetch to revalidate
          try {
            const result = await inventoryService.getInventoryList(params);
            // Cache the results for future use
            inventoryCacheService.cacheInventoryList(params, result.data, result.count);
            setIsStaleData(false);
          } catch (err) {
            console.error('Background revalidation failed:', err);
          }
        } else {
          // Data is fresh from cache
          setIsStaleData(false);
        }
        
        // Turn off loading state
        setLoading(false);
        
        // Initial load is complete if it was active
        setIsInitialLoading(false);
        
        return;
      }
      
      // No cached data, fetch from service
      const result = await inventoryService.getInventoryList(params);
      
      // Update state with fetched data
      setProducts(result.data);
      setTotalCount(result.count);
      setIsStaleData(false);
      
      // Cache the results for future use
      inventoryCacheService.cacheInventoryList(params, result.data, result.count);
      
    } catch (err) {
      console.error('Error fetching inventory data:', err);
      const error = err instanceof Error ? err : new Error('Failed to fetch inventory data');
      setError(error);
      // Clear products on error for consistent behavior
      setProducts([]);
      setTotalCount(0);
    } finally {
      // Always turn off loading state
      setLoading(false);
      
      // Initial load is complete regardless of the outcome
      setIsInitialLoading(false);
    }
  }, [currentPage, pageSize, sortConfig]);

  // When debounced search term or active filters change, fetch with new parameters
  useEffect(() => {
    // Transform active filters to API parameters
    const params = transformFiltersToParams(activeFilters, debouncedSearchTerm, stockStatusFilter);
    
    // Fetch with combined parameters
    fetchProducts(params);
  }, [activeFilters, debouncedSearchTerm, stockStatusFilter, fetchProducts]);
  
  // Custom setCurrentPage that triggers data fetch with updated page
  const handleSetCurrentPage = useCallback((page: number) => {
    setCurrentPage(page);
    // Immediately fetch with the new page
    const updatedParams = {
      ...currentFiltersRef.current,
      page: page,
      pageSize: pageSize
    };
    
    // Add sorting if available
    if (sortConfig.column) {
      updatedParams.sortBy = sortConfig.column as string;
      updatedParams.sortDirection = sortConfig.direction;
    }
    
    // Fetch directly without relying on the effect
    setLoading(true);
    inventoryService.getInventoryList(updatedParams)
      .then(result => {
        setProducts(result.data);
        setTotalCount(result.count);
        setLoading(false);
      })
      .catch(err => {
        console.error('Error fetching inventory data after page change:', err);
        const error = err instanceof Error ? err : new Error('Failed to fetch inventory data');
        setError(error);
        setLoading(false);
      });
  }, [pageSize, sortConfig]);
  
  // Custom setPageSize that resets to page 1 and fetches
  const handleSetPageSize = useCallback((size: number) => {
    setPageSize(size);
    setCurrentPage(1); // Reset to first page
    
    // Immediately fetch with new page size
    const updatedParams = {
      ...currentFiltersRef.current,
      page: 1,
      pageSize: size
    };
    
    // Add sorting if available
    if (sortConfig.column) {
      updatedParams.sortBy = sortConfig.column as string;
      updatedParams.sortDirection = sortConfig.direction;
    }
    
    // Fetch directly without relying on the effect
    setLoading(true);
    inventoryService.getInventoryList(updatedParams)
      .then(result => {
        setProducts(result.data);
        setTotalCount(result.count);
        setLoading(false);
      })
      .catch(err => {
        console.error('Error fetching inventory data after page size change:', err);
        const error = err instanceof Error ? err : new Error('Failed to fetch inventory data');
        setError(error);
        setLoading(false);
      });
  }, [sortConfig]);
  
  // Handle sorting column clicks
  const handleSort = useCallback((column: keyof InventoryView) => {
    setSortConfig(prevConfig => {
      const newDirection = prevConfig.column === column && prevConfig.direction === 'asc' ? 'desc' : 'asc';
      return { column, direction: newDirection };
    });
    
    // Reset to first page when sorting changes
    setCurrentPage(1);
    
    // Fetch will happen via the useEffect that watches sortConfig
  }, []);
  
  // Handle product selection
  const selectProduct = useCallback((productId: string) => {
    setSelectedProductIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(productId)) {
        newSet.delete(productId);
      } else {
        newSet.add(productId);
      }
      return newSet;
    });
  }, []);
  
  // Handle select all products
  const selectAllProducts = useCallback((isSelected: boolean) => {
    if (isSelected) {
      const allIds = new Set(products.map(p => p.product_id || ''));
      setSelectedProductIds(allIds);
    } else {
      setSelectedProductIds(new Set());
    }
  }, [products]);
  
  // Check if all products are selected
  const isAllSelected = useMemo(() => {
    return products.length > 0 && selectedProductIds.size === products.length;
  }, [products, selectedProductIds]);
  
  // Check if some products are selected
  const isSomeSelected = useMemo(() => {
    return selectedProductIds.size > 0 && selectedProductIds.size < products.length;
  }, [products, selectedProductIds]);
  
  // Clear selection
  const clearSelection = useCallback(() => {
    setSelectedProductIds(new Set());
  }, []);
  
  // Optimistic update for product stock
  const updateProductStock = useCallback((productId: string, adjustmentType: 'increase' | 'decrease' | 'set', quantity: number) => {
    setProducts(prevProducts => {
      return prevProducts.map(product => {
        if (product.product_id === productId) {
          const currentStock = product.current_stock || 0;
          let newStock: number;
          
          switch (adjustmentType) {
            case 'increase':
              newStock = currentStock + quantity;
              break;
            case 'decrease':
              newStock = Math.max(0, currentStock - quantity);
              break;
            case 'set':
              newStock = quantity;
              break;
            default:
              newStock = currentStock;
          }
          
          return {
            ...product,
            current_stock: newStock
          };
        }
        return product;
      });
    });
  }, []);

  // Filter handlers
  const handleSearchChange = useCallback((term: string) => {
    setSearchTerm(term);
  }, []);

  const handleOpenFilterModal = useCallback(() => setIsFilterModalOpen(true), []);
  const handleCloseFilterModal = useCallback(() => setIsFilterModalOpen(false), []);

  const handleApplyFilters = useCallback((newFilters: ActiveFilter[]) => {
    setActiveFilters(newFilters);
    // Reset to first page when filters change
    setCurrentPage(1);
  }, []);

  const handleRemoveFilter = useCallback((filterId: string) => {
    setActiveFilters(prev => prev.filter(f => f.id !== filterId));
  }, []);

  // Handle stock status filter change
  const handleStockStatusFilterChange = useCallback((status: string) => {
    setStockStatusFilter(status);
    setCurrentPage(1); // Reset to first page when filter changes
    
    if (status !== 'all') {
      // Remove any existing stock status filters
      const updatedFilters = activeFilters.filter(f => f.type !== 'stockStatus');
      
      // Add the new stock status filter
      const newFilter = createStockStatusFilter(status);
      setActiveFilters([...updatedFilters, newFilter]);
    } else {
      // Remove any stock status filters if 'all' is selected
      setActiveFilters(prev => prev.filter(f => f.type !== 'stockStatus'));
    }
  }, [activeFilters]);

  // Handler for clearing all filters
  const handleClearAllFilters = useCallback(() => {
    setActiveFilters([]);
    setSearchTerm('');
    setStockStatusFilter('all');
    setCurrentPage(1);
  }, []);

  // Return all values and handlers
  return useMemo(() => ({
    // Data state
    products,
    loading,
    isInitialLoading,
    error,
    totalCount,
    fetchProducts,
    
    // Selection state
    selectProduct,
    selectAllProducts,
    selectedProductIds,
    isAllSelected,
    isSomeSelected,
    clearSelection,
    
    // Pagination
    currentPage,
    totalPages,
    pageSize,
    setCurrentPage: handleSetCurrentPage,
    setPageSize: handleSetPageSize,
    hasNextPage,
    hasPreviousPage,
    
    // Sorting
    sortConfig,
    setSortConfig,
    handleSort,
    
    // Optimistic updates
    updateProductStock,
    
    // Offline status
    isOffline,
    
    // Filter state
    searchTerm,
    activeFilters,
    isFilterModalOpen,
    stockStatusFilter,
    availableProductTypes,
    
    // Filter handlers
    handleSearchChange,
    handleOpenFilterModal,
    handleCloseFilterModal,
    handleApplyFilters,
    handleRemoveFilter,
    handleStockStatusFilterChange,
    handleClearAllFilters
  }), [
    // Data state
    products,
    loading,
    isInitialLoading,
    error,
    totalCount,
    fetchProducts,
    
    // Selection state
    selectProduct,
    selectAllProducts,
    selectedProductIds,
    isAllSelected,
    isSomeSelected,
    clearSelection,
    
    // Pagination
    currentPage,
    totalPages,
    pageSize,
    handleSetCurrentPage,
    handleSetPageSize,
    hasNextPage,
    hasPreviousPage,
    
    // Sorting
    sortConfig,
    setSortConfig,
    handleSort,
    
    // Optimistic updates
    updateProductStock,
    
    // Offline status
    isOffline,
    
    // Filter state
    searchTerm,
    activeFilters,
    isFilterModalOpen,
    stockStatusFilter,
    availableProductTypes,
    
    // Filter handlers
    handleSearchChange,
    handleOpenFilterModal,
    handleCloseFilterModal,
    handleApplyFilters,
    handleRemoveFilter,
    handleStockStatusFilterChange,
    handleClearAllFilters
  ]);
} 