import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import App from './src/app/main';

// Create QueryClient instance with better configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false,
    },
  },
});

// Configure React Router with future flags to avoid warnings
const routerOptions = {
  future: {
    v7_startTransition: true,
    v7_relativeSplatPath: true
  }
};

const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error("Could not find root element to mount to");
}

const root = ReactDOM.createRoot(rootElement);

// For development: Temporarily disable StrictMode to avoid duplicate initialization
// This can help debug "Multiple GoTrueClient instances" warnings
// In production, you can re-enable StrictMode
if (process.env.NODE_ENV !== 'production') {
  root.render(
    <QueryClientProvider client={queryClient}>
      <BrowserRouter {...routerOptions}>
        <App />
      </BrowserRouter>
    </QueryClientProvider>
  );
} else {
  root.render(
    <React.StrictMode>
      <QueryClientProvider client={queryClient}>
        <BrowserRouter {...routerOptions}>
          <App />
        </BrowserRouter>
      </QueryClientProvider>
    </React.StrictMode>
  );
}