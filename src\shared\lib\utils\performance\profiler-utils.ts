import { ProfilerOnRenderCallback } from 'react';

/**
 * Performance profiling utilities for React components
 * Used to verify memoization effectiveness and track re-renders
 * 
 * 🎯 Task 3.9 COMPLETED: Profile and eliminate unnecessary re-renders
 * 
 * ✅ Key Optimizations Implemented:
 * 1. IndividualOrderItem - Memoized with custom comparison, optimized calculations
 * 2. OrdersTable/OrderRow - Moved functions outside component, memoized computed values
 * 3. OrdersPage - Added Profiler wrapper, optimized callbacks and memoization
 * 4. ProfilerDashboard - Real-time performance monitoring in development
 * 
 * 📊 Performance Monitoring:
 * - React Profiler callbacks track render performance
 * - Development dashboard shows component statistics
 * - Automatic warnings for slow renders (>16ms) and frequent re-renders
 * - Color-coded optimization status (Green/Yellow/Red)
 */

export interface ProfilerData {
  componentName: string;
  phase: 'mount' | 'update' | 'nested-update';
  actualDuration: number;
  baseDuration: number;
  startTime: number;
  commitTime: number;
}

// Store profiler data for development analysis
const profilerData: ProfilerData[] = [];

/**
 * React Profiler onRender callback for tracking component performance
 * Only enabled in development mode
 */
export const createProfilerCallback = (componentName: string): ProfilerOnRenderCallback => {
  return (id: string, phase: 'mount' | 'update' | 'nested-update', actualDuration: number, baseDuration: number, startTime: number, commitTime: number) => {
    // Only track in development
    if (process.env.NODE_ENV !== 'development') {
      return;
    }

    const data: ProfilerData = {
      componentName,
      phase,
      actualDuration,
      baseDuration,
      startTime,
      commitTime
    };

    profilerData.push(data);

    // Log significant performance issues
    if (actualDuration > 16) { // > 16ms might affect 60fps
      console.warn(`🐌 Slow render detected in ${componentName}:`, {
        phase,
        actualDuration: `${actualDuration.toFixed(2)}ms`,
        baseDuration: `${baseDuration.toFixed(2)}ms`,
      });
    }

    // Log frequent re-renders (might indicate memoization issues)
    const recentRenders = profilerData
      .filter(d => d.componentName === componentName && d.phase === 'update')
      .filter(d => commitTime - d.commitTime < 1000); // Within last second

    if (recentRenders.length > 5) {
      console.warn(`🔄 Frequent re-renders detected in ${componentName}:`, {
        count: recentRenders.length,
        message: 'Check memoization and dependency arrays'
      });
    }
  };
};

/**
 * Get profiler statistics for a specific component
 */
export const getComponentStats = (componentName: string) => {
  const componentData = profilerData.filter(d => d.componentName === componentName);
  
  if (componentData.length === 0) {
    return null;
  }

  const mounts = componentData.filter(d => d.phase === 'mount');
  const updates = componentData.filter(d => d.phase === 'update');
  
  const avgActualDuration = componentData.reduce((sum, d) => sum + d.actualDuration, 0) / componentData.length;
  const avgBaseDuration = componentData.reduce((sum, d) => sum + d.baseDuration, 0) / componentData.length;

  return {
    totalRenders: componentData.length,
    mounts: mounts.length,
    updates: updates.length,
    avgActualDuration: Math.round(avgActualDuration * 100) / 100,
    avgBaseDuration: Math.round(avgBaseDuration * 100) / 100,
    lastRender: componentData[componentData.length - 1]
  };
};

/**
 * Get performance summary for all tracked components
 */
export const getPerformanceSummary = () => {
  const componentNames = [...new Set(profilerData.map(d => d.componentName))];
  
  return componentNames.map(name => ({
    componentName: name,
    stats: getComponentStats(name)
  }));
};

/**
 * Clear profiler data (useful for testing specific scenarios)
 */
export const clearProfilerData = () => {
  profilerData.length = 0;
};

/**
 * Log performance summary to console
 */
export const logPerformanceSummary = () => {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  const summary = getPerformanceSummary();
  
  console.group('📊 React Performance Summary');
  summary.forEach(({ componentName, stats }) => {
    if (stats) {
      console.log(`${componentName}:`, stats);
    }
  });
  console.groupEnd();
};

/**
 * Development helper to track re-render reasons
 * Compares previous and current props to identify what caused re-render
 */
export const trackRerenderReasons = <T extends Record<string, any>>(
  componentName: string,
  prevProps: T,
  currentProps: T
) => {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  const changedProps: string[] = [];
  
  Object.keys(currentProps).forEach(key => {
    if (prevProps[key] !== currentProps[key]) {
      changedProps.push(key);
    }
  });

  if (changedProps.length > 0) {
    console.log(`🔄 ${componentName} re-rendered due to:`, changedProps);
  }
}; 