import { 
  WebSocketMessage, 
  WebSocketConfig, 
  WebSocketState, 
  WebSocketEventHandler, 
  WebSocketStateHandler,
  DEFAULT_WEBSOCKET_CONFIG 
} from './websocket-config';
import { WebSocketConnection } from './websocket-connection';
import { WebSocketReconnection } from './websocket-reconnection';
import { WebSocketHeartbeat } from './websocket-heartbeat';
import { WebSocketMessageHandler } from './websocket-message-handler';

// Component subscription tracking
interface Subscription {
  componentId: string;
  handler: WebSocketEventHandler;
}

class WebSocketService {
  private config: WebSocketConfig;
  private state: WebSocketState;
  private connection: WebSocketConnection;
  private reconnection: WebSocketReconnection;
  private heartbeat: WebSocketHeartbeat;
  private messageHandler: WebSocketMessageHandler;
  private isDestroyed = false;
  
  // Connection management with reference counting
  private connectionCount = 0;
  private isReconnecting = false;
  
  // Component tracking for event subscriptions
  private subscriptions: Map<string, Subscription[]> = new Map();

  constructor(config: Partial<WebSocketConfig> = {}) {
    this.config = { ...DEFAULT_WEBSOCKET_CONFIG, ...config };
    
    this.state = {
      isConnected: false,
      isReconnecting: false,
      reconnectAttempts: 0,
      lastHeartbeat: null,
      connectionStartTime: null,
    };

    this.connection = new WebSocketConnection(this.config);
    this.reconnection = new WebSocketReconnection(this.config);
    this.heartbeat = new WebSocketHeartbeat(this.config);
    this.messageHandler = new WebSocketMessageHandler();
  }

  /**
   * Ensures WebSocket connection is established
   * Returns a promise that resolves when connection is ready
   */
  public ensureConnection(): Promise<void> {
    if (this.state.isConnected) {
      return Promise.resolve();
    }
    
    // If already connecting, return a promise that resolves when connected
    if (this.connectionCount > 0) {
      return new Promise((resolve, reject) => {
        const checkInterval = setInterval(() => {
          if (this.state.isConnected) {
            clearInterval(checkInterval);
            resolve();
          }
          
          // Add timeout for connection attempt
          const startTime = this.state.connectionStartTime instanceof Date 
            ? this.state.connectionStartTime.getTime() 
            : (this.state.connectionStartTime || Date.now());
            
          if (Date.now() - startTime > 10000) {
            clearInterval(checkInterval);
            reject(new Error('Connection timeout'));
          }
        }, 100);
      });
    }
    
    // Otherwise, initiate a new connection
    return this.connect();
  }

  /**
   * Establishes WebSocket connection to Supabase Realtime
   * Now uses reference counting to manage connection lifecycle
   */
  public async connect(): Promise<void> {
    if (this.isDestroyed) {
      throw new Error('WebSocket service has been destroyed');
    }

    // Increment connection counter
    this.connectionCount++;
    
    // If we're currently reconnecting, don't attempt another connection
    if (this.isReconnecting) {
      return;
    }

    // Only connect if not already connected
    if (this.state.isConnected) {
      return;
    }

    try {
      this.connection.connect(
        () => this.handleConnectionOpen(),
        (message) => this.handleIncomingMessage(message),
        (event) => this.handleConnectionClose(event),
        (event) => this.handleConnectionError(event)
      );
    } catch (error) {
      console.error('Failed to initiate WebSocket connection:', error);
      this.handleConnectionError(new Event('error'));
    }
  }

  /**
   * Disconnects from WebSocket if no more active connections are needed
   * @param force When true, forces disconnect regardless of reference count
   */
  public async disconnect(force: boolean = false): Promise<void> {
    if (force) {
      this.connectionCount = 0;
    } else {
      // Decrement connection counter (ensure it never goes below 0)
      this.connectionCount = Math.max(0, this.connectionCount - 1);
    }

    // Only disconnect if no more active connections are needed
    if (this.connectionCount === 0) {
      // Cancel any reconnection attempts in progress
      this.isReconnecting = false;
      this.reconnection.cancelReconnection();
      this.heartbeat.stop();
      
      await this.connection.disconnect();
  
      this.updateState({
        isConnected: false,
        isReconnecting: false,
        lastHeartbeat: null,
        connectionStartTime: null,
        reconnectAttempts: 0,
      });
    }
  }

  /**
   * Destroys the WebSocket service permanently
   */
  public async destroy(): Promise<void> {
    this.isDestroyed = true;
    await this.disconnect(true); // Force disconnect
    
    this.connection.destroy();
    this.reconnection.destroy();
    this.heartbeat.destroy();
    this.messageHandler.destroy();
    
    // Clear all subscriptions
    this.subscriptions.clear();
  }

  /**
   * Subscribes to WebSocket events with component ID tracking
   * @param eventType The event type to subscribe to
   * @param componentId A unique identifier for the subscribing component
   * @param handler The event handler function
   * @returns Unsubscribe function
   */
  public subscribeWithId(
    eventType: string, 
    componentId: string, 
    handler: WebSocketEventHandler
  ): () => void {
    // Auto-connect if this is the first subscription
    if (this.connectionCount === 0) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`First subscriber detected (${componentId} for ${eventType}), auto-connecting WebSocket`);
      }
      this.connect().catch(err => {
        console.error('Failed to auto-connect WebSocket:', err);
      });
    }
    
    // Register with message handler (which actually dispatches events)
    this.messageHandler.on(eventType, handler);
    
    // Track the subscription by component ID
    if (!this.subscriptions.has(eventType)) {
      this.subscriptions.set(eventType, []);
    }
    
    this.subscriptions.get(eventType)!.push({
      componentId,
      handler
    });
    
    // Return unsubscribe function
    return () => this.unsubscribeWithId(eventType, componentId, handler);
  }
  
  /**
   * Unsubscribes from WebSocket events using component ID
   */
  private unsubscribeWithId(
    eventType: string, 
    componentId: string, 
    handler: WebSocketEventHandler
  ): void {
    // Remove from message handler
    this.messageHandler.off(eventType, handler);
    
    // Remove from our tracking
    const subs = this.subscriptions.get(eventType);
    if (subs) {
      const index = subs.findIndex(
        sub => sub.componentId === componentId && sub.handler === handler
      );
      
      if (index !== -1) {
        subs.splice(index, 1);
      }
      
      // Clean up empty subscription arrays
      if (subs.length === 0) {
        this.subscriptions.delete(eventType);
      }
    }
  }

  /**
   * Subscribes to WebSocket events (legacy method)
   */
  public on(eventType: string, handler: WebSocketEventHandler): void {
    this.messageHandler.on(eventType, handler);
  }

  /**
   * Unsubscribes from WebSocket events (legacy method)
   */
  public off(eventType: string, handler: WebSocketEventHandler): void {
    this.messageHandler.off(eventType, handler);
  }

  /**
   * Subscribes to WebSocket state changes
   */
  public onStateChange(handler: WebSocketStateHandler): void {
    this.messageHandler.onStateChange(handler);
  }

  /**
   * Gets current WebSocket state
   */
  public getState(): WebSocketState {
    return { ...this.state };
  }

  /**
   * Sends a message through the WebSocket
   */
  public send(message: Omit<WebSocketMessage, 'timestamp'>): void {
    this.connection.send(message);
  }
  
  /**
   * Gets current subscription count for an event type
   * Useful for debugging and monitoring
   */
  public getSubscriptionCount(eventType?: string): number {
    if (eventType) {
      const subs = this.subscriptions.get(eventType);
      return subs ? subs.length : 0;
    }
    
    // Return total subscription count across all events
    let total = 0;
    this.subscriptions.forEach(subs => {
      total += subs.length;
    });
    return total;
  }

  /**
   * Updates the WebSocket state
   */
  private updateState(updates: Partial<WebSocketState>): void {
    this.state = { ...this.state, ...updates };
    this.messageHandler.notifyStateChange(this.state);
  }

  /**
   * Initiates reconnection process
   */
  private initiateReconnection(): void {
    if (this.isReconnecting || this.isDestroyed || this.connectionCount === 0) {
      return;
    }

    this.isReconnecting = true;
    this.updateState({ isReconnecting: true });

    this.reconnection.scheduleReconnection(
      this.state.reconnectAttempts,
      (attempts) => this.updateState({ reconnectAttempts: attempts }),
      () => {
        // Only attempt reconnection if still needed
        if (this.connectionCount > 0) {
          return this.connect();
        } else {
          this.isReconnecting = false;
          this.updateState({ isReconnecting: false });
          return Promise.resolve();
        }
      }
    );
  }

  /**
   * Handles successful connection opening.
   */
  private handleConnectionOpen(): void {
    this.isReconnecting = false;
    this.updateState({
      isConnected: true,
      isReconnecting: false,
      reconnectAttempts: 0,
      connectionStartTime: new Date(),
    });

    this.heartbeat.start(
      () => this.state.isConnected,
      (updates) => this.updateState(updates),
      (message) => this.connection.send(message)
    );
  }

  /**
   * Handles the connection closing.
   */
  private handleConnectionClose(event: CloseEvent): void {
    this.updateState({
      isConnected: false,
      lastHeartbeat: null,
    });

    this.heartbeat.stop();

    // Only attempt reconnection if not cleanly closed, not destroyed, and still needed
    if (event.code !== 1000 && !this.isDestroyed && this.connectionCount > 0) {
      this.initiateReconnection();
    } else {
      this.isReconnecting = false;
      this.updateState({
        isReconnecting: false,
        connectionStartTime: null,
      });
    }
  }

  /**
   * Handles connection errors.
   */
  private handleConnectionError(_event: Event): void {
    console.error('WebSocket connection error');
    
    this.updateState({
      isConnected: false,
      lastHeartbeat: null,
    });

    this.heartbeat.stop();

    // Only attempt reconnection if not destroyed and still needed
    if (!this.isDestroyed && this.connectionCount > 0) {
      this.initiateReconnection();
    } else {
      this.isReconnecting = false;
    }
  }

  /**
   * Handles incoming WebSocket messages
   */
  private handleIncomingMessage(message: WebSocketMessage): void {
    this.messageHandler.handleIncomingMessage(message);

    if (message.type === 'heartbeat' || message.type === 'connection_ack') {
      this.heartbeat.updateHeartbeat((updates) => this.updateState(updates));
    }
  }
}

// Create and export a singleton instance
export const webSocketService = new WebSocketService();
export default webSocketService;

// Export class for testing
export { WebSocketService }; 