

import { ProductItem } from '../../types';

export const MOCK_PRODUCTS: ProductItem[] = [
  { id: 'prod-001', selected: false, status: 'active', itemNumber: 'iphone-15-fair', name: 'iPhone 15 Fair', productType: 'Standard Product', onHand: '1', cost: 0.00, price: 0.00 },
  { id: 'prod-002', selected: false, status: 'active', itemNumber: 'iphone-16-good', name: 'iPhone 16 Good', productType: 'Standard Product', onHand: '5', cost: 0.00, price: 0.00 },
  { id: 'prod-003', selected: false, status: 'active', itemNumber: 'phone', name: 'Opaque Phone', productType: 'Standard Product', onHand: '14', cost: 0.00, price: 0.00 },
  { id: 'prod-004', selected: false, status: 'new', itemNumber: 'LNB2', name: 'Lighting N Beyond Product 2', productType: 'Standard Product', onHand: '900', cost: 0.00, price: 0.00 },
  { id: 'prod-005', selected: false, status: 'active', itemNumber: 'LNB1', name: 'Lighting N Beyond Product 1', productType: 'Standard Product', onHand: '1,100', cost: 0.00, price: 0.00 },
  { id: 'prod-006', selected: false, status: 'discontinued', itemNumber: 'attttt', name: 'Tim Test Tim', productType: 'Standard Product', onHand: '0', cost: 0.00, price: 0.00 },
  { id: 'prod-007', selected: false, status: 'active', itemNumber: 'Tim', name: 'Tim Test', productType: 'Standard Product', onHand: '804', cost: 0.00, price: 0.00 },
  { id: 'prod-008', selected: true, status: 'new', itemNumber: 'ZQ620-Z-13029-10', name: '1 - Zebra ZQ620 Mobile Printer & 10 - Zebra Mobile Printer Labels - 4 x 6" Bundle', productType: 'Kit Product', onHand: '13,792', cost: 10.01, price: 1475.00 },
  { id: 'prod-009', selected: false, status: 'active', itemNumber: 'Z-13029', name: 'Zebra Mobile Printer Labels - 4 x 6"', productType: 'Standard Product', onHand: '2,702 + 500', cost: 9.69, price: 8.00 },
  { id: 'prod-010', selected: false, status: 'active', itemNumber: 'ZQ620', name: 'Zebra ZQ620 Mobile Printer - Print labels on the spot in warehouses, shipping departments...', productType: 'Standard Product', onHand: '1,253 + 600', cost: 950.00, price: 1099.00 },
  { id: 'prod-011', selected: false, status: 'new', itemNumber: 'HQ-133B-12-9093', name: 'Space Age Totes Bulk Pack - 18 x 13 x 12" - 12 Pieces + Tote Picking Cart - 68 x 28 x 70"', productType: 'Kit Product', onHand: '203', cost: 1900.00, price: 3000.00 },
  { id: 'prod-012', selected: false, status: 'active', itemNumber: 'HQ-133B-12K', name: 'Space Age Totes Bulk Pack - 18 x 13 x 12" - 12 Pieces', productType: 'Kit Product', onHand: '201', cost: 118.00, price: 600.00 },
  { id: 'prod-013', selected: false, status: 'active', itemNumber: 'HQ-9094', name: 'TOTE PICKING CART', productType: 'Group Product', onHand: '0', cost: 509.99, price: 1050.00 },
  { id: 'prod-014', selected: false, status: 'issue', itemNumber: 'HQ-P093', name: 'Tote Picking Cart * Cart Only, 68 x 28 x 70"', productType: 'Standard Product', onHand: '211 + 55', cost: 390.00, price: 600.00 },
  { id: 'prod-015', selected: false, status: 'active', itemNumber: 'HQ-133B', name: 'Space Age Totes Bulk Pack - 18 x 13 x 12"', productType: 'Standard Product', onHand: '7,481 + 100', cost: 11.00, price: 20.00 },
  { id: 'prod-016', selected: false, status: 'active', itemNumber: 'ITEM-X1', name: 'Another Active Item', productType: 'Standard Product', onHand: '150', cost: 25.50, price: 49.99 },
  { id: 'prod-017', selected: false, status: 'new', itemNumber: 'ITEM-N2', name: 'New Gadget Pro', productType: 'Kit Product', onHand: '75', cost: 199.00, price: 349.00 },
  { id: 'prod-018', selected: false, status: 'discontinued', itemNumber: 'ITEM-D3', name: 'Old Model Charger', productType: 'Accessory', onHand: '0', cost: 5.00, price: 9.99 },
  { id: 'prod-019', selected: false, status: 'issue', itemNumber: 'ITEM-I4', name: 'Faulty Batch Alpha', productType: 'Component', onHand: '30 + 5', cost: 12.00, price: 15.00 },
  { id: 'prod-020', selected: false, status: 'active', itemNumber: 'ITEM-A5', name: 'Premium Blend Coffee', productType: 'Consumable', onHand: '500', cost: 8.75, price: 19.95 },
];