import React from 'react';
import { Pagination } from '@/shared/ui/navigation/Pagination';
import { FetchOrdersResponse } from '@/shared/lib/services/order/live-order-service';

interface OrdersTableFooterProps {
  ordersData: FetchOrdersResponse;
  onPageChange: (page: number) => void;
}

const OrdersTableFooter: React.FC<OrdersTableFooterProps> = ({ 
  ordersData, 
  onPageChange 
}) => {
  return (
    <div className="px-6 py-3 border-t border-gray-200 bg-gray-50">
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-700">
          Showing {ordersData.orders.length} of {ordersData.totalCount.toLocaleString()} orders
        </div>
        <Pagination
          currentPage={ordersData.currentPage}
          totalPages={ordersData.totalPages}
          hasNextPage={ordersData.hasNextPage}
          hasPreviousPage={ordersData.hasPreviousPage}
          onPageChange={onPageChange}
        />
      </div>
    </div>
  );
};

export default OrdersTableFooter; 