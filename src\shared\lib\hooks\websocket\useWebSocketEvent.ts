import { useEffect, useRef, useCallback } from 'react';
import { WebSocketMessage } from '../../services/websocket/websocket-config';
import { webSocketService } from '../../services/websocket/websocket-service';
import { v4 as uuidv4 } from 'uuid';

export type WebSocketEventHandler = (message: WebSocketMessage) => void;

/**
 * Custom hook for subscribing to WebSocket events with automatic cleanup
 * and component identity tracking to prevent duplicate handlers
 * 
 * @param eventType The event type to subscribe to
 * @param callback The callback function to be executed when the event is received
 * @param deps Dependencies array for the callback (similar to useEffect deps)
 * @param componentId Optional custom component ID (defaults to auto-generated UUID)
 * @returns void
 * 
 * @example
 * // Basic usage:
 * useWebSocketEvent('order_update', (message) => {
 *   console.log('Order updated:', message.payload);
 * });
 * 
 * // With dependencies:
 * useWebSocketEvent('order_update', (message) => {
 *   updateOrders(message.payload);
 * }, [updateOrders]);
 * 
 * // With custom component ID:
 * useWebSocketEvent('order_update', handleOrderUpdate, [orderId], `order-detail-${orderId}`);
 */
export function useWebSocketEvent(
  eventType: string,
  callback: WebSocketEventHandler,
  deps: any[] = [],
  componentId?: string
): void {
  // Generate a stable component ID if not provided
  const stableComponentId = useRef(componentId || `component-${uuidv4()}`).current;
  
  // Keep a reference to the latest callback
  const callbackRef = useRef<WebSocketEventHandler>(callback);
  
  // Update the callback reference when it changes
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);
  
  // Track if component is mounted
  const isMountedRef = useRef(true);
  
  // Create a stable callback wrapper that always uses the latest callback reference
  const stableCallback = useCallback((message: WebSocketMessage) => {
    try {
      // Only call the callback if the component is still mounted
      if (isMountedRef.current) {
        // Add some useful debugging in development
        if (process.env.NODE_ENV === 'development') {
          console.log(`WebSocket event received (${eventType}):`, {
            componentId: stableComponentId,
            timestamp: new Date().toISOString(),
            type: message.type,
            eventType,
          });
        }
        
        // Use the latest callback from the ref
        callbackRef.current(message);
      }
    } catch (error) {
      console.error(`Error in WebSocket event handler for ${eventType}:`, error);
    }
  }, [eventType, stableComponentId, ...deps]);
  
  // Handle connection and subscription
  useEffect(() => {
    // Ensure WebSocket service is connected
    webSocketService.ensureConnection();
    
    // Subscribe to the event with our component ID
    const unsubscribe = webSocketService.subscribeWithId(
      eventType,
      stableComponentId,
      stableCallback
    );
    
    // Cleanup on unmount or when dependencies change
    return () => {
      isMountedRef.current = false;
      unsubscribe();
    };
  }, [eventType, stableComponentId, stableCallback]);
  
  // Set mounted to false on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);
}

export default useWebSocketEvent; 