import { WebSocketConfig, WebSocketMessage } from './websocket-config';

export class WebSocketConnection {
  private socket: WebSocket | null = null;
  private config: WebSocketConfig;
  private isSocketConnected = false;

  constructor(config: WebSocketConfig) {
    this.config = config;
  }

  /**
   * Establishes a standard WebSocket connection to the Edge Function.
   */
  public connect(
    onOpen: () => void,
    onMessage: (message: WebSocketMessage) => void,
    onClose: (event: CloseEvent) => void,
    onError: (event: Event) => void
  ): void {
    try {
      if (this.socket && (this.socket.readyState === WebSocket.OPEN || this.socket.readyState === WebSocket.CONNECTING)) {
        return; // Already connected or connecting
      }
      
      // Check if we have a valid URL
      if (!this.config.edgeFunctionUrl) {
        throw new Error('Invalid WebSocket URL: The URL is empty');
      }
      
      // Log connection attempt
      if (process.env.NODE_ENV === 'development') {
        console.log(`Attempting WebSocket connection to: ${this.config.edgeFunctionUrl}`);
      }

      this.socket = new WebSocket(this.config.edgeFunctionUrl);

      this.socket.onopen = () => {
        this.isSocketConnected = true;
        if (process.env.NODE_ENV === 'development') {
          console.log('WebSocket connection established successfully');
        }
        onOpen();
      };

      this.socket.onmessage = (event: MessageEvent) => {
        try {
          const data = JSON.parse(event.data);
          onMessage({
            ...data,
            timestamp: data.timestamp || new Date()
          });
        } catch (err) {
          console.error('Error parsing WebSocket message:', err);
          console.error('Raw message data:', event.data);
        }
      };

      this.socket.onclose = (event: CloseEvent) => {
        this.isSocketConnected = false;
        if (process.env.NODE_ENV === 'development') {
          console.log(`WebSocket connection closed: Code=${event.code}, Reason=${event.reason || 'No reason provided'}`);
        }
        onClose(event);
      };

      this.socket.onerror = (event: Event) => {
        console.error('WebSocket connection error:', event);
        onError(event);
      };
    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      throw error;
    }
  }

  /**
   * Disconnects from the WebSocket.
   */
  public async disconnect(): Promise<void> {
    return new Promise((resolve) => {
      if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
        this.isSocketConnected = false;
        resolve();
        return;
      }

      const onCloseHandler = () => {
        this.socket!.removeEventListener('close', onCloseHandler);
        this.isSocketConnected = false;
        resolve();
      };

      this.socket.addEventListener('close', onCloseHandler);
      this.socket.close(1000, 'Normal closure');
    });
  }

  /**
   * Permanently destroys the connection instance.
   */
  public destroy(): void {
    this.disconnect();
  }

  /**
   * Sends a message through the WebSocket.
   */
  public send(message: Omit<WebSocketMessage, 'timestamp'>): void {
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
      console.warn('Cannot send message: WebSocket not connected or not open.');
      return;
    }

    const fullMessage: WebSocketMessage = {
      ...message,
      timestamp: new Date().toISOString(),
    };

    try {
      this.socket.send(JSON.stringify(fullMessage));
    } catch (err) {
      console.error('Error sending WebSocket message:', err);
    }
  }
  
  /**
   * Gets the underlying WebSocket instance.
   */
  public getSocket(): WebSocket | null {
    return this.socket;
  }

  /**
   * Returns whether the socket is currently connected.
   */
  public isConnected(): boolean {
    return this.isSocketConnected;
  } 

}