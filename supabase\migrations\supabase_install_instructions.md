# Supabase CLI Installation Guide for Windows

## Option 1: Using PowerShell Installer (Recommended)

1. **Open PowerShell as Administrator**
   - Right-click on PowerShell from the Start menu
   - Select "Run as Administrator"

2. **Run the installer**
   ```powershell
   iwr https://cli.supabase.com/install.ps1 -useb | iex
   ```

3. **Verify installation**
   ```powershell
   supabase --version
   ```

## Option 2: Manual Binary Installation

1. **Download the latest binary**
   - Visit https://github.com/supabase/cli/releases/latest
   - Download the file named `supabase_windows_amd64.exe`
   
2. **Rename and move the file**
   - Rename the downloaded file to `supabase.exe`
   - Create a folder at `C:\Program Files\Supabase`
   - Move `supabase.exe` to this folder

3. **Add to PATH**
   - Open "System Properties" (Win+X > System > Advanced system settings)
   - Click "Environment Variables"
   - Under "System variables", find "Path" and click "Edit"
   - Add `C:\Program Files\Supabase`
   - Click OK on all dialogs

4. **Verify installation**
   - Open a new PowerShell window
   - Run `supabase --version`

## Option 3: Using NPM (Alternative)

1. **Install Node.js**
   - Download and install Node.js from https://nodejs.org/
   
2. **Install Supabase CLI**
   ```powershell
   npm install -g supabase
   ```

3. **Verify installation**
   ```powershell
   supabase --version
   ```

# Deploying Your PostgreSQL Function Without Supabase CLI

If you're unable to install the Supabase CLI, you can deploy your PostgreSQL function using these alternative methods:

## Method 1: Supabase Studio SQL Editor

1. **Log in to your Supabase project**
   - Go to https://app.supabase.com/
   - Select your project

2. **Open SQL Editor**
   - Click on "SQL Editor" in the left navigation

3. **Create a new query**
   - Click "New query"
   - Copy and paste the entire contents of your SQL function file (`supabase/migrations/20240716_create_bulk_order_function.sql`)
   - Run the query

## Method 2: Using the Supabase Management API

1. **Get your Supabase service role key**
   - In your Supabase dashboard, go to Project Settings > API
   - Copy the `service_role` key (keep this secure!)

2. **Make an API request to execute SQL**
   ```powershell
   $headers = @{
       "apikey" = "YOUR_SERVICE_ROLE_KEY"
       "Authorization" = "Bearer YOUR_SERVICE_ROLE_KEY"
       "Content-Type" = "application/json"
   }

   $body = @{
       query = Get-Content -Path "supabase/migrations/20240716_create_bulk_order_function.sql" -Raw
   } | ConvertTo-Json

   $response = Invoke-RestMethod -Uri "https://YOUR_PROJECT_REF.supabase.co/rest/v1/rpc/query" -Method POST -Headers $headers -Body $body
   ```

# Deploying Your Edge Function Without Supabase CLI

## Method 1: Using the Supabase Dashboard

1. **Log in to your Supabase project**
   - Go to https://app.supabase.com/
   - Select your project

2. **Navigate to Edge Functions**
   - Click on "Edge Functions" in the left navigation

3. **Create or update your function**
   - If creating new: Click "New Function", enter "bulk-order-create" as the name
   - If updating: Click on your existing "bulk-order-create" function
   - Copy and paste the contents of `supabase/functions/bulk-order-create/index.ts`
   - Save/Deploy the function

## Method 2: Using GitHub Integration

If your Supabase project is connected to GitHub:

1. **Commit and push your changes to your repository**
   ```powershell
   git add supabase/functions/bulk-order-create/index.ts
   git add supabase/migrations/20240716_create_bulk_order_function.sql
   git commit -m "Update bulk order creation function"
   git push
   ```

2. **Wait for automatic deployment**
   - Supabase will automatically detect changes and deploy your updated Edge Function

# Testing Your Functions

After deployment, you can test your functions using your frontend application or tools like Postman:

1. **Test PostgreSQL function**
   ```sql
   SELECT process_bulk_orders('[{"order_number":"TEST-123","platform":"amazon","channel":"amazon_fba","customer_email":"<EMAIL>","customer_name":"Test User","shipping_street":"123 Main St","shipping_city":"City","shipping_state":"State","shipping_zip_code":"12345","shipping_country":"USA","order_items":[{"product_identifier":"SKU123","quantity":2}],"_row_indices":[0]}]');
   ```

2. **Test Edge Function**
   ```
   curl -X POST "https://YOUR_PROJECT_REF.supabase.co/functions/v1/bulk-order-create" \
     -H "Authorization: Bearer YOUR_ANON_KEY" \
     -H "Content-Type: application/json" \
     -d '{"orders":[{"order_number":"TEST-123","platform":"amazon","channel":"amazon_fba","customer_email":"<EMAIL>","customer_name":"Test User","shipping_street":"123 Main St","shipping_city":"City","shipping_state":"State","shipping_zip_code":"12345","shipping_country":"USA","order_items":[{"product_identifier":"SKU123","quantity":2}],"_row_indices":[0]}]}'
   ``` 