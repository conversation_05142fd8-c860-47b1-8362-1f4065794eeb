import React, { useState, useEffect } from 'react';
import { getPerformanceSummary, clearProfilerData, logPerformanceSummary } from '@/shared/lib/utils/performance/profiler-utils';

interface ProfilerDashboardProps {
  pollInterval?: number;
  maxEntries?: number;
}

/**
 * Real-time React performance monitoring dashboard
 * Shows component render statistics and optimization status
 * Only visible in development mode
 */
const ProfilerDashboard: React.FC<ProfilerDashboardProps> = ({ 
  pollInterval = 2000,
  maxEntries = 10
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [performanceData, setPerformanceData] = useState<ReturnType<typeof getPerformanceSummary>>([]);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Poll for performance data
  useEffect(() => {
    if (!isVisible) return;

    const interval = setInterval(() => {
      const summary = getPerformanceSummary();
      setPerformanceData(summary);
      setLastUpdated(new Date());
    }, pollInterval);

    return () => clearInterval(interval);
  }, [isVisible, pollInterval]);

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  // Get optimization status based on render metrics
  const getOptimizationStatus = (stats: any) => {
    if (!stats) return 'neutral';

    const { avgActualDuration, updates } = stats;
    
    if (avgActualDuration < 3 && updates < 10) {
      return 'good'; // Well optimized
    } else if (avgActualDuration < 10 && updates < 20) {
      return 'warning'; // Could be better
    } else {
      return 'bad'; // Needs optimization
    }
  };

  // UI visibility toggle
  const toggleVisibility = () => {
    setIsVisible(!isVisible);
    if (!isVisible) {
      // Fetch data immediately when opening
      const summary = getPerformanceSummary();
      setPerformanceData(summary);
      setLastUpdated(new Date());
    }
  };

  // Handle clear data
  const handleClearData = () => {
    clearProfilerData();
    setPerformanceData([]);
    setLastUpdated(new Date());
  };

  // Log data to console
  const handleLogData = () => {
    logPerformanceSummary();
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'bg-green-500';
      case 'warning': return 'bg-yellow-500';
      case 'bad': return 'bg-red-500';
      default: return 'bg-gray-400';
    }
  };

  return (
    <>
      {/* Toggle Button */}
      <button 
        onClick={toggleVisibility}
        className="fixed bottom-4 right-4 z-50 p-2 bg-blue-600 text-white rounded shadow-lg hover:bg-blue-700"
      >
        {isVisible ? 'Hide Profiler' : 'Show Profiler'}
      </button>

      {/* Dashboard UI */}
      {isVisible && (
        <div className="fixed inset-y-0 right-0 w-96 bg-gray-900 text-white shadow-lg overflow-y-auto z-40 p-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-bold">Performance Monitor</h2>
            <div className="space-x-2">
              <button
                onClick={handleClearData}
                className="px-2 py-1 bg-red-600 text-xs rounded hover:bg-red-700"
              >
                Clear Data
              </button>
              <button
                onClick={handleLogData}
                className="px-2 py-1 bg-blue-600 text-xs rounded hover:bg-blue-700"
              >
                Log to Console
              </button>
            </div>
          </div>

          <div className="text-xs text-gray-400 mb-4">
            Last updated: {lastUpdated?.toLocaleTimeString() || 'Never'}
          </div>

          {performanceData.length === 0 ? (
            <div className="text-gray-500 italic">No performance data collected yet.</div>
          ) : (
            <div className="space-y-3">
              {performanceData
                .sort((a, b) => {
                  // Sort by most renders first
                  const aCount = a.stats?.totalRenders || 0;
                  const bCount = b.stats?.totalRenders || 0;
                  return bCount - aCount;
                })
                .slice(0, maxEntries)
                .map(({ componentName, stats }) => {
                  const status = getOptimizationStatus(stats);
                  const statusColor = getStatusColor(status);
                  
                  return (
                    <div key={componentName} className="bg-gray-800 p-3 rounded">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <div className={`w-3 h-3 rounded-full mr-2 ${statusColor}`}></div>
                          <h3 className="font-medium">{componentName}</h3>
                        </div>
                        <div className="text-xs bg-gray-700 px-2 py-1 rounded">
                          Renders: {stats?.totalRenders || 0}
                        </div>
                      </div>

                      {stats && (
                        <div className="mt-2 grid grid-cols-2 gap-2 text-xs">
                          <div className="text-gray-400">Mounts:</div>
                          <div>{stats.mounts}</div>
                          <div className="text-gray-400">Updates:</div>
                          <div>{stats.updates}</div>
                          <div className="text-gray-400">Avg Render Time:</div>
                          <div>{stats.avgActualDuration.toFixed(2)}ms</div>
                          <div className="text-gray-400">Ideal Time:</div>
                          <div>{stats.avgBaseDuration.toFixed(2)}ms</div>
                        </div>
                      )}
                    </div>
                  );
                })}
            </div>
          )}
          
          <div className="mt-4 pt-3 border-t border-gray-700 text-xs text-center text-gray-500">
            Monitoring React performance for memoization effectiveness
          </div>
        </div>
      )}
    </>
  );
};

export default ProfilerDashboard; 