import React, { memo } from 'react';

interface BulkActionsToolbarProps {
  selectedCount: number;
  onMarkAsResend: () => void;
  onMarkForRefund: () => void;
  onClearSelection: () => void;
}

const BulkActionsToolbar: React.FC<BulkActionsToolbarProps> = ({
  selectedCount,
  onMarkAsResend,
  onMarkForRefund,
  onClearSelection
}) => {
  // Memoized text to prevent recalculation
  const selectedText = `${selectedCount} item${selectedCount > 1 ? 's' : ''} selected`;

  if (selectedCount === 0) {
    return null;
  }

  return (
    <div className="bg-blue-50 border-b border-t border-blue-200 px-4 py-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <span className="text-sm font-medium text-blue-800">
            {selectedText}
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={onMarkAsResend}
            className="inline-flex items-center px-3 py-1.5 text-sm font-medium bg-white border border-blue-300 text-blue-700 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            <svg className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Mark as Resend
          </button>
          
          <button
            onClick={onMarkForRefund}
            className="inline-flex items-center px-3 py-1.5 text-sm font-medium bg-white border border-red-300 text-red-700 rounded-md hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
          >
            <svg className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
            Mark for Refund
          </button>
          
          <div className="h-4 w-px bg-blue-300"></div>
          
          <button 
            onClick={onClearSelection}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium focus:outline-none focus:underline"
          >
            Clear Selection
          </button>
        </div>
      </div>
      
      {/* Additional info bar */}
      <div className="mt-2 text-xs text-blue-600">
        Actions will be applied to all selected delivery items and their corresponding orders.
      </div>
    </div>
  );
};

export default memo(BulkActionsToolbar); 