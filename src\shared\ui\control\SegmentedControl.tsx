import React from 'react';

interface SegmentedControlOption {
  value: string;
  label: string;
}

interface SegmentedControlProps {
  options: SegmentedControlOption[];
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

export const SegmentedControl: React.FC<SegmentedControlProps> = ({
  options,
  value,
  onChange,
  className = ''
}) => {
  return (
    <div className={`inline-flex bg-gray-100 rounded-xl p-1 ${className}`}>
      {options.map((option) => (
        <button
          key={option.value}
          onClick={() => onChange(option.value)}
          className={`px-6 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${
            value === option.value
              ? 'bg-blue-600 text-white shadow-sm'
              : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
          }`}
        >
          {option.value === 'packing' && (
            <span className="mr-2">📦</span>
          )}
          {option.value === 'label' && (
            <span className="mr-2">🏷️</span>
          )}
          {option.label}
        </button>
      ))}
    </div>
  );
}; 