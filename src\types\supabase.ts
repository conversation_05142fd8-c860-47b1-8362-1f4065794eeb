export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      audit_logs: {
        Row: {
          action: string
          created_at: string | null
          id: string
          ip_address: unknown | null
          new_values: Json | null
          old_values: Json | null
          record_id: string
          table_name: string
          user_agent: string | null
          user_email: string | null
          user_id: string | null
        }
        Insert: {
          action: string
          created_at?: string | null
          id?: string
          ip_address?: unknown | null
          new_values?: Json | null
          old_values?: Json | null
          record_id: string
          table_name: string
          user_agent?: string | null
          user_email?: string | null
          user_id?: string | null
        }
        Update: {
          action?: string
          created_at?: string | null
          id?: string
          ip_address?: unknown | null
          new_values?: Json | null
          old_values?: Json | null
          record_id?: string
          table_name?: string
          user_agent?: string | null
          user_email?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "audit_logs_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      business_units: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          is_active: boolean | null
          name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      carriers: {
        Row: {
          code: string
          created_at: string | null
          id: string
          is_active: boolean | null
          name: string
          tracking_url_template: string | null
          updated_at: string | null
        }
        Insert: {
          code: string
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          tracking_url_template?: string | null
          updated_at?: string | null
        }
        Update: {
          code?: string
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          tracking_url_template?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      channels: {
        Row: {
          code: string
          created_at: string | null
          id: string
          is_active: boolean | null
          name: string
          platform_id: string
          updated_at: string | null
        }
        Insert: {
          code: string
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          platform_id: string
          updated_at?: string | null
        }
        Update: {
          code?: string
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          platform_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "channels_platform_id_fkey"
            columns: ["platform_id"]
            isOneToOne: false
            referencedRelation: "platforms"
            referencedColumns: ["id"]
          },
        ]
      }
      customers: {
        Row: {
          address_city: string | null
          address_country: string | null
          address_state: string | null
          address_street: string | null
          address_zip_code_1: string | null
          created_at: string | null
          customer_id: string
          email: string | null
          first_order_date: string | null
          id: string
          last_order_date: string | null
          name: string
          notes: string | null
          phone: string | null
          total_orders_count: number | null
          total_spent: number | null
          updated_at: string | null
        }
        Insert: {
          address_city?: string | null
          address_country?: string | null
          address_state?: string | null
          address_street?: string | null
          address_zip_code_1?: string | null
          created_at?: string | null
          customer_id?: string
          email?: string | null
          first_order_date?: string | null
          id?: string
          last_order_date?: string | null
          name: string
          notes?: string | null
          phone?: string | null
          total_orders_count?: number | null
          total_spent?: number | null
          updated_at?: string | null
        }
        Update: {
          address_city?: string | null
          address_country?: string | null
          address_state?: string | null
          address_street?: string | null
          address_zip_code_1?: string | null
          created_at?: string | null
          customer_id?: string
          email?: string | null
          first_order_date?: string | null
          id?: string
          last_order_date?: string | null
          name?: string
          notes?: string | null
          phone?: string | null
          total_orders_count?: number | null
          total_spent?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      deliveries: {
        Row: {
          actual_delivery: string | null
          carrier_id: string
          created_at: string | null
          destination_city: string | null
          destination_state: string | null
          destination_zip_code: string | null
          estimated_delivery: string | null
          has_exception: boolean | null
          id: string
          is_delayed: boolean | null
          last_update: string | null
          notes: string | null
          order_id: string
          package_count: number | null
          requires_action: boolean | null
          ship_date: string
          shipping_method: string | null
          status: Database["public"]["Enums"]["delivery_status_enum"]
          tracking_number: string
          updated_at: string | null
          weight: number | null
        }
        Insert: {
          actual_delivery?: string | null
          carrier_id: string
          created_at?: string | null
          destination_city?: string | null
          destination_state?: string | null
          destination_zip_code?: string | null
          estimated_delivery?: string | null
          has_exception?: boolean | null
          id?: string
          is_delayed?: boolean | null
          last_update?: string | null
          notes?: string | null
          order_id: string
          package_count?: number | null
          requires_action?: boolean | null
          ship_date: string
          shipping_method?: string | null
          status?: Database["public"]["Enums"]["delivery_status_enum"]
          tracking_number: string
          updated_at?: string | null
          weight?: number | null
        }
        Update: {
          actual_delivery?: string | null
          carrier_id?: string
          created_at?: string | null
          destination_city?: string | null
          destination_state?: string | null
          destination_zip_code?: string | null
          estimated_delivery?: string | null
          has_exception?: boolean | null
          id?: string
          is_delayed?: boolean | null
          last_update?: string | null
          notes?: string | null
          order_id?: string
          package_count?: number | null
          requires_action?: boolean | null
          ship_date?: string
          shipping_method?: string | null
          status?: Database["public"]["Enums"]["delivery_status_enum"]
          tracking_number?: string
          updated_at?: string | null
          weight?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "deliveries_carrier_id_fkey"
            columns: ["carrier_id"]
            isOneToOne: false
            referencedRelation: "carriers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deliveries_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: true
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deliveries_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: true
            referencedRelation: "orders_view"
            referencedColumns: ["id"]
          },
        ]
      }
      delivery_events: {
        Row: {
          created_at: string | null
          delivery_id: string
          description: string | null
          event_address: Json | null
          event_timestamp: string
          id: string
          is_current: boolean | null
          location: string | null
          status: Database["public"]["Enums"]["delivery_status_enum"]
        }
        Insert: {
          created_at?: string | null
          delivery_id: string
          description?: string | null
          event_address?: Json | null
          event_timestamp: string
          id?: string
          is_current?: boolean | null
          location?: string | null
          status: Database["public"]["Enums"]["delivery_status_enum"]
        }
        Update: {
          created_at?: string | null
          delivery_id?: string
          description?: string | null
          event_address?: Json | null
          event_timestamp?: string
          id?: string
          is_current?: boolean | null
          location?: string | null
          status?: Database["public"]["Enums"]["delivery_status_enum"]
        }
        Relationships: [
          {
            foreignKeyName: "delivery_events_delivery_id_fkey"
            columns: ["delivery_id"]
            isOneToOne: false
            referencedRelation: "deliveries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "delivery_events_delivery_id_fkey"
            columns: ["delivery_id"]
            isOneToOne: false
            referencedRelation: "deliveries_view"
            referencedColumns: ["id"]
          },
        ]
      }
      inventory: {
        Row: {
          available_stock: number | null
          created_at: string | null
          current_stock: number
          id: string
          last_counted_at: string | null
          last_restocked_at: string | null
          maximum_threshold: number | null
          minimum_threshold: number | null
          notes: string | null
          product_id: string
          reorder_point: number | null
          reserved_stock: number | null
          updated_at: string | null
        }
        Insert: {
          available_stock?: number | null
          created_at?: string | null
          current_stock?: number
          id?: string
          last_counted_at?: string | null
          last_restocked_at?: string | null
          maximum_threshold?: number | null
          minimum_threshold?: number | null
          notes?: string | null
          product_id: string
          reorder_point?: number | null
          reserved_stock?: number | null
          updated_at?: string | null
        }
        Update: {
          available_stock?: number | null
          created_at?: string | null
          current_stock?: number
          id?: string
          last_counted_at?: string | null
          last_restocked_at?: string | null
          maximum_threshold?: number | null
          minimum_threshold?: number | null
          notes?: string | null
          product_id?: string
          reorder_point?: number | null
          reserved_stock?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "inventory_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: true
            referencedRelation: "inventory_view"
            referencedColumns: ["product_id"]
          },
          {
            foreignKeyName: "inventory_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: true
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      inventory_alerts: {
        Row: {
          alert_type: string
          created_at: string | null
          id: string
          inventory_id: string
          is_resolved: boolean | null
          message: string
          resolved_at: string | null
          resolved_by: string | null
          severity: string | null
          updated_at: string | null
        }
        Insert: {
          alert_type: string
          created_at?: string | null
          id?: string
          inventory_id: string
          is_resolved?: boolean | null
          message: string
          resolved_at?: string | null
          resolved_by?: string | null
          severity?: string | null
          updated_at?: string | null
        }
        Update: {
          alert_type?: string
          created_at?: string | null
          id?: string
          inventory_id?: string
          is_resolved?: boolean | null
          message?: string
          resolved_at?: string | null
          resolved_by?: string | null
          severity?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "inventory_alerts_inventory_id_fkey"
            columns: ["inventory_id"]
            isOneToOne: false
            referencedRelation: "inventory"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_alerts_inventory_id_fkey"
            columns: ["inventory_id"]
            isOneToOne: false
            referencedRelation: "inventory_view"
            referencedColumns: ["inventory_id"]
          },
          {
            foreignKeyName: "inventory_alerts_resolved_by_fkey"
            columns: ["resolved_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      linked_orders: {
        Row: {
          child_order_id: string
          created_at: string | null
          id: string
          parent_order_id: string
          reason: string | null
          relationship_type: Database["public"]["Enums"]["order_relationship_enum"]
        }
        Insert: {
          child_order_id: string
          created_at?: string | null
          id?: string
          parent_order_id: string
          reason?: string | null
          relationship_type: Database["public"]["Enums"]["order_relationship_enum"]
        }
        Update: {
          child_order_id?: string
          created_at?: string | null
          id?: string
          parent_order_id?: string
          reason?: string | null
          relationship_type?: Database["public"]["Enums"]["order_relationship_enum"]
        }
        Relationships: [
          {
            foreignKeyName: "linked_orders_child_order_id_fkey"
            columns: ["child_order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "linked_orders_child_order_id_fkey"
            columns: ["child_order_id"]
            isOneToOne: false
            referencedRelation: "orders_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "linked_orders_parent_order_id_fkey"
            columns: ["parent_order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "linked_orders_parent_order_id_fkey"
            columns: ["parent_order_id"]
            isOneToOne: false
            referencedRelation: "orders_view"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          action_url: string | null
          category: string | null
          created_at: string | null
          expires_at: string | null
          id: string
          is_read: boolean | null
          message: string
          read_at: string | null
          title: string
          type: string | null
          user_id: string | null
        }
        Insert: {
          action_url?: string | null
          category?: string | null
          created_at?: string | null
          expires_at?: string | null
          id?: string
          is_read?: boolean | null
          message: string
          read_at?: string | null
          title: string
          type?: string | null
          user_id?: string | null
        }
        Update: {
          action_url?: string | null
          category?: string | null
          created_at?: string | null
          expires_at?: string | null
          id?: string
          is_read?: boolean | null
          message?: string
          read_at?: string | null
          title?: string
          type?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "notifications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      order_items: {
        Row: {
          created_at: string | null
          id: string
          order_id: string
          product_id: string
          product_name: string
          quantity: number
          sku: string
          total_price: number | null
          unit_cost_oi: number
          updated_at: string | null
          pack_size: number
          subtotal_item: number
        }
        Insert: {
          created_at?: string | null
          id?: string
          order_id: string
          product_id: string
          product_name: string
          quantity: number
          sku: string
          total_price?: number | null
          unit_cost_oi: number
          pack_size: number
          subtotal_item: number
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          order_id?: string
          product_id?: string
          product_name?: string
          quantity?: number
          sku?: string
          total_price?: number | null
          unit_cost_oi?: number
          pack_size?: number
          subtotal_item?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "order_items_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_items_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_items_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "inventory_view"
            referencedColumns: ["product_id"]
          },
          {
            foreignKeyName: "order_items_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      order_notes: {
        Row: {
          author_id: string | null
          author_name: string
          content: string
          created_at: string | null
          id: string
          is_important: boolean | null
          note_type: string | null
          order_id: string
          updated_at: string | null
        }
        Insert: {
          author_id?: string | null
          author_name: string
          content: string
          created_at?: string | null
          id?: string
          is_important?: boolean | null
          note_type?: string | null
          order_id: string
          updated_at?: string | null
        }
        Update: {
          author_id?: string | null
          author_name?: string
          content?: string
          created_at?: string | null
          id?: string
          is_important?: boolean | null
          note_type?: string | null
          order_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "order_notes_author_id_fkey"
            columns: ["author_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_notes_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_notes_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders_view"
            referencedColumns: ["id"]
          },
        ]
      }
      orders: {
        Row: {
          cancelled_at: string | null
          channel_id: string
          created_at: string | null
          customer_id: string
          delivered_at: string | null
          expected_delivery: string | null
          has_notes: boolean | null
          id: string
          is_problem: boolean | null
          is_resent: boolean | null
          is_urgent: boolean | null
          item_count: number
          order_date: string
          order_number: string
          packed_at: string | null
          platform_id: string
          quantity: number | null
          shipped_at: string | null
          shipping: number
          shipping_city: string | null
          shipping_country: string | null
          shipping_fee_paid: boolean | null
          shipping_method: string | null
          shipping_state: string | null
          shipping_street: string | null
          shipping_zip_code: string | null
          status: string
          subtotal: number
          tax: number
          total_amount: number | null
          tracking_number: string | null
          updated_at: string | null
        }
        Insert: {
          cancelled_at?: string | null
          channel_id: string
          created_at?: string | null
          customer_id: string
          delivered_at?: string | null
          expected_delivery?: string | null
          has_notes?: boolean | null
          id?: string
          is_problem?: boolean | null
          is_resent?: boolean | null
          is_urgent?: boolean | null
          item_count?: number
          order_date?: string
          order_number?: string
          packed_at?: string | null
          platform_id: string
          quantity?: number | null
          shipped_at?: string | null
          shipping?: number
          shipping_city?: string | null
          shipping_country?: string | null
          shipping_fee_paid?: boolean | null
          shipping_method?: string | null
          shipping_state?: string | null
          shipping_street?: string | null
          shipping_zip_code?: string | null
          status?: string
          subtotal?: number
          tax?: number
          total_amount?: number | null
          tracking_number?: string | null
          updated_at?: string | null
        }
        Update: {
          cancelled_at?: string | null
          channel_id?: string
          created_at?: string | null
          customer_id?: string
          delivered_at?: string | null
          expected_delivery?: string | null
          has_notes?: boolean | null
          id?: string
          is_problem?: boolean | null
          is_resent?: boolean | null
          is_urgent?: boolean | null
          item_count?: number
          order_date?: string
          order_number?: string
          packed_at?: string | null
          platform_id?: string
          quantity?: number | null
          shipped_at?: string | null
          shipping?: number
          shipping_city?: string | null
          shipping_country?: string | null
          shipping_fee_paid?: boolean | null
          shipping_method?: string | null
          shipping_state?: string | null
          shipping_street?: string | null
          shipping_zip_code?: string | null
          status?: string
          subtotal?: number
          tax?: number
          total_amount?: number | null
          tracking_number?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "orders_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "orders_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "orders_platform_id_fkey"
            columns: ["platform_id"]
            isOneToOne: false
            referencedRelation: "platforms"
            referencedColumns: ["id"]
          },
        ]
      }
      platforms: {
        Row: {
          created_at: string | null
          id: string
          is_active: boolean | null
          key: string
          name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          key: string
          name: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          key?: string
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      product_identifiers: {
        Row: {
          code_name: string | null
          created_at: string | null
          id: string
          pack_size: number
          platform_id: string
          platform_identifier: string
          product_id: string
        }
        Insert: {
          code_name?: string | null
          created_at?: string | null
          id?: string
          pack_size?: number
          platform_id: string
          platform_identifier: string
          product_id: string
        }
        Update: {
          code_name?: string | null
          created_at?: string | null
          id?: string
          pack_size?: number
          platform_id?: string
          platform_identifier?: string
          product_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "product_identifiers_platform_id_fkey"
            columns: ["platform_id"]
            isOneToOne: false
            referencedRelation: "platforms"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_identifiers_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "inventory_view"
            referencedColumns: ["product_id"]
          },
          {
            foreignKeyName: "product_identifiers_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      products: {
        Row: {
          business_unit_id: string | null
          category: string | null
          created_at: string | null
          description: string | null
          dimensions_height: number | null
          dimensions_length: number | null
          dimensions_width: number | null
          id: string
          name: string
          product_type: string | null
          sku: string
          status: Database["public"]["Enums"]["product_status_enum"] | null
          unit_cost: number | null
          unit_price: number | null
          updated_at: string | null
          weight: number | null
        }
        Insert: {
          business_unit_id?: string | null
          category?: string | null
          created_at?: string | null
          description?: string | null
          dimensions_height?: number | null
          dimensions_length?: number | null
          dimensions_width?: number | null
          id?: string
          name: string
          product_type?: string | null
          sku: string
          status?: Database["public"]["Enums"]["product_status_enum"] | null
          unit_cost?: number | null
          unit_price?: number | null
          updated_at?: string | null
          weight?: number | null
        }
        Update: {
          business_unit_id?: string | null
          category?: string | null
          created_at?: string | null
          description?: string | null
          dimensions_height?: number | null
          dimensions_length?: number | null
          dimensions_width?: number | null
          id?: string
          name?: string
          product_type?: string | null
          sku?: string
          status?: Database["public"]["Enums"]["product_status_enum"] | null
          unit_cost?: number | null
          unit_price?: number | null
          updated_at?: string | null
          weight?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "products_business_unit_id_fkey"
            columns: ["business_unit_id"]
            isOneToOne: false
            referencedRelation: "business_units"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          business_unit_id: string | null
          created_at: string | null
          email: string
          full_name: string
          id: string
          is_active: boolean | null
          last_login_at: string | null
          role: Database["public"]["Enums"]["user_role"] | null
          updated_at: string | null
        }
        Insert: {
          business_unit_id?: string | null
          created_at?: string | null
          email: string
          full_name: string
          id: string
          is_active?: boolean | null
          last_login_at?: string | null
          role?: Database["public"]["Enums"]["user_role"] | null
          updated_at?: string | null
        }
        Update: {
          business_unit_id?: string | null
          created_at?: string | null
          email?: string
          full_name?: string
          id?: string
          is_active?: boolean | null
          last_login_at?: string | null
          role?: Database["public"]["Enums"]["user_role"] | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "users_business_unit_id_fkey"
            columns: ["business_unit_id"]
            isOneToOne: false
            referencedRelation: "business_units"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      deliveries_view: {
        Row: {
          actual_delivery: string | null
          carrier_code: string | null
          carrier_id: string | null
          carrier_name: string | null
          customer_name: string | null
          estimated_delivery: string | null
          id: string | null
          last_update: string | null
          order_id: string | null
          order_number: string | null
          requires_action: boolean | null
          ship_date: string | null
          status: Database["public"]["Enums"]["delivery_status_enum"] | null
          tracking_number: string | null
        }
        Relationships: [
          {
            foreignKeyName: "deliveries_carrier_id_fkey"
            columns: ["carrier_id"]
            isOneToOne: false
            referencedRelation: "carriers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deliveries_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: true
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deliveries_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: true
            referencedRelation: "orders_view"
            referencedColumns: ["id"]
          },
        ]
      }
      inventory_view: {
        Row: {
          available_stock: number | null
          current_stock: number | null
          inventory_id: string | null
          minimum_threshold: number | null
          name: string | null
          needs_reorder: boolean | null
          product_id: string | null
          product_type: string | null
          reserved_stock: number | null
          sku: string | null
          status: Database["public"]["Enums"]["product_status_enum"] | null
        }
        Relationships: []
      }
      orders_view: {
        Row: {
          channel_code: string | null
          customer_name: string | null
          has_notes: boolean | null
          id: string | null
          is_problem: boolean | null
          is_resent: boolean | null
          is_urgent: boolean | null
          item_count: number | null
          order_date: string | null
          order_number: string | null
          platform_key: string | null
          quantity: number | null
          status: string | null
          total_amount: number | null
          tracking_number: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      assign_tracking_and_create_delivery: {
        Args: {
          p_order_id: string
          p_tracking_number: string
          p_carrier_id: string
        }
        Returns: string
      }
      cancel_order_and_restock: {
        Args: { p_order_id: string }
        Returns: undefined
      }
      create_order_transaction: {
        Args: {
          customer_email: string
          customer_name: string
          p_order_number: string
          p_platform_id: string
          p_channel_id: string
          p_order_quantity: number
          order_items: Json
          p_shipping_street: string
          p_shipping_city: string
          p_shipping_state: string
          p_shipping_zip_code: string
        }
        Returns: {
          order_id: string
          order_number: string
        }[]
      }
      create_product_with_inventory: {
        Args: {
          p_sku: string
          p_name: string
          p_description: string
          p_category: string
          p_product_type: string
          p_unit_cost: number
          p_unit_price: number
          p_initial_stock: number
        }
        Returns: string
      }
      update_delivery_from_webhook_payload: {
        Args: { p_tracking_number: string; p_payload: Json }
        Returns: undefined
      }
    }
    Enums: {
      delivery_status_enum:
        | "label_created"
        | "in_transit"
        | "out_for_delivery"
        | "delivered"
        | "delayed"
        | "lost"
        | "exception"
        | "returned"
        | "picked_up"
      order_relationship_enum:
        | "original"
        | "replacement"
        | "refund"
        | "exchange"
      order_status_enum:
        | "open"
        | "on_hold"
        | "packed"
        | "shipped"
        | "completed"
        | "cancelled"
        | "refund"
        | "reshipment_scheduled"
      product_status_enum: "active" | "new" | "issue" | "discontinued"
      user_role: "master" | "admin" | "staff"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      delivery_status_enum: [
        "label_created",
        "in_transit",
        "out_for_delivery",
        "delivered",
        "delayed",
        "lost",
        "exception",
        "returned",
        "picked_up",
      ],
      order_relationship_enum: [
        "original",
        "replacement",
        "refund",
        "exchange",
      ],
      order_status_enum: [
        "open",
        "on_hold",
        "packed",
        "shipped",
        "completed",
        "cancelled",
        "refund",
        "reshipment_scheduled",
      ],
      product_status_enum: ["active", "new", "issue", "discontinued"],
      user_role: ["master", "admin", "staff"],
    },
  },
} as const
