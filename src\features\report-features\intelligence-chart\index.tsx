// /src/features/intelligence-chart/IntelligenceChart.tsx
// This is the corrected and improved version of your component.

import React, { useRef, useMemo } from 'react';
import { Chart } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  LineController,
  BarElement,
  Title,
  Tooltip,
  Legend,
  TimeScale, 
  ChartOptions,
  DefaultDataPoint,
  Filler,
} from 'chart.js';
import 'chartjs-adapter-date-fns'
import { INTELLIGENCE_CHART_DATA } from '@/shared/lib/mock-data/dashboard';
import { IntelligenceChartDataPoint } from '@/types/index';


// Register all the necessary Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  LineController,
  BarElement,
  Title,
  Tooltip,
  Legend,
  TimeScale,
  Filler
);

interface IntelligenceChartProps {
  data?: IntelligenceChartDataPoint[];
}

// This is our main component
const IntelligenceChart: React.FC<IntelligenceChartProps> = ({ 
  data = INTELLIGENCE_CHART_DATA 
}) => {
  // Adjust the type of the ref
  const chartRef = useRef<ChartJS<'bar' | 'line', DefaultDataPoint<'bar' | 'line'>, unknown>>(null);

  // Memoize expensive data transformations to avoid recalculation on every render
  const chartData = useMemo(() => {
    // Convert string dates from mock data into real Date objects for the time scale
    const chartLabels = data.map(item => new Date(item.date));

    // --- DATA CONFIGURATION ---
    // Here we define the data and apply the professional styling
    return {
      labels: chartLabels,
      datasets: [
        {
          type: 'line' as const,
          label: 'Revenue',
          data: data.map(item => item.revenue),
          borderColor: '#2563eb', // Updated color
          backgroundColor: (context: any) => {
              const chart = context.chart;
              const {ctx, chartArea} = chart;
              if (!chartArea) return null;
              const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
              gradient.addColorStop(0, 'rgba(59, 130, 246, 0)');
              gradient.addColorStop(1, 'rgba(59, 130, 246, 0.4)'); // Updated gradient
              return gradient;
          },
          borderWidth: 2.5,
          pointRadius: 0,
          pointHoverRadius: 6,
          pointHoverBackgroundColor: '#2563eb',
          pointHoverBorderColor: '#ffffff',
          pointHoverBorderWidth: 2,
          yAxisID: 'yRevenue',
          tension: 0.4,
          fill: true,
        },
        {
          type: 'bar' as const,
          label: 'Orders',
          data: data.map(item => item.orders),
          backgroundColor: '#e5e7eb', // Updated color for a neutral look
          borderColor: '#e5e7eb',
          yAxisID: 'yOrders',
          borderRadius: 4,
        },
      ],
    };
  }, [data]);

  // Memoize chart options since they're static and don't change
  const options = useMemo((): ChartOptions<('line' | 'bar')> => ({
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: '#111827',
        titleFont: { weight: 'bold' },
        bodyFont: { size: 14 },
        padding: 12,
        boxPadding: 4,
        cornerRadius: 8,
      },
    },
    scales: {
      x: {
        type: 'time',
        time: {
          unit: 'day',
        },
        grid: {
          display: false,
        },
        ticks: {
          autoSkip: true,
          maxTicksLimit: 10,
          maxRotation: 0,
          source: 'auto' as const,
        }
      },
      yRevenue: {
        type: 'linear' as const,
        display: true,
        position: 'left' as const,
        ticks: {
          callback: function(value: any) {
            return `$${(Number(value) / 1000)}k`;
          },
        },
      },
      yOrders: {
        type: 'linear' as const,
        display: true,
        position: 'right' as const,
        grid: {
          drawOnChartArea: false,
        },
        beginAtZero: true,
        ticks: {
          display: true,
          callback: function(value: any) {
            return Number(value).toFixed(0);
          }
        },
      },
    },
  }), []);

  return (
    // This is the parent component that holds the chart and the custom legend
    <div className="bg-white p-6 rounded-xl shadow-sm">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-semibold text-gray-800">Intelligence Chart</h3>
        {/* Custom legend */}
        <div className="flex space-x-4">
            <div className="flex items-center text-sm text-gray-600">
                <div className="w-2.5 h-2.5 rounded-full bg-blue-600 mr-2"></div>Revenue
            </div>
            <div className="flex items-center text-sm text-gray-600">
                <div className="w-2.5 h-2.5 rounded-full bg-gray-300 mr-2"></div>Orders
            </div>
        </div>
      </div>
      <div className="h-96">
        {/* For a mixed chart, it's best to set the primary type to 'bar' here */}
        <Chart ref={chartRef} type='bar' data={chartData} options={options} />
      </div>
    </div>
  );
};


export default IntelligenceChart;