/// <reference types="https://deno.land/x/deno_types/index.d.ts" />

// @ts-nocheck
// deno-lint-ignore-file

import { serve } from "std/http/server.ts"
import { createClient } from "supabase"
import { corsHeaders } from "../_shared/cors.ts"

interface StockUpdateItem {
  productId: string
  quantity: number
  type: 'increase' | 'decrease' | 'set'
  reason: string
  reasonCategory: string
  notes?: string
}

interface RequestBody {
  items: StockUpdateItem[]
  userId: string
}

// Helper to safely get error messages
type ErrorWithMessage = { message: string };
function isErrorWithMessage(error: unknown): error is ErrorWithMessage {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof (error as Record<string, unknown>).message === 'string'
  );
}

function getErrorMessage(error: unknown): string {
  if (isErrorWithMessage(error)) return error.message;
  return String(error);
}

// Define request type
interface HttpRequest {
  method: string;
  headers: Headers;
  json(): Promise<any>;
}

serve(async (req: HttpRequest) => {
  console.log('Edge Function: Received request'); // Log start of request processing
  
  // Handle preflight OPTIONS request for CORS. The browser sends this before
  // the actual POST request to ensure the server allows the request.
  if (req.method === 'OPTIONS') {
    console.log('Edge Function: Handling OPTIONS request');
    return new Response('ok', {
      headers: corsHeaders
    });
  }

  try {
    console.log('Edge Function: Processing request body');
    const { items, userId } = await req.json() as RequestBody
    
    if (!items || !Array.isArray(items) || items.length === 0) {
      console.log('Edge Function: Invalid items array');
      return new Response(
        JSON.stringify({ success: false, error: 'No items provided for update' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log(`Edge Function: Processing ${items.length} items`);
    
    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      { global: { headers: { Authorization: req.headers.get('Authorization') || '' } } }
    )
    
    // Process all items
    const results = [];
    const errors = [];
    let processedCount = 0;
    let failedCount = 0;
    
    // Process items sequentially to avoid rate limits
    for (const item of items) {
      try {
        console.log(`Edge Function: Processing item for product ${item.productId}`);
        
        // Get inventory record for product
        const { data: inventory, error: inventoryError } = await supabaseClient
          .from('inventory')
          .select('id, current_stock')
          .eq('product_id', item.productId)
          .single();
          
        if (inventoryError) {
          console.error(`Error fetching inventory for ${item.productId}:`, inventoryError);
          errors.push({ productId: item.productId, error: inventoryError.message });
          failedCount++;
          continue;
        }
        
        if (!inventory) {
          console.error(`No inventory found for ${item.productId}`);
          errors.push({ productId: item.productId, error: 'No inventory found' });
          failedCount++;
          continue;
        }
        
        // Calculate new stock value
        let newStock = inventory.current_stock;
        switch (item.type) {
          case 'increase':
            newStock += item.quantity;
            break;
          case 'decrease':
            newStock = Math.max(0, newStock - item.quantity); // Prevent negative stock
            break;
          case 'set':
            newStock = Math.max(0, item.quantity); // Ensure non-negative
            break;
          default:
            errors.push({ productId: item.productId, error: 'Invalid adjustment type' });
            failedCount++;
            continue;
        }
        
        // Update inventory record
        const { error: updateError } = await supabaseClient
          .from('inventory')
          .update({ 
            current_stock: newStock,
            updated_at: new Date().toISOString()
          })
          .eq('id', inventory.id);
          
        if (updateError) {
          console.error(`Error updating inventory for ${item.productId}:`, updateError);
          errors.push({ productId: item.productId, error: updateError.message });
          failedCount++;
          continue;
        }
        
        // Record movement
        const { error: movementError } = await supabaseClient
          .from('inventory_movements')
          .insert({
            inventory_id: inventory.id,
            product_id: item.productId,
            movement_type: item.type,
            quantity: item.quantity,
            previous_stock: inventory.current_stock,
            new_stock: newStock,
            reason: item.reason,
            reason_category: item.reasonCategory,
            notes: item.notes,
            user_id: userId
          });
          
        if (movementError) {
          console.error(`Error recording movement for ${item.productId}:`, movementError);
          errors.push({ productId: item.productId, error: movementError.message });
          // We don't increment failedCount here as the stock was already updated
        }
        
        // Add to results
        results.push({
          productId: item.productId,
          previousStock: inventory.current_stock,
          newStock: newStock,
          success: true
        });
        processedCount++;
        
      } catch (err) {
        console.error(`Error processing item ${item.productId}:`, err);
        errors.push({ 
          productId: item.productId, 
          error: getErrorMessage(err)
        });
        failedCount++;
      }
    }
    
    console.log(`Edge Function: Completed. Processed: ${processedCount}, Failed: ${failedCount}`);
    
    // Return response with results
    return new Response(
      JSON.stringify({
        success: failedCount === 0,
        processedCount,
        failedCount,
        results,
        errors: errors.length > 0 ? errors : undefined
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (err) {
    console.error('Edge Function: Fatal error:', err);
    const errorMessage = getErrorMessage(err);
    return new Response(
      JSON.stringify({ success: false, error: errorMessage }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
}) 