import * as React from 'react';
import { useState, useEffect, useMemo } from 'react';
import Modal from '@/shared/ui/overlay/Modal';
import { FulfillOrderItem, Carrier } from '@/types';
import { getCarriers } from '@/shared/lib/services/carrier/carrier-service';
import { carrierParserService } from '@/shared/lib/services/carrier/carrier-parser-service';
import { bulkAssignTrackingNumbers } from '@/shared/lib/services/fulfill/fulfill-service';

interface AssignTrackingModalProps {
  isOpen: boolean;
  onClose: () => void;
  orders: FulfillOrderItem[];
  onAssignment: () => void; // Callback for when assignment is successful
}

export const AssignTrackingModal: React.FC<AssignTrackingModalProps> = ({
  isOpen,
  onClose,
  orders,
  onAssignment,
}) => {
  const [trackingNumber, setTrackingNumber] = useState('');
  const [detectedCarrier, setDetectedCarrier] = useState<string | null>(null);
  const [carriers, setCarriers] = useState<Carrier[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Task 3.5: Optimize carrier lookup with Map for O(1) access instead of O(n) array.find()
  const carriersByName = useMemo(() => {
    const map = new Map<string, Carrier>();
    carriers.forEach(carrier => {
      map.set(carrier.name.toLowerCase(), carrier);
    });
    return map;
  }, [carriers]);

  // Reset state when modal is opened
  useEffect(() => {
    if (isOpen) {
      setTrackingNumber('');
      setDetectedCarrier(null);
      setError(null);
      
      const fetchCarriers = async () => {
        setError(null);
        try {
          const fetchedCarriers = await getCarriers();
          if (fetchedCarriers.length > 0) {
            setCarriers(fetchedCarriers);
          } else {
            setError('Could not load carrier information.');
          }
        } catch (err) {
          setError('Failed to fetch carriers.');
          console.error(err);
        }
      };

      fetchCarriers();
    }
  }, [isOpen]);

  const handleTrackingNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setTrackingNumber(value);

    // Use the carrier parser service for detection
    const detectedCarrierName = carrierParserService.detectCarrierFromTracking(value);
    setDetectedCarrier(detectedCarrierName ? detectedCarrierName.toUpperCase() : null);
  };

  const handleAssign = async () => {
    setError(null);

    if (!detectedCarrier) {
      setError('Invalid tracking number. Please enter a valid 12 or 22-digit number.');
      return;
    }

    if (!trackingNumber || trackingNumber.trim() === '') {
      setError('Please enter a tracking number.');
      return;
    }

    // Task 3.5: Use Map.get() for O(1) carrier lookup instead of array.find() O(n)
    const carrier = carriersByName.get(detectedCarrier.toLowerCase());

    if (!carrier) {
      setError(`Could not find a carrier ID for ${detectedCarrier}. Please check carrier configuration.`);
      return;
    }

    const carrierId = carrier.id;
    
    // Ensure all required fields are present
    const payload = orders.map(order => ({
      order_id: order.id,
      tracking_number: trackingNumber.trim(),
      carrier_id: carrierId,
      new_status: 'ready_to_ship',
    }));

    // Validate payload before sending
    if (payload.length === 0) {
      setError('No orders selected for tracking assignment.');
      return;
    }

    setIsLoading(true);
    try {
      console.log('Sending payload:', payload); // Debug log
      const response = await bulkAssignTrackingNumbers(payload);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to assign tracking numbers.');
      }
      
      onAssignment(); // Call success callback
      onClose();
    } catch (err) {
      const message = err instanceof Error ? err.message : 'An unknown error occurred.';
      setError(`Assignment failed: ${message}`);
      console.error('Tracking assignment error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const modalFooter = (
    <div className="flex justify-end">
      <button
        onClick={handleAssign}
        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
        disabled={!trackingNumber || isLoading}
      >
        {isLoading ? 'Assigning...' : 'Assign'}
      </button>
    </div>
  );

  if (!isOpen) {
    return null;
  }

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose} 
      title="Assign Tracking Number"
      footer={modalFooter}
      showDefaultFooter={false}
    >
      <div className="p-6">
        <p className="text-sm text-gray-600 mb-4">
          Enter the tracking number for the {orders.length} order(s) for buyer: <strong>{orders[0]?.buyer.name}</strong>
        </p>
        
        {error && (
          <div className="bg-red-50 p-3 rounded-md mb-4">
            <p className="text-sm text-red-700">{error}</p>
          </div>
        )}

        {orders.length > 0 && (
          <div className="mb-4 bg-gray-50 p-3 rounded-md text-sm">
            <p className="font-medium text-gray-700 mb-2">Order Information:</p>
            <ul className="space-y-1 max-h-40 overflow-y-auto">
              {orders.map(order => (
                <li key={order.id} className="flex justify-between">
                  <span className="text-gray-600">ID: <span className="font-mono">{order.id.substring(0, 8)}...</span></span>
                  <span className="text-gray-600">Number: <span className="font-medium">{order.order_number}</span></span>
                </li>
              ))}
            </ul>
          </div>
        )}

        <fieldset disabled={isLoading}>
          <div className="mt-4">
            <label htmlFor="tracking-number" className="block text-sm font-medium text-gray-700">
              Tracking Number
            </label>
            <input
              type="text"
              id="tracking-number"
              value={trackingNumber}
              onChange={handleTrackingNumberChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="Enter 12 or 22-digit tracking number"
              autoFocus
            />
            {detectedCarrier && (
              <p className="mt-2 text-sm text-green-600">
                Detected Carrier: <strong>{detectedCarrier}</strong>
              </p>
            )}
          </div>
        </fieldset>
      </div>
    </Modal>
  );
};

export default AssignTrackingModal; 