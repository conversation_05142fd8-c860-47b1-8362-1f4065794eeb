import React, { memo, useMemo } from 'react';
import { AllOrdersDetail, AllOrdersStatus } from '@/types';
import { Icon } from '@/shared/ui/core/Icon';

interface OrderBasicInfoProps {
  order: AllOrdersDetail;
  getStatusBadge: (status: AllOrdersStatus) => React.ReactNode;
  formatCurrency: (amount: number) => string;
  formatDate: (dateString: string) => string;
}

const OrderBasicInfo: React.FC<OrderBasicInfoProps> = ({
  order,
  getStatusBadge,
  formatCurrency,
  formatDate
}) => {
  // PERFORMANCE: Memoize order flags computation
  const orderFlags = useMemo(() => {
    const flags = [];
    if (order.isUrgent) {
      flags.push({ type: 'urgent', label: '🚨 Urgent', className: 'bg-red-100 text-red-800' });
    }
    if (order.isProblem) {
      flags.push({ type: 'problem', label: '⚠️ Problem', className: 'bg-yellow-100 text-yellow-800' });
    }
    if (order.isResent) {
      flags.push({ type: 'resent', label: '🔄 Resent', className: 'bg-blue-100 text-blue-800' });
    }
    if (order.hasNotes) {
      flags.push({ type: 'notes', label: '📝 Has Notes', className: 'bg-gray-100 text-gray-800' });
    }
    return flags;
  }, [order.isUrgent, order.isProblem, order.isResent, order.hasNotes]);

  return (
    <section>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <Icon 
            platform={order.platform} 
            className="h-8 w-8"
            variant="circle"
          />
          <div>
            <h3 className="text-xl font-semibold text-gray-900">{order.orderNumber}</h3>
            <p className="text-sm text-gray-500">{order.channel}</p>
          </div>
        </div>
        {getStatusBadge(order.status)}
      </div>

      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span className="font-medium text-gray-500">Order Date:</span>
          <p className="text-gray-900">{formatDate(order.orderDate)}</p>
        </div>
        <div>
          <span className="font-medium text-gray-500">Total Amount:</span>
          <p className="text-gray-900 font-semibold">{formatCurrency(order.totalAmount)}</p>
        </div>
        {order.trackingNumber && (
          <div className="col-span-2">
            <span className="font-medium text-gray-500">Tracking Number:</span>
            <p className="text-gray-900 font-mono">{order.trackingNumber}</p>
          </div>
        )}
      </div>

      {/* Order Flags */}
      {orderFlags.length > 0 && (
        <div className="mt-4 flex flex-wrap gap-2">
          {orderFlags.map((flag) => (
            <span 
              key={flag.type}
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${flag.className}`}
            >
              {flag.label}
            </span>
          ))}
        </div>
      )}
    </section>
  );
};

// PERFORMANCE: Aggressive memoization with deep comparison for order props
export default memo(OrderBasicInfo, (prevProps, nextProps) => {
  const prevOrder = prevProps.order;
  const nextOrder = nextProps.order;
  
  return (
    prevOrder.id === nextOrder.id &&
    prevOrder.orderNumber === nextOrder.orderNumber &&
    prevOrder.channel === nextOrder.channel &&
    prevOrder.platform === nextOrder.platform &&
    prevOrder.status === nextOrder.status &&
    prevOrder.orderDate === nextOrder.orderDate &&
    prevOrder.totalAmount === nextOrder.totalAmount &&
    prevOrder.trackingNumber === nextOrder.trackingNumber &&
    prevOrder.isUrgent === nextOrder.isUrgent &&
    prevOrder.isProblem === nextOrder.isProblem &&
    prevOrder.isResent === nextOrder.isResent &&
    prevOrder.hasNotes === nextOrder.hasNotes &&
    prevProps.getStatusBadge === nextProps.getStatusBadge &&
    prevProps.formatCurrency === nextProps.formatCurrency &&
    prevProps.formatDate === nextProps.formatDate
  );
}); 