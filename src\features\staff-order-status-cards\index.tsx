import React, { useState, useEffect } from 'react';
import { fetchOrders } from '@/shared/lib/services/order/live-order-service';
import LoadingSpinner from '@/shared/ui/feedback/LoadingSpinner';

interface OrderStatusCardsProps {
  onCardClick: (status: string) => void;
}

interface StatusCount {
  open: number;
  shipped: number;
  completed: number;
  packed: number;
}

const OrderStatusCards: React.FC<OrderStatusCardsProps> = ({ onCardClick }) => {
  const [statusCounts, setStatusCounts] = useState<StatusCount>({
    open: 0,
    shipped: 0,
    completed: 0,
    packed: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOrderCounts = async () => {
      try {
        setLoading(true);
        
        // Fetch counts for each status
        const [openOrders, shippedOrders, completedOrders, packedOrders] = await Promise.all([
          fetchOrders({ page: 1, limit: 1 }, { status: ['open'] }),
          fetchOrders({ page: 1, limit: 1 }, { status: ['shipped'] }),
          fetchOrders({ page: 1, limit: 1 }, { status: ['completed'] }),
          fetchOrders({ page: 1, limit: 1 }, { status: ['packed'] }),
        ]);

        setStatusCounts({
          open: openOrders.totalCount,
          shipped: shippedOrders.totalCount,
          completed: completedOrders.totalCount,
          packed: packedOrders.totalCount,
        });
      } catch (err) {
        setError('Failed to load order counts');
        console.error('Error fetching order counts:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchOrderCounts();
  }, []);

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, index) => (
          <div key={index} className="bg-white p-6 rounded-lg shadow border border-gray-200">
            <div className="flex items-center justify-center h-16">
              <LoadingSpinner />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-600 text-sm">{error}</p>
      </div>
    );
  }

  const statusCards = [
    {
      status: 'Open',
      count: statusCounts.open,
      color: 'bg-blue-500',
      hoverColor: 'hover:bg-blue-600',
      iconColor: 'text-blue-100',
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
        </svg>
      ),
    },
    {
      status: 'Shipped',
      count: statusCounts.shipped,
      color: 'bg-yellow-500',
      hoverColor: 'hover:bg-yellow-600',
      iconColor: 'text-yellow-100',
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
          <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
          <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-.293-.707L15 4.586A1 1 0 0014.414 4H14v3z" />
        </svg>
      ),
    },
    {
      status: 'Completed',
      count: statusCounts.completed,
      color: 'bg-green-500',
      hoverColor: 'hover:bg-green-600',
      iconColor: 'text-green-100',
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
        </svg>
      ),
    },
    {
      status: 'Packed',
      count: statusCounts.packed,
      color: 'bg-purple-500',
      hoverColor: 'hover:bg-purple-600',
      iconColor: 'text-purple-100',
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
          <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
        </svg>
      ),
    },
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      {statusCards.map((card) => (
        <div
          key={card.status}
          onClick={() => onCardClick(card.status)}
          className={`${card.color} ${card.hoverColor} cursor-pointer transform transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl rounded-lg p-6 text-white`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-lg font-semibold opacity-90">{card.status}</p>
              <p className="text-3xl font-bold">{card.count.toLocaleString()}</p>
            </div>
            <div className={`${card.iconColor}`}>
              {card.icon}
            </div>
          </div>
          <div className="mt-4 text-sm opacity-80">
            Click to view {card.status.toLowerCase()} orders
          </div>
        </div>
      ))}
    </div>
  );
};

export default OrderStatusCards; 