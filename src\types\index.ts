import React from 'react';
// Import Supabase types for database alignment
import type { Database } from './supabase';

// Database type aliases for easier use
export type DbUser = Database['public']['Tables']['users']['Row'];
export type DbOrder = Database['public']['Tables']['orders']['Row'];
export type DbOrderItem = Database['public']['Tables']['order_items']['Row'];
export type DbOrderNote = Database['public']['Tables']['order_notes']['Row'];
export type DbCustomer = Database['public']['Tables']['customers']['Row'];
export type DbDelivery = Database['public']['Tables']['deliveries']['Row'];
export type DbDeliveryEvent = Database['public']['Tables']['delivery_events']['Row'];
export type DbProduct = Database['public']['Tables']['products']['Row'];
export type DbInventory = Database['public']['Tables']['inventory']['Row'];
export type DbPlatform = Database['public']['Tables']['platforms']['Row'];
export type DbChannel = Database['public']['Tables']['channels']['Row'];
export type DbBusinessUnit = Database['public']['Tables']['business_units']['Row'];
export type DbCarrier = Database['public']['Tables']['carriers']['Row'];
export type DbProductIdentifier = Database['public']['Tables']['product_identifiers']['Row'];

// Database enums
export type UserRole = Database['public']['Enums']['user_role'];
export type DeliveryStatusEnum = Database['public']['Enums']['delivery_status_enum'];
export type OrderStatusEnum = Database['public']['Enums']['order_status_enum'];
export type ProductStatusEnum = Database['public']['Enums']['product_status_enum'];
export type OrderRelationshipEnum = Database['public']['Enums']['order_relationship_enum'];

// Extended type to include ready_to_ship status
export type ExtendedOrderStatusEnum = OrderStatusEnum | 'ready_to_ship';

export interface NavItemType {
  label: string;
  icon: (props: React.SVGProps<SVGSVGElement>) => React.ReactNode;
  path: string;
  children?: NavItemType[];
  expanded?: boolean;
  isBottom?: boolean;
}

export interface KpiDataType {
  value: string;
  label: string;
  icon: (props: React.SVGProps<SVGSVGElement>) => React.ReactNode;
  color: string; // Used for icon and progress arc color
  progress: number; // Percentage (0-100) for the progress arc
}

export interface SalesDataPoint {
  name: string;         // X-axis label (e.g., 'January', 'Day 1', 'Month 1')
  currentValue: number; // Data for the current period (e.g., This Year, This Month)
  previousValue: number;// Data for the previous period (e.g., Last Year, Last Month)
}

export interface IntelligenceChartDataPoint {
  date: string;   // Date label (e.g., 'May 1', 'May 2')
  revenue: number; // Revenue value for the line chart
  orders: number;  // Orders value for the bar chart
}

export interface DropdownOption {
  value: string;
  label: string;
}

export type ProductStatusType = ProductStatusEnum;

export interface ProductItem {
  id: string;
  selected: boolean;
  status: ProductStatusType;
  itemNumber: string;
  name: string;
  productType: string;
  onHand: string; // Can be "X" or "X + Y"
  cost: number;
  price: number;
}

export type PlatformKey = string; // Allow any platform key from database

export interface TopSellerData {
  id: string;
  store: string;
  platform: PlatformKey;
  country: string; // e.g., "USA"
  sales: number;
  salesPercentage: number;
  orders: number;
  ordersPercentage: number;
  units: number;
  unitsPercentage: number;
  changeInSalesValue: number;
  changeInSalesPercentage: number;
  color: string; // For donut chart and table indicator
}

export type FilterCategory = 'status' | 'productType' | 'dateRange' | 'store' | 'productName' | 'stockCondition' | 'stockStatus';

export interface FilterOption {
  value: string;
  label: string;
}

export interface ActiveFilter {
  id: string; // Unique ID for the filter chip, e.g., "status_active"
  type: FilterCategory;
  value: string; // The actual value being filtered, e.g., "active"
  label: string; // Display label for the chip, e.g., "Status: Active"
}

export type OrderStatus = ExtendedOrderStatusEnum;

export interface Customer {
  id: string;
  customer_id: string;
  name: string;
  email: string | null;
  phone: string | null;
  address_street: string | null;
  address_city: string | null;
  address_state: string | null;
  address_zip_code_1: string | null;
  address_country: string | null;
  notes: string | null;
  total_orders_count: number | null;
  total_spent: number | null;
  first_order_date: string | null;
  last_order_date: string | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface Buyer {
  name: string;
  email: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
}

export interface OrderItem {
  id: string;
  order_id: string;
  product_id: string;
  product_name: string;
  sku: string;
  quantity: number;
  unit_cost_oi: number;
  pack_size: number;
  subtotal_item: number;
  total_price: number | null;  // Total price for the item
  created_at: string | null;
  updated_at: string | null;
}

export interface Order {
  id: string;
  order_number: string;
  order_date: string;
  platform_id: string;
  channel_id: string;
  customer_id: string;
  status: string;
  item_count: number;
  quantity: number | null;
  subtotal: number;
  tax: number;
  shipping: number;
  total_amount: number | null;
  tracking_number: string | null;
  shipping_method: string | null;
  shipping_street: string | null;
  shipping_city: string | null;
  shipping_state: string | null;
  shipping_zip_code: string | null;
  shipping_country: string | null;
  shipping_fee_paid: boolean | null;
  expected_delivery: string | null;
  packed_at: string | null;
  shipped_at: string | null;
  delivered_at: string | null;
  cancelled_at: string | null;
  is_urgent: boolean | null;
  is_problem: boolean | null;
  is_resent: boolean | null;
  has_notes: boolean | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface DeliveryOrderItem {
  id: string;
  platform: string; // e.g., "Amazon", "eBay"
  store: string;
  name: string; // Buyer's name
  sku: string;
  product: string;
  date: string; // Order date
  trackingId: string; 
  status?: string; // e.g., 'In Transit', 'Out for Delivery', 'Delivered', 'Damaged', 'Lost'
  destinationState: string; // e.g., 'CA', 'NY'
  updateDate: string; // Last status update date/time
}

export type AllOrdersStatus = OrderStatus;

export interface AllOrdersViewItem {
  id: string;
  orderNumber: string;
  orderDate: string;
  platform: PlatformKey;
  channel: string; // e.g., "Amz-NHU", "ebay-SeamS"
  status: AllOrdersStatus;
  customerName: string;
  totalAmount: number;
  itemCount: number;
  trackingNumber?: string;
  hasNotes: boolean;
  isUrgent: boolean;
  isProblem: boolean;
  isResent: boolean;
}

export interface OrderNote {
  id: string;
  order_id: string;
  author_id: string | null;
  author_name: string;
  content: string;
  note_type: string | null;
  is_important: boolean | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface LinkedOrder {
  id: string;
  parent_order_id: string;
  child_order_id: string;
  relationship_type: OrderRelationshipEnum;
  reason: string | null;
  created_at: string | null;
}

export interface CustomerHistoryItem {
  orderId: string;
  orderNumber: string;
  date: string;
  totalAmount: number;
  status: AllOrdersStatus;
  platform: PlatformKey;
}

export interface AllOrdersDetail {
  id: string;
  orderNumber: string;
  orderDate: string;
  platform: PlatformKey;
  channel: string;
  status: AllOrdersStatus;
  
  customer: {
    name: string;
    email: string;
    phone?: string;
    address: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
      country: string;
    };
  };
  
  items: {
    id: string;
    sku: string;
    productName: string;
    quantity: number;
    unit_cost_oi: number;
    pack_size: number;
    subtotal_item: number;
  }[];
  
  subtotal: number;
  tax: number;
  shipping: number;
  totalAmount: number;
  
  shippingMethod: string;
  trackingNumber?: string;
  expectedDelivery?: string;
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  
  isUrgent: boolean;
  isProblem: boolean;
  isResent: boolean;
  hasNotes: boolean;
  
  notes: OrderNote[];
  linkedOrders: LinkedOrder[];
  customerHistory: CustomerHistoryItem[];
  
  createdAt: string;
  updatedAt: string;
  packedAt?: string;
  shippedAt?: string;
  deliveredAt?: string;
}

export enum DeliveryStatus {
  picked_up = 'picked_up',
  label_created = 'label_created',
  in_transit = 'in_transit',
  delayed = 'delayed', 
  delivered = 'delivered',
  lost = 'lost',
  out_for_delivery = 'out_for_delivery',
  exception = 'exception',
  returned = 'returned'
}

export interface DeliveryListParams {
  status?: DeliveryStatusEnum[];
  carrier?: string[];
  dateFrom?: string;
  dateTo?: string;
  search?: string; // For order number, tracking number, or customer name
  page?: number;
  limit?: number;
  sortBy?: 'ship_date' | 'estimated_delivery' | 'last_update';
  sortDirection?: 'asc' | 'desc';
  requiresAction?: boolean;
}

export interface DeliveryTrackingViewItem {
  id: string;
  order_id: string;
  order_number: string;
  tracking_number: string;
  carrier_name: string;
  carrier_code: string;
  customer_name: string;
  status: DeliveryStatusEnum;
  ship_date: string;
  estimated_delivery: string | null;
  actual_delivery: string | null;
  last_update: string | null;
  requires_action: boolean;
  destination: { city: string | null; state: string | null; zipCode: string | null };
}

export interface DeliveryDetails {
  id: string;
  order_id: string;
  order_number: string;
  tracking_number: string;
  carrier_name: string;
  carrier_code: string;
  customer_name: string;
  status: DeliveryStatusEnum;
  ship_date: string;
  estimated_delivery: string | null;
  actual_delivery: string | null;
  shipping_method: string | null;
  last_update: string | null;
  notes: string | null;
  destination: {
    city: string | null;
    state: string | null;
    zipCode: string | null;
  };
  events: DeliveryEvent[];
}

export interface DeliveryItem {
  id: string;
  order_id: string;
  tracking_number: string;
  carrier_id: string;
  status: DeliveryStatusEnum;
  ship_date: string;
  estimated_delivery: string | null;
  actual_delivery: string | null;
  last_update: string | null;
  destination_city: string | null;
  destination_state: string | null;
  destination_zip_code: string | null;
  shipping_method: string | null;
  package_count: number | null;
  weight: number | null;
  notes: string | null;
  is_delayed: boolean | null;
  has_exception: boolean | null;
  requires_action: boolean | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface UserProfile {
  id: string;
  full_name: string;
  email: string;
  role: UserRole | undefined;
  business_unit_id: string | null;
  is_active: boolean | null;
  last_login_at: string | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface AuthContextType {
  session: any; // Supabase session type
  user: any; // Supabase user type  
  profile: UserProfile | null;
  isLoading: boolean;
  refreshAuth: () => Promise<void>; // Function to force refresh auth data
  fetchUserRole: () => Promise<void>; // Function to manually fetch user role from database
  logout?: () => void; // Function to handle user logout
}

export interface CreateOrderItem {
  product_id: string;
  subtotal_item: number; // Price per item
}

export interface CreateOrderItemUI {
  product_id: string;
  sku: string;
  product_name: string;
  quantity: number;
  unit_cost_oi: number;
  pack_size: number;
  subtotal_item: number; // Price per item
}

export interface CreateOrderFormData {
  customer_email: string;
  customer_name: string;
  platform_id: string;
  channel_id: string;
  order_number: string;
  order_quantity: number;
  order_items: CreateOrderItem[];
  shipping_street: string;
  shipping_city: string;
  shipping_state: string;
  shipping_zip_code: string;
  shipping_country?: string;
  status: OrderStatusEnum;
  // subtotal: number;
}

export interface Platform {
  id: string;
  name: string;
  key: string;
  is_active: boolean | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface Channel {
  id: string;
  name: string;
  code: string;
  platform_id: string;
  is_active: boolean | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface ProductIdentifier {
  id: string;
  product_id: string;
  platform_id: string;
  platform_identifier: string;
  platform_name?: string; // Added for display purposes
  code_name: string | null;
  pack_size: number;
  created_at: string | null;
  updated_at: string | null; // Added to match other interfaces
}

export interface BusinessUnit {
  id: string;
  name: string;
  description: string | null;
  is_active: boolean | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface Carrier {
  id: string;
  name: string;
  code: string;
  is_active: boolean | null;
  tracking_url_template: string | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface Product {
  id: string;
  name: string;
  sku: string;
  description: string | null;
  category: string | null;
  product_type: string | null;
  unit_cost: number | null;
  unit_price: number | null;
  weight: number | null;
  dimensions_length: number | null;
  dimensions_width: number | null;
  dimensions_height: number | null;
  status: ProductStatusEnum | null;
  business_unit_id: string | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface Inventory {
  id: string;
  product_id: string;
  current_stock: number;
  available_stock: number | null;
  reserved_stock: number | null;
  minimum_threshold: number | null;
  maximum_threshold: number | null;
  reorder_point: number | null;
  last_counted_at: string | null;
  last_restocked_at: string | null;
  notes: string | null;
  created_at: string | null;
  updated_at: string | null;
}

// Define movement types and reason categories used across the codebase
export type InventoryMovementType = 'increase' | 'decrease' | 'set' | 'count' | 'reserve' | 'release';
export type InventoryReasonCategory = 'new_shipment' | 'inventory_count' | 'damaged_goods' | 'returns' | 'initial_setup' | 'sales' | 'adjustment' | 'other';

// Updated InventoryMovement interface based on how it's used in the codebase
export interface InventoryMovement {
  id: string;
  inventory_id: string;
  product_id: string;
  type: InventoryMovementType;
  quantity: number;
  previous_stock: number;
  new_stock: number;
  reason: string;
  reason_category: InventoryReasonCategory;
  notes: string | null;
  order_id: string | null;
  user_id: string | null;
  user_name: string | null;
  timestamp: string;
  created_at: string | null;
  updated_at: string | null;
}

export interface DeliveryEvent {
  id: string;
  delivery_id: string;
  status: DeliveryStatusEnum;
  date: string;
  time: string;
  event_timestamp: string;
  location: string | null;
  description: string | null;
  event_address: any; // JSON field
  is_current: boolean | null;
  created_at: string | null;
}

export interface OrdersView {
  id: string | null;
  order_number: string | null;
  order_date: string | null;
  platform_key: string | null;
  channel_code: string | null;
  customer_name: string | null;
  status: string | null;
  total_amount: number | null;
  item_count: number | null;
  quantity: number | null;
  tracking_number: string | null;
  has_notes: boolean | null;
  is_urgent: boolean | null;
  is_problem: boolean | null;
  is_resent: boolean | null;
}

export interface DeliveriesView {
  id: string | null;
  order_id: string | null;
  order_number: string | null;
  tracking_number: string | null;
  carrier_id: string | null;
  carrier_name: string | null;
  carrier_code: string | null;
  customer_name: string | null;
  status: DeliveryStatusEnum | null;
  ship_date: string | null;
  estimated_delivery: string | null;
  actual_delivery: string | null;
  last_update: string | null;
  requires_action: boolean | null;
}

export interface InventoryView {
  inventory_id: string | null;
  product_id: string | null;
  name: string | null;
  sku: string | null;
  product_type: string | null;
  status: ProductStatusEnum | null;
  current_stock: number | null;
  available_stock: number | null;
  reserved_stock: number | null;
  minimum_threshold: number | null;
  needs_reorder: boolean | null;
}

export interface BuyerGroup {
  buyerName: string;
  orders: FulfillOrderItem[];
}

export interface FulfillOrderItem {
  id: string;
  order_number: string;
  order_date: string;
  status: string;
  store: string;
  platform: string;
  shipping_fee_paid: boolean;
  tracking_number?: string;
  buyer: {
    name: string;
    email: string;
    address: string;
  };
  items: Array<{
    id: string;
    productName: string;
    sku: string;
    quantity: number;
    unit_cost_oi: number;
    pack_size: number;
    subtotal_item: number;
  }>;
}

export interface ChannelGroup {
  channel: string;
  subChannels: {
    name: string;
    orders: FulfillOrderItem[];
  }[];
}