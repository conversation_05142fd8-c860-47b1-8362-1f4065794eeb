import React, { useState, useEffect } from 'react';
import Modal from '@/shared/ui/overlay/Modal';
import { usePlatformLinking } from '@/shared/lib/hooks/inventory/usePlatformLinking';
import LoadingSpinner from '@/shared/ui/feedback/LoadingSpinner';
import ErrorMessage from '@/shared/ui/feedback/ErrorMessage';
import { PlusCircleIcon, TrashIcon, PencilIcon } from '@heroicons/react/24/outline';
import { Platform, ProductIdentifier } from '@/types';
import { notificationService } from '@/shared/lib/services/notification/notification-service';

interface PlatformLinkingModalProps {
  isOpen: boolean;
  onClose: () => void;
  productId: string;
}

interface PlatformIdentifierFormData {
  platform_id: string;
  platform_identifier: string;
  code_name: string;
  pack_size: number;
}

const PlatformLinkingModal: React.FC<PlatformLinkingModalProps> = ({
  isOpen,
  onClose,
  productId
}) => {
  // State for the form
  const [formData, setFormData] = useState<PlatformIdentifierFormData>({
    platform_id: '',
    platform_identifier: '',
    code_name: '',
    pack_size: 1
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [editingId, setEditingId] = useState<string | null>(null);

  // Use the platform linking hook
  const [{ platforms, identifiers, isLoading, isSubmitting, error }, {
    addIdentifier,
    updateIdentifier,
    deleteIdentifier
  }] = usePlatformLinking({ productId });

  // Reset form when modal opens/closes or when editing id changes
  useEffect(() => {
    if (isOpen) {
      if (editingId) {
        const identifier = identifiers.find(i => i.id === editingId);
        if (identifier) {
          // Find the platform to get its ID
          const platform = platforms.find(p => p.id === identifier.platform_id);
          
          setFormData({
            platform_id: identifier.platform_id || '',
            platform_identifier: identifier.platform_identifier || '',
            code_name: identifier.code_name || '',
            pack_size: identifier.pack_size || 1
          });
        }
      } else {
        // Reset form for new entry
        setFormData({
          platform_id: platforms.length > 0 ? platforms[0].id : '',
          platform_identifier: '',
          code_name: '',
          pack_size: 1
        });
      }
      setFormErrors({});
    }
  }, [isOpen, editingId, identifiers, platforms]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    // For number inputs, convert to number
    if (name === 'pack_size') {
      setFormData(prev => ({ ...prev, [name]: parseInt(value, 10) || 0 }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // Clear errors for this field
    if (formErrors[name]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Validate form data
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.platform_id) {
      errors.platform_id = 'Please select a platform';
    }

    if (!formData.platform_identifier.trim()) {
      errors.platform_identifier = 'Platform identifier is required';
    }

    if (formData.pack_size <= 0) {
      errors.pack_size = 'Pack size must be greater than 0';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      if (editingId) {
        // Update existing identifier
        await updateIdentifier(editingId, {
          platform_identifier: formData.platform_identifier,
          code_name: formData.code_name || null,
          pack_size: formData.pack_size
        });
        
        const platformName = getPlatformName(formData.platform_id);
        notificationService.success(
          `Platform identifier for ${platformName} was updated successfully.`,
          'Platform Link Updated'
        );
        
        setEditingId(null);
      } else {
        // Add new identifier
        await addIdentifier({
          platform_id: formData.platform_id,
          platform_identifier: formData.platform_identifier,
          code_name: formData.code_name || null,
          pack_size: formData.pack_size
        });
        
        const platformName = getPlatformName(formData.platform_id);
        notificationService.success(
          `Product was successfully linked to ${platformName}.`,
          'Platform Link Added'
        );
      }

      // Reset form
      setFormData({
        platform_id: platforms.length > 0 ? platforms[0].id : '',
        platform_identifier: '',
        code_name: '',
        pack_size: 1
      });
    } catch (err) {
      console.error('Error submitting platform identifier:', err);
      
      // Show error notification
      notificationService.error(
        err instanceof Error 
          ? err.message 
          : 'An unexpected error occurred while linking platform.',
        'Platform Link Error'
      );
    }
  };

  // Handle edit button click
  const handleEdit = (identifier: ProductIdentifier) => {
    setEditingId(identifier.id);
  };

  // Handle delete button click
  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this platform identifier?')) {
      try {
        const identifierToDelete = identifiers.find(i => i.id === id);
        const platformName = identifierToDelete 
          ? getPlatformName(identifierToDelete.platform_id)
          : 'Unknown platform';
        
        await deleteIdentifier(id);
        
        // Show success notification
        notificationService.success(
          `Platform link to ${platformName} was removed successfully.`,
          'Platform Link Removed'
        );
      } catch (err) {
        console.error('Error deleting platform identifier:', err);
        
        // Show error notification
        notificationService.error(
          err instanceof Error 
            ? err.message 
            : 'An unexpected error occurred while removing platform link.',
          'Platform Link Error'
        );
      }
    }
  };

  // Get platform name by id
  const getPlatformName = (platformId: string): string => {
    const platform = platforms.find(p => p.id === platformId);
    return platform ? platform.name : 'Unknown Platform';
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setEditingId(null);
    setFormData({
      platform_id: platforms.length > 0 ? platforms[0].id : '',
      platform_identifier: '',
      code_name: '',
      pack_size: 1
    });
    setFormErrors({});
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Link to Sales Platforms"
      size="lg"
    >
      <div className="space-y-6">
        {error && <ErrorMessage message={error} />}

        {isLoading ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner />
          </div>
        ) : (
          <>
            {/* Platform Identifiers List */}
            <div>
              <h3 className="text-base font-medium text-gray-900 mb-3">Current Platform Links</h3>
              {identifiers.length === 0 ? (
                <div className="text-sm text-gray-500 bg-gray-50 p-4 rounded-md">
                  No platform links found. Add your first one below.
                </div>
              ) : (
                <div className="overflow-hidden bg-white shadow-sm ring-1 ring-gray-200 sm:rounded-lg">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Platform
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Identifier
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Code Name
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Pack Size
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {identifiers.map((identifier) => (
                        <tr key={identifier.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {getPlatformName(identifier.platform_id)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                            {identifier.platform_identifier}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                            {identifier.code_name || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                            {identifier.pack_size}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              type="button"
                              onClick={() => handleEdit(identifier)}
                              className="text-blue-600 hover:text-blue-900 mr-3"
                              disabled={isSubmitting}
                            >
                              <PencilIcon className="h-4 w-4" />
                            </button>
                            <button
                              type="button"
                              onClick={() => handleDelete(identifier.id)}
                              className="text-red-600 hover:text-red-900"
                              disabled={isSubmitting}
                            >
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>

            {/* Add/Edit Form */}
            <div className="bg-gray-50 p-4 rounded-md">
              <h3 className="text-base font-medium text-gray-900 mb-3">
                {editingId ? 'Edit Platform Link' : 'Add Platform Link'}
              </h3>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  {/* Platform Selection */}
                  <div>
                    <label htmlFor="platform_id" className="block text-sm font-medium text-gray-700 mb-1">
                      Platform <span className="text-red-500">*</span>
                    </label>
                    <select
                      id="platform_id"
                      name="platform_id"
                      value={formData.platform_id}
                      onChange={handleInputChange}
                      className={`shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md ${
                        formErrors.platform_id ? 'border-red-300' : ''
                      }`}
                      disabled={isSubmitting || !!editingId}
                    >
                      <option value="">Select a platform</option>
                      {platforms.map((platform) => (
                        <option key={platform.id} value={platform.id}>
                          {platform.name}
                        </option>
                      ))}
                    </select>
                    {formErrors.platform_id && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.platform_id}</p>
                    )}
                  </div>

                  {/* Platform Identifier */}
                  <div>
                    <label htmlFor="platform_identifier" className="block text-sm font-medium text-gray-700 mb-1">
                      Identifier <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="platform_identifier"
                      name="platform_identifier"
                      value={formData.platform_identifier}
                      onChange={handleInputChange}
                      className={`shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md ${
                        formErrors.platform_identifier ? 'border-red-300' : ''
                      }`}
                      placeholder="e.g., ASIN-B07X1KZP57"
                      disabled={isSubmitting}
                    />
                    {formErrors.platform_identifier && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.platform_identifier}</p>
                    )}
                  </div>

                  {/* Code Name (Optional) */}
                  <div>
                    <label htmlFor="code_name" className="block text-sm font-medium text-gray-700 mb-1">
                      Code Name
                    </label>
                    <input
                      type="text"
                      id="code_name"
                      name="code_name"
                      value={formData.code_name}
                      onChange={handleInputChange}
                      className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      placeholder="e.g., Amazon US"
                      disabled={isSubmitting}
                    />
                    <p className="mt-1 text-xs text-gray-500">Optional store/marketplace identifier</p>
                  </div>

                  {/* Pack Size */}
                  <div>
                    <label htmlFor="pack_size" className="block text-sm font-medium text-gray-700 mb-1">
                      Pack Size <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="number"
                      id="pack_size"
                      name="pack_size"
                      value={formData.pack_size}
                      onChange={handleInputChange}
                      className={`shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md ${
                        formErrors.pack_size ? 'border-red-300' : ''
                      }`}
                      min="1"
                      step="1"
                      disabled={isSubmitting}
                    />
                    {formErrors.pack_size && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.pack_size}</p>
                    )}
                    <p className="mt-1 text-xs text-gray-500">Number of units in a single pack</p>
                  </div>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-3">
                  {editingId && (
                    <button
                      type="button"
                      onClick={handleCancelEdit}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      disabled={isSubmitting}
                    >
                      Cancel
                    </button>
                  )}
                  <button
                    type="submit"
                    className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? 'Saving...' : editingId ? 'Update' : 'Add'}
                    {!isSubmitting && !editingId && <PlusCircleIcon className="ml-2 -mr-0.5 h-4 w-4" />}
                  </button>
                </div>
              </form>
            </div>
          </>
        )}

        {/* Modal Footer */}
        <div className="pt-4 border-t border-gray-200">
          <div className="flex justify-end">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              disabled={isSubmitting}
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default PlatformLinkingModal;