import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import ProductTable from '../ProductTable';
import { InventoryView, ProductStatusEnum } from '@/types';

// Mock data for testing
const mockProducts: InventoryView[] = [
  {
    inventory_id: '1',
    product_id: '1',
    name: 'Test Product 1',
    sku: 'TP001',
    product_type: 'Electronics',
    status: 'active' as ProductStatusEnum,
    current_stock: 10,
    available_stock: 8,
    reserved_stock: 2,
    minimum_threshold: 5,
    needs_reorder: false
  },
  {
    inventory_id: '2',
    product_id: '2',
    name: 'Test Product 2',
    sku: 'TP002',
    product_type: 'Perishable',
    status: 'active' as ProductStatusEnum,
    current_stock: 3,
    available_stock: 3,
    reserved_stock: 0,
    minimum_threshold: 5,
    needs_reorder: true
  },
  {
    inventory_id: '3',
    product_id: '3',
    name: 'Test Product 3',
    sku: 'TP003',
    product_type: 'Electronics',
    status: 'discontinued' as ProductStatusEnum,
    current_stock: 0,
    available_stock: 0,
    reserved_stock: 0,
    minimum_threshold: 5,
    needs_reorder: true
  }
];

// Default props for tests
const defaultProps = {
  products: mockProducts,
  loading: false,
  error: null,
  onSelectProduct: jest.fn(),
  onSelectAll: jest.fn(),
  isAllSelected: false,
  isSomeSelected: false,
  currentPage: 1,
  totalPages: 1,
  onPageChange: jest.fn(),
  hasNextPage: false,
  hasPreviousPage: false,
  onSort: jest.fn(),
  sortConfig: { column: null, direction: 'asc' as const },
  onProductClick: jest.fn(),
  onRetryLoad: jest.fn(),
  onAddNewProduct: jest.fn(),
  onClearFilters: jest.fn(),
  activeFilters: []
};

describe('ProductTable Component', () => {
  test('renders table with products', () => {
    render(<ProductTable {...defaultProps} />);
    
    // Check if product names are displayed
    expect(screen.getByText('Test Product 1')).toBeInTheDocument();
    expect(screen.getByText('Test Product 2')).toBeInTheDocument();
    expect(screen.getByText('Test Product 3')).toBeInTheDocument();
    
    // Check if SKUs are displayed
    expect(screen.getByText('TP001')).toBeInTheDocument();
    expect(screen.getByText('TP002')).toBeInTheDocument();
    expect(screen.getByText('TP003')).toBeInTheDocument();
  });

  test('renders loading state', () => {
    render(<ProductTable {...defaultProps} loading={true} />);
    
    // Check for loading indicator
    expect(screen.getByTestId('table-loading-indicator')).toBeInTheDocument();
  });

  test('renders error state', () => {
    const errorProps = {
      ...defaultProps,
      error: new Error('Test error message'),
      products: []
    };
    
    render(<ProductTable {...errorProps} />);
    
    // Check for error message
    expect(screen.getByText(/Test error message/i)).toBeInTheDocument();
    expect(screen.getByText(/Retry/i)).toBeInTheDocument();
  });

  test('renders empty state when no products', () => {
    const emptyProps = {
      ...defaultProps,
      products: []
    };
    
    render(<ProductTable {...emptyProps} />);
    
    // Check for empty state message
    expect(screen.getByText(/No products found/i)).toBeInTheDocument();
  });

  test('calls onSort when column header is clicked', () => {
    render(<ProductTable {...defaultProps} />);
    
    // Find and click the name column header
    const nameHeader = screen.getByText(/Product Name/i);
    fireEvent.click(nameHeader);
    
    // Check if onSort was called with correct column
    expect(defaultProps.onSort).toHaveBeenCalledWith('name');
  });

  test('calls onProductClick when view button is clicked', () => {
    render(<ProductTable {...defaultProps} />);
    
    // Find and click the first view button
    const viewButtons = screen.getAllByTitle(/View details/i);
    fireEvent.click(viewButtons[0]);
    
    // Check if onProductClick was called with correct product ID
    expect(defaultProps.onProductClick).toHaveBeenCalledWith('1');
  });

  test('calls onSelectProduct when checkbox is clicked', () => {
    render(<ProductTable {...defaultProps} />);
    
    // Find and click the first product checkbox
    const checkboxes = screen.getAllByRole('checkbox');
    // Skip the first checkbox which is the "select all" checkbox
    fireEvent.click(checkboxes[1]);
    
    // Check if onSelectProduct was called with correct product ID
    expect(defaultProps.onSelectProduct).toHaveBeenCalledWith('1');
  });

  test('calls onSelectAll when header checkbox is clicked', () => {
    render(<ProductTable {...defaultProps} />);
    
    // Find and click the select all checkbox (first checkbox)
    const checkboxes = screen.getAllByRole('checkbox');
    fireEvent.click(checkboxes[0]);
    
    // Check if onSelectAll was called
    expect(defaultProps.onSelectAll).toHaveBeenCalled();
  });

  test('displays correct status badges', () => {
    render(<ProductTable {...defaultProps} />);
    
    // Check if status badges are displayed correctly
    const activeStatuses = screen.getAllByText(/Active/i);
    expect(activeStatuses.length).toBe(2);
    
    const discontinuedStatus = screen.getByText(/Discontinued/i);
    expect(discontinuedStatus).toBeInTheDocument();
  });

  test('displays pagination when totalPages > 1', () => {
    const paginationProps = {
      ...defaultProps,
      currentPage: 1,
      totalPages: 3,
      hasNextPage: true,
      hasPreviousPage: false
    };
    
    render(<ProductTable {...paginationProps} />);
    
    // Check if pagination component is displayed
    expect(screen.getByText(/Page 1 of 3/i)).toBeInTheDocument();
    
    // Find and click the next page button
    const nextButton = screen.getByLabelText(/next page/i);
    fireEvent.click(nextButton);
    
    // Check if onPageChange was called with correct page number
    expect(paginationProps.onPageChange).toHaveBeenCalledWith(2);
  });
}); 