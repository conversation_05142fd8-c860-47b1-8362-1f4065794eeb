-- Order Subtotal Calculation Trigger
-- This migration adds a trigger to automatically calculate and update the subtotal
-- in the orders table based on the sum of subtotal_item from order_items

-- Drop the function and trigger if they already exist (for idempotent migrations)
DROP TRIGGER IF EXISTS tr_update_order_subtotal ON order_items;
DROP FUNCTION IF EXISTS update_order_subtotal();

-- Create the function that will calculate and update the order subtotal
CREATE OR REPLACE FUNCTION update_order_subtotal()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_order_id UUID;
BEGIN
  -- Determine which order_id to update based on the operation
  IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
    v_order_id := NEW.order_id;
  ELSIF TG_OP = 'DELETE' THEN
    v_order_id := OLD.order_id;
  END IF;

  -- Update the order's subtotal with the sum of subtotal_item values
  UPDATE orders
  SET subtotal = (
    SELECT COALESCE(SUM(subtotal_item * quantity), 0)
    FROM order_items
    WHERE order_id = v_order_id
  )
  WHERE id = v_order_id;

  -- Return the appropriate record based on the operation
  IF TG_OP = 'DELETE' THEN
    RETURN OLD;
  ELSE
    RETURN NEW;
  END IF;
END;
$$;

-- Create a trigger that runs after INSERT, UPDATE, or DELETE on order_items
CREATE TRIGGER tr_update_order_subtotal
AFTER INSERT OR UPDATE OR DELETE ON order_items
FOR EACH ROW
EXECUTE FUNCTION update_order_subtotal();

-- Add comment to explain the function
COMMENT ON FUNCTION update_order_subtotal() IS 'Automatically updates the subtotal in orders table based on the sum of subtotal_item values in order_items';

-- Create a function to manually recalculate subtotals for existing orders
CREATE OR REPLACE FUNCTION recalculate_all_order_subtotals()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_updated_count INTEGER := 0;
BEGIN
  -- Update subtotal for all orders
  UPDATE orders o
  SET subtotal = (
    SELECT COALESCE(SUM(subtotal_item * quantity), 0)
    FROM order_items oi
    WHERE oi.order_id = o.id
  );
  
  GET DIAGNOSTICS v_updated_count = ROW_COUNT;
  
  RETURN v_updated_count;
END;
$$;

COMMENT ON FUNCTION recalculate_all_order_subtotals() IS 'Manually recalculates subtotals for all existing orders';

-- Create a test function to verify the trigger works
CREATE OR REPLACE FUNCTION test_order_subtotal_trigger()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_order_id UUID;
  v_product_id UUID;
  v_subtotal DECIMAL(10,2);
  v_expected_subtotal DECIMAL(10,2) := 150.00;
BEGIN
  -- Create a test order
  INSERT INTO orders (
    order_number,
    customer_id,
    platform_id,
    channel_id,
    status,
    item_count,
    total_amount,
    subtotal
  ) VALUES (
    'TEST-' || extract(epoch from now()),
    (SELECT id FROM customers LIMIT 1),
    (SELECT id FROM platforms LIMIT 1),
    (SELECT id FROM channels LIMIT 1),
    'open',
    2,
    0,
    0
  )
  RETURNING id INTO v_order_id;
  
  -- Get a product ID for testing
  SELECT id INTO v_product_id FROM products LIMIT 1;
  
  -- Insert test order items
  INSERT INTO order_items (
    order_id,
    product_id,
    sku,
    product_name,
    quantity,
    unit_price,
    subtotal_item,
    pack_size,
    total_price
  ) VALUES
  (
    v_order_id,
    v_product_id,
    'TEST-SKU-1',
    'Test Product 1',
    1,
    50.00,
    50.00,
    1,
    50.00
  ),
  (
    v_order_id,
    v_product_id,
    'TEST-SKU-2',
    'Test Product 2',
    2,
    50.00,
    50.00,
    1,
    100.00
  );
  
  -- Check if subtotal was updated correctly
  SELECT subtotal INTO v_subtotal FROM orders WHERE id = v_order_id;
  
  -- Clean up test data
  DELETE FROM order_items WHERE order_id = v_order_id;
  DELETE FROM orders WHERE id = v_order_id;
  
  -- Return true if the trigger worked correctly
  RETURN v_subtotal = v_expected_subtotal;
END;
$$;

COMMENT ON FUNCTION test_order_subtotal_trigger() IS 'Test function to verify the order subtotal trigger works correctly'; 