import React from 'react';
import { CreateOrderFormData, Platform, Channel } from '@/types';

interface PlatformChannelSectionProps {
  formData: CreateOrderFormData;
  platforms: Platform[];
  filteredChannels: Channel[];
  onChange: (updates: Partial<CreateOrderFormData>) => void;
}

const PlatformChannelSection: React.FC<PlatformChannelSectionProps> = ({ 
  formData, 
  platforms, 
  filteredChannels, 
  onChange 
}) => {
  return (
    <div className="grid grid-cols-2 gap-4">
      <select
        value={formData.platform_id}
        onChange={(e) => onChange({ platform_id: e.target.value })}
        className="px-3 py-2 border rounded-md"
        required
      >
        <option value="">Select Platform</option>
        {platforms.map(platform => (
          <option key={platform.id} value={platform.id}>
            {platform.name}
          </option>
        ))}
      </select>
      <select
        value={formData.channel_id}
        onChange={(e) => onChange({ channel_id: e.target.value })}
        className="px-3 py-2 border rounded-md"
        required
        disabled={!formData.platform_id}
      >
        <option value="">Select Channel</option>
        {filteredChannels.map(channel => (
          <option key={channel.id} value={channel.id}>
            {channel.name}
          </option>
        ))}
      </select>
    </div>
  );
};

export default PlatformChannelSection; 