import React, { useState, useEffect, useMemo, memo } from 'react';
import Modal from '@/shared/ui/overlay/Modal';
import { ActiveFilter, FilterOption } from '@/types';
import { cn } from '@/shared/lib/utils/core/cn';

interface FilterModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApplyFilters: (filters: ActiveFilter[]) => void;
  currentFilters: ActiveFilter[];
  availableProductTypes: FilterOption[];
}

const productStatuses: FilterOption[] = [
  { value: 'active', label: 'Active' },
  { value: 'new', label: 'New' },
  { value: 'discontinued', label: 'Discontinued' },
  { value: 'issue', label: 'Issue' },
];

// Stock level conditions
const stockConditions: FilterOption[] = [
  { value: 'low_stock', label: 'Low Stock' },
  { value: 'out_of_stock', label: 'Out of Stock' },
  { value: 'needs_reorder', label: 'Needs Reorder' },
  { value: 'in_stock', label: 'In Stock' },
];

const FilterModal: React.FC<FilterModalProps> = ({
  isOpen,
  onClose,
  onApplyFilters,
  currentFilters,
  availableProductTypes,
}) => {
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const [selectedProductTypes, setSelectedProductTypes] = useState<string[]>([]);
  const [selectedStockConditions, setSelectedStockConditions] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<'status' | 'type' | 'stock'>('status');

  // Optimize label lookups with Maps for O(1) access
  const statusLabelMap = useMemo(() => {
    const map = new Map<string, string>();
    productStatuses.forEach(status => {
      map.set(status.value, status.label);
    });
    return map;
  }, []);

  const productTypeLabelMap = useMemo(() => {
    const map = new Map<string, string>();
    availableProductTypes.forEach(type => {
      map.set(type.value, type.label);
    });
    return map;
  }, [availableProductTypes]);

  const stockConditionLabelMap = useMemo(() => {
    const map = new Map<string, string>();
    stockConditions.forEach(condition => {
      map.set(condition.value, condition.label);
    });
    return map;
  }, []);

  // Initialize selections based on current filters when modal opens
  useEffect(() => {
    if (isOpen) {
      // Single-pass optimization: combine filter+map into reduce for better performance
      const { statusValues, productTypeValues, stockConditionValues } = currentFilters.reduce(
        (acc, filter) => {
          if (filter.type === 'status') {
            acc.statusValues.push(filter.value);
          } else if (filter.type === 'productType') {
            acc.productTypeValues.push(filter.value);
          } else if (filter.type === 'stockCondition') {
            acc.stockConditionValues.push(filter.value);
          }
          return acc;
        },
        { 
          statusValues: [] as string[], 
          productTypeValues: [] as string[],
          stockConditionValues: [] as string[]
        }
      );
      
      setSelectedStatuses(statusValues);
      setSelectedProductTypes(productTypeValues);
      setSelectedStockConditions(stockConditionValues);
    }
  }, [isOpen, currentFilters]);

  // Event handlers for selection changes
  const handleStatusChange = (statusValue: string) => {
    setSelectedStatuses(prev => 
      prev.includes(statusValue) ? prev.filter(s => s !== statusValue) : [...prev, statusValue]
    );
  };

  const handleProductTypeChange = (typeValue: string) => {
    setSelectedProductTypes(prev =>
      prev.includes(typeValue) ? prev.filter(t => t !== typeValue) : [...prev, typeValue]
    );
  };

  const handleStockConditionChange = (conditionValue: string) => {
    setSelectedStockConditions(prev =>
      prev.includes(conditionValue) ? prev.filter(c => c !== conditionValue) : [...prev, conditionValue]
    );
  };

  // Apply filters and close modal
  const handleApply = () => {
    const newFilters: ActiveFilter[] = [];
    
    // Add status filters
    selectedStatuses.forEach(value => {
      const label = statusLabelMap.get(value) || value;
      newFilters.push({ 
        id: `status_${value}`, 
        type: 'status', 
        value, 
        label: `Status: ${label}` 
      });
    });
    
    // Add product type filters
    selectedProductTypes.forEach(value => {
      const label = productTypeLabelMap.get(value) || value;
      newFilters.push({ 
        id: `productType_${value}`, 
        type: 'productType', 
        value, 
        label: `Type: ${label}` 
      });
    });
    
    // Add stock condition filters
    selectedStockConditions.forEach(value => {
      const label = stockConditionLabelMap.get(value) || value;
      newFilters.push({ 
        id: `stockCondition_${value}`, 
        type: 'stockCondition', 
        value, 
        label: `Stock: ${label}` 
      });
    });
    
    onApplyFilters(newFilters);
    onClose();
  };

  // Reset all filters
  const handleClearAll = () => {
    setSelectedStatuses([]);
    setSelectedProductTypes([]);
    setSelectedStockConditions([]);
  };

  // Count selected filters
  const selectedCount = 
    selectedStatuses.length + 
    selectedProductTypes.length + 
    selectedStockConditions.length;

  // Tab navigation component
  const renderTabNavigation = () => {
    const tabClass = (tab: typeof activeTab) => 
      cn(
        "px-3 py-3 sm:py-2 text-sm font-medium border-b-2 flex-1 text-center",
        activeTab === tab 
          ? "border-blue-600 text-blue-600" 
          : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
      );

    return (
      <nav className="flex border-b border-gray-200 mb-4">
        <button 
          className={tabClass('status')} 
          onClick={() => setActiveTab('status')}
        >
          Status
          {selectedStatuses.length > 0 && (
            <span className="ml-1 bg-blue-100 text-blue-800 text-xs font-semibold rounded-full px-2">
              {selectedStatuses.length}
            </span>
          )}
        </button>
        <button 
          className={tabClass('type')} 
          onClick={() => setActiveTab('type')}
        >
          Product Type
          {selectedProductTypes.length > 0 && (
            <span className="ml-1 bg-blue-100 text-blue-800 text-xs font-semibold rounded-full px-2">
              {selectedProductTypes.length}
            </span>
          )}
        </button>
        <button 
          className={tabClass('stock')} 
          onClick={() => setActiveTab('stock')}
        >
          Stock Conditions
          {selectedStockConditions.length > 0 && (
            <span className="ml-1 bg-blue-100 text-blue-800 text-xs font-semibold rounded-full px-2">
              {selectedStockConditions.length}
            </span>
          )}
        </button>
      </nav>
    );
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Filter Products">
      <div className="space-y-4 max-h-[70vh] overflow-y-auto px-1 sm:px-0">
        {renderTabNavigation()}

        {/* Status filters */}
        {activeTab === 'status' && (
          <div className="pb-3">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              {productStatuses.map(status => (
                <label 
                  key={status.value} 
                  className={cn(
                    "flex items-center px-3 py-3 sm:py-2 border rounded-md cursor-pointer transition-colors",
                    selectedStatuses.includes(status.value)
                      ? "bg-blue-50 border-blue-300"
                      : "bg-white border-gray-300 hover:bg-gray-50"
                  )}
                >
                  <input 
                    type="checkbox" 
                    className="h-5 w-5 sm:h-4 sm:w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    checked={selectedStatuses.includes(status.value)}
                    onChange={() => handleStatusChange(status.value)}
                  />
                  <span className="ml-2 text-sm font-medium text-gray-700">{status.label}</span>
                </label>
              ))}
            </div>
          </div>
        )}

        {/* Product type filters */}
        {activeTab === 'type' && (
          <div className="pb-3">
            {availableProductTypes.length === 0 ? (
              <p className="text-sm text-gray-500 italic">No product types available</p>
            ) : (
              <div className="max-h-48 overflow-y-auto pr-2">
                {availableProductTypes.map(type => (
                  <label 
                    key={type.value} 
                    className={cn(
                      "flex items-center px-3 py-3 sm:py-2 border rounded-md cursor-pointer transition-colors mb-2",
                      selectedProductTypes.includes(type.value)
                        ? "bg-blue-50 border-blue-300"
                        : "bg-white border-gray-300 hover:bg-gray-50"
                    )}
                  >
                    <input 
                      type="checkbox" 
                      className="h-5 w-5 sm:h-4 sm:w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      checked={selectedProductTypes.includes(type.value)}
                      onChange={() => handleProductTypeChange(type.value)}
                    />
                    <span className="ml-2 text-sm font-medium text-gray-700">{type.label}</span>
                  </label>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Stock condition filters */}
        {activeTab === 'stock' && (
          <div className="pb-3">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              {stockConditions.map(condition => (
                <label 
                  key={condition.value} 
                  className={cn(
                    "flex items-center px-3 py-3 sm:py-2 border rounded-md cursor-pointer transition-colors",
                    selectedStockConditions.includes(condition.value)
                      ? "bg-blue-50 border-blue-300"
                      : "bg-white border-gray-300 hover:bg-gray-50"
                  )}
                >
                  <input 
                    type="checkbox" 
                    className="h-5 w-5 sm:h-4 sm:w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    checked={selectedStockConditions.includes(condition.value)}
                    onChange={() => handleStockConditionChange(condition.value)}
                  />
                  <span className="ml-2 text-sm font-medium text-gray-700">{condition.label}</span>
                </label>
              ))}
            </div>
          </div>
        )}

        {/* Action buttons */}
        <div className="pt-4 border-t border-gray-200 mt-4 flex flex-col sm:flex-row justify-between sm:space-x-2 space-y-3 sm:space-y-0">
          <button 
            type="button"
            onClick={handleClearAll}
            disabled={selectedCount === 0}
            className={cn(
              "px-3 py-2 text-sm font-medium rounded-md order-2 sm:order-1",
              selectedCount > 0
                ? "text-gray-700 hover:text-gray-900 hover:underline"
                : "text-gray-400 cursor-not-allowed"
            )}
          >
            Clear all
          </button>
          
          <div className="flex space-x-2 order-1 sm:order-2">
            <button 
              type="button"
              onClick={onClose}
              className="flex-1 sm:flex-none px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button 
              type="button"
              onClick={handleApply}
              className={cn(
                "flex-1 sm:flex-none px-4 py-2 text-sm font-medium text-white border border-transparent rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",
                selectedCount > 0
                  ? "bg-blue-600 hover:bg-blue-700"
                  : "bg-blue-400"
              )}
            >
              {selectedCount > 0 ? `Apply (${selectedCount})` : 'Apply'}
            </button>
          </div>
        </div>
      </div>
    </Modal>
  );
};

// Custom comparison function for memoization
const areEqual = (prevProps: FilterModalProps, nextProps: FilterModalProps) => {
  // Only re-render if these props change
  if (prevProps.isOpen !== nextProps.isOpen) {
    return false;
  }
  
  // Skip comparison of other props when modal is closed
  if (!prevProps.isOpen && !nextProps.isOpen) {
    return true;
  }
  
  // Compare function references
  if (
    prevProps.onClose !== nextProps.onClose || 
    prevProps.onApplyFilters !== nextProps.onApplyFilters
  ) {
    return false;
  }
  
  // Compare filter arrays
  if (prevProps.currentFilters !== nextProps.currentFilters) {
    return false;
  }
  
  // Compare available product types
  if (prevProps.availableProductTypes !== nextProps.availableProductTypes) {
    return false;
  }
  
  return true;
};

// Export memoized component with custom comparison function
export default memo(FilterModal, areEqual);