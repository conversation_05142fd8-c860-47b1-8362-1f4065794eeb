import { OrderFilters, SearchParams, PaginationParams } from '../order/live-order-service';

export interface CacheKey {
  filters: OrderFilters;
  search: SearchParams;
  pagination: PaginationParams;
}

export class CacheKeyManager {
  /**
   * Generate a generic cache key from a namespace and params object
   * @param namespace The namespace for the cache (e.g., 'orders', 'inventory')
   * @param params Any object containing parameters to be included in the key
   * @returns A string key for cache storage
   */
  generateCacheKey(namespace: string, params: Record<string, any>): string {
    // Create a key object that includes the namespace
    const keyObj = {
      namespace,
      ...this.cleanParams(params)
    };

    return btoa(JSON.stringify(keyObj)).replace(/[+/=]/g, '');
  }

  /**
   * Remove undefined, null, and empty string values from params
   * to ensure consistent key generation
   */
  private cleanParams(params: Record<string, any>): Record<string, any> {
    const cleaned: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(params)) {
      // Skip undefined, null, and empty strings
      if (value === undefined || value === null || value === '') continue;
      
      // Include everything else
      cleaned[key] = value;
    }
    
    return cleaned;
  }

  /**
   * Generate cache key from filters, search, and pagination (for backwards compatibility)
   */
  static generateKey(filters: OrderFilters, search: SearchParams, pagination: PaginationParams): string {
    const keyObj = {
      // Filters
      status: filters.status || null,
      platform: filters.platform || null,
      channel: filters.channel || null,
      dateFrom: filters.dateFrom || null,
      dateTo: filters.dateTo || null,
      customerName: filters.customerName || null,
      isUrgent: filters.isUrgent || null,
      hasNotes: filters.hasNotes || null,
      isReturned: filters.isReturned || null,
      // Search
      orderNumber: search.orderNumber || null,
      customerNameSearch: search.customerName || null,
      trackingNumber: search.trackingNumber || null,
      // Pagination
      page: pagination.page || 1,
      limit: pagination.limit || 20,
    };

    return btoa(JSON.stringify(keyObj)).replace(/[+/=]/g, '');
  }

  /**
   * Check if two cache keys match based on filters
   */
  static matchesFilters(cacheKey: CacheKey, filters: Partial<OrderFilters>): boolean {
    if (filters.status && cacheKey.filters.status === filters.status) return true;
    if (filters.platform && cacheKey.filters.platform === filters.platform) return true;
    if (filters.channel && cacheKey.filters.channel === filters.channel) return true;
    if (filters.customerName && cacheKey.filters.customerName === filters.customerName) return true;
    
    return false;
  }
} 