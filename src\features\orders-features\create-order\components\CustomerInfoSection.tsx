import React from 'react';
import { CreateOrderFormData } from '@/types';

interface CustomerInfoSectionProps {
  formData: CreateOrderFormData;
  onChange: (updates: Partial<CreateOrderFormData>) => void;
}

const CustomerInfoSection: React.FC<CustomerInfoSectionProps> = ({ formData, onChange }) => {
  return (
    <div className="grid grid-cols-2 gap-4">
      <input
        type="email"
        placeholder="Customer Email"
        value={formData.customer_email}
        onChange={(e) => onChange({ customer_email: e.target.value })}
        className="px-3 py-2 border rounded-md"
        required
      />
      <input
        type="text"
        placeholder="Customer Name"
        value={formData.customer_name}
        onChange={(e) => onChange({ customer_name: e.target.value })}
        className="px-3 py-2 border rounded-md"
        required
      />
    </div>
  );
};

export default CustomerInfoSection; 