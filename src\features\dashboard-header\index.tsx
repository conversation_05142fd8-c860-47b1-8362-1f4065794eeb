import React, { useState, useMemo, useCallback, memo } from 'react';
import { ChevronDownIcon } from '@heroicons/react/24/outline';

interface DashboardHeaderProps {
  title?: string;
  subtitle?: string;
  viewOptions?: Array<{
    value: string;
    label: string;
  }>;
  selectedView?: string;
  onViewChange?: (value: string) => void;
}

const DashboardHeaderComponent: React.FC<DashboardHeaderProps> = ({
  title = "Operations Dashboard",
  subtitle = "A unified view of your entire business empire.",
  viewOptions = [
    { value: "all_units", label: "Command View (All Units)" },
    { value: "shrimp_products", label: "Shrimp Products" },
    { value: "dropship_products", label: "Dropship Products" },
  ],
  selectedView = "all_units",
  onViewChange
}) => {
  // PERFORMANCE: Memoize viewOptions map for O(1) lookups
  const viewOptionsMap = useMemo(() => {
    const map = new Map<string, { value: string; label: string }>();
    viewOptions.forEach(option => {
      map.set(option.value, option);
    });
    return map;
  }, [viewOptions]);

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [currentView, setCurrentView] = useState(selectedView);

  // PERFORMANCE: Memoize view change handler to prevent recreation
  const handleViewChange = useCallback((value: string) => {
    setCurrentView(value);
    setIsDropdownOpen(false);
    if (onViewChange) {
      onViewChange(value);
    }
  }, [onViewChange]);

  // PERFORMANCE: Memoize dropdown toggle handler
  const toggleDropdown = useCallback(() => {
    setIsDropdownOpen(prev => !prev);
  }, []);

  // PERFORMANCE: Memoize current view label calculation
  const currentViewLabel = useMemo(() => {
    const option = viewOptionsMap.get(currentView);
    return option ? option.label : "Command View (All Units)";
  }, [viewOptionsMap, currentView]);

  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Left side - Title and subtitle */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
          <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
        </div>

        {/* Right side - View selector */}
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-600">Viewing Dashboard:</span>
          <div className="relative">
            <button
              onClick={toggleDropdown}
              className="flex items-center space-x-2 bg-white border border-gray-300 rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <span>{currentViewLabel}</span>
              <ChevronDownIcon 
                className={`h-4 w-4 transition-transform duration-200 ${
                  isDropdownOpen ? 'rotate-180' : ''
                }`} 
              />
            </button>

            {/* Dropdown menu */}
            {isDropdownOpen && (
              <div className="absolute right-0 mt-2 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                <div className="py-1">
                  {viewOptions.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => handleViewChange(option.value)}
                      className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 ${
                        currentView === option.value
                          ? 'bg-blue-50 text-blue-700 font-medium'
                          : 'text-gray-700'
                      }`}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

// PERFORMANCE: Memoize component to prevent unnecessary re-renders
const DashboardHeader = memo(DashboardHeaderComponent, (prevProps, nextProps) => {
  // Custom comparison for optimal performance
  if (prevProps.title !== nextProps.title ||
      prevProps.subtitle !== nextProps.subtitle ||
      prevProps.selectedView !== nextProps.selectedView ||
      prevProps.onViewChange !== nextProps.onViewChange) {
    return false;
  }

  // Compare viewOptions array by length and content
  if (prevProps.viewOptions?.length !== nextProps.viewOptions?.length) {
    return false;
  }

  // If both are undefined/null, they're equal
  if (!prevProps.viewOptions && !nextProps.viewOptions) {
    return true;
  }

  // If one is undefined/null and the other isn't, they're different
  if (!prevProps.viewOptions || !nextProps.viewOptions) {
    return false;
  }

  // Compare each option
  return prevProps.viewOptions.every((option, index) => 
    option.value === nextProps.viewOptions![index]?.value &&
    option.label === nextProps.viewOptions![index]?.label
  );
});

DashboardHeader.displayName = 'DashboardHeader';

export default DashboardHeader;