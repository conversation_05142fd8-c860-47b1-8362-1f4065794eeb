// API Response type
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

// Customers API specific types
interface CustomersListParams {
  page?: number;
  limit?: number;
  search?: string;
  country?: string;
  state?: string;
  city?: string;
  businessUnit?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
import type {
  Customer,
  CustomerHistoryItem,
  AllOrdersViewItem
} from '../../../types';

// Customers API Functions
export const customersApi = {
  // List customers with filtering and pagination
  async list(params: CustomersListParams = {}): Promise<ApiResponse<Customer[]>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, String(value));
      }
    });

    const response = await fetch(`/api/customers?${queryParams}`);
    return response.json();
  },

  // Get customer details
  async getById(id: string): Promise<ApiResponse<Customer>> {
    const response = await fetch(`/api/customers/${id}`);
    return response.json();
  },

  // Create new customer
  async create(data: {
    customer_id?: string;
    name: string;
    email?: string;
    phone?: string;
    address_street?: string;
    address_city?: string;
    address_state?: string;
    address_zip_code_1?: string;
    address_country?: string;
    notes?: string;
  }): Promise<ApiResponse<Customer>> {
    const response = await fetch('/api/customers', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    return response.json();
  },

  // Update customer
  async update(id: string, data: Partial<Customer>): Promise<ApiResponse<Customer>> {
    const response = await fetch(`/api/customers/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    return response.json();
  },

  // Delete customer
  async delete(id: string): Promise<ApiResponse<void>> {
    const response = await fetch(`/api/customers/${id}`, {
      method: 'DELETE',
    });
    return response.json();
  },

  // Get customer order history
  async getOrderHistory(customerId: string, params?: {
    limit?: number;
    page?: number;
    status?: string;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<ApiResponse<AllOrdersViewItem[]>> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, String(value));
        }
      });
    }

    const response = await fetch(`/api/customers/${customerId}/orders?${queryParams}`);
    return response.json();
  },

  // Get customer statistics
  async getStats(customerId: string): Promise<ApiResponse<{
    totalOrders: number;
    totalSpent: number;
    averageOrderValue: number;
    firstOrderDate: string | null;
    lastOrderDate: string | null;
    favoriteProducts: Array<{
      productId: string;
      productName: string;
      orderCount: number;
      totalQuantity: number;
    }>;
    ordersByStatus: Array<{
      status: string;
      count: number;
    }>;
  }>> {
    const response = await fetch(`/api/customers/${customerId}/stats`);
    return response.json();
  },

  // Search customers
  async search(query: string, limit: number = 20): Promise<ApiResponse<Customer[]>> {
    const queryParams = new URLSearchParams({
      q: query,
      limit: String(limit),
    });

    const response = await fetch(`/api/customers/search?${queryParams}`);
    return response.json();
  },

  // Get customers by location
  async getByLocation(params: {
    country?: string;
    state?: string;
    city?: string;
    zipCode?: string;
  }): Promise<ApiResponse<Customer[]>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, String(value));
      }
    });

    const response = await fetch(`/api/customers/by-location?${queryParams}`);
    return response.json();
  },

  // Get top customers by spending
  async getTopCustomers(params?: {
    period?: string;
    limit?: number;
    businessUnit?: string;
  }): Promise<ApiResponse<Array<Customer & {
    totalSpent: number;
    orderCount: number;
    averageOrderValue: number;
  }>>> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, String(value));
        }
      });
    }

    const response = await fetch(`/api/customers/top?${queryParams}`);
    return response.json();
  },

  // Merge customers (combine duplicate customers)
  async merge(primaryCustomerId: string, secondaryCustomerId: string): Promise<ApiResponse<Customer>> {
    const response = await fetch(`/api/customers/${primaryCustomerId}/merge`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ secondaryCustomerId }),
    });
    return response.json();
  },

  // Add customer note
  async addNote(customerId: string, note: string): Promise<ApiResponse<void>> {
    const response = await fetch(`/api/customers/${customerId}/notes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ note }),
    });
    return response.json();
  }
};

export default customersApi; 