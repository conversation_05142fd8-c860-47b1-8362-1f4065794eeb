# Supabase Edge Functions Guidelines

This document outlines best practices and implementation patterns for using Supabase Edge Functions in our application.

## Architecture Overview

Our application follows a layered architecture pattern for database interactions:

```
UI Components → Hooks → Services → API / Edge Functions → Database
```

Edge Functions are used for operations that require:
- Complex business logic that would be inefficient in the client
- Batch operations that need transaction safety
- Operations requiring privileged database access
- Performance-critical operations

## Directory Structure

```
project/
├── src/
│   └── shared/
│       ├── api/                  # API layer for read operations
│       └── lib/
│           ├── services/         # Service layer with Edge Function calls
│           └── hooks/            # Hooks that use services
└── supabase/                     # ALL Supabase related code
    ├── functions/                # Edge Functions
    │   ├── _shared/              # Shared utilities
    │   │   ├── cors.ts           # CORS headers
    │   │   └── test-utils.ts     # Test utilities
    │   ├── bulk-stock-update/    # Stock update Edge Function
    │   │   ├── index.ts          # Implementation
    │   │   └── index.test.ts     # TDD tests
    │   ├── bulk-product-update/  # Product update Edge Function
    │   │   ├── index.ts          # Implementation
    │   │   └── index.test.ts     # TDD tests
    │   └── bulk-product-identifiers/ # Product identifiers Edge Function
    │       ├── index.ts          # Implementation
    │       └── index.test.ts     # TDD tests
    ├── import_map.json           # Deno import map
    └── deno.json                 # Deno configuration
```

## Implementation Pattern

### 1. Edge Function Implementation

Each Edge Function follows this structure:

```typescript
// @ts-nocheck
// deno-lint-ignore-file

import { serve } from "std/http/server.ts"
import { createClient } from "supabase"
import { corsHeaders } from "../_shared/cors.ts"

// Type definitions
interface RequestBody {
  // Request parameters
}

// Helper functions
function getErrorMessage(error: unknown): string {
  // Error handling
}

// Main handler
serve(async (req) => {
  // 1. CORS preflight handling
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // 2. Parse request body
    const params = await req.json() as RequestBody
    
    // 3. Validate input
    if (!isValid(params)) {
      return errorResponse(400, 'Validation error message')
    }

    // 4. Create Supabase client with SERVICE_ROLE
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      { global: { headers: { Authorization: req.headers.get('Authorization') } } }
    )
    
    // 5. Perform database operations
    // ...
    
    // 6. Return success response
    return new Response(
      JSON.stringify({
        success: true,
        data: result,
        count: result?.length || 0
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (err) {
    // 7. Handle errors
    console.error('Operation failed:', getErrorMessage(err))
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: getErrorMessage(err)
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
```

### 2. Service Layer Integration

Services call Edge Functions like this:

```typescript
// In service file (e.g., inventory-stock-service.ts)
async bulkStockUpdateEdge(items: StockUpdateItem[], userId: string) {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/bulk-stock-update`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${sessionStorage.getItem('supabase.auth.token')}`
        },
        body: JSON.stringify({ items, userId })
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `Failed to update stock (${response.status})`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error in bulk stock update:', error);
    throw error;
  }
}
```

### 3. Hook Implementation

Hooks use services to call Edge Functions:

```typescript
// In hook file (e.g., useBulkStockAdjustment.ts)
const bulkAdjustStock = useCallback(async (data: BulkStockAdjustmentData) => {
  setIsProcessing(true);
  
  try {
    // Prepare data for Edge Function
    const items = data.productIds.map(productId => ({
      productId,
      quantity: data.quantity,
      type: data.type,
      reason: data.reason,
      reasonCategory: data.reasonCategory,
      notes: data.notes
    }));
    
    // Call service method that uses Edge Function
    const result = await inventoryStockService.bulkStockUpdateEdge(
      items, 
      user?.id || 'unknown'
    );
    
    return { success: true, ...result };
  } catch (err) {
    return { success: false };
  } finally {
    setIsProcessing(false);
  }
}, [user?.id]);
```

## TDD Testing

We use Deno's built-in testing framework for Edge Functions:

```typescript
// In test file (e.g., bulk-stock-update/index.test.ts)
import { assertEquals } from "std/testing/asserts.ts";
import { createMockRequest } from "../_shared/test-utils.ts";

Deno.test("bulk-stock-update - success case", async () => {
  const { serve } = await import("./index.ts");
  
  const req = createMockRequest({
    method: "POST",
    headers: { "Authorization": "Bearer mock-token" },
    body: JSON.stringify({ /* test data */ }),
  });

  const response = await serve(req);
  assertEquals(response.status, 200);
  
  const responseBody = await response.json();
  assertEquals(responseBody.success, true);
});
```

## Security Best Practices

1. **Always validate input**: Validate all parameters from client requests
2. **Use proper error handling**: Sanitize error messages before returning to clients
3. **Set CORS headers properly**: Only allow necessary origins/methods
4. **Use authorization**: Always check for valid JWT tokens
5. **Log appropriately**: Log errors with context but avoid sensitive data

## Deployment Process

1. **Local Development**:
   ```bash
   supabase functions serve
   ```

2. **Deployment**:
   ```bash
   supabase functions deploy bulk-stock-update
   supabase functions deploy bulk-product-update
   supabase functions deploy bulk-product-identifiers
   ```

## When to Use Edge Functions vs. Direct API Calls

Use **Edge Functions** when:
- Operation involves multiple database operations that need transaction safety
- Operation requires elevated database privileges
- Operation would benefit from server-side performance (like bulk operations)
- Operation requires complex business logic better executed server-side

Use **Direct API Calls** when:
- Simple read operations
- Simple single-record writes
- Operations that can rely on RLS policies for security 