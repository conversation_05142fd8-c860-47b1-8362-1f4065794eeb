import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { WebSocketReconnection } from './websocket-reconnection';
import { WebSocketService } from './websocket-service';
import { DEFAULT_WEBSOCKET_CONFIG } from './websocket-config';

// Mock service
jest.mock('./websocket-service', () => {
  return {
    WebSocketService: jest.fn().mockImplementation(() => ({
      connect: jest.fn().mockResolvedValue(undefined),
      disconnect: jest.fn().mockResolvedValue(undefined),
      getState: jest.fn().mockReturnValue({
        isConnected: false,
        isReconnecting: false,
        reconnectAttempts: 0,
      }),
      updateState: jest.fn(),
    })),
  };
});

describe('WebSocket Reconnection System', () => {
  let reconnection: WebSocketReconnection;
  let mockConfig: any;
  let consoleSpy: any;
  let mockOnAttempt: jest.Mock;
  let mockOnReconnect: jest.Mock;
  
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    
    // Mock console methods
    consoleSpy = {
      log: jest.spyOn(console, 'log').mockImplementation(() => {}),
      error: jest.spyOn(console, 'error').mockImplementation(() => {}),
    };
    
    // Create config with test values
    mockConfig = {
      ...DEFAULT_WEBSOCKET_CONFIG,
      reconnectInterval: 1000,
      maxReconnectAttempts: 5,
      maxReconnectInterval: 10000,
    };
    
    // Create fresh reconnection module
    reconnection = new WebSocketReconnection(mockConfig);
    
    // Create mock callback functions
    mockOnAttempt = jest.fn();
    mockOnReconnect = jest.fn().mockResolvedValue(undefined);
  });
  
  afterEach(() => {
    jest.useRealTimers();
    jest.restoreAllMocks();
  });
  
  describe('scheduleReconnection', () => {
    it('should schedule reconnection with the correct delay', () => {
      reconnection.scheduleReconnection(0, mockOnAttempt, mockOnReconnect);
      
      expect(mockOnAttempt).toHaveBeenCalledWith(1);
      expect(setTimeout).toHaveBeenCalledTimes(1);
      
      // Advance timer and check if reconnect was called
      jest.runOnlyPendingTimers();
      expect(mockOnReconnect).toHaveBeenCalledTimes(1);
    });
    
    it('should use exponential backoff for reconnection delays', () => {
      const setTimeoutSpy = jest.spyOn(global, 'setTimeout');
      
      // First attempt (should be around base delay)
      reconnection.scheduleReconnection(0, mockOnAttempt, mockOnReconnect);
      const firstDelay = setTimeoutSpy.mock.calls[0][1];
      expect(firstDelay).toBeGreaterThanOrEqual(1000); // Base delay
      expect(firstDelay).toBeLessThan(2000); // Base delay + max jitter
      
      jest.runOnlyPendingTimers();
      setTimeoutSpy.mockClear();
      
      // Second attempt (should be around 2x base delay)
      reconnection.scheduleReconnection(1, mockOnAttempt, mockOnReconnect);
      const secondDelay = setTimeoutSpy.mock.calls[0][1];
      expect(secondDelay).toBeGreaterThanOrEqual(2000); // 2x base delay
      expect(secondDelay).toBeLessThan(3000); // 2x base delay + max jitter
    });
    
    it('should cap delay at maxReconnectInterval', () => {
      const setTimeoutSpy = jest.spyOn(global, 'setTimeout');
      
      // Try a high attempt count that would exceed max interval
      reconnection.scheduleReconnection(10, mockOnAttempt, mockOnReconnect);
      const delay = setTimeoutSpy.mock.calls[0][1];
      
      // Should be capped at maxReconnectInterval (plus jitter)
      expect(delay).toBeGreaterThanOrEqual(10000);
      expect(delay).toBeLessThan(11000); // max delay + max jitter
    });
  });
  
  describe('cancelReconnection', () => {
    it('should cancel pending reconnection', () => {
      const clearTimeoutSpy = jest.spyOn(global, 'clearTimeout');
      
      reconnection.scheduleReconnection(0, mockOnAttempt, mockOnReconnect);
      reconnection.cancelReconnection();
      
      expect(clearTimeoutSpy).toHaveBeenCalled();
      
      // Timer should be cancelled
      jest.runAllTimers();
      expect(mockOnReconnect).not.toHaveBeenCalled();
    });
  });
  
  describe('hasReachedMaxAttempts', () => {
    it('should return false when attempts are under the limit', () => {
      expect(reconnection.hasReachedMaxAttempts(4)).toBe(false);
    });
    
    it('should return true when attempts reach or exceed the limit', () => {
      expect(reconnection.hasReachedMaxAttempts(5)).toBe(true);
      expect(reconnection.hasReachedMaxAttempts(6)).toBe(true);
    });
  });
  
  describe('integration with WebSocketService', () => {
    let service: any;
    
    beforeEach(() => {
      // Create a partial mock of WebSocketService for testing
      service = {
        connectionCount: 1,
        isReconnecting: false,
        connect: jest.fn().mockResolvedValue(undefined),
        updateState: jest.fn(),
        getState: jest.fn().mockReturnValue({
          reconnectAttempts: 0,
        }),
      };
    });
    
    it('should not increment connection count during reconnection', async () => {
      // Save initial connection count
      const initialCount = service.connectionCount;
      
      // Schedule reconnection
      reconnection.scheduleReconnection(
        0,
        (attempts) => service.updateState({ reconnectAttempts: attempts }),
        async () => {
          service.isReconnecting = true;
          await service.connect();
          service.isReconnecting = false;
        }
      );
      
      // Fast forward to trigger reconnect
      jest.runOnlyPendingTimers();
      await Promise.resolve(); // Let the async reconnect complete
      
      // Verify service.connect was called
      expect(service.connect).toHaveBeenCalledTimes(1);
      
      // Connection count should remain the same
      expect(service.connectionCount).toBe(initialCount);
    });
    
    it('should not attempt reconnection when destroyed', () => {
      reconnection.destroy();
      
      reconnection.scheduleReconnection(0, mockOnAttempt, mockOnReconnect);
      
      jest.runAllTimers();
      
      expect(mockOnReconnect).not.toHaveBeenCalled();
    });
  });
}); 