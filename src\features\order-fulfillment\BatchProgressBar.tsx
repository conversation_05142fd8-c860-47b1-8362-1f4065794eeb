import React from 'react';

interface BatchProgressBarProps {
  total: number;
  completed: number;
}

export const BatchProgressBar: React.FC<BatchProgressBarProps> = ({ total, completed }) => {
  const percentage = total === 0 ? 0 : Math.round((completed / total) * 100);
  


  return (
    <div className="w-full bg-gray-200 rounded-full h-2.5 overflow-hidden">
      <div 
        className="bg-blue-600 h-2.5 transition-all duration-300"
        style={{ width: `${percentage}%` }}
        role="progressbar"
        aria-valuenow={percentage}
        aria-valuemin={0}
        aria-valuemax={100}
      />
    </div>
  );
}; 