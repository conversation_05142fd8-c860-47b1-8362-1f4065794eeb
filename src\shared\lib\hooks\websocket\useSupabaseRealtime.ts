import { useEffect, useRef, useCallback } from 'react';
import { supabase } from '../../../../../supabase/supabase_client/client';
import { RealtimeChannel } from '@supabase/supabase-js';

interface PostgresChangesPayload<T> {
  schema: string;
  table: string;
  commit_timestamp: string;
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new: T;
  old: T;
  errors: any;
}

type SupabaseRealtimeCallback<T = Record<string, any>> = (payload: PostgresChangesPayload<T>) => void;

/**
 * Custom hook for subscribing to Supabase Realtime database changes
 * 
 * @param tableName The database table to subscribe to
 * @param event The event type ('INSERT', 'UPDATE', 'DELETE', or '*' for all)
 * @param callback The callback function to execute when an event occurs
 * @param schema The database schema (defaults to 'public')
 * @param filter Optional filter object to filter events by column values
 * @returns void
 * 
 * @example
 * // Basic usage to listen for all changes to the orders table:
 * useSupabaseRealtime('orders', '*', (payload) => {
 *   console.log('Order changed:', payload);
 * });
 * 
 * // Listen only for new orders:
 * useSupabaseRealtime('orders', 'INSERT', (payload) => {
 *   console.log('New order:', payload.new);
 * });
 * 
 * // Listen for updates to a specific order:
 * useSupabaseRealtime('orders', 'UPDATE', (payload) => {
 *   console.log('Order updated:', payload.new);
 * }, 'public', { id: 'eq.123' });
 */
export function useSupabaseRealtime<T = Record<string, any>>(
  tableName: string,
  event: 'INSERT' | 'UPDATE' | 'DELETE' | '*',
  callback: SupabaseRealtimeCallback<T>,
  schema: string = 'public',
  filter?: Record<string, string>,
  debug: boolean = false
): void {
  // Store the channel reference
  const channelRef = useRef<RealtimeChannel | null>(null);
  const callbackRef = useRef(callback);
  const retryTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const attemptRef = useRef(0);
  const isSubscribingRef = useRef(false);
  const filterStringRef = useRef(JSON.stringify(filter || {}));

  // Update the callback ref when it changes
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  // Update the filter string ref when filter changes
  useEffect(() => {
    filterStringRef.current = JSON.stringify(filter || {});
  }, [filter]);

  // Stable function to handle subscription setup
  const setupSubscription = useCallback(() => {
    if (isSubscribingRef.current) return;
    isSubscribingRef.current = true;

    if (debug) console.log(`Setting up Supabase Realtime subscription for ${schema}.${tableName} (${event})`);
    
    try {
      // Only create a channel if one doesn't exist yet
      if (!channelRef.current) {
        // Create a stable channel name without timestamps
        const stableChannelName = `${schema}_${tableName}_${event}_${Math.random().toString(36).slice(2, 7)}`;
        
        // Create the channel
        const channel = supabase.channel(stableChannelName);
        
        // Use the correct type for the callback
        const wrappedCallback = (payload: any) => {
          callbackRef.current(payload as PostgresChangesPayload<T>);
        };
        
        // Use type assertion to work around TypeScript limitations with the Supabase API
        // This is safe because we know the Supabase API supports 'postgres_changes'
        (channel as any).on(
          'postgres_changes', 
          { event, schema, table: tableName, filter },
          wrappedCallback
        );
        
        // Subscribe to the channel
        channel.subscribe((status) => {
          if (debug) console.log(`Supabase Realtime subscription status for ${schema}.${tableName}:`, status);
        });
        
        // Store the channel reference
        channelRef.current = channel;
      }
      
      isSubscribingRef.current = false;
      
    } catch (error) {
      console.error(`Error setting up Supabase Realtime for ${schema}.${tableName}:`, error);
      isSubscribingRef.current = false;
      
      // Retry on error
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      
      retryTimeoutRef.current = setTimeout(() => {
        attemptRef.current += 1;
        setupSubscription();
      }, 2000);
    }
  }, [tableName, event, schema, debug]);
  
  // Set up subscription on mount
  useEffect(() => {
    setupSubscription();
    
    // Clean up on unmount
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      
      if (channelRef.current) {
        if (debug) console.log(`Removing Supabase Realtime subscription for ${schema}.${tableName}`);
        try {
          supabase.removeChannel(channelRef.current);
        } catch (err) {
          console.error(`Error removing channel for ${schema}.${tableName}:`, err);
        }
        channelRef.current = null;
      }
    };
  }, [tableName, event, schema, setupSubscription, debug]);
}

export default useSupabaseRealtime; 