/**
 * TypeScript definitions for Deno APIs
 * This resolves "Cannot find name '<PERSON><PERSON>'" errors in Edge Functions
 */

declare namespace Deno {
  namespace env {
    function get(key: string): string | undefined;
    function set(key: string, value: string): void;
  }

  function serve(handler: (req: Request) => Promise<Response> | Response): void;

  interface DeployOptions {
    hostname?: string;
    port?: number;
    signal?: AbortSignal;
  }
} 