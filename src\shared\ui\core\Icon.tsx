import React from 'react';
import { iconMap } from '@/shared/lib/utils/core/icon-map';
import { cn } from '@/shared/lib/utils/core/cn';
import { IconBaseProps } from 'react-icons';

interface IconProps extends IconBaseProps {
  platform: string;
  variant?: 'circle' | 'none';
}

export const Icon: React.FC<IconProps> = ({ platform, className, variant = 'circle', ...props }) => {
  // Normalize platform name to lowercase and handle edge cases
  const normalizedPlatform = platform?.toLowerCase().trim() || '';
  const IconComponent =
    iconMap.platformIconMap[normalizedPlatform] ||
    iconMap.carrierIconMap[normalizedPlatform] ||
    iconMap.ActionIcons[normalizedPlatform] ||
    iconMap.DefaultIcon;

  if (variant === 'circle') {
    return (
      <span className={cn(
        'flex items-center justify-center rounded-full bg-white shadow border border-gray-200 h-10 w-10 p-1.5',
        className 
      )}>
        <IconComponent className="h-full w-full text-gray-700" {...props} />
      </span>
    );
  }

  // No changes needed for the non-circle variant, but you could apply a similar pattern if you wish.
  return <IconComponent className={cn('h-6 w-6', className)} {...props} />;
};