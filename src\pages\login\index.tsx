
import { useState, FormEvent } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../../../supabase/supabase_client/client';
import Modal from '@/shared/ui/overlay/Modal';
import { PaperAirplaneIcon } from '@/shared/config';

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);
    setIsLoading(true);

    try {
      const emailToCheck = email.trim().toLowerCase();
      
      // TEMPORARY: Skip user table check and just send magic link
      // We'll fix the user validation later
  
      
      const { error: authError } = await supabase.auth.signInWithOtp({
        email: emailToCheck,
        options: {
          shouldCreateUser: true,
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        }
      });

      if (authError) {
        console.error('Auth error:', authError);
        setError(`Login failed: ${authError.message}`);
        setIsModalOpen(true);
      } else {
        // Success message - user should check their email
        setError('Check your email for the login link!');
        setIsModalOpen(true);
      }
    } catch (err) {
      console.error('Unexpected error:', err);
      setError('An unexpected error occurred. Please try again.');
      setIsModalOpen(true);
    }

    setIsLoading(false);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-main-bg py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full bg-card-bg p-8 sm:p-10 rounded-xl shadow-xl border border-gray-200">
        <div className="space-y-6">
          <div>
            <h1 className="text-center text-4xl font-bold text-gray-900">
              WAS holdings
            </h1>
            <p className="mt-3 text-center text-sm text-gray-600">
              Enter your email to sign in.
            </p>
          </div>
          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            <div>
              <label htmlFor="email-address" className="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <input
                id="email-address"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="appearance-none rounded-lg relative block w-full px-4 py-3 sm:text-sm bg-white text-gray-900 placeholder-gray-400 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-login-button-bg focus:border-login-button-bg"
                placeholder="e.g., <EMAIL>"
                disabled={isLoading}
              />
            </div>

            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="group relative w-full flex items-center justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-login-button-bg hover:bg-login-button-hover-bg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-login-button-bg disabled:bg-gray-400 transition-colors"
              >
                <PaperAirplaneIcon className="h-5 w-5 mr-2 transform -rotate-45 text-white" aria-hidden="true" />
                {isLoading ? 'Signing in...' : 'Continue'}
                <span className="ml-2 text-white" aria-hidden="true">&rarr;</span>
              </button>
            </div>
          </form>
          <div className="text-center mt-4">
            <a href="#" className="text-sm text-gray-500 hover:text-gray-700 hover:underline">
              Privacy Policy
            </a>
          </div>
        </div>
      </div>
      <div className="pt-8 pb-4 text-center text-xs text-gray-500">
        <p>&copy; {new Date().getFullYear()} Washolding. All rights reserved.</p>
        <p>For internal use only.</p>
      </div>
      <Modal isOpen={isModalOpen} onClose={closeModal} title="Login Failed">
        <p>{error}</p>
      </Modal>
    </div>
  );
};

export default LoginPage;