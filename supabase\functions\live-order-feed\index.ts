import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.12.0';
import { corsHeaders } from '../_shared/cors.ts';

// Define WebSocket state constants since they might not be available in the Edge Function environment
const WebSocketStates = {
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3
};

// WebSocket connections storage
const clients = new Set<WebSocket>();

// Function to clean up stale connections
function cleanupStaleConnections() {
  let removedCount = 0;
  
  for (const client of clients) {
    if (client.readyState === WebSocketStates.CLOSING || client.readyState === WebSocketStates.CLOSED) {
      clients.delete(client);
      removedCount++;
    }
  }
  
  if (removedCount > 0) {
    console.log(`Cleaned up ${removedCount} stale WebSocket connections. Remaining: ${clients.size}`);
  }
  
  return removedCount;
}

// Set up periodic cleanup every 60 seconds
setInterval(cleanupStaleConnections, 60000);

// Create a Supabase client for auth verification and Realtime subscriptions
const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Create a separate Supabase client for Realtime subscriptions
const realtimeClient = createClient(supabaseUrl, supabaseServiceKey, {
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
});

// Set up Supabase Realtime subscription
let realtimeChannel: any = null;

function setupRealtimeSubscription() {
  if (realtimeChannel) {
    realtimeClient.removeChannel(realtimeChannel);
  }

  console.log('Setting up Supabase Realtime subscription');
  
  realtimeChannel = realtimeClient.channel('db-changes')
    .on('postgres_changes', 
      { event: '*', schema: 'public', table: 'orders' },
      (payload) => {
        console.log('Received database change (orders):', payload);
        console.log('Orders payload details:', {
          eventType: payload.eventType,
          id: payload.new?.id || payload.old?.id,
          columns: payload.new ? Object.keys(payload.new) : [],
          changedFields: payload.new && payload.old ? 
            Object.keys(payload.new).filter(key => 
              JSON.stringify(payload.new[key]) !== JSON.stringify(payload.old[key])
            ) : []
        });
        
        // Always broadcast order changes
        broadcastToClients('order_update', payload);
        
        // Broadcast a specific order_changes event for better client handling
        broadcastToClients('order_changes', {
          type: payload.eventType,
          table: 'orders',
          id: payload.new?.id || payload.old?.id,
          data: payload.new || payload.old
        });
      }
    )
    .on('postgres_changes', 
      { event: '*', schema: 'public', table: 'order_items' },
      (payload) => {
        console.log('Received database change (order_items):', payload);
        console.log('Order items payload details:', {
          eventType: payload.eventType,
          orderId: payload.new?.order_id || payload.old?.order_id,
          columns: payload.new ? Object.keys(payload.new) : []
        });
        
        // Broadcast order_items changes
        broadcastToClients('order_items_update', payload);
        
        // Also broadcast a generic order_changes event with the parent order ID
        const orderId = payload.new?.order_id || payload.old?.order_id;
        if (orderId) {
          broadcastToClients('order_changes', {
            type: 'CHILD_UPDATE',
            table: 'order_items',
            id: orderId, // This is the parent order ID
            childId: payload.new?.id || payload.old?.id,
            data: payload.new || payload.old
          });
        }
      }
    )
    .subscribe((status) => {
      console.log('Realtime subscription status:', status);
    });
}

// Function to check if a WebSocket is valid and connected
function isValidWebSocket(ws: WebSocket): boolean {
  try {
    return ws && ws.readyState === WebSocketStates.OPEN;
  } catch (e) {
    return false;
  }
}

// Function to broadcast messages to all connected clients
function broadcastToClients(type: string, payload: any) {
  const message = {
    type,
    payload,
    timestamp: new Date().toISOString()
  };
  
  const serialized = JSON.stringify(message);
  let sentCount = 0;
  
  // Clean up stale connections before broadcasting
  cleanupStaleConnections();
  
  // If no clients are connected, don't bother serializing and sending
  if (clients.size === 0) {
    console.log(`No clients connected, skipping broadcast of ${type}`);
    return 0;
  }
  
  // Check for clients in OPEN state before sending
  for (const client of clients) {
    try {
      // Use our helper function to check if the WebSocket is valid and connected
      if (isValidWebSocket(client)) {
        try {
          client.send(serialized);
          sentCount++;
        } catch (sendError) {
          console.error('Failed to send message to client:', sendError);
          // The connection might be broken, so remove this client
          clients.delete(client);
        }
      } else {
        try {
          console.log(`Client in non-OPEN state (${client.readyState}), skipping broadcast`);
        } catch (e) {
          console.log('Could not determine client state, removing from clients list');
        }
        // Remove invalid clients
        clients.delete(client);
      }
    } catch (err) {
      console.error('Error accessing client:', err);
      // If we can't even access the client properties, it's safer to remove it
      clients.delete(client);
    }
  }
  
  console.log(`Broadcast ${type} to ${sentCount}/${clients.size} clients`);
  return sentCount;
}

// Initialize Realtime subscription
setupRealtimeSubscription();

serve(async (req) => {
  // Add CORS headers to all responses
  const headers = {
    ...corsHeaders
  };

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers
    });
  }

  // Check if it's a WebSocket connection request
  if (req.headers.get('upgrade')?.toLowerCase() === 'websocket') {
    try {
      // Authentication can be done via URL query params or headers
      const url = new URL(req.url);
      
      // Try to get JWT from query params (better for WebSocket which can't easily set headers)
      let jwt = url.searchParams.get("jwt");
      
      // If no JWT in query params, try to get it from Sec-WebSocket-Protocol
      if (!jwt) {
        // WebSocket protocols might contain the JWT (format: 'jwt-TOKEN, other-protocols')
        const protocols = (req.headers.get("Sec-WebSocket-Protocol") || '').split(',').map((p) => p.trim());
        const jwtProtocol = protocols.find((p) => p.startsWith('jwt-'));
        if (jwtProtocol) {
          jwt = jwtProtocol.replace('jwt-', '');
        }
      }
      
      // If still no JWT, try the Authorization header (less reliable for WebSockets)
      if (!jwt) {
        const authHeader = req.headers.get('Authorization');
        if (authHeader && authHeader.startsWith('Bearer ')) {
          jwt = authHeader.slice(7);
        }
      }
      
      // Skip auth in development if needed - remove this in production!
      const skipAuth = Deno.env.get("SKIP_AUTH") === "true";
      
      // Verify the token if provided and auth isn't skipped
      if (jwt && !skipAuth) {
        const { error } = await supabase.auth.getUser(jwt);
        if (error) {
          console.error('Auth error:', error.message);
          return new Response("Authentication failed", {
            status: 401,
            headers: {
              ...headers,
              'Content-Type': 'text/plain'
            }
          });
        }
      } else if (!skipAuth) {
        // No auth token provided and auth is not skipped
        console.log('No authentication token provided for WebSocket connection');
      }

      // Upgrade the connection to WebSocket
      const { socket, response } = Deno.upgradeWebSocket(req);

      // WebSocket connection handlers
      socket.onopen = () => {
        clients.add(socket);
        console.log(`WebSocket client connected (total: ${clients.size})`);
        
        // Send connection acknowledgment
        const ackMessage = {
          type: 'connection_ack',
          payload: {
            message: 'Connected successfully'
          },
          timestamp: new Date().toISOString()
        };
        
        try {
          // Only send if socket is valid and connected
          if (isValidWebSocket(socket)) {
            socket.send(JSON.stringify(ackMessage));
          }
        } catch (err) {
          console.error('Error sending ack message:', err);
        }
        
        // Start heartbeat interval for this connection
        const heartbeatInterval = setInterval(() => {
          // Only send if socket is valid and connected
          if (isValidWebSocket(socket)) {
            try {
              const heartbeat = {
                type: 'heartbeat',
                payload: {
                  timestamp: new Date().toISOString()
                },
                timestamp: new Date().toISOString()
              };
              socket.send(JSON.stringify(heartbeat));
            } catch (err) {
              console.error('Error sending heartbeat:', err);
              clearInterval(heartbeatInterval);
              clients.delete(socket); // Remove invalid client
            }
          } else {
            clearInterval(heartbeatInterval);
            clients.delete(socket); // Remove invalid client
          }
        }, 30000); // 30 seconds
      };

      socket.onmessage = async (event) => {
        try {
          // Process incoming message
          const message = JSON.parse(event.data);
          console.log('Received message:', message);
          
                      // Handle specific message types
            if (message.type === 'ping') {
              // Only send if socket is valid and connected
              if (isValidWebSocket(socket)) {
                try {
                  socket.send(JSON.stringify({
                    type: 'pong',
                    timestamp: new Date().toISOString()
                  }));
                } catch (err) {
                  console.error('Error sending pong response:', err);
                  clients.delete(socket); // Remove invalid client
                }
              }
            } 
            // Example: Echo message back (for testing)
            else if (isValidWebSocket(socket)) {
              try {
                socket.send(event.data);
              } catch (err) {
                console.error('Error echoing message:', err);
                clients.delete(socket); // Remove invalid client
              }
            }
        } catch (e) {
          console.error('Error processing message:', e);
        }
      };

      socket.onclose = (event) => {
        clients.delete(socket);
        console.log(`WebSocket client disconnected (code: ${event.code}, reason: ${event.reason || 'No reason provided'}) (remaining: ${clients.size})`);
      };

      socket.onerror = (error) => {
        console.error('WebSocket error:', error);
        try {
          // Try to close the socket gracefully
          socket.close(1011, 'Error occurred');
        } catch (e) {
          console.error('Error closing WebSocket after error:', e);
        } finally {
          clients.delete(socket);
        }
      };

      return response;
    } catch (err) {
      console.error('Error handling WebSocket connection:', err);
      return new Response(`WebSocket connection error: ${err.message}`, {
        status: 500,
        headers: {
          ...headers,
          'Content-Type': 'text/plain'
        }
      });
    }
  }

  // For non-WebSocket requests, provide API endpoints to trigger broadcasts
  // Example: POST endpoint to broadcast order updates to all connected clients
  if (req.method === 'POST') {
    try {
      const { type, payload } = await req.json();
      
      if (!type || !payload) {
        return new Response(JSON.stringify({
          error: 'Missing required fields'
        }), {
          status: 400,
          headers: {
            ...headers,
            'Content-Type': 'application/json'
          }
        });
      }
      
      // Use the broadcast function
      const sentCount = broadcastToClients(type, payload);
      
      return new Response(JSON.stringify({
        success: true,
        sent: sentCount,
        clients: clients.size
      }), {
        status: 200,
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        }
      });
    } catch (error) {
      console.error('Error processing broadcast request:', error);
      return new Response(JSON.stringify({
        error: error.message
      }), {
        status: 500,
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        }
      });
    }
  }

  // GET endpoint to check server status and restart Realtime subscription if needed
  if (req.method === 'GET' && new URL(req.url).pathname.endsWith('/restart-realtime')) {
    setupRealtimeSubscription();
    return new Response(JSON.stringify({
      success: true,
      message: 'Realtime subscription restarted'
    }), {
      status: 200,
      headers: {
        ...headers,
        'Content-Type': 'application/json'
      }
    });
  }

  // Default response for other HTTP methods
  return new Response(JSON.stringify({
    status: 'WebSocket server active',
    connections: clients.size,
    realtimeActive: realtimeChannel !== null,
    endpoints: {
      websocket: 'Connect with WebSocket protocol',
      post: 'Send a message to broadcast to all clients',
      'get /restart-realtime': 'Restart the Realtime subscription'
    }
  }), {
    status: 200,
    headers: {
      ...headers,
      'Content-Type': 'application/json'
    }
  });
}); 