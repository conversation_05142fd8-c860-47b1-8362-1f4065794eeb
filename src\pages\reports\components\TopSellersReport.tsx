import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  Tooltip,
  // Legend, // Legend is available if needed
} from 'recharts';
import { TopSellerData, DropdownOption } from '@/types'; // Adjusted path
import { ArrowUpIcon, ArrowDownIcon, ClockIcon } from '@/shared/config'; // Adjusted path
import { Icon } from '@/shared/ui/core/Icon';
import Dropdown from '@/shared/ui/input/Dropdown'; // Adjusted path
// import { topSellersDropdownOptions } from '@/shared/lib/mock-data/reports'; // TODO: Verify path or create mock data. Original path was ../../lib/mock-data/reports which seems incorrect after move.

interface TopSellersReportProps {
  data: TopSellerData[]; // Data is still passed as a prop
}

const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="bg-white p-2 border border-gray-300 shadow-lg rounded-md">
        <p className="font-semibold">{`${data.store}`}</p>
        <p className="text-sm text-gray-700">{`Sales: ${formatCurrency(data.sales)} (${data.salesPercentage.toFixed(2)}%)`}</p>
      </div>
    );
  }
  return null;
};

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(value);
};

const TopSellersReport: React.FC<TopSellersReportProps> = ({ data }) => {
  // State for dropdowns and time, moved from ReportsView
  const [currentTime, setCurrentTime] = useState(new Date());
  // TODO: topSellersDropdownOptions import is commented out, so these need default values or to be re-enabled once mock data is sorted.
  const [selectedStoreFilter, setSelectedStoreFilter] = useState<DropdownOption | null>(null);
  const [selectedMetric, setSelectedMetric] = useState<DropdownOption | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<DropdownOption | null>(null);
  const [selectedExtraFilter, setSelectedExtraFilter] = useState<DropdownOption | null>(null);

  useEffect(() => {
    const timerId = setInterval(() => setCurrentTime(new Date()), 60000); // Update time every minute
    return () => clearInterval(timerId);
  }, []);

  const formattedTime = currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

  // TODO: Implement filtering of `data` based on selectedStoreFilter, selectedMetric, etc.
  // For now, the passed `data` prop is used directly.
  const displayedData = data; 

  return (
    <div className="bg-card-bg p-4 sm:p-6 rounded-lg shadow">
      {/* Header for Top Sellers card (moved from ReportsView) */}
      <div className="flex flex-col sm:flex-row justify-between items-center mb-4">
        <h3 className="text-xl font-semibold text-gray-800 mb-3 sm:mb-0">Top Sellers</h3>
        <div className="flex flex-wrap items-center gap-2 w-full sm:w-auto">
          <Dropdown
            options={[] /* topSellersDropdownOptions.stores */} 
            selectedOption={selectedStoreFilter}
            onSelect={setSelectedStoreFilter}
            className="w-full min-w-[150px] sm:w-auto"
            buttonClassName="bg-gray-100 border-gray-300 hover:bg-gray-200 text-gray-700 rounded-md px-3 py-2 text-sm"
          />
          <Dropdown
            options={[] /* topSellersDropdownOptions.metric */} 
            selectedOption={selectedMetric}
            onSelect={setSelectedMetric}
            className="w-full min-w-[120px] sm:w-auto"
            buttonClassName="bg-gray-100 border-gray-300 hover:bg-gray-200 text-gray-700 rounded-md px-3 py-2 text-sm"
          />
          <Dropdown
            options={[] /* topSellersDropdownOptions.period */} 
            selectedOption={selectedPeriod}
            onSelect={setSelectedPeriod}
            className="w-full min-w-[220px] sm:w-auto"
            buttonClassName="bg-gray-100 border-gray-300 hover:bg-gray-200 text-gray-700 rounded-md px-3 py-2 text-sm"
          />
          <span className="text-sm text-gray-500 hidden md:inline">with</span>
          <Dropdown
            options={[] /* topSellersDropdownOptions.filters */} 
            selectedOption={selectedExtraFilter}
            onSelect={setSelectedExtraFilter}
            className="w-full min-w-[130px] sm:w-auto"
            buttonClassName="bg-gray-100 border-gray-300 hover:bg-gray-200 text-gray-700 rounded-md px-3 py-2 text-sm"
          />
          <div className="flex items-center text-sm text-gray-500 ml-auto sm:ml-2 pt-1 sm:pt-0">
            <ClockIcon className="w-4 h-4 mr-1" />
            {formattedTime}
          </div>
        </div>
      </div>

      {/* Existing Chart and Table */}
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Donut Chart */}
        <div className="w-full lg:w-1/3 h-72 lg:h-auto flex justify-center items-center">
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={displayedData} // Use displayedData
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={150}
                innerRadius={60}
                fill="#8884d8"
                dataKey="sales"
                nameKey="store"
                paddingAngle={1}
              >
                {displayedData.map((entry, index) => ( // Use displayedData
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Data Table */}
        <div className="w-full lg:w-2/3 overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Store</th>
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Channel</th>
                <th scope="col" className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Sales</th>
                <th scope="col" className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Orders</th>
                <th scope="col" className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Units</th>
                <th scope="col" className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Change in Sales</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {displayedData.map((item) => ( // Use displayedData
                <tr key={item.id} className="hover:bg-gray-50">
                  <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <span style={{ backgroundColor: item.color }} className="w-3 h-3 rounded-sm mr-2 shrink-0"></span>
                      {item.store}
                    </div>
                  </td>
                  <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center">
                      <Icon platform={item.platform} className="mr-1.5 h-4 w-4" />
                      {item.country}
                    </div>
                  </td>
                  <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                    {formatCurrency(item.sales)}
                    <span className="ml-1 text-xs text-gray-400 block sm:inline">({item.salesPercentage.toFixed(2)}%)</span>
                  </td>
                  <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                    {item.orders.toLocaleString()}
                    <span className="ml-1 text-xs text-gray-400 block sm:inline">({item.ordersPercentage.toFixed(2)}%)</span>
                  </td>
                  <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                    {item.units.toLocaleString()}
                    <span className="ml-1 text-xs text-gray-400 block sm:inline">({item.unitsPercentage.toFixed(2)}%)</span>
                  </td>
                  <td className="px-3 py-4 whitespace-nowrap text-sm text-right">
                    <span className={`flex items-center justify-end ${item.changeInSalesValue >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {item.changeInSalesValue >= 0 ? (
                        <ArrowUpIcon className="w-3 h-3 mr-0.5" />
                      ) : (
                        <ArrowDownIcon className="w-3 h-3 mr-0.5" />
                      )}
                      {formatCurrency(Math.abs(item.changeInSalesValue))} {/* Show absolute for currency */}
                      <span className={`ml-1 text-xs ${item.changeInSalesValue >= 0 ? 'text-green-500' : 'text-red-500'} block sm:inline`}>
                        ({item.changeInSalesPercentage > 0 ? '+' : ''}{item.changeInSalesPercentage}%)
                      </span>
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
           {displayedData.length === 0 && ( // Use displayedData
            <p className="text-center text-gray-500 py-8 col-span-full">No top seller data available for the selected filters.</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default TopSellersReport;