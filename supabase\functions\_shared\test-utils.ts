// @ts-nocheck
// Test utilities for Edge Function testing

/**
 * Creates a mock request object for testing Edge Functions
 */
export function createMockRequest({
  method = 'GET',
  url = 'https://example.com',
  headers = {},
  body = null,
}: {
  method?: string;
  url?: string;
  headers?: Record<string, string>;
  body?: string | null;
} = {}): Request {
  const headersObj = new Headers();
  Object.entries(headers).forEach(([key, value]) => {
    headersObj.append(key, value);
  });

  return {
    method,
    url,
    headers: headersObj,
    json: async () => body ? JSON.parse(body) : {},
  } as unknown as Request;
}

/**
 * Creates a mock Supabase client for testing
 */
export function createMockSupabaseClient({
  fromResponse = { data: null, error: null },
  updateResponse = { data: null, error: null },
  insertResponse = { data: null, error: null },
  deleteResponse = { data: null, error: null },
  upsertResponse = { data: null, error: null },
}: {
  fromResponse?: { data: any; error: any };
  updateResponse?: { data: any; error: any };
  insertResponse?: { data: any; error: any };
  deleteResponse?: { data: any; error: any };
  upsertResponse?: { data: any; error: any };
} = {}) {
  // Create basic mock structure
  const mockClient = {
    from: () => ({
      select: () => ({
        eq: () => ({
          single: () => Promise.resolve(fromResponse),
          maybeSingle: () => Promise.resolve(fromResponse),
          ...fromResponse
        }),
        in: () => ({
          select: () => Promise.resolve(fromResponse),
          ...fromResponse
        }),
        range: () => Promise.resolve(fromResponse),
        order: () => Promise.resolve(fromResponse),
        limit: () => Promise.resolve(fromResponse),
        ...fromResponse
      }),
      update: () => ({
        eq: () => ({
          select: () => ({
            single: () => Promise.resolve(updateResponse)
          }),
          ...updateResponse
        }),
        in: () => ({
          select: () => Promise.resolve(updateResponse),
          ...updateResponse
        }),
        ...updateResponse
      }),
      insert: () => ({
        select: () => ({
          single: () => Promise.resolve(insertResponse),
          ...insertResponse
        }),
        ...insertResponse
      }),
      delete: () => ({
        eq: () => Promise.resolve(deleteResponse),
        in: () => Promise.resolve(deleteResponse),
        ...deleteResponse
      }),
      upsert: () => ({
        select: () => ({
          single: () => Promise.resolve(upsertResponse),
          ...upsertResponse
        }),
        ...upsertResponse
      })
    })
  };

  return mockClient;
}

/**
 * Mock response for testing
 */
export class MockResponse {
  status: number;
  headers: Headers;
  body: string;

  constructor(body: string, options: ResponseInit = {}) {
    this.body = body;
    this.status = options.status || 200;
    this.headers = new Headers(options.headers);
  }

  json() {
    return JSON.parse(this.body);
  }

  text() {
    return this.body;
  }
}

/**
 * Creates a mock Edge Function context
 */
export function createMockContext(overrides: Record<string, any> = {}) {
  return {
    env: {
      SUPABASE_URL: 'https://mock.supabase.co',
      SUPABASE_SERVICE_ROLE_KEY: 'mock-service-role-key',
      ...overrides.env
    },
    ...overrides
  };
} 