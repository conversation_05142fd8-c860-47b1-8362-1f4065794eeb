import React from 'react';
import { useProductIdentifiers } from '@/shared/lib/hooks/inventory/products/useProductIdentifiers';
import { PanelLoadingSkeleton } from '@/shared/ui/feedback/LoadingSkeleton';

interface ProductPlatformIdentifiersProps {
  productId: string;
}

const ProductPlatformIdentifiers: React.FC<ProductPlatformIdentifiersProps> = ({ productId }) => {
  const {
    identifiers,
    isLoading,
    error,
    debugInfo,
    retryCount,
    retry
  } = useProductIdentifiers(productId);

  if (isLoading) {
    return (
      <section>
        <h4 className="text-lg font-medium text-gray-900 mb-3">Platform Identifiers</h4>
        <PanelLoadingSkeleton />
      </section>
    );
  }

  if (error) {
    return (
      <section>
        <h4 className="text-lg font-medium text-gray-900 mb-3">Platform Identifiers</h4>
        <div className="bg-red-50 p-4 rounded-md">
          <p className="text-red-700 mb-2">{error}</p>
          {retryCount > 0 && (
            <p className="text-sm text-gray-600 mb-2">
              Retry attempt {retryCount} of 3
            </p>
          )}
          {debugInfo && (
            <details className="mb-3 text-xs text-gray-700">
              <summary className="cursor-pointer">Debug Info</summary>
              <pre className="mt-2 p-2 bg-gray-100 rounded overflow-auto max-h-40">
                {JSON.stringify(debugInfo, null, 2)}
              </pre>
            </details>
          )}
          <button 
            onClick={retry}
            disabled={retryCount >= 3}
            className={`px-3 py-1 ${retryCount >= 3 ? 'bg-gray-400' : 'bg-red-600 hover:bg-red-700'} text-white rounded text-sm`}
          >
            {retryCount >= 3 ? 'Max retries reached' : 'Try Again'}
          </button>
        </div>
      </section>
    );
  }

  // Handle empty state
  if (identifiers.length === 0) {
    return (
      <section>
        <h4 className="text-lg font-medium text-gray-900 mb-3">Platform Identifiers</h4>
        <div className="text-gray-500 italic bg-gray-50 p-4 rounded-md">
          No platform identifiers found for this product.
        </div>
      </section>
    );
  }

  return (
    <section>
      <h4 className="text-lg font-medium text-gray-900 mb-3">Platform Identifiers</h4>
      <div className="overflow-hidden bg-white shadow-sm ring-1 ring-gray-200 sm:rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Platform
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Identifier
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Pack Size
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {identifiers.map((identifier) => (
              <tr key={identifier.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {identifier.platform_name || identifier.code_name || 'Unknown'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                  {identifier.platform_identifier}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                  {identifier.pack_size || 1}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </section>
  );
};

export default ProductPlatformIdentifiers; 
