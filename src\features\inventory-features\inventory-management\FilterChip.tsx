import React, { memo } from 'react';

interface FilterChipProps {
  label: string;
  onRemove: () => void;
}

// Custom comparison function for memoization
const areEqual = (prevProps: FilterChipProps, nextProps: FilterChipProps) => {
  return (
    prevProps.label === nextProps.label && 
    prevProps.onRemove === nextProps.onRemove
  );
};

const FilterChip: React.FC<FilterChipProps> = ({ label, onRemove }) => {
  return (
    <span className="inline-flex items-center px-2.5 py-1 sm:py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
      {label}
      <button 
        type="button"
        className="flex-shrink-0 ml-1.5 p-1 sm:p-0.5 text-blue-700 hover:text-blue-500 focus:outline-none focus:text-blue-500 rounded-full"
        onClick={onRemove}
        aria-label={`Remove ${label} filter`}
      >
        <span className="sr-only">Remove filter</span>
        <svg className="h-3 w-3 sm:h-2 sm:w-2" stroke="currentColor" fill="none" viewBox="0 0 8 8">
          <path strokeLinecap="round" strokeWidth="1.5" d="M1 1l6 6m0-6L1 7" />
        </svg>
      </button>
    </span>
  );
};

// Export memoized component with custom comparison
export default memo(FilterChip, areEqual);