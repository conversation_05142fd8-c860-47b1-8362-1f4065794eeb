import { UserProfile } from '../../../../types';

// Cache keys for localStorage
const CACHE_KEYS = {
  SESSION: 'washolding_session_cache',
  PROFILE: 'washolding_profile_cache',
  TIMESTAMP: 'washolding_cache_timestamp'
};

// Cache duration: 3 day in milliseconds
const CACHE_DURATION = 3 * 24 * 60 * 60 * 1000;

// Refresh threshold: 80% of cache duration (refresh when 80% expired)
const REFRESH_THRESHOLD = CACHE_DURATION * 0.8;

export class AuthCacheService {
  // Check if cache is still valid
  static isCacheValid(): boolean {
    const timestamp = localStorage.getItem(CACHE_KEYS.TIMESTAMP);
    if (!timestamp) return false;
    
    const cacheTime = parseInt(timestamp, 10);
    const now = Date.now();
    const isValid = (now - cacheTime) < CACHE_DURATION;
    
    return isValid;
  }

  // Check if cache should be refreshed (80% of duration passed)
  static shouldRefreshCache(): boolean {
    const timestamp = localStorage.getItem(CACHE_KEYS.TIMESTAMP);
    if (!timestamp) return true;
    
    const cacheTime = parseInt(timestamp, 10);
    const now = Date.now();
    const shouldRefresh = (now - cacheTime) > REFRESH_THRESHOLD;
    
    return shouldRefresh;
  }

  // Get cache age in hours
  static getCacheAge(): number {
    const timestamp = localStorage.getItem(CACHE_KEYS.TIMESTAMP);
    if (!timestamp) return 0;
    
    const cacheTime = parseInt(timestamp, 10);
    const now = Date.now();
    const ageInHours = (now - cacheTime) / (1000 * 60 * 60);
    
    return Math.round(ageInHours * 10) / 10; // Round to 1 decimal place
  }

  // Save session and profile to cache
  static saveToCache(session: any, profile: UserProfile | null): void {
    try {
      // Validate data before saving
      if (!session || !session.access_token || !session.user) {
        // Invalid session data
        return;
      }

      if (!profile || !profile.id || !profile.role) {
        // Invalid profile data
        return;
      }

      localStorage.setItem(CACHE_KEYS.SESSION, JSON.stringify(session));
      localStorage.setItem(CACHE_KEYS.PROFILE, JSON.stringify(profile));
      localStorage.setItem(CACHE_KEYS.TIMESTAMP, Date.now().toString());
      // Session cached successfully
    } catch (error) {
      // Failed to save to cache
    }
  }

  // Load session and profile from cache
  static loadFromCache(): { session: any; profile: UserProfile | null } | null {
    try {
      if (!this.isCacheValid()) {
        this.clearCache();
        return null;
      }

      const sessionStr = localStorage.getItem(CACHE_KEYS.SESSION);
      const profileStr = localStorage.getItem(CACHE_KEYS.PROFILE);

      if (!sessionStr || !profileStr) {
        return null;
      }

      const session = JSON.parse(sessionStr);
      const profile = JSON.parse(profileStr);

      // Validate session structure
      if (!session || typeof session !== 'object' || !session.access_token || !session.user) {
        this.clearCache();
        return null;
      }

      // Validate profile structure
      if (!profile || typeof profile !== 'object' || !profile.id || !profile.role) {
        this.clearCache();
        return null;
      }
      return { session, profile };
    } catch (error) {
      // Failed to load from cache
      this.clearCache();
      return null;
    }
  }

  // Clear all cache
  static clearCache(): void {
    localStorage.removeItem(CACHE_KEYS.SESSION);
    localStorage.removeItem(CACHE_KEYS.PROFILE);
    localStorage.removeItem(CACHE_KEYS.TIMESTAMP);
  }

  // Force cache expiration (for manual refresh)
  static expireCache(): void {
    const expiredTimestamp = (Date.now() - CACHE_DURATION - 1000).toString();
    localStorage.setItem(CACHE_KEYS.TIMESTAMP, expiredTimestamp);
  }

  // Get cache status information
  static getCacheStatus(): {
    isValid: boolean;
    shouldRefresh: boolean;
    ageInHours: number;
    expiresInHours: number;
  } {
    const isValid = this.isCacheValid();
    const shouldRefresh = this.shouldRefreshCache();
    const ageInHours = this.getCacheAge();
    const maxAgeInHours = CACHE_DURATION / (1000 * 60 * 60);
    const expiresInHours = Math.max(0, maxAgeInHours - ageInHours);

    return {
      isValid,
      shouldRefresh,
      ageInHours,
      expiresInHours: Math.round(expiresInHours * 10) / 10
    };
  }
} 