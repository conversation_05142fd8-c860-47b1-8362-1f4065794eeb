import { notificationService } from '../notification/notification-service';

export interface CsvProcessingResult {
  success: boolean;
  data?: any;
  message?: string;
  processedCount?: number;
  failedCount?: number;
  processedOrders?: Array<{
    order_id: string;
    order_number?: string;
    tracking_number: string;
  }>;
}

/**
 * Display appropriate notification for CSV processing result
 */
export const showCsvProcessingNotification = (result: CsvProcessingResult): void => {
  if (result.success) {
    // Get the number of orders processed
    const count = result.processedCount || (result.processedOrders?.length) || (result.data && result.data.length) || 'All';
    
    // Create detailed success message with order numbers
    let detailedMessage = '';
    
    if (result.processedOrders && result.processedOrders.length > 0) {
      // Show the first message line
      detailedMessage = `Successfully assigned ${count} orders:\n`;
      
      // Add each order number to the message, limiting to 10 for readability
      const ordersToShow = result.processedOrders.slice(0, 10);
      ordersToShow.forEach(order => {
        // Only show order_number and ensure it's not undefined
        const displayValue = order.order_number || order.order_id;
        detailedMessage += `${displayValue}\n`;
      });
      
      // Add a note if there are more orders than shown
      if (result.processedOrders.length > 10) {
        detailedMessage += `...and ${result.processedOrders.length - 10} more`;
      }
    } else {
      detailedMessage = result.message || `Successfully processed ${count} orders.`;
    }
    
    notificationService.success(detailedMessage, 'CSV Processing Complete');
  } else {
    // Show error notification
    const message = result.message || 'Failed to process CSV file.';
    notificationService.error(message, 'CSV Processing Failed');
  }
};

/**
 * Display appropriate notification for CSV validation error
 */
export const showCsvValidationError = (error: string): void => {
  notificationService.error(error, 'CSV Validation Failed');
};

/**
 * Display CSV parsing error notification
 */
export const showCsvParsingError = (error: Error): void => {
  notificationService.error(error.message, 'CSV Parsing Failed');
};

export default {
  showCsvProcessingNotification,
  showCsvValidationError,
  showCsvParsingError
}; 