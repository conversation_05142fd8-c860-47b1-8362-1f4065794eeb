import React, { useCallback, useMemo, memo } from 'react';
import { DeliveryTrackingViewItem } from '@/types';
import { DocumentDuplicateIcon } from '@heroicons/react/24/outline';
import { Icon } from '@/shared/ui/core/Icon';
import { getStatusBadge } from '@/shared/lib/utils/domain/delivery/delivery-table-utils';

interface DeliveryRowProps {
  delivery: DeliveryTrackingViewItem;
  isSelected: boolean;
  onRowClick: (deliveryId: string) => void;
  onRowCheckboxChange: (deliveryId: string) => void;
  onCopyTracking: (trackingNumber: string) => void;
  formatDate: (dateString: string) => string;
}

const DeliveryRow = memo<DeliveryRowProps>(({ 
  delivery, 
  isSelected, 
  onRowClick, 
  onRowCheckboxChange, 
  onCopyTracking, 
  formatDate 
}) => {
  // Memoized click handlers
  const handleRowClick = useCallback(() => {
    onRowClick(delivery.id);
  }, [onRowClick, delivery.id]);

  const handleCheckboxChange = useCallback(() => {
    onRowCheckboxChange(delivery.id);
  }, [onRowCheckboxChange, delivery.id]);

  const handleCheckboxClick = useCallback((e: React.MouseEvent<HTMLInputElement>) => {
    e.stopPropagation();
  }, []);

  const handleCopyClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onCopyTracking(delivery.tracking_number);
  }, [onCopyTracking, delivery.tracking_number]);

  const handleTrackingClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onRowClick(delivery.id);
  }, [onRowClick, delivery.id]);

  // Memoized formatted dates
  const formattedShipDate = useMemo(() => 
    delivery.ship_date ? formatDate(delivery.ship_date) : 'N/A',
    [delivery.ship_date, formatDate]
  );

  const formattedEstDelivery = useMemo(() => 
    delivery.estimated_delivery ? formatDate(delivery.estimated_delivery) : 'N/A',
    [delivery.estimated_delivery, formatDate]
  );

  const formattedLastUpdate = useMemo(() => 
    delivery.last_update ? formatDate(delivery.last_update) : 'N/A',
    [delivery.last_update, formatDate]
  );

  // Memoized status badge
  const statusBadge = useMemo(() => 
    getStatusBadge(delivery.status),
    [delivery.status]
  );

  const rowBgColor = isSelected ? 'bg-blue-50' : 'bg-white';
  const rowHoverColor = isSelected ? 'bg-blue-100' : 'bg-gray-50';
  
  // Calculate exact background colors for use in inline styles
  const bgColorValue = isSelected ? 'rgb(239, 246, 255)' : 'rgb(255, 255, 255)'; // blue-50 or white
  const hoverColorValue = isSelected ? 'rgb(219, 234, 254)' : 'rgb(249, 250, 251)'; // blue-100 or gray-50

  return (
    <tr 
      className={`group border-b border-gray-200 transition-colors cursor-pointer ${rowBgColor} hover:${rowHoverColor}`}
      onClick={handleRowClick}
      data-testid={`delivery-row-${delivery.id}`}
    >
      {/* Checkbox Cell */}
      <td 
        style={{
          position: 'sticky',
          left: 0,
          backgroundColor: bgColorValue,
          zIndex: 20,
          padding: 0,
          width: '40px',
          backgroundClip: 'padding-box',
          transform: 'translateZ(0)' // Force GPU acceleration
        }}
        className="group-hover:bg-opacity-0"
        data-testid="checkbox-cell"
      >
        <div className="flex h-full items-center justify-center px-4 py-3"
          style={{
            backgroundColor: 'inherit',
            transition: 'background-color 150ms'
          }}
      >
        <input
          type="checkbox"
          checked={isSelected}
          onChange={handleCheckboxChange}
          onClick={handleCheckboxClick}
          className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
        />
        </div>
      </td>
      
      {/* Package Status Cell */}
      <td 
        style={{
          position: 'sticky',
          left: '40px',
          backgroundColor: bgColorValue,
          zIndex: 20,
          backgroundClip: 'padding-box',
          transform: 'translateZ(0)' // Force GPU acceleration
        }}
        className="px-4 py-3 group-hover:bg-opacity-0"
        data-testid="status-cell"
      >
        <div style={{backgroundColor: 'inherit', transition: 'background-color 150ms'}}>
        {statusBadge}
        </div>
      </td>
      
      {/* Numbers Cell - Expanded width for 35 chars */}
      <td 
        style={{
          position: 'sticky',
          left: '168px',
          backgroundColor: bgColorValue,
          zIndex: 20,
          backgroundClip: 'padding-box',
          width: '280px', // Ensure adequate width for 35 chars
          minWidth: '280px',
          transform: 'translateZ(0)' // Force GPU acceleration
        }}
        className="px-4 py-3 group-hover:bg-opacity-0"
        data-testid="number-cell"
      >
        <div style={{backgroundColor: 'inherit', transition: 'background-color 150ms'}}>
        <div className="space-y-1">
          <div className="flex items-center text-sm font-medium text-blue-600 group/tracking">
            <button
              onClick={handleTrackingClick}
              className="font-mono truncate hover:text-blue-800 hover:underline transition-all cursor-pointer text-left"
              title="View shipment details"
              data-testid="tracking-number"
            >
              {delivery.tracking_number}
            </button>
            <button 
              onClick={handleCopyClick}
              className="ml-1 opacity-0 group-hover/tracking:opacity-100 transition-opacity text-gray-400 hover:text-blue-600 flex-shrink-0"
              title="Copy tracking number"
            >
              <DocumentDuplicateIcon className="h-4 w-4" />
            </button>
          </div>
            <div className="text-xs text-gray-500">
              <p className="text-gray-900 font-medium" style={{
                wordBreak: 'break-all', // Ensures long numbers wrap correctly
                overflowWrap: 'break-word'
              }} data-testid="order-number">
                {delivery.order_number}
              </p>
            </div>
          </div>
        </div>
      </td>
      
      {/* Carrier - with shadow edge */}
      <td 
        style={{
          position: 'sticky',
          left: '448px', // Adjusted for wider number column
          backgroundColor: bgColorValue,
          zIndex: 10,
          backgroundClip: 'padding-box',
          boxShadow: '4px 0 6px -2px rgba(0, 0, 0, 0.1)',
          transform: 'translateZ(0)' // Force GPU acceleration for stability
        }}
        className="px-4 py-3 align-middle group-hover:bg-opacity-0"
        data-testid="carrier-cell"
      >
        <div style={{backgroundColor: 'inherit', transition: 'background-color 150ms', position: 'relative'}}>
        <div className="flex items-center">
          <Icon platform={delivery.carrier_name?.toLowerCase() || 'unknown'} />
          <span className="ml-3 font-semibold text-gray-800">{delivery.carrier_name}</span>
          </div>
          <div 
            className="absolute top-0 bottom-0 right-0 w-6 bg-gradient-to-r from-transparent to-gray-200/20 pointer-events-none" 
            style={{transform: 'translateX(100%)'}}
          ></div>
        </div>
      </td>
      
      {/* Customer Cell - scrollable */}
      <td className="px-4 py-3 align-middle text-gray-700 truncate" data-testid="customer-cell">
        {delivery.customer_name}
      </td>
      
      {/* Ship Date Cell - scrollable */}
      <td className="px-4 py-3 align-middle text-gray-700">
        {formattedShipDate}
      </td>
      
      {/* Est. Delivery Cell - scrollable */}
      <td className="px-4 py-3 align-middle text-gray-700">
        {formattedEstDelivery}
      </td>
      
      {/* Last Update Cell - scrollable */}
      <td className="px-4 py-3 align-middle text-gray-700">
        {formattedLastUpdate}
      </td>
    </tr>
  );
}, (prevProps, nextProps) => {
  // Custom comparison to prevent unnecessary re-renders
  return (
    prevProps.delivery.id === nextProps.delivery.id &&
    prevProps.isSelected === nextProps.isSelected &&
    prevProps.onRowClick === nextProps.onRowClick &&
    prevProps.onRowCheckboxChange === nextProps.onRowCheckboxChange &&
    prevProps.onCopyTracking === nextProps.onCopyTracking &&
    prevProps.formatDate === nextProps.formatDate &&
    // Deep compare delivery object only if references are different
    (prevProps.delivery === nextProps.delivery || 
     JSON.stringify(prevProps.delivery) === JSON.stringify(nextProps.delivery))
  );
});

DeliveryRow.displayName = 'DeliveryRow';

export default DeliveryRow; 