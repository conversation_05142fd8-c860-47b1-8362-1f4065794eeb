import { useContext } from 'react';
import { AuthContext } from '@/app/providers/AuthContext';

/**
 * Hook to access authentication context
 * 
 * @returns Authentication context with user, session, and auth methods
 */
export function useAuth() {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
} 