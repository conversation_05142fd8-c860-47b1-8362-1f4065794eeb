import { AllOrdersDetail, AllOrdersStatus} from '@/types';

/**
 * Data transformation modules for converting database responses to frontend interfaces
 */

// Status transformation
export const statusTransformers = {
  /**
   * Normalize status from database to AllOrdersStatus type
   */
  normalizeStatus: (status: string): AllOrdersStatus => {
    return status.toLowerCase() as AllOrdersStatus;
  }
};

// Customer data transformers
export const customerTransformers = {
  /**
   * Transform customer database response to frontend format
   */
  transformCustomer: (customerData: any, fallbackData: any = {}) => {
    // For bulk-created orders, customer data might exist but without address fields
    // So we need to prioritize shipping address from the order data
    const street = fallbackData.shipping_street || customerData?.address_street || '';
    const city = fallbackData.shipping_city || customerData?.address_city || '';
    const state = fallbackData.shipping_state || customerData?.address_state || '';
    const zipCode = fallbackData.shipping_zip_code || customerData?.address_zip_code || '';
    const country = fallbackData.shipping_country || customerData?.address_country || 'US';
    
    if (!customerData) {
      // Use more shipping info from order data when customer data is missing
      return {
        name: fallbackData.customer_name || 'Unknown Customer',
        email: fallbackData.customer_email || '',
        phone: fallbackData.customer_phone || null,
        address: {
          street,
          city,
          state,
          zipCode,
          country
        }
      };
    }

    return {
      name: customerData.name || 'Unknown Customer',
      email: customerData.email || '',
      phone: customerData.phone || undefined,
      address: {
        street,
        city,
        state,
        zipCode,
        country
      }
    };
  }
};

// Order items transformers
export const orderItemsTransformers = {
  /**
   * Transform order items database response to frontend format
   */
  transformOrderItems: (itemsData: any[], fallbackOrderData: any = {}) => {
    if (!itemsData || itemsData.length === 0) {
      // Create fallback item if no items found
      //const unitPrice = (fallbackOrderData.total_amount || 0) / (fallbackOrderData.item_count || 1);
      return [{
        id: '1',
        productName: `Order ${fallbackOrderData.order_number || 'Unknown'} Items`,
        sku: 'MULTIPLE',
        quantity: fallbackOrderData.item_count || 1,
        subtotal_item: fallbackOrderData.total_amount || 0,
        pack_size: 1
      }];
    }

    return itemsData.map(item => ({
      id: item.id,
      productName: item.product_name,
      sku: item.sku,
      quantity: item.quantity,
      subtotal_item: item.subtotal_item,
      pack_size: item.pack_size || 1,
    }));
  }
};

// Order notes transformers
export const orderNotesTransformers = {
  /**
   * Transform order notes database response to frontend format
   */
  transformOrderNotes: (notesData: any[], customerNotes?: string, orderDate?: string) => {
    const notes = [];

    // Add customer notes if available
    if (customerNotes) {
      notes.push({
        id: 'customer-note',
        order_id: '',
        content: customerNotes,
        author: 'Customer Service',
        author_id: 'system',
        author_name: 'Customer Service',
        timestamp: orderDate || new Date().toISOString(),
        type: 'internal' as const,
        note_type: 'customer',
        created_at: orderDate || new Date().toISOString(),
        updated_at: orderDate || new Date().toISOString(),
        is_important: false
      });
    }

    // Add order-specific notes
    if (notesData && notesData.length > 0) {
      const orderNotes = notesData.map(note => ({
        id: note.id,
        order_id: note.order_id || '',
        content: note.content,
        author: note.author || 'System',
        author_id: note.author_id || 'system',
        author_name: note.author_name || note.author || 'System',
        timestamp: note.created_at,
        type: note.type || 'internal' as const,
        note_type: note.note_type || 'order',
        created_at: note.created_at,
        updated_at: note.updated_at || note.created_at,
        is_important: note.is_important || false
      }));
      notes.push(...orderNotes);
    }

    return notes;
  }
};

// Main order transformer
export const orderTransformers = {
  /**
   * Transform complete order data to AllOrdersDetail format
   */
  transformOrderDetail: (
    orderData: any,
    customerData: any,
    itemsData: any[],
    notesData: any[] = []
  ): AllOrdersDetail => {
    const customer = customerTransformers.transformCustomer(customerData, orderData);
    const items = orderItemsTransformers.transformOrderItems(itemsData, orderData);
    const notes = orderNotesTransformers.transformOrderNotes(notesData, customerData?.notes, orderData.order_date);

    // For bulk-created orders, always prioritize shipping address from the order data
    const shippingAddress = {
      street: orderData.shipping_street || '',
      city: orderData.shipping_city || '',
      state: orderData.shipping_state || '',
      zipCode: orderData.shipping_zip_code || '',
      country: orderData.shipping_country || 'US'
    };

    return {
      id: orderData.id,
      orderNumber: orderData.order_number,
      orderDate: orderData.order_date,
      platform: orderData.platform_key || orderData.platform,
      channel: orderData.channel_code || orderData.channel,
      status: statusTransformers.normalizeStatus(orderData.status),
      customer,
      items,
      subtotal: orderData.subtotal || 0,
      tax: orderData.tax || orderData.tax_amount || 0,
      shipping: orderData.shipping || orderData.shipping_cost || 0,
      totalAmount: orderData.total_amount,
      shippingMethod: orderData.shipping_method || 'Standard Shipping',
      trackingNumber: orderData.tracking_number,
      expectedDelivery: orderData.expected_delivery,
      shippingAddress,
      isUrgent: orderData.is_urgent || false,
      isProblem: orderData.is_problem || false,
      isResent: orderData.is_resent || false,
      hasNotes: orderData.has_notes || false,
      notes,
      linkedOrders: [],
      customerHistory: [],
      createdAt: orderData.order_date,
      updatedAt: orderData.updated_at || orderData.order_date
    };
  }
};