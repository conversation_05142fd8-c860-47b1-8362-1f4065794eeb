import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import StockAdjustmentModal from '../StockAdjustmentModal';
import { useStockAdjustment } from '@/shared/lib/hooks/inventory/useStockAdjustment';
import { useBulkStockAdjustment } from '@/shared/lib/hooks/inventory/useBulkStockAdjustment';
import { InventoryView } from '@/types';

// Mock the hooks
jest.mock('@/shared/lib/hooks/inventory/useStockAdjustment');
jest.mock('@/shared/lib/hooks/inventory/useBulkStockAdjustment');

describe('StockAdjustmentModal', () => {
  // Mock product data
  const mockProduct: InventoryView = {
    inventory_id: 'inv-1',
    product_id: 'prod-1',
    name: 'Test Product',
    sku: 'TP001',
    current_stock: 10,
    available_stock: 8,
    reserved_stock: 2,
    product_type: null,
    status: 'active',
    minimum_threshold: null,
    needs_reorder: null
  };

  // Mock functions
  const mockOnClose = jest.fn();
  const mockOnSuccess = jest.fn();
  const mockOnError = jest.fn();
  const mockUpdateStock = jest.fn();
  const mockBulkUpdateStock = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock single stock adjustment hook
    (useStockAdjustment as jest.Mock).mockReturnValue({
      isModalOpen: true,
      isUpdating: false,
      selectedProductId: 'prod-1',
      selectedProductName: 'Test Product',
      currentStock: 10,
      error: null,
      movements: [],
      openModal: jest.fn(),
      closeModal: jest.fn(),
      updateStock: mockUpdateStock,
      fetchMovements: jest.fn()
    });
    
    // Mock bulk stock adjustment hook
    (useBulkStockAdjustment as jest.Mock).mockReturnValue({
      isModalOpen: true,
      isUpdating: false,
      selectedProducts: [mockProduct],
      error: null,
      openModal: jest.fn(),
      closeModal: jest.fn(),
      updateStock: mockBulkUpdateStock
    });
  });

  test('renders correctly for single product', () => {
    render(
      <StockAdjustmentModal
        isOpen={true}
        onClose={mockOnClose}
        products={[mockProduct]}
        onSuccess={mockOnSuccess}
        onError={mockOnError}
      />
    );
    
    // Check product info is displayed
    expect(screen.getByText('Product')).toBeInTheDocument();
    expect(screen.getByText('Test Product')).toBeInTheDocument();
    expect(screen.getByText('Current Stock')).toBeInTheDocument();
    expect(screen.getByText('10 units')).toBeInTheDocument();
    
    // Check form elements exist
    expect(screen.getByLabelText(/Quantity/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Increase/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Decrease/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Set Value/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Reason Category/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Reason/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Notes/i)).toBeInTheDocument();
  });

  test('renders correctly for bulk mode', () => {
    render(
      <StockAdjustmentModal
        isOpen={true}
        onClose={mockOnClose}
        products={[mockProduct, {...mockProduct, product_id: 'prod-2', name: 'Test Product 2'}]}
        onSuccess={mockOnSuccess}
        onError={mockOnError}
      />
    );
    
    // Check bulk info is displayed
    expect(screen.getByText('Selected Products')).toBeInTheDocument();
    expect(screen.getByText('2 products selected')).toBeInTheDocument();
    expect(screen.getByText('Average Stock')).toBeInTheDocument();
  });

  test('validates form correctly - rejects empty quantity', async () => {
    render(
      <StockAdjustmentModal
        isOpen={true}
        onClose={mockOnClose}
        products={[mockProduct]}
        onSuccess={mockOnSuccess}
        onError={mockOnError}
      />
    );
    
    // Submit form without entering quantity
    fireEvent.click(screen.getByText('Update Stock'));
    
    // Check error message
    await waitFor(() => {
      expect(screen.getByText('Quantity is required')).toBeInTheDocument();
    });
    
    // Verify updateStock was not called
    expect(mockUpdateStock).not.toHaveBeenCalled();
  });

  test('validates form correctly - rejects negative quantity', async () => {
    render(
      <StockAdjustmentModal
        isOpen={true}
        onClose={mockOnClose}
        products={[mockProduct]}
        onSuccess={mockOnSuccess}
        onError={mockOnError}
      />
    );
    
    // Enter negative quantity
    fireEvent.change(screen.getByLabelText(/Quantity/i), { target: { value: '-5' } });
    
    // Submit form
    fireEvent.click(screen.getByText('Update Stock'));
    
    // Check error message
    await waitFor(() => {
      expect(screen.getByText('Quantity must be greater than zero')).toBeInTheDocument();
    });
    
    // Verify updateStock was not called
    expect(mockUpdateStock).not.toHaveBeenCalled();
  });

  test('validates form correctly - rejects empty reason', async () => {
    render(
      <StockAdjustmentModal
        isOpen={true}
        onClose={mockOnClose}
        products={[mockProduct]}
        onSuccess={mockOnSuccess}
        onError={mockOnError}
      />
    );
    
    // Enter quantity but no reason
    fireEvent.change(screen.getByLabelText(/Quantity/i), { target: { value: '5' } });
    
    // Submit form
    fireEvent.click(screen.getByText('Update Stock'));
    
    // Check error message
    await waitFor(() => {
      expect(screen.getByText('Reason is required')).toBeInTheDocument();
    });
  });

  test('calculates new stock preview correctly for increase', async () => {
    render(
      <StockAdjustmentModal
        isOpen={true}
        onClose={mockOnClose}
        products={[mockProduct]}
        onSuccess={mockOnSuccess}
        onError={mockOnError}
      />
    );
    
    // Select increase and enter quantity
    fireEvent.click(screen.getByLabelText(/Increase/i));
    fireEvent.change(screen.getByLabelText(/Quantity/i), { target: { value: '5' } });
    
    // Check preview
    await waitFor(() => {
      expect(screen.getByText('New stock will be: 15 units')).toBeInTheDocument();
    });
  });

  test('calculates new stock preview correctly for decrease', async () => {
    render(
      <StockAdjustmentModal
        isOpen={true}
        onClose={mockOnClose}
        products={[mockProduct]}
        onSuccess={mockOnSuccess}
        onError={mockOnError}
      />
    );
    
    // Select decrease and enter quantity
    fireEvent.click(screen.getByLabelText(/Decrease/i));
    fireEvent.change(screen.getByLabelText(/Quantity/i), { target: { value: '3' } });
    
    // Check preview
    await waitFor(() => {
      expect(screen.getByText('New stock will be: 7 units')).toBeInTheDocument();
    });
  });

  test('calculates new stock preview correctly for set value', async () => {
    render(
      <StockAdjustmentModal
        isOpen={true}
        onClose={mockOnClose}
        products={[mockProduct]}
        onSuccess={mockOnSuccess}
        onError={mockOnError}
      />
    );
    
    // Select set value and enter quantity
    fireEvent.click(screen.getByLabelText(/Set Value/i));
    fireEvent.change(screen.getByLabelText(/Quantity/i), { target: { value: '20' } });
    
    // Check preview
    await waitFor(() => {
      expect(screen.getByText('New stock will be: 20 units')).toBeInTheDocument();
    });
  });

  test('submits form correctly for single product', async () => {
    render(
      <StockAdjustmentModal
        isOpen={true}
        onClose={mockOnClose}
        products={[mockProduct]}
        onSuccess={mockOnSuccess}
        onError={mockOnError}
      />
    );
    
    // Fill out form
    fireEvent.click(screen.getByLabelText(/Increase/i));
    fireEvent.change(screen.getByLabelText(/Quantity/i), { target: { value: '5' } });
    fireEvent.change(screen.getByLabelText(/Reason/i), { target: { value: 'Test reason' } });
    
    // Select reason category
    fireEvent.change(screen.getByLabelText(/Reason Category/i), { target: { value: 'new_shipment' } });
    
    // Add notes
    fireEvent.change(screen.getByLabelText(/Notes/i), { target: { value: 'Test notes' } });
    
    // Submit form
    fireEvent.click(screen.getByText('Update Stock'));
    
    // Verify updateStock was called with correct parameters
    await waitFor(() => {
      expect(mockUpdateStock).toHaveBeenCalledWith({
        productId: 'prod-1',
        adjustmentType: 'increase',
        quantity: 5,
        reason: 'Test reason',
        reasonCategory: 'new_shipment',
        notes: 'Test notes'
      });
    });
    
    // Verify onClose was called
    expect(mockOnClose).toHaveBeenCalled();
  });

  test('submits form correctly for bulk update', async () => {
    // Mock bulk mode
    (useBulkStockAdjustment as jest.Mock).mockReturnValue({
      isModalOpen: true,
      isUpdating: false,
      selectedProducts: [mockProduct, {...mockProduct, product_id: 'prod-2'}],
      error: null,
      openModal: jest.fn(),
      closeModal: jest.fn(),
      updateStock: mockBulkUpdateStock
    });
    
    render(
      <StockAdjustmentModal
        isOpen={true}
        onClose={mockOnClose}
        products={[mockProduct, {...mockProduct, product_id: 'prod-2'}]}
        onSuccess={mockOnSuccess}
        onError={mockOnError}
      />
    );
    
    // Fill out form
    fireEvent.click(screen.getByLabelText(/Increase/i));
    fireEvent.change(screen.getByLabelText(/Quantity/i), { target: { value: '5' } });
    fireEvent.change(screen.getByLabelText(/Reason/i), { target: { value: 'Bulk update' } });
    
    // Select reason category
    fireEvent.change(screen.getByLabelText(/Reason Category/i), { target: { value: 'new_shipment' } });
    
    // Submit form
    fireEvent.click(screen.getByText('Update Stock'));
    
    // Verify bulkUpdateStock was called with correct parameters
    await waitFor(() => {
      expect(mockBulkUpdateStock).toHaveBeenCalledWith({
        productIds: ['prod-1', 'prod-2'],
        adjustmentType: 'increase',
        quantity: 5,
        reason: 'Bulk update',
        reasonCategory: 'new_shipment',
        notes: ''
      });
    });
  });

  test('displays error message when update fails', async () => {
    // Mock error state
    (useStockAdjustment as jest.Mock).mockReturnValue({
      isModalOpen: true,
      isUpdating: false,
      selectedProductId: 'prod-1',
      selectedProductName: 'Test Product',
      currentStock: 10,
      error: new Error('Update failed'),
      movements: [],
      openModal: jest.fn(),
      closeModal: jest.fn(),
      updateStock: mockUpdateStock,
      fetchMovements: jest.fn()
    });
    
    render(
      <StockAdjustmentModal
        isOpen={true}
        onClose={mockOnClose}
        products={[mockProduct]}
        onSuccess={mockOnSuccess}
        onError={mockOnError}
      />
    );
    
    // Check error message is displayed
    expect(screen.getByText('Update failed')).toBeInTheDocument();
  });
});
