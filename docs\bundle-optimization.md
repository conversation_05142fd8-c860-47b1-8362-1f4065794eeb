# Vendor Chunk Splitting Optimization

## Strategy Overview

This document explains our strategy for optimizing vendor chunk splitting to reduce the initial bundle size of the application.

### Goals

1. Reduce initial JavaScript bundle size to improve First Contentful Paint (FCP)
2. Group related dependencies efficiently to improve caching
3. Separate large, infrequently used libraries from critical path code
4. Optimize page load performance through intelligent chunk splitting

## Implementation Details

### Vendor Chunk Categories

We've divided our vendor dependencies into the following logical chunks:

1. **vendor-react-core**: Core React library without DOM-specific code
   - Smaller bundle that contains essential React functionality
   - Required for all routes
   - Example: `react`, `react/jsx-runtime`

2. **vendor-react-dom**: React DOM-specific code
   - Larger bundle with browser rendering logic
   - Required for all routes but separating from core React improves caching
   - Example: `react-dom`, `react-dom/client`

3. **vendor-routing**: Routing-related libraries
   - Required for all routes but not for initial rendering
   - Example: `react-router-dom`, `@remix-run/router`

4. **vendor-supabase**: Supabase SDK
   - Large authentication and database library
   - Only needed for authenticated features
   - Example: `@supabase/supabase-js`

5. **vendor-charts**: Visualization libraries
   - Very large libraries only needed on specific pages
   - Example: `recharts`, `chart.js`, `react-chartjs-2`

6. **vendor-d3**: D3 visualization dependencies
   - Often used by chart libraries
   - Separated to optimize caching and reduce duplication
   - Example: `d3`, `internmap`, `delaunator`

7. **vendor-ui-utils**: UI utility libraries
   - Small, commonly used across the app
   - Example: `@heroicons/react`, `clsx`, `tailwind-merge`

8. **vendor-data-utils**: Data processing libraries
   - Used for state management and data manipulation
   - Example: `zustand`, `@tanstack/react-query`, `papaparse`

9. **vendor-stagewise**: Stagewise plugins
   - Feature-specific plugins
   - Example: `@stagewise/toolbar-react`

10. **vendor-others**: Other smaller dependencies
    - Catch-all for remaining node_modules packages
    - Grouped to reduce HTTP requests

### Helper Functions

We implemented two helper functions to improve module grouping logic:

1. **isModuleFrom**: Check if a module is from a specific package
   ```typescript
   const isModuleFrom = (id: string, pkgName: string): boolean => {
     return id.includes(`node_modules/${pkgName}`);
   };
   ```

2. **isInGroup**: Check if a module belongs to a package group
   ```typescript
   const isInGroup = (id: string, pkgNames: string[]): boolean => {
     return pkgNames.some(pkg => isModuleFrom(id, pkg));
   };
   ```

## Testing Strategy

Our optimization has been tested using several approaches:

1. **Unit Tests**: Verify that chunk assignment logic works correctly
   - Test helper functions
   - Test module path classification logic
   - Ensure consistent chunk assignment

2. **Bundle Analysis**: Measure bundle size before and after optimization
   - Track total vendor bundle size
   - Monitor individual chunk sizes
   - Identify potential further optimizations

3. **Performance Testing**: Measure real-world performance impact
   - Check First Contentful Paint (FCP)
   - Monitor Time To Interactive (TTI)
   - Evaluate Largest Contentful Paint (LCP)

## Results

| Metric | Before Optimization | After Optimization | Improvement |
|--------|-------------------:|-------------------:|------------:|
| Initial JS Payload | 1584 KB | 1484 KB | 6.3% reduction |
| Vendor Chunks | 4 | 10 | More granular |
| Largest Vendor Chunk | 437.91 KB (vendor-charts) | 437.91 KB (vendor-charts) | Better isolation |
| Total Build Size | 2092.6 KB | 2073.2 KB | 0.9% reduction |

### Vendor Chunk Sizes After Optimization

| Chunk Name | Size (KB) | gzip Size (KB) | Description |
|------------|----------:|---------------:|-------------|
| vendor-charts | 437.91 | 115.52 | Chart libraries (recharts, chart.js) |
| vendor-stagewise | 277.21 | 71.70 | Stagewise plugins |
| vendor-react-dom | 272.48 | 82.68 | React DOM renderer |
| vendor-others | 125.92 | 40.89 | Other smaller dependencies |
| vendor-supabase | 114.37 | 30.11 | Supabase database SDK |
| vendor-react-core | 96.45 | 26.79 | Core React library |
| vendor-d3 | 62.28 | 19.96 | D3 visualization dependencies |
| vendor-data-utils | 53.32 | 17.06 | Data processing libraries |
| vendor-ui-utils | 48.08 | N/A | UI utility libraries |
| vendor-routing | 30.69 | 11.03 | React Router libraries |

### Key Improvements

1. **Better Initial Loading**: By separating React core from React DOM, users get faster initial loads as only necessary code is loaded first.

2. **Improved Cache Efficiency**: More granular chunks mean better cache hits when only part of the app changes.

3. **Isolated Large Libraries**: Chart libraries and Stagewise plugins are now isolated, preventing their large size from affecting core app performance.

4. **Related Module Grouping**: Libraries that are commonly used together are grouped for better loading efficiency.

5. **Reduced Dependency Duplication**: Common dependencies are better grouped, reducing duplication across chunks.

## Future Improvements

1. Implement route-based preloading for key dependencies
2. Further optimize specific libraries with high impact
3. Implement module federation for shared micro-frontends
4. Explore using importmap for better dependency management

## References

- [Vite Code Splitting Guide](https://vitejs.dev/guide/build.html#chunking-strategy)
- [Web.dev Guide to Code Splitting](https://web.dev/articles/reduce-javascript-payloads-with-code-splitting)
- [Rollup manualChunks Documentation](https://rollupjs.org/configuration-options/#output-manualchunks) 