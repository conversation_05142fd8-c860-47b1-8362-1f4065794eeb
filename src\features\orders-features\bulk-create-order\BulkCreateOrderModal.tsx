import React, { useState } from 'react';
import Modal from '@/shared/ui/overlay/Modal';
import LoadingSpinner from '@/shared/ui/feedback/LoadingSpinner';
import CSVUpload from './components/CSVUpload';
import FailedOrdersReport from './components/FailedOrdersReport';
import { useBulkOrderCreation } from '@/shared/lib/hooks/orders/useBulkOrderCreation';

interface BulkCreateOrderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (data: any) => void;
}

/**
 * Modal component for bulk order creation via CSV upload
 */
const BulkCreateOrderModal: React.FC<BulkCreateOrderModalProps> = ({ 
  isOpen, 
  onClose,
  onSuccess 
}) => {
  const [step, setStep] = useState<'upload' | 'processing' | 'results'>('upload');
  
  const { 
    uploadCsv,
    downloadTemplate,
    processingStatus,
    error,
    failedOrders,
    successfulOrders,
    isProcessing,
    downloadFailedOrdersReport
  } = useBulkOrderCreation();

  const handleFileUpload = async (file: File) => {
    setStep('processing');
    const result = await uploadCsv(file);
    if (result.success) {
      setStep('results');
      if (onSuccess) {
        onSuccess(result.data);
      }
    } else {
      setStep('upload');
    }
  };

  const handleDownloadTemplate = () => {
    downloadTemplate();
  };

  const handleClose = () => {
    // Reset state when closing the modal
    setStep('upload');
    onClose();
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={handleClose} 
      title="Bulk Create Orders" 
      size="lg"
      showDefaultFooter={false}
    >
      <div className="p-4">
        {step === 'upload' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Upload CSV File</h3>
              <button
                onClick={handleDownloadTemplate}
                className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
              >
                <span className="mr-1">Download Template</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
              </button>
            </div>
            
            <CSVUpload onFileUpload={handleFileUpload} />
            
            {error && (
              <div className="mt-4 bg-red-50 text-red-700 p-3 rounded-md">
                <p className="text-sm font-medium">Error: {error}</p>
              </div>
            )}
            
            <div className="bg-gray-50 p-4 rounded-md mt-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Guidelines</h4>
              <ul className="text-xs text-gray-600 space-y-1 list-disc pl-4">
                <li>Maximum 40 orders per upload</li>
                <li>Required fields: order_number, platform, channel, customer_email</li>
                <li>Order items require valid product identifiers</li>
                <li>CSV file size should not exceed 5MB</li>
              </ul>
            </div>
          </div>
        )}
        
        {step === 'processing' && (
          <div className="text-center py-12">
            <LoadingSpinner size="lg" />
            <p className="mt-4 text-gray-600">Processing your orders...</p>
            {/* {processingStatus && (
              <p className="text-sm text-gray-500 mt-2">{processingStatus}</p>
            )} */}
          </div>
        )}
        
        {step === 'results' && (
          <div className="space-y-6">
            <div className="bg-green-50 text-green-800 p-4 rounded-md">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <p className="font-medium">
                  Successfully created {successfulOrders?.length || 0} orders
                </p>
              </div>
            </div>
            
            {failedOrders && failedOrders.length > 0 && (
              <FailedOrdersReport 
                failedOrders={failedOrders} 
                onDownload={downloadFailedOrdersReport} 
              />
            )}
            
            <div className="flex justify-end pt-4 border-t">
              <button
                onClick={handleClose}
                className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md"
              >
                Done
              </button>
            </div>
          </div>
        )}
        
        <div className="mt-6 flex justify-end space-x-3 pt-4 border-t">
          {step === 'upload' && (
            <>
              <button 
                onClick={handleClose}
                className="border border-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
            </>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default BulkCreateOrderModal; 