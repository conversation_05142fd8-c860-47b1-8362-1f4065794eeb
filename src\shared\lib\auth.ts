
const USER_EMAIL_KEY = 'washolding_user_email';
const AUTH_TOKEN_KEY = 'washolding_auth_token';
const TOKEN_EXPIRY_KEY = 'washolding_token_expiry';

const ONE_DAY_MS = 24 * 60 * 60 * 1000; // 1 day in milliseconds

interface AuthSession {
  email: string;
  token: string;
  expiry: number;
}

export const setAuthSession = (email: string): void => {
  try {
    const expiry = Date.now() + ONE_DAY_MS;
    // In a real app, the token would come from a server
    const token = `mock_token_for_${email}_${Date.now()}`;

    localStorage.setItem(USER_EMAIL_KEY, email);
    localStorage.setItem(AUTH_TOKEN_KEY, token);
    localStorage.setItem(TOKEN_EXPIRY_KEY, expiry.toString());
  } catch (error) {
    console.warn('localStorage write failed:', error);
  }
};

export const getAuthSession = (): AuthSession | null => {
  try {
    const email = localStorage.getItem(USER_EMAIL_KEY);
    const token = localStorage.getItem(AUTH_TOKEN_KEY);
    const expiryString = localStorage.getItem(TOKEN_EXPIRY_KEY);

    if (email && token && expiryString) {
      const expiry = parseInt(expiryString, 10);
      if (!isNaN(expiry)) {
        return { email, token, expiry };
      }
    }
  } catch (error) {
    console.warn('localStorage access failed:', error);
  }
  return null;
};

export const clearAuthSession = (): void => {
  try {
    localStorage.removeItem(USER_EMAIL_KEY);
    localStorage.removeItem(AUTH_TOKEN_KEY);
    localStorage.removeItem(TOKEN_EXPIRY_KEY);
  } catch (error) {
    console.warn('localStorage clear failed:', error);
  }
};

export const isAuthenticated = (): boolean => {
  const session = getAuthSession();
  if (!session) {
    return false;
  }
  // Check if the token is expired
  return Date.now() < session.expiry;
};
