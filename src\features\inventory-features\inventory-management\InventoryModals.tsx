import React, { Suspense } from 'react';
import FilterModal from './FilterModal';
import { ProductFormModal } from '../product-form';
import { ActiveFilter, FilterOption, InventoryView, ProductStatusEnum } from '@/types';
import { StockAdjustmentModal } from '../stock-update';
import type { StockUpdateFormData, BulkStockUpdateFormData } from '../stock-update';
import { TableLoadingSkeleton } from '@/shared/ui/feedback/LoadingSkeleton';

interface InventoryModalsProps {
  // Filter modal props
  isFilterModalOpen: boolean;
  onCloseFilterModal: () => void;
  onApplyFilters: (filters: ActiveFilter[]) => void;
  currentFilters: ActiveFilter[];
  availableProductTypes: FilterOption[];
  
  // Product form modal props
  isProductFormModalOpen: boolean;
  onCloseProductFormModal: () => void;
  onProductSaved: () => void;
  productFormTitle: string;
  productFormButtonText: string;
  productToEdit: any | undefined;
  
  // Stock update modal props
  isStockUpdateModalOpen: boolean;
  stockUpdateProductId: string | null;
  stockUpdateProductName: string | null;
  stockUpdateCurrentStock: number | null;
  onCloseStockUpdateModal: () => void;
  onUpdateStock: (data: StockUpdateFormData) => Promise<void>;
  
  // Bulk stock update modal props
  isBulkStockUpdateModalOpen: boolean;
  bulkSelectedProducts: InventoryView[];
  onCloseBulkStockUpdateModal: () => void;
  onBulkUpdateStock: (data: BulkStockUpdateFormData) => void;
}

/**
 * Component that encapsulates all modals used in the inventory management page
 */
const InventoryModals: React.FC<InventoryModalsProps> = ({
  // Filter modal props
  isFilterModalOpen,
  onCloseFilterModal,
  onApplyFilters,
  currentFilters,
  availableProductTypes,
  
  // Product form modal props
  isProductFormModalOpen,
  onCloseProductFormModal,
  onProductSaved,
  productFormTitle,
  productFormButtonText,
  productToEdit,
  
  // Stock update modal props
  isStockUpdateModalOpen,
  stockUpdateProductId,
  stockUpdateProductName,
  stockUpdateCurrentStock,
  onCloseStockUpdateModal,
  onUpdateStock,
  
  // Bulk stock update modal props
  isBulkStockUpdateModalOpen,
  bulkSelectedProducts,
  onCloseBulkStockUpdateModal,
  onBulkUpdateStock
}) => {
  // Create products array for single product update
  const singleProductArray: InventoryView[] = stockUpdateProductId && stockUpdateProductName && stockUpdateCurrentStock !== null 
    ? [{
        product_id: stockUpdateProductId,
        name: stockUpdateProductName,
        current_stock: stockUpdateCurrentStock,
        inventory_id: `inv-${stockUpdateProductId}`,
        sku: '',
        product_type: null,
        status: 'active' as ProductStatusEnum,
        available_stock: stockUpdateCurrentStock,
        reserved_stock: 0,
        minimum_threshold: null,
        needs_reorder: null
      }] 
    : [];

  // Unified handler for both single and bulk updates
  const handleStockUpdate = async (data: StockUpdateFormData | BulkStockUpdateFormData) => {
    if ('productId' in data) {
      // Single product update
      return onUpdateStock(data);
    } else {
      // Bulk product update
      return onBulkUpdateStock(data);
    }
  };

  return (
    <>
      {/* Filter Modal */}
      <FilterModal
        isOpen={isFilterModalOpen}
        onClose={onCloseFilterModal}
        onApplyFilters={onApplyFilters}
        currentFilters={currentFilters}
        availableProductTypes={availableProductTypes}
      />

      {/* Product Form Modal */}
      <ProductFormModal
        isOpen={isProductFormModalOpen}
        onClose={onCloseProductFormModal}
        onSuccess={onProductSaved}
        title={productFormTitle}
        submitButtonText={productFormButtonText}
        initialData={productToEdit || undefined}
      />

      {/* Unified Stock Adjustment Modal - Handles both single and bulk operations */}
      {(isStockUpdateModalOpen || isBulkStockUpdateModalOpen) && (
        <Suspense fallback={<TableLoadingSkeleton rowCount={3} columnCount={2} className="h-60 w-full rounded-md" />}>
          <StockAdjustmentModal
            isOpen={isStockUpdateModalOpen || isBulkStockUpdateModalOpen}
            onClose={isStockUpdateModalOpen ? onCloseStockUpdateModal : onCloseBulkStockUpdateModal}
            onSuccess={handleStockUpdate}
            products={isStockUpdateModalOpen ? singleProductArray : bulkSelectedProducts}
          />
        </Suspense>
      )}
    </>
  );
};

export default InventoryModals; 