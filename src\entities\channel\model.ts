

import { KpiDataType, SalesDataPoint, DropdownOption } from '../../types';
import {
  InboxStackIcon, // For Open Orders (was FlagIcon for Review)
  ArchiveBoxArrowDownIcon, // For Packed Orders (was CubeIcon for Pick)
  PaperAirplaneIcon, // For Ship (remains)
  CheckCircleIcon, // For Delivered order (was TruckIcon for Dropship)
} from '../../shared/config';


// Updated KPI_DATA for 4 items as per user request
export const KPI_DATA: KpiDataType[] = [
  { value: '13', label: 'Open Orders', icon: InboxStackIcon, color: 'text-primary', progress: 25 },
  { value: '19', label: 'Packed Orders', icon: ArchiveBoxArrowDownIcon, color: 'text-primary', progress: 35 },
  { value: '202', label: 'Ship', icon: PaperAirplaneIcon, color: 'text-primary', progress: 80 },
  { value: '2', label: 'Delivered order', icon: CheckCircleIcon, color: 'text-primary', progress: 10 },
];


export const SALES_CHART_DATA: SalesDataPoint[] = [
  { name: 'January', currentValue: 4000, previousValue: 2400 },
  { name: 'February', currentValue: 95000, previousValue: 13980 },
  { name: 'March', currentValue: 10000, previousValue: 5000 },
  { name: 'April', currentValue: 15000, previousValue: 1000 },
  { name: 'May', currentValue: 18000, previousValue: 3000 },
  { name: 'June', currentValue: 2000, previousValue: 4000 },
  { name: 'July', currentValue: 0, previousValue: 0 },
  { name: 'August', currentValue: 0, previousValue: 0 },
  { name: 'September', currentValue: 0, previousValue: 0 },
  { name: 'October', currentValue: 0, previousValue: 0 },
  { name: 'November', currentValue: 0, previousValue: 0 },
  { name: 'December', currentValue: 0, previousValue: 0 },
];

export const salesTypeOptions: DropdownOption[] = [
  { value: 'net_sales', label: 'Net Sales' },
  { value: 'gross_sales', label: 'Gross Sales' },
];
export const yearComparisonOptions: DropdownOption[] = [
  { value: 'yearly_this_vs_last', label: 'Yearly: This Year vs. Last Year' },
  { value: 'monthly_this_vs_last', label: 'Monthly: This Month vs. Last Month' },
  { value: 'quarterly_this_vs_last', label: 'Quarterly: This Quarter vs. Last Quarter' },
];
// filterOptions is removed as per user request.


export interface LowStockItem {
  sku: string;
  stockLeft: number;
}

export const MOCK_LOW_STOCK_ALERTS: LowStockItem[] = [
  { sku: '20N', stockLeft: 4 },
  { sku: '10Blue', stockLeft: 5 },
  { sku: '10FR', stockLeft: 8 },
  // Add more items as needed
];

// If you have other exports in this file, make sure they are preserved.
// For example, if KPI_DATA is defined here, it should remain.
// export const KPI_DATA = [...];