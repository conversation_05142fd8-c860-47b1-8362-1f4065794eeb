import { deliveriesApi } from '../../../api';
import { DeliveryListParams } from '../../../../types';

/**
 * The DeliveryService provides a clean, high-level interface for delivery-related operations.
 * It encapsulates the logic for interacting with the deliveriesAPI and can be easily
 * extended to include caching, data transformation, or other business logic.
 */
export const deliveryService = {
  /**
   * Fetches a paginated and filtered list of deliveries.
   * @param params - The filtering, sorting, and pagination parameters.
   * @returns A promise that resolves to the API response containing the list of deliveries.
   */
  async getDeliveries(params: DeliveryListParams) {
    // Here you could add caching logic with React Query, SWR, or a custom cache.
    // For now, it directly calls the API.
    return deliveriesApi.list(params);
  },

  /**
   * Fetches the detailed information for a single delivery.
   * @param id - The unique identifier of the delivery.
   * @returns A promise that resolves to the API response containing the delivery details.
   */
  async getDeliveryDetails(id: string) {
    return deliveriesApi.getById(id);
  },

  /**
   * Fetches real-time delivery statistics.
   * @returns A promise that resolves to the API response containing delivery stats.
   */
  async getDeliveryStats() {
    return deliveriesApi.getStats();
  }
}; 