import React, { useState, useEffect, useMemo } from 'react';
import { useAuth } from '@/app/providers/AuthProvider';
import InviteUserModal from '@/features/user-invite-modal/InviteUserModal';
import { getPendingInvitations, resendInvitation, cancelInvitation, repairOrphanedInvitation } from '@/shared/lib/services/auth/invite.service';
import { supabase } from '../../../supabase/supabase_client/client';
import { UserRole } from '@/types';
import LoadingSpinner from '@/shared/ui/feedback/LoadingSpinner';
import { Icon } from '@/shared/ui/core/Icon';

const UserManagementPage: React.FC = () => {
  const { profile } = useAuth();
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [pendingInvitations, setPendingInvitations] = useState<any[]>([]);
  const [allUsers, setAllUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Only allow master users to access this page
  if (profile?.role !== 'master') {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Access Restricted</h3>
          <p className="text-gray-600">This page is only available to master users.</p>
        </div>
      </div>
    );
  }

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Load pending invitations
      const inviteResult = await getPendingInvitations();

      
      if (inviteResult.success) {
        setPendingInvitations(inviteResult.invitations);
      } else {
        setError(inviteResult.message);
      }

      // Also load all users to show recent activity
      const { data: users, error: usersError } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(10);

      if (usersError) {
        console.error('Failed to load users:', usersError);
      } else {
        setAllUsers(users || []);
      }

    } catch (error) {
      console.error('Failed to load data:', error);
      setError('Failed to load user data');
    } finally {
      setLoading(false);
    }
  };

  const handleInviteSuccess = (email: string, role: UserRole) => {
    setSuccessMessage(`Invitation sent successfully to ${email} as ${role}`);
    loadData(); // Refresh the list
    
    // Clear success message after 5 seconds
    setTimeout(() => setSuccessMessage(null), 5000);
  };

  const handleResendInvitation = async (email: string, userId: string) => {
    setActionLoading(userId);
    try {
      const result = await resendInvitation(email);
      if (result.success) {
        setSuccessMessage(result.message);
        setTimeout(() => setSuccessMessage(null), 5000);
      }
    } catch (error) {
      console.error('Failed to resend invitation:', error);
    } finally {
      setActionLoading(null);
    }
  };

  const handleCancelInvitation = async (userId: string) => {
    if (!confirm('Are you sure you want to cancel this invitation?')) {
      return;
    }

    setActionLoading(userId);
    try {
      const result = await cancelInvitation(userId);
      if (result.success) {
        setSuccessMessage(result.message);
        loadData(); // Refresh the list
        setTimeout(() => setSuccessMessage(null), 5000);
      }
    } catch (error) {
      console.error('Failed to cancel invitation:', error);
    } finally {
      setActionLoading(null);
    }
  };

  const handleRepairInvitation = async (userId: string) => {
    if (!confirm('This will create the missing database record for this invitation. Continue?')) {
      return;
    }

    setActionLoading(userId);
    try {
      const result = await repairOrphanedInvitation(userId);
      if (result.success) {
        setSuccessMessage(result.message);
        loadData(); // Refresh the list
        setTimeout(() => setSuccessMessage(null), 5000);
      } else {
        setError(result.message);
        setTimeout(() => setError(null), 5000);
      }
    } catch (error) {
      console.error('Failed to repair invitation:', error);
      setError('Failed to repair invitation');
      setTimeout(() => setError(null), 5000);
    } finally {
      setActionLoading(null);
    }
  };

  // Task 3.6: Memoize role badge color mapping to avoid recreation on every render
  const getRoleBadgeColor = useMemo(() => (role: string) => {
    switch (role) {
      case 'master': return 'bg-red-100 text-red-800';
      case 'admin': return 'bg-blue-100 text-blue-800';
      case 'staff': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }, []);

  // Task 3.6: Memoize expensive date formatting function
  const formatDate = useMemo(() => (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }, []);

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex-1 flex flex-col">
        {/* Page Header */}
        <header className="flex-shrink-0 bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-6 py-6">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
                <p className="text-gray-600 mt-1">Invite and manage system users</p>
              </div>
              <button
                onClick={() => setIsInviteModalOpen(true)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                <div className="flex items-center">
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Invite New User
                </div>
              </button>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1">
          <div className="max-w-7xl mx-auto px-6 py-6">
            {/* Success Message */}
            {successMessage && (
              <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex">
                  <svg className="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <div className="ml-3">
                    <p className="text-sm text-green-700">{successMessage}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Error Message */}
            {error && (
              <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex">
                  <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                    <p className="text-xs text-red-600 mt-1">
                      Check browser console for more details, or verify your Supabase service role key is configured.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Pending Invitations */}
            <div className="bg-white shadow-sm rounded-lg border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">
                  Pending Invitations ({pendingInvitations.length})
                </h2>
                <p className="text-sm text-gray-600 mt-1">
                  Users who have been invited but haven't accepted yet
                </p>
              </div>

              {loading ? (
                <div className="flex justify-center py-12">
                  <LoadingSpinner />
                </div>
              ) : pendingInvitations.length === 0 ? (
                <div className="text-center py-12">
                  <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.196-2.196M17 20H7m10 0v-2c0-1.333-.312-2.588-.863-3.707M7 20v-2a3 3 0 015.196-2.196M7 20v-2c0-1.333.312-2.588.863-3.707m2.137-3.293A7.002 7.002 0 1118 12.172M7 20H2v-2a3 3 0 015.196-2.196M2 12.172C2 8.735 4.735 6 8.172 6a7.002 7.002 0 017.656 4.172" />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No pending invitations</h3>
                  <p className="mt-1 text-sm text-gray-500">All invited users have joined the system.</p>
                </div>
              ) : (
                <div className="overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          User
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Role
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Invited At
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {pendingInvitations.map((invitation) => (
                        <tr key={invitation.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {invitation.full_name || invitation.user_metadata?.full_name || 'Unknown'}
                              </div>
                              <div className="text-sm text-gray-500">{invitation.email}</div>
                              {invitation.source === 'auth_only' && (
                                <div className="text-xs text-orange-600 mt-1">
                                  ⚠️ Needs database record repair
                                </div>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleBadgeColor(invitation.role || invitation.user_metadata?.role)}`}>
                              {invitation.role || invitation.user_metadata?.role || 'Unknown'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {invitation.invited_at ? formatDate(invitation.invited_at) : 'Unknown'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            {invitation.is_orphaned ? (
                              <div className="flex items-center justify-end space-x-3">
                                <button
                                  onClick={() => handleRepairInvitation(invitation.id)}
                                  disabled={actionLoading === invitation.id}
                                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 disabled:opacity-50 transition-colors"
                                >
                                  <Icon platform="repair" variant="none" className="mr-2 h-4 w-4" />
                                  Repair
                                </button>
                              </div>
                            ) : (
                              <div className="flex items-center justify-end space-x-3">
                                <button
                                  onClick={() => handleResendInvitation(invitation.email, invitation.id)}
                                  disabled={actionLoading === invitation.id}
                                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 transition-colors"
                                >
                                  <Icon platform="send" variant="none" className="mr-2 h-4 w-4" />
                                  Resend
                                </button>
                                <button
                                  onClick={() => handleCancelInvitation(invitation.id)}
                                  disabled={actionLoading === invitation.id}
                                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 transition-colors"
                                >
                                  <Icon platform="trash" variant="none" className="mr-2 h-4 w-4" />
                                  Revoke
                                </button>
                              </div>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        </main>
      </div>

      {/* Invite User Modal */}
      <InviteUserModal
        isOpen={isInviteModalOpen}
        onClose={() => setIsInviteModalOpen(false)}
        onSuccess={handleInviteSuccess}
      />
    </div>
  );
};

export default UserManagementPage; 