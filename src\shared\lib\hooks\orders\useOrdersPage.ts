import { useState, useReducer, useCallback, useMemo, useEffect } from 'react';
import { ExtendedOrderFilters, updateUrlWithFilters, getFiltersFromUrl } from '@/shared/lib/utils/data/url-params';
import { useOrderData } from './useOrderData';
import { useAuth } from '@/app/providers/AuthProvider';

// Create a stable empty object for initialization
const EMPTY_FILTERS: ExtendedOrderFilters = { page: 1 };

type FilterAction =
  | { type: 'INITIALIZE_FILTERS'; payload: ExtendedOrderFilters }
  | { type: 'SET_SEARCH'; payload: { searchTerm: string } }
  | { type: 'APPLY_FILTERS'; payload: { filters: ExtendedOrderFilters; searchTerm: string } }
  | { type: 'REMOVE_FILTER'; payload: { filterType: keyof ExtendedOrderFilters; value?: string } }
  | { type: 'CLEAR_ALL_FILTERS'; payload: { searchTerm: string } };

const filtersReducer = (state: ExtendedOrderFilters, action: FilterAction): ExtendedOrderFilters => {
  let updatedFilters: ExtendedOrderFilters;
  switch (action.type) {
    case 'INITIALIZE_FILTERS':
      return action.payload;
    case 'SET_SEARCH':
      updatedFilters = { ...state, search: action.payload.searchTerm || undefined, page: 1 };
      updateUrlWithFilters(updatedFilters);
      return updatedFilters;
    case 'APPLY_FILTERS':
      updatedFilters = { ...action.payload.filters, search: action.payload.searchTerm || undefined, page: 1 };
      updateUrlWithFilters(updatedFilters);
      return updatedFilters;
    case 'REMOVE_FILTER':
      updatedFilters = { ...state };
      const { filterType, value } = action.payload;
      if (value && Array.isArray(updatedFilters[filterType])) {
        (updatedFilters[filterType] as string[]) = (updatedFilters[filterType] as string[]).filter(item => item !== value);
        if ((updatedFilters[filterType] as string[]).length === 0) delete updatedFilters[filterType];
      } else {
        delete updatedFilters[filterType];
      }
      updatedFilters.page = 1;
      updateUrlWithFilters(updatedFilters);
      return updatedFilters;
    case 'CLEAR_ALL_FILTERS':
      updatedFilters = { search: action.payload.searchTerm || undefined, page: 1 };
      updateUrlWithFilters(updatedFilters);
      return updatedFilters;
    default:
      return state;
  }
};

export const useOrdersPage = () => {
  const { profile } = useAuth();
  
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);

  const [filters, dispatch] = useReducer(filtersReducer, EMPTY_FILTERS);
  const searchTerm = useMemo(() => filters.search || '', [filters.search]);

  // Initialize filters from URL only once
  useEffect(() => {
    const urlFilters = getFiltersFromUrl();
    if (Object.keys(urlFilters).length > 0) {
      dispatch({ type: 'INITIALIZE_FILTERS', payload: urlFilters });
    }
  }, []);

  // Compute active filter count once
  const activeFiltersCount = useMemo(() => {
    const { page, limit, search, ...actualFilters } = filters;
    return Object.keys(actualFilters).length;
  }, [filters]);
  
  // Handler functions - all memoized with proper dependencies
  const handleSearchChange = useCallback((newSearchTerm: string) => {
    dispatch({ type: 'SET_SEARCH', payload: { searchTerm: newSearchTerm } });
  }, []);

  const handleApplyFilters = useCallback((newFilters: ExtendedOrderFilters) => {
    dispatch({ type: 'APPLY_FILTERS', payload: { filters: newFilters, searchTerm } });
  }, [searchTerm]);

  const handleRemoveFilter = useCallback((filterType: keyof ExtendedOrderFilters, value?: string) => {
    dispatch({ type: 'REMOVE_FILTER', payload: { filterType, value } });
  }, []);

  const handleClearAllFilters = useCallback(() => {
    dispatch({ type: 'CLEAR_ALL_FILTERS', payload: { searchTerm } });
  }, [searchTerm]);

  // Get order data from hook
  const {
    orders,
    loading: isLoading,
    error,
    totalCount,
    currentPage,
    totalPages,
    userRole,
    selectedOrder,
    fetchOrder,
    refreshOrders,
    clearSelectedOrder,
    fetchOrders,
  } = useOrderData();

  // Only fetch when filters actually change
  useEffect(() => {
    fetchOrders(filters);
  }, [
    filters.page,
    filters.search,
    filters.status?.length,
    filters.platform?.length,
    filters.channel?.length,
    filters.isUrgent,
    filters.isProblem,
    filters.hasNotes,
    filters.isResent,
    filters.dateFrom,
    filters.dateTo,
    fetchOrders
  ]);
  
  // Memoize pagination to prevent unnecessary renders
  const pagination = useMemo(() => ({
    totalCount,
    currentPage,
    totalPages,
  }), [totalCount, currentPage, totalPages]);

  // Panel state
  const [panelOrderId, setPanelOrderId] = useState<string | null>(null);
  const [isPanelOpen, setIsPanelOpen] = useState(false);

  const openPanel = useCallback((orderId: string) => {
    setPanelOrderId(orderId);
    setIsPanelOpen(true);
    fetchOrder(orderId);
  }, [fetchOrder]);

  const closePanel = useCallback(() => {
    setIsPanelOpen(false);
    setPanelOrderId(null);
    clearSelectedOrder();
  }, [clearSelectedOrder]);

  // Memoize the setFilters function to prevent unnecessary renders
  const setFilters = useCallback((newFilters: ExtendedOrderFilters) => {
    dispatch({ 
      type: 'APPLY_FILTERS', 
      payload: { filters: newFilters, searchTerm } 
    });
  }, [searchTerm]);

  // Return memoized object to prevent unnecessary renders of consumers
  return useMemo(() => ({
    profile,
    isFilterModalOpen,
    searchTerm,
    filters,
    activeFiltersCount,
    combinedFilters: filters,
    orders,
    isLoading,
    error,
    pagination,
    userRole,
    selectedOrder,
    panelOrderId,
    isPanelOpen,
    setIsFilterModalOpen,
    handleSearchChange,
    handleApplyFilters,
    handleRemoveFilter,
    handleClearAllFilters,
    openPanel,
    closePanel,
    refreshOrders,
    setFilters
  }), [
    profile,
    isFilterModalOpen,
    searchTerm,
    filters,
    activeFiltersCount,
    orders,
    isLoading,
    error,
    pagination,
    userRole,
    selectedOrder,
    panelOrderId,
    isPanelOpen,
    handleSearchChange,
    handleApplyFilters,
    handleRemoveFilter,
    handleClearAllFilters,
    openPanel,
    closePanel,
    refreshOrders,
    setFilters
  ]);
}; 