import React, { useState } from 'react';
import { useProductForm } from '@/shared/lib/hooks/inventory/products/useProductForm';
import { Product, ProductStatusEnum } from '@/types';
import { ExclamationCircleIcon, CheckCircleIcon } from '@heroicons/react/24/outline';

interface ProductFormProps {
  initialData?: any;
  onSuccess?: (product: Product) => void;
  onCancel?: () => void;
  submitButtonText?: string;
}

// Required field indicator component
const RequiredIndicator = () => (
  <span className="text-red-500 ml-1">*</span>
);

const ProductForm: React.FC<ProductFormProps> = ({
  initialData,
  onSuccess,
  onCancel,
  submitButtonText = 'Save Product'
}) => {
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const {
    formData,
    errors,
    isSubmitting,
    submitError,
    businessUnits,
    isLoading,
    handleChange,
    handleSubmit
  } = useProductForm({
    initialData,
    onSuccess: (product) => {
      setSuccessMessage(`Product ${initialData?.id ? 'updated' : 'created'} successfully`);
      setTimeout(() => {
        setSuccessMessage(null);
        if (onSuccess) {
          onSuccess(product);
        }
      }, 3000);
    }
  });

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    // Convert numeric values to proper types
    if (type === 'number') {
      handleChange(name as any, value === '' ? null : parseFloat(value));
    } else {
      handleChange(name as any, value);
    }
  };
  
  // Handle form submission
  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    handleSubmit();
  };

  return (
    <div className="bg-white rounded-lg">
      <form onSubmit={onSubmit} className="space-y-6">
        {/* Success message */}
        {successMessage && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-start">
            <CheckCircleIcon className="h-5 w-5 text-green-400 mt-0.5 mr-3" />
            <p className="text-green-800">{successMessage}</p>
          </div>
        )}
        
        {/* Error message */}
        {submitError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start">
            <ExclamationCircleIcon className="h-5 w-5 text-red-400 mt-0.5 mr-3" />
            <p className="text-red-800">{submitError}</p>
          </div>
        )}
        
        {/* Required fields note */}
        <div className="text-sm text-gray-600 mb-6 italic flex items-center">
          <span className="text-red-500 mr-1">*</span> Required fields
        </div>
        
        {/* Form fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="md:col-span-2">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Basic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Product Name */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Product Name <RequiredIndicator />
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className={`shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md ${
                    errors.name ? 'border-red-300' : ''
                  }`}
                  disabled={isSubmitting}
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                )}
              </div>
              
              {/* SKU */}
              <div>
                <label htmlFor="sku" className="block text-sm font-medium text-gray-700 mb-1">
                  SKU <RequiredIndicator />
                </label>
                <input
                  type="text"
                  id="sku"
                  name="sku"
                  value={formData.sku}
                  onChange={handleInputChange}
                  className={`shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md ${
                    errors.sku ? 'border-red-300' : ''
                  }`}
                  disabled={isSubmitting}
                />
                {errors.sku && (
                  <p className="mt-1 text-sm text-red-600">{errors.sku}</p>
                )}
              </div>
              
              {/* Status */}
              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                  Status <RequiredIndicator />
                </label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className={`shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md ${
                    errors.status ? 'border-red-300' : ''
                  }`}
                  disabled={isSubmitting}
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="discontinued">Discontinued</option>
                </select>
                {errors.status && (
                  <p className="mt-1 text-sm text-red-600">{errors.status}</p>
                )}
              </div>
              
              {/* Business Unit */}
              <div>
                <label htmlFor="business_unit_id" className="block text-sm font-medium text-gray-700 mb-1">
                  Business Unit
                </label>
                <select
                  id="business_unit_id"
                  name="business_unit_id"
                  value={formData.business_unit_id || ''}
                  onChange={handleInputChange}
                  className={`shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md ${
                    errors.business_unit_id ? 'border-red-300' : ''
                  }`}
                  disabled={isSubmitting || isLoading}
                >
                  <option value="">No specific business unit</option>
                  {businessUnits.map(unit => (
                    <option key={unit.id} value={unit.id}>
                      {unit.name}
                    </option>
                  ))}
                </select>
                {errors.business_unit_id && (
                  <p className="mt-1 text-sm text-red-600">{errors.business_unit_id}</p>
                )}
              </div>
              
              {/* Description */}
              <div className="md:col-span-2">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  id="description"
                  name="description"
                  rows={3}
                  value={formData.description}
                  onChange={handleInputChange}
                  className={`shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md ${
                    errors.description ? 'border-red-300' : ''
                  }`}
                  disabled={isSubmitting}
                ></textarea>
                {errors.description && (
                  <p className="mt-1 text-sm text-red-600">{errors.description}</p>
                )}
              </div>
            </div>
          </div>
          
          {/* Pricing */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">Pricing</h3>
            <div className="space-y-4">
              {/* Unit Cost */}
              <div>
                <label htmlFor="unit_cost" className="block text-sm font-medium text-gray-700 mb-1">
                  Unit Cost
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 sm:text-sm">$</span>
                  </div>
                  <input
                    type="number"
                    id="unit_cost"
                    name="unit_cost"
                    value={formData.unit_cost === null ? '' : formData.unit_cost}
                    onChange={handleInputChange}
                    step="0.01"
                    min="0"
                    className={`pl-7 focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md ${
                      errors.unit_cost ? 'border-red-300' : ''
                    }`}
                    disabled={isSubmitting}
                  />
                </div>
                {errors.unit_cost && (
                  <p className="mt-1 text-sm text-red-600">{errors.unit_cost}</p>
                )}
              </div>
              
              {/* Unit Price */}
              <div>
                <label htmlFor="unit_price" className="block text-sm font-medium text-gray-700 mb-1">
                  Unit Price <RequiredIndicator />
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 sm:text-sm">$</span>
                  </div>
                  <input
                    type="number"
                    id="unit_price"
                    name="unit_price"
                    value={formData.unit_price === null ? '' : formData.unit_price}
                    onChange={handleInputChange}
                    step="0.01"
                    min="0"
                    className={`pl-7 focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md ${
                      errors.unit_price ? 'border-red-300' : ''
                    }`}
                    disabled={isSubmitting}
                  />
                </div>
                {errors.unit_price && (
                  <p className="mt-1 text-sm text-red-600">{errors.unit_price}</p>
                )}
              </div>
              
              {/* Initial Stock (only for new products) */}
              {!initialData?.id && (
                <div>
                  <label htmlFor="initial_stock" className="block text-sm font-medium text-gray-700 mb-1">
                    Initial Stock
                  </label>
                  <input
                    type="number"
                    id="initial_stock"
                    name="initial_stock"
                    value={formData.initial_stock === null ? '' : formData.initial_stock}
                    onChange={handleInputChange}
                    min="0"
                    step="1"
                    className={`shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md ${
                      errors.initial_stock ? 'border-red-300' : ''
                    }`}
                    disabled={isSubmitting}
                  />
                  {errors.initial_stock && (
                    <p className="mt-1 text-sm text-red-600">{errors.initial_stock}</p>
                  )}
                  <p className="mt-1 text-xs text-gray-500">Initial inventory quantity</p>
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* Form Actions */}
        <div className="pt-5 border-t border-gray-200">
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : submitButtonText}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default ProductForm; 