import { createClient, SupabaseClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY 
const supabaseServiceKey = import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env file.')
}

// Export constants for use in other files
export { supabaseUrl, supabaseAnonKey }

// Singleton instances - prevents recreation even if this module is imported multiple times
let supabaseInstance: SupabaseClient | null = null;
let supabaseAdminInstance: SupabaseClient | null = null;

// Track connection status
let realtimeConnected = false;
let connectionRetryCount = 0;
const MAX_RETRY_COUNT = 5;

// Function to handle WebSocket status changes
const handleWebSocketStatusChange = (status: string) => {
  const enableDebug = process.env.NODE_ENV === 'development';
  
  if (enableDebug) {
    console.log(`Supabase Realtime status: ${status}`);
  }
  
  if (status === 'CONNECTED') {
    realtimeConnected = true;
    connectionRetryCount = 0; // Reset retry counter on successful connection
  } else if (status === 'DISCONNECTED' && supabaseInstance) {
    realtimeConnected = false;
    
    // Only try to reconnect if we haven't exceeded max retries
    if (connectionRetryCount < MAX_RETRY_COUNT) {
      connectionRetryCount++;
      
      // Exponential backoff for retries
      const delay = Math.min(1000 * Math.pow(2, connectionRetryCount - 1), 30000);
      
      if (enableDebug) {
        console.log(`Realtime disconnected, attempting reconnect in ${delay}ms (attempt ${connectionRetryCount})`);
      }
      
      setTimeout(() => {
        try {
          if (supabaseInstance && !realtimeConnected) {
            // Force a reconnect by creating a new channel
            const channel = supabaseInstance.channel('reconnect-trigger');
            channel.subscribe((status) => {
              if (status === 'SUBSCRIBED') {
                supabaseInstance?.removeChannel(channel);
              }
            });
          }
        } catch (error) {
          console.error('Error reconnecting to Supabase Realtime:', error);
        }
      }, delay);
    } else if (enableDebug) {
      console.error(`Failed to reconnect after ${MAX_RETRY_COUNT} attempts`);
    }
  }
};

// Get the regular client instance - create only once
export const supabase = (() => {
  if (!supabaseInstance) {
    // Enable debug logging in development
    const enableDebug = process.env.NODE_ENV === 'development';
    
    if (enableDebug) {
      console.log('Initializing Supabase client with debug logging enabled');
    }
    
    supabaseInstance = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        persistSession: true,
        storageKey: 'washolding-internal-auth', // Unique storage key to avoid conflicts
        autoRefreshToken: true,
        debug: false // Disable auth debug logs to reduce console noise
      },
      realtime: {
        params: {
          eventsPerSecond: 10 // Increase from default 1
        }
      },
      global: {
        headers: {
          'x-application-name': 'washolding-app'
        }
      }
    });
    
    // Only add debug listeners if explicitly requested
    // This prevents automatic connection creation on import
  }
  return supabaseInstance;
})();

// Create a getter function for the admin client to lazy-load it only when needed
const getAdminClient = (): SupabaseClient | null => {
  if (!supabaseAdminInstance && supabaseServiceKey) {
    supabaseAdminInstance = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        storageKey: 'washolding-internal-admin-auth' // Different storage key for admin
      },
      realtime: {
        params: {
          eventsPerSecond: 10 // Increase from default 1
        }
      }
    });
  }
  return supabaseAdminInstance;
};

// Export a proxy that will only initialize the admin client when actually used
export const supabaseAdmin = new Proxy({} as SupabaseClient, {
  get: (target, prop) => {
    const adminClient = getAdminClient();
    if (!adminClient) {
      throw new Error('Admin client not available. Missing service role key.');
    }
    return adminClient[prop as keyof SupabaseClient];
  }
});

// Helper to check if admin operations are available
export const isAdminAvailable = () => !!supabaseServiceKey;

// Export instance info for internal use
export const getSupabaseInstanceInfo = () => {
  return {
    hasClientInstance: !!supabaseInstance,
    hasAdminInstance: !!supabaseAdminInstance,
    realtimeConnected
  };
};

// Helper to enable debug mode and monitoring
export const enableRealtimeDebugMode = () => {
  if (!supabaseInstance) return false;
  
  try {
    // Add event listener for Supabase debug events
    window.addEventListener('supabase.debug', (e: any) => {
      if (e.detail?.extension === 'realtime') {
        console.log('Supabase Realtime debug:', e.detail);
        
        // Track connection status changes
        if (e.detail?.eventType === 'connection_status') {
          handleWebSocketStatusChange(e.detail.data?.status);
        }
      }
    });
    
    // Create a monitoring channel to track connection status
    const monitoringChannel = supabaseInstance.channel('connection-monitor');
    monitoringChannel.subscribe((status) => {
      handleWebSocketStatusChange(status);
    });
    
    return true;
  } catch (error) {
    console.error('Failed to enable Realtime debug mode:', error);
    return false;
  }
};

// Helper to force reconnect Realtime
export const forceRealtimeReconnect = () => {
  if (supabaseInstance) {
    try {
      // Create a temporary channel to trigger reconnection
      const channel = supabaseInstance.channel('manual-reconnect-trigger');
      channel.subscribe(() => {
        // Remove the channel once we've triggered a reconnection
        setTimeout(() => {
          if (supabaseInstance) {
            supabaseInstance.removeChannel(channel);
          }
        }, 1000);
      });
      return true;
    } catch (error) {
      console.error('Error forcing Realtime reconnection:', error);
      return false;
    }
  }
  return false;
};
