import { WebSocketConfig, WebSocketState, WebSocketMessage } from './websocket-config';

export class WebSocketHeartbeat {
  private config: WebSocketConfig;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private isDestroyed = false;

  constructor(config: WebSocketConfig) {
    this.config = config;
  }

  /**
   * Starts heartbeat mechanism
   */
  public start(
    getIsConnected: () => boolean,
    onStateUpdate: (updates: Partial<WebSocketState>) => void,
    sendMessage: (message: Omit<WebSocketMessage, 'timestamp'>) => void
  ): void {
    this.stop();
    
    this.heartbeatTimer = setInterval(() => {
      if (getIsConnected()) {
        sendMessage({
          type: 'heartbeat',
          payload: { clientTime: new Date().toISOString() },
        });
        
        onStateUpdate({
          lastHeartbeat: new Date(),
        });
      }
    }, this.config.heartbeatInterval);
  }

  /**
   * Stops heartbeat mechanism
   */
  public stop(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * Updates heartbeat timestamp when receiving heartbeat response
   */
  public updateHeartbeat(onStateUpdate: (updates: Partial<WebSocketState>) => void): void {
    onStateUpdate({
      lastHeartbeat: new Date(),
    });
  }

  /**
   * Destroys the heartbeat manager
   */
  public destroy(): void {
    this.isDestroyed = true;
    this.stop();
  }
} 