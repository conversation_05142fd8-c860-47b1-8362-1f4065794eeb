# Supabase Edge Functions

This directory contains Supabase Edge Functions for the Washolding Internal App.

## Development

### Prerequisites

1. [Supabase CLI](https://supabase.com/docs/guides/cli)
2. Deno runtime

### Local Development

```bash
# Start the local development server
supabase start

# Deploy a specific function to the local development server
supabase functions serve bulk-stock-update --no-verify-jwt

# Test the function
curl -i --location --request POST 'http://localhost:54321/functions/v1/bulk-stock-update' \
  --header 'Authorization: Bearer YOUR_JWT_TOKEN' \
  --header 'Content-Type: application/json' \
  --data '{
    "items": [
      {
        "productId": "123",
        "quantity": 10,
        "type": "increase",
        "reason": "Purchase",
        "reasonCategory": "supplier_delivery"
      }
    ],
    "userId": "user123"
  }'
```

### Deploying to Production

```bash
# Deploy a specific function to production
supabase functions deploy bulk-stock-update --project-ref YOUR_PROJECT_REF
```

## Available Functions

### bulk-stock-update

Processes bulk inventory stock updates with transaction history.

**Request:**
```json
{
  "items": [
    {
      "productId": "string",
      "quantity": "number",
      "type": "increase|decrease|set",
      "reason": "string",
      "reasonCategory": "string",
      "notes": "string (optional)"
    }
  ],
  "userId": "string"
}
```

**Response:**
```json
{
  "success": "boolean",
  "results": [
    {
      "productId": "string",
      "success": "boolean",
      "previousStock": "number",
      "newStock": "number"
    }
  ],
  "errors": [
    {
      "productId": "string",
      "success": "boolean",
      "error": "string"
    }
  ],
  "processedCount": "number",
  "failedCount": "number",
  "totalCount": "number"
}
``` 