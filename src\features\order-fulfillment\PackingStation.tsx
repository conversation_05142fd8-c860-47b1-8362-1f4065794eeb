import * as React from 'react';
import { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { OrderStatus } from '@/types';
import { ProductAccordion, ProductBatch } from './ProductAccordion';
import { bulkPackOrders } from '@/shared/lib/services/fulfill/fulfill-service';
import { useAuth } from '@/app/providers/AuthProvider';

interface PackingStationProps {
  productBatches: ProductBatch[];
  onStatusChange: (orderId: string, newStatus: OrderStatus, userId: string, trackingId?: string) => void;
  openOrderCount: number;
  onDataRefresh?: () => Promise<void>;
}

export const PackingStation: React.FC<PackingStationProps> = React.memo(({ 
  productBatches, 
  onStatusChange, 
  openOrderCount,
  onDataRefresh
}) => {
  const { user } = useAuth();
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [animatingOut, setAnimatingOut] = useState<Set<string>>(new Set());
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Use ref to track component mounted state to prevent memory leaks
  const isMountedRef = useRef(true);
  
  // Store refs to props to maintain stable references
  const onStatusChangeRef = useRef(onStatusChange);
  const onDataRefreshRef = useRef(onDataRefresh);

  // Update refs when props change
  useEffect(() => {
    onStatusChangeRef.current = onStatusChange;
    onDataRefreshRef.current = onDataRefresh;
  }, [onStatusChange, onDataRefresh]);
  
  // Clean up on unmount
  useEffect(() => {
    // Set mounted flag
    isMountedRef.current = true;
    
    // Clean up function runs when component unmounts
    return () => {
      isMountedRef.current = false;
      // Clear selections and animations to prevent memory leaks
      setSelectedItems(new Set());
      setAnimatingOut(new Set());
    };
  }, []);

  // Handle status updates for bulk operations - memoized with useCallback
  const handleStatusUpdate = useCallback(async (orderIds: string[], newStatus: string): Promise<void> => {
    if (!user?.id) {
      setError('User authentication required. Please log in again.');
      return;
    }

    setIsUpdating(true);
    setError(null);

    try {
      // Add orders to animating set
      setAnimatingOut(prev => {
        const newSet = new Set(prev);
        orderIds.forEach(id => newSet.add(id));
        return newSet;
      });
      
      // Use bulkPackOrders instead of individual updateOrderStatus calls
      if (newStatus === 'packed') {
        // Use the authenticated user's ID
        await bulkPackOrders(orderIds, user.id);
      } else {
        // For other statuses, fall back to individual updates
        for (const orderId of orderIds) {
          await onStatusChangeRef.current(orderId, newStatus as OrderStatus, user.id);
        }
      }
      
      // Remove from selected set (only if component is still mounted)
      if (isMountedRef.current) {
        setSelectedItems(prev => {
          const newSet = new Set(prev);
          orderIds.forEach(id => newSet.delete(id));
          return newSet;
        });
      }
      
      // Clear animating after delay (only if component is still mounted)
      setTimeout(() => {
        if (isMountedRef.current) {
          setAnimatingOut(prev => {
            const newSet = new Set(prev);
            orderIds.forEach(id => newSet.delete(id));
            return newSet;
          });
        }
      }, 1000); // Animation duration (800ms) + extra buffer (200ms)
      
      if (onDataRefreshRef.current && isMountedRef.current) {
        await onDataRefreshRef.current();
      }
    } catch (error) {
      console.error('Failed to update order status:', error);
      setError(error instanceof Error ? error.message : 'Failed to update order status');
    } finally {
      if (isMountedRef.current) {
        setIsUpdating(false);
      }
    }
  }, [user]);

  // Memoized setter for selectedItems to prevent unnecessary re-renders
  const handleSetSelectedItems = useCallback((newSelectedItems: Set<string> | ((prev: Set<string>) => Set<string>)) => {
    if (isMountedRef.current) {
      setSelectedItems(newSelectedItems);
    }
  }, []);

  // Memoize the status change handler to provide stable reference
  const handleStatusChangeStable = useCallback((orderId: string, newStatus: OrderStatus, userId: string) => {
    onStatusChangeRef.current(orderId, newStatus, userId);
  }, []);

  // Memoize the data refresh handler to provide stable reference
  const handleDataRefreshStable = useCallback(async () => {
    if (onDataRefreshRef.current) {
      await onDataRefreshRef.current();
    }
  }, []);

  // Show completion state when all open orders are packed
  if (openOrderCount === 0) {
    return (
      <div className="h-full overflow-y-auto">
        <div className="max-w-6xl mx-auto p-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg className="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">All Orders Packed!</h2>
            <p className="text-gray-600">Great job! All open orders have been processed and are ready for shipping.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto">
      <div className="max-w-6xl mx-auto p-6">
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <svg className="h-5 w-5 text-red-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              <span className="text-red-800 text-sm">{error}</span>
              <button
                onClick={() => setError(null)}
                className="ml-auto text-red-600 hover:text-red-800"
                aria-label="Dismiss error message"
              >
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        )}
        <div className="space-y-6">
          {productBatches.map((batch) => (
            <ProductAccordion 
              key={batch.sku} 
              batch={batch}
              onStatusChange={handleStatusChangeStable}
              onDataRefresh={handleDataRefreshStable}
              selectedItems={selectedItems}
              setSelectedItems={handleSetSelectedItems}
              animatingOut={animatingOut}
              onStatusUpdate={handleStatusUpdate}
              isUpdating={isUpdating}
            />
          ))}
        </div>
      </div>
    </div>
  );
});

PackingStation.displayName = 'PackingStation';