import fs from 'fs';
import path from 'path';

// Configuration
const DIST_DIR = path.resolve('dist');
const ASSETS_DIR = path.join(DIST_DIR, 'assets');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

// Function to format file size
const formatSize = (bytes) => {
  if (bytes < 1024) return bytes + ' B';
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
  return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
};

// Function to check if a chunk contains certain libraries
const chunkContains = (chunkContent, keywords) => {
  return keywords.some(keyword => chunkContent.includes(keyword));
};

// Main analysis function
const analyzeChunks = () => {
  console.log(`${colors.bright}${colors.blue}=== Vendor Chunk Analysis ===${colors.reset}\n`);

  // Check if dist directory exists
  if (!fs.existsSync(DIST_DIR)) {
    console.error(`${colors.red}Error: dist directory not found. Run 'npm run build' first.${colors.reset}`);
    process.exit(1);
  }

  // Get all JS files in the assets directory
  const files = fs.readdirSync(ASSETS_DIR).filter(file => file.endsWith('.js'));
  
  // Get HTML files to check script loading order
  const htmlFiles = fs.readdirSync(DIST_DIR).filter(file => file.endsWith('.html'));
  const htmlContent = htmlFiles.length > 0 ? fs.readFileSync(path.join(DIST_DIR, htmlFiles[0]), 'utf-8') : '';
  
  // Extract vendor chunks
  const vendorChunks = files.filter(file => file.includes('vendor-'));

  // Sort vendor chunks by their prefixes to match loading order
  const sortedVendorChunks = [...vendorChunks].sort();
  
  // Get chunk loading order from HTML
  const chunkOrder = [];
  if (htmlContent) {
    const scriptTags = htmlContent.match(/<script[^>]*src="[^"]*"[^>]*>/g) || [];
    for (const tag of scriptTags) {
      const src = tag.match(/src="([^"]*)"/)?.[1];
      if (src) {
        const fileName = path.basename(src);
        if (vendorChunks.includes(fileName)) {
          chunkOrder.push(fileName);
        }
      }
    }
  }

  // Print chunk information
  console.log(`${colors.bright}Found ${vendorChunks.length} vendor chunks:${colors.reset}\n`);

  // Print sorted by name (roughly matches load order)
  console.log(`${colors.bright}Chunks by name:${colors.reset}`);
  for (const chunk of sortedVendorChunks) {
    const filePath = path.join(ASSETS_DIR, chunk);
    const stats = fs.statSync(filePath);
    const content = fs.readFileSync(filePath, 'utf-8');
    
    // Determine chunk type
    let chunkType = '';
    if (chunk.includes('react-core')) chunkType = `${colors.green}[React Core]${colors.reset}`;
    else if (chunk.includes('react-dom')) chunkType = `${colors.green}[React DOM]${colors.reset}`;
    else if (chunk.includes('react-dependent')) chunkType = `${colors.green}[React Dependent]${colors.reset}`;
    else if (chunk.includes('data-utils')) chunkType = `${colors.yellow}[Data Utils]${colors.reset}`;
    
    // Check for problematic libraries
    let containsZustand = chunkContains(content, ['zustand', 'createStore', 'useStore']);
    let containsTanstack = chunkContains(content, ['@tanstack', 'QueryClient', 'createQuery']);
    let containsReact = chunkContains(content, ['react.', 'createContext', 'createElement']);
    
    // Build the dependencies string
    let dependencies = [];
    if (containsReact) dependencies.push('React');
    if (containsZustand) dependencies.push('Zustand');
    if (containsTanstack) dependencies.push('Tanstack');
    
    // Output chunk info
    console.log(`${colors.bright}${chunk}${colors.reset} ${chunkType} - ${formatSize(stats.size)}`);
    if (dependencies.length > 0) {
      console.log(`  Contains: ${colors.cyan}${dependencies.join(', ')}${colors.reset}`);
    }
    
    // Check for potential issues
    if ((containsZustand || containsTanstack) && !containsReact && !chunk.includes('react-dependent')) {
      console.log(`  ${colors.red}⚠️ WARNING: Contains React-dependent libraries but not in react-dependent chunk${colors.reset}`);
    }
    
    console.log('');
  }
  
  // Print actual loading order from HTML
  if (chunkOrder.length > 0) {
    console.log(`${colors.bright}Actual script loading order from HTML:${colors.reset}`);
    for (let i = 0; i < chunkOrder.length; i++) {
      const chunk = chunkOrder[i];
      console.log(`${i + 1}. ${chunk}`);
    }
    
    // Check for correct ordering
    const reactCoreIndex = chunkOrder.findIndex(c => c.includes('vendor-react-core'));
    const reactDependentIndex = chunkOrder.findIndex(c => c.includes('vendor-react-dependent'));
    
    if (reactCoreIndex > -1 && reactDependentIndex > -1) {
      if (reactCoreIndex < reactDependentIndex) {
        console.log(`\n${colors.green}✓ React core loads before React-dependent libraries${colors.reset}`);
      } else {
        console.log(`\n${colors.red}❌ WARNING: React-dependent libraries load before React core${colors.reset}`);
      }
    }
  } else {
    console.log(`${colors.yellow}No HTML file found to check loading order${colors.reset}`);
  }
};

// Run the analysis
analyzeChunks(); 