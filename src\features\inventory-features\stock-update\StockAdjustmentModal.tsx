import React, { useState, useEffect } from 'react';
import Modal from '@/shared/ui/overlay/Modal';
import { InventoryView, InventoryReasonCategory } from '@/types';
import { StockUpdateFormData, BulkStockUpdateFormData } from './index';

interface StockAdjustmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  products: InventoryView[]; // Can be single product or multiple products
  onSuccess?: (data: StockUpdateFormData | BulkStockUpdateFormData) => void;
  onError?: (error: Error) => void;
}

const StockAdjustmentModal: React.FC<StockAdjustmentModalProps> = ({
  isOpen,
  onClose,
  products,
  onSuccess,
  onError,
}) => {
  // Local state for form values
  const [adjustmentType, setAdjustmentType] = useState<'increase' | 'decrease' | 'set'>('increase');
  const [quantity, setQuantity] = useState<string>('');
  const [reason, setReason] = useState<string>('');
  const [reasonCategory, setReasonCategory] = useState<string>('adjustment');
  const [notes, setNotes] = useState<string>('');
  const [error, setError] = useState<Error | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  // Determine if we're in bulk mode (multiple products)
  const isBulkMode = products.length > 1;

  // Reset form when modal opens/closes or products change
  useEffect(() => {
    if (isOpen) {
      // Reset form values when modal opens
      setAdjustmentType('increase');
      setQuantity('');
      setReason('');
      setReasonCategory('adjustment');
      setNotes('');
      setError(null);
    }
  }, [isOpen, products]);

  // Close handler
  const handleClose = () => {
    onClose();
  };

  // Form validation
  const validateForm = (): boolean => {
    // Validate quantity
    const parsedQuantity = parseFloat(quantity);
    if (isNaN(parsedQuantity) || parsedQuantity <= 0) {
      setError(new Error('Quantity must be a positive number'));
      return false;
    }

    // Validate reason
    if (!reason.trim()) {
      setError(new Error('Reason is required'));
      return false;
    }

    setError(null);
    return true;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    const parsedQuantity = parseFloat(quantity);
    setIsUpdating(true);
    
    try {
      if (isBulkMode) {
        // Get product IDs
        const productIds = products
          .filter(p => p.product_id)
          .map(p => p.product_id as string);
        
        // Call the API through the parent's onSuccess callback
        if (onSuccess) {
          onSuccess({
            productIds,
            adjustmentType,
            quantity: parsedQuantity,
            reason,
            reasonCategory: reasonCategory as InventoryReasonCategory,
            notes: notes.trim() || undefined,
          });
        }
      } else if (products.length === 1) {
        // Single product update
        const product = products[0];
        
        // Call the API through the parent's onSuccess callback
                  if (onSuccess) {
          console.log('Submitting stock update for product:', product);
          onSuccess({
            productId: product.product_id || '',
            adjustmentType,
            quantity: parsedQuantity,
            reason,
            reasonCategory: reasonCategory as InventoryReasonCategory,
            notes: notes.trim() || undefined,
          });
        }
      }
      
      // Close the modal on success
      handleClose();
    } catch (err) {
      console.error('Error submitting form:', err);
      const error = err instanceof Error ? err : new Error('Failed to update stock');
      setError(error);
      
      // Call the error callback if provided
      if (onError) {
        onError(error);
      }
    } finally {
      setIsUpdating(false);
    }
  };

  // Get product name for display
  const getProductName = () => {
    if (isBulkMode) {
      return `${products.length} products`;
    } else if (products.length === 1) {
      return products[0].name || 'Product';
    }
    return 'Product';
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={`${adjustmentType === 'increase' ? 'Add' : adjustmentType === 'decrease' ? 'Remove' : 'Set'} Stock - ${getProductName()}`}
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Error message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            {error.message}
          </div>
        )}
        
        {/* Adjustment Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Adjustment Type
          </label>
          <div className="flex space-x-4">
            <label className="inline-flex items-center">
              <input
                type="radio"
                className="form-radio"
                name="adjustmentType"
                value="increase"
                checked={adjustmentType === 'increase'}
                onChange={() => setAdjustmentType('increase')}
              />
              <span className="ml-2">Add Stock</span>
            </label>
            <label className="inline-flex items-center">
              <input
                type="radio"
                className="form-radio"
                name="adjustmentType"
                value="decrease"
                checked={adjustmentType === 'decrease'}
                onChange={() => setAdjustmentType('decrease')}
              />
              <span className="ml-2">Remove Stock</span>
            </label>
            <label className="inline-flex items-center">
              <input
                type="radio"
                className="form-radio"
                name="adjustmentType"
                value="set"
                checked={adjustmentType === 'set'}
                onChange={() => setAdjustmentType('set')}
              />
              <span className="ml-2">Set Stock</span>
            </label>
          </div>
        </div>
        
        {/* Quantity */}
        <div>
          <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-1">
            Quantity
          </label>
          <input
            type="number"
            id="quantity"
            name="quantity"
            value={quantity}
            onChange={(e) => setQuantity(e.target.value)}
            min="0"
            step="1"
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            required
          />
        </div>
        
        {/* Reason Category */}
        <div>
          <label htmlFor="reasonCategory" className="block text-sm font-medium text-gray-700 mb-1">
            Reason Category
          </label>
          <select
            id="reasonCategory"
            name="reasonCategory"
            value={reasonCategory}
            onChange={(e) => setReasonCategory(e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            required
          >
            <option value="inventory_count">Inventory Count</option>
            <option value="new_shipment">New Shipment</option>
            <option value="damaged_goods">Damaged Goods</option>
            <option value="returns">Returns</option>
            <option value="initial_setup">Initial Setup</option>
            <option value="sales">Sales</option>
            <option value="adjustment">Adjustment</option>
            <option value="other">Other</option>
          </select>
        </div>
        
        {/* Reason */}
        <div>
          <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-1">
            Reason
          </label>
          <input
            type="text"
            id="reason"
            name="reason"
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            required
          />
        </div>
        
        {/* Notes */}
        <div>
          <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
            Notes (Optional)
          </label>
          <textarea
            id="notes"
            name="notes"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            rows={3}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
        
        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={handleClose}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isUpdating}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            {isUpdating ? 'Updating...' : 'Update Stock'}
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default StockAdjustmentModal; 