import { RetryOptions } from './retry';
import { ErrorCode } from '@/shared/api/inventory/inventoryAPI-types';

/**
 * Determines if an error from the inventory API should trigger a retry
 */
export const shouldRetryInventoryApi = (error: any): boolean => {
  // If error has our API error structure, use it for decision
  if (error.error?.code) {
    switch (error.error.code) {
      // Network and server errors should be retried
      case ErrorCode.NETWORK_ERROR:
      case ErrorCode.TIMEOUT_ERROR:
      case ErrorCode.SERVER_ERROR:
      case ErrorCode.DATABASE_ERROR:
        return true;
        
      // Client errors should not be retried as they won't succeed
      case ErrorCode.VALIDATION_ERROR:
      case ErrorCode.AUTH_ERROR:
      case ErrorCode.NOT_FOUND:
      case ErrorCode.DUPLICATE_ERROR:
      case ErrorCode.INSUFFICIENT_STOCK:
      case ErrorCode.INVALID_OPERATION:
        return false;
        
      // For unknown errors, only retry if it looks transient
      case ErrorCode.UNKNOWN_ERROR:
        return isLikelyTransient(error);
        
      default:
        return false;
    }
  }
  
  // For errors without our structure, use generic detection
  return isLikelyTransient(error);
};

/**
 * Helper to determine if an error is likely transient based on common patterns
 */
function isLikelyTransient(error: any): boolean {
  // Network connectivity issues
  if (!navigator.onLine) return true;
  
  // Check error message for common transient error patterns
  const message = error.message?.toLowerCase() || '';
  const isNetworkRelated = 
    message.includes('network') || 
    message.includes('connection') ||
    message.includes('offline') ||
    message.includes('abort') ||
    message.includes('fetch');
    
  // Check for timeout patterns
  const isTimeout = 
    message.includes('timeout') || 
    message.includes('timed out') ||
    error.code === 'ECONNABORTED';
    
  // Check for server overload patterns
  const isServerOverload = 
    error.status === 429 || 
    error.status === 503 || 
    error.status === 504 ||
    message.includes('too many requests') ||
    message.includes('service unavailable') ||
    message.includes('gateway timeout');
    
  return isNetworkRelated || isTimeout || isServerOverload;
}

/**
 * Standard retry options for inventory API calls
 */
export const inventoryApiRetryOptions: RetryOptions = {
  maxRetries: 3,
  initialDelay: 500,
  maxDelay: 10000,
  backoffFactor: 2,
  shouldRetry: shouldRetryInventoryApi,
  onRetry: (error, attempt) => {
    console.warn(`Inventory API retry attempt ${attempt}:`, error);
  }
};

/**
 * Read-only operations can be retried more aggressively
 */
export const readOperationRetryOptions: RetryOptions = {
  ...inventoryApiRetryOptions,
  maxRetries: 5,
  initialDelay: 300
};

/**
 * Write operations should be more conservative with retries
 */
export const writeOperationRetryOptions: RetryOptions = {
  ...inventoryApiRetryOptions,
  maxRetries: 2,
  initialDelay: 800
};

/**
 * Bulk operations may need longer timeouts
 */
export const bulkOperationRetryOptions: RetryOptions = {
  ...writeOperationRetryOptions,
  maxRetries: 3,
  maxDelay: 15000
}; 