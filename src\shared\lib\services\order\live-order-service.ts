import { supabase } from '../../../../../supabase/supabase_client/client';
import { AllOrdersViewItem, AllOrdersStatus, AllOrdersDetail, UserRole } from '@/types';
import { orderQueries, customerQueries, orderItemQueries, orderNotesQueries } from './order-queries';
import { orderTransformers } from './data-transformers';

export interface OrderFilters {
  searchTerm?: string;
  search?: string; // Add compatibility with URL params
  status?: AllOrdersStatus[];
  platform?: string[];
  channel?: string[];
  dateRange?: {
    start?: string;
    end?: string;
  };
  dateFrom?: string;
  dateTo?: string;
  customerName?: string;
  hasNotes?: boolean;
  isUrgent?: boolean;
  isReturned?: boolean;
  isProblem?: boolean;
  isResent?: boolean;
  page?: number;
  pageSize?: number;
  skipCache?: boolean; // Add flag to bypass cache for WebSocket refreshes
}

export interface SearchParams {
  orderNumber?: string;
  customerName?: string;
  trackingNumber?: string;
}

export interface PaginationParams {
  page: number;
  limit: number;
}

// Unified response interface for backward compatibility
export interface FetchOrdersResponse {
  orders: AllOrdersViewItem[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  hasMore: boolean; // Computed property for backward compatibility
}

// Alias for cleaner imports
export type OrdersResponse = FetchOrdersResponse;

// Utility function for exponential backoff with retry
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const retryWithBackoff = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = 1,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt < maxRetries) {
        const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000; // Add jitter
        await sleep(delay);
      }
    }
  }
  
  throw lastError!;
};

/**
 * Backward compatibility alias for fetchOrders
 */
export const fetchOrders = async (
  pagination: PaginationParams,
  filters?: OrderFilters,
  search?: SearchParams
  // userRole?: UserRole
): Promise<FetchOrdersResponse> => {
  const combinedFilters: OrderFilters = {
    ...filters,
    page: pagination.page,
    pageSize: pagination.limit,
    searchTerm: search?.orderNumber || search?.customerName || search?.trackingNumber
  };
  
  return getOrders(combinedFilters);
};

/**
 * Performance optimized function to get orders with filtering, search, and pagination
 * Compatible with mock service interface for seamless migration
 */
export const getOrders = async (filters: OrderFilters = {}): Promise<FetchOrdersResponse> => {
  return retryWithBackoff(async () => {
    // Query the orders table directly instead of the view to avoid refresh delays
    let query = supabase
      .from('orders')
      .select(`
        id, 
        order_number,
        order_date,
        status,
        total_amount,
        item_count,
        tracking_number,
        is_urgent,
        is_problem,
        is_resent,
        has_notes,
        customer:customer_id (name),
        platform:platform_id (key, name),
        channel:channel_id (code, name)
      `, { count: 'exact' });

    // Handle search term with special syntax support (matching mock service)
    // Support both searchTerm and search for compatibility
    const searchValue = filters.searchTerm || filters.search;
    if (searchValue) {
      const searchLower = searchValue.toLowerCase();
      
      if (searchLower.startsWith('status:')) {
        const statusValue = searchLower.replace('status:', '');
        query = query.eq('status', statusValue);
      } else if (searchLower.startsWith('urgent:')) {
        const urgentValue = searchLower.replace('urgent:', '') === 'true';
        query = query.eq('is_urgent', urgentValue);
      } else {
        // Global search across multiple fields
        query = query.or(
          `order_number.ilike.%${searchValue}%,` +
          `tracking_number.ilike.%${searchValue}%`
        );
      }
    }

    // Apply status filters
    if (filters.status && filters.status.length > 0) {
      query = query.in('status', filters.status);
    }

    // Apply platform filters
    if (filters.platform && filters.platform.length > 0) {
      query = query.in('platform.key', filters.platform);
    }

    // Apply channel filters
    if (filters.channel && filters.channel.length > 0) {
      query = query.in('channel.code', filters.channel);
    }

    // Apply date range filters - support both dateRange and dateFrom/dateTo
    if (filters.dateRange?.start || filters.dateFrom) {
      const startDate = filters.dateRange?.start || filters.dateFrom;
      query = query.gte('order_date', startDate);
    }
    if (filters.dateRange?.end || filters.dateTo) {
      const endDate = filters.dateRange?.end || filters.dateTo;
      query = query.lte('order_date', endDate);
    }

    // Apply customer name filter
    if (filters.customerName) {
      query = query.ilike('customer.name', `%${filters.customerName}%`);
    }

    // Apply boolean flags
    if (filters.hasNotes !== undefined) {
      query = query.eq('has_notes', filters.hasNotes);
    }
    if (filters.isUrgent !== undefined) {
      query = query.eq('is_urgent', filters.isUrgent);
    }
    if (filters.isProblem !== undefined) {
      query = query.eq('is_problem', filters.isProblem);
    }
    if (filters.isResent !== undefined) {
      query = query.eq('is_resent', filters.isResent);
    }

    // Apply pagination
    const page = filters.page || 1;
    const pageSize = filters.pageSize || 25;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize - 1;
    query = query.range(startIndex, endIndex);

    // Order by most recent first
    query = query.order('order_date', { ascending: false });
    
    const { data, error, count } = await query;

    if (error) {
      console.error('❌ Supabase error:', error);
      throw new Error(`Failed to fetch orders: ${error.message}`);
    }

    const totalCount = count || 0;
    const totalPages = Math.ceil(totalCount / pageSize);

    // Transform nested response to flat structure
    const transformedOrders: AllOrdersViewItem[] = (data || []).map((row: any) => ({
      id: row.id,
      orderNumber: row.order_number,
      orderDate: row.order_date,
      platform: row.platform?.key || '',
      channel: row.channel?.code || '',
      status: row.status,
      customerName: row.customer?.name || '',
      totalAmount: row.total_amount,
      itemCount: row.item_count,
      trackingNumber: row.tracking_number || null,
      hasNotes: row.has_notes || false,
      isUrgent: row.is_urgent || false,
      isProblem: row.is_problem || false,
      isResent: row.is_resent || false
    }));

    if (process.env.NODE_ENV === 'development') {
      console.log(`📊 Fetched ${transformedOrders.length} orders directly from orders table`);
    }

    return {
      orders: transformedOrders,
      totalCount,
      currentPage: page,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
      hasMore: page < totalPages // Backward compatibility
    };
  });
};

/**
 * Fetches a single order by ID from the orders table with full details
 */
export const fetchOrderById = async (orderId: string): Promise<AllOrdersDetail | null> => {
  return retryWithBackoff(async () => {
    // First get order data
    const orderData = await orderQueries.fetchOrderById(orderId);
    if (!orderData) {
      return null;
    }

    // Then fetch related data in parallel
    const [customerData, itemsData, notesData] = await Promise.allSettled([
      customerQueries.fetchCustomerById(orderData.customer_id),
      orderItemQueries.fetchOrderItems(orderId),
      orderNotesQueries.fetchOrderNotes(orderId)
    ]);

    const customer = customerData.status === 'fulfilled' ? customerData.value : null;
    const items = itemsData.status === 'fulfilled' ? itemsData.value : [];
    const notes = notesData.status === 'fulfilled' ? notesData.value : [];

    // Transform all data using the modular transformer
    return orderTransformers.transformOrderDetail(orderData, customer, items, notes);
  });
};

/**
 * Checks if the current user has permission to perform specific actions
 */
export const checkUserPermissions = async (action: 'refund' | 'reshipment' | 'delete'): Promise<boolean> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return false;
    }

    // Get user profile with role information
    const { data: profile, error } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (error || !profile) {
      return false;
    }

    const userRole = profile.role as UserRole;

    switch (action) {
      case 'refund':
        return userRole === 'master' || userRole === 'admin';
      case 'reshipment':
        return userRole === 'master' || userRole === 'admin';
      case 'delete':
        return userRole === 'master';
      default:
        return false;
    }
  } catch (error) {
    console.error('Error checking user permissions:', error);
    return false;
  }
};

/**
 * Gets the current user's role
 */
export const getCurrentUserRole = async (): Promise<UserRole | undefined> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return undefined;
    }

    const { data: profile, error } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();

    if (error || !profile) {
      return undefined;
    }

    return profile.role as UserRole;
  } catch (error) {
    console.error('Error getting user role:', error);
    return undefined;
  }
};

/**
 * Update order status with optimistic updates and error recovery
 */
export const updateOrderStatus = async (orderId: string, newStatus: AllOrdersStatus): Promise<AllOrdersDetail | null> => {
  return retryWithBackoff(async () => {
    const { data, error } = await supabase
      .from('orders')
      .update({ 
        status: newStatus,
        updated_at: new Date().toISOString(),
        // Add specific timestamps based on status
        ...(newStatus === 'packed' && { packed_at: new Date().toISOString() }),
        ...(newStatus === 'shipped' && { shipped_at: new Date().toISOString() }),
        ...(newStatus === 'completed' && { delivered_at: new Date().toISOString() })
      })
      .eq('id', orderId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update order status: ${error.message}`);
    }

    // Fetch the complete order details after update
    return await fetchOrderById(orderId);
  });
};

/**
 * Add note to order with proper typing and validation
 */
export const addOrderNote = async (orderId: string, content: string, author: string = 'System'): Promise<AllOrdersDetail | null> => {
  return retryWithBackoff(async () => {
    // Insert the note
    const { error: noteError } = await supabase
      .from('order_notes')
      .insert({
        order_id: orderId,
        author_name: author,
        content: content,
        note_type: 'internal',
        created_at: new Date().toISOString()
      });

    if (noteError) {
      throw new Error(`Failed to add order note: ${noteError.message}`);
    }

    // Update order to mark it as having notes
    await supabase
      .from('orders')
      .update({ 
        has_notes: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', orderId);

    // Return updated order details
    return await fetchOrderById(orderId);
  });
};

/**
 * Cancel order with proper status update and note logging
 */
export const cancelOrder = async (orderId: string, reason: string): Promise<AllOrdersDetail | null> => {
  return retryWithBackoff(async () => {
    // Update order status
    await updateOrderStatus(orderId, 'cancelled');
    
    // Add cancellation note
    await addOrderNote(orderId, `Order cancelled: ${reason}`, 'System');
    
    return await fetchOrderById(orderId);
  });
};

/**
 * Resend order confirmation with tracking
 */
export const resendOrderConfirmation = async (orderId: string): Promise<AllOrdersDetail | null> => {
  return retryWithBackoff(async () => {
    // Update order flags
    const { error } = await supabase
      .from('orders')
      .update({ 
        is_resent: true,
        has_notes: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', orderId);

    if (error) {
      throw new Error(`Failed to mark order as resent: ${error.message}`);
    }

    // Add system note
    await addOrderNote(orderId, 'Order confirmation resent to customer', 'System');
    
    return await fetchOrderById(orderId);
  });
};

/**
 * Process refund with amount tracking and proper status update
 */
export const processRefund = async (orderId: string, amount: number, reason: string): Promise<AllOrdersDetail | null> => {
  return retryWithBackoff(async () => {
    // Update order status to refund
    await updateOrderStatus(orderId, 'refund');
    
    // Add refund note with amount details
    await addOrderNote(orderId, `Refund processed: $${amount.toFixed(2)} - ${reason}`, 'Support Team');
    
    return await fetchOrderById(orderId);
  });
};

/**
 * Mark order as resent by order number (for delivery page bulk actions)
 */
export const markOrderAsResent = async (orderNumber: string): Promise<AllOrdersDetail | null> => {
  return retryWithBackoff(async () => {
    // Find order by order number first
    const { data: orderData, error: findError } = await supabase
      .from('orders')
      .select('id')
      .eq('order_number', orderNumber)
      .single();

    if (findError || !orderData) {
      throw new Error(`Order not found: ${orderNumber}`);
    }

    // Use the existing resendOrderConfirmation function
    return await resendOrderConfirmation(orderData.id);
  });
};

/**
 * Get available filter options from the database for dynamic filtering
 */
export const getFilterOptions = async () => {
  return retryWithBackoff(async () => {
    // Get distinct values from orders_view for filter options
    const [statusResult, platformResult, channelResult] = await Promise.all([
      supabase.from('orders_view').select('status').not('status', 'is', null),
      supabase.from('orders_view').select('platform').not('platform', 'is', null),
      supabase.from('orders_view').select('channel').not('channel', 'is', null)
    ]);

    const statuses = [...new Set((statusResult.data || []).map(row => row.status))];
    const platforms = [...new Set((platformResult.data || []).map(row => row.platform))];
    const channels = [...new Set((channelResult.data || []).map(row => row.channel))];

    return {
      statuses,
      platforms,
      channels
    };
  });
}; 