import { AllOrdersViewItem } from '@/types';
import { OrderFilters, SearchParams, PaginationParams } from '../order/live-order-service';

// Base interface that all cache entries must implement
export interface BaseCacheEntry {
  timestamp: Date;
  expiresAt: Date;
}

// Original order cache entry for backward compatibility
export interface CacheEntry extends BaseCacheEntry {
  orders: AllOrdersViewItem[];
  totalCount: number;
  filters: OrderFilters;
  search: SearchParams;
  pagination: PaginationParams;
}

export interface CacheConfig {
  maxEntries: number;
  defaultTTL: number; // Time to live in milliseconds
  staleWhileRevalidate: number; // Serve stale data while fetching fresh
}

export class CacheStorage<T extends BaseCacheEntry = CacheEntry> {
  private cache = new Map<string, T>();
  private config: CacheConfig;

  constructor(config: CacheConfig) {
    this.config = config;
  }

  /**
   * Get cache entry by key
   */
  get(key: string): T | undefined {
    return this.cache.get(key);
  }

  /**
   * Set cache entry with size management
   */
  set(key: string, entry: T): void {
    // Remove oldest entries if we exceed max size
    if (this.cache.size >= this.config.maxEntries) {
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey) {
        this.cache.delete(oldestKey);
        console.log('Evicted oldest cache entry:', oldestKey);
      }
    }

    this.cache.set(key, entry);
  }

  /**
   * Delete cache entry by key
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get all cache entries
   */
  entries(): IterableIterator<[string, T]> {
    return this.cache.entries();
  }

  /**
   * Get all cache values
   */
  values(): IterableIterator<T> {
    return this.cache.values();
  }

  /**
   * Get cache size
   */
  get size(): number {
    return this.cache.size;
  }

  /**
   * Check if entry is valid (not expired)
   */
  isValid(entry: T): boolean {
    return new Date() < entry.expiresAt;
  }

  /**
   * Check if entry is stale but within revalidate window
   */
  isStaleButRevalidatable(entry: T): boolean {
    const now = new Date();
    return now >= entry.expiresAt && 
           now < new Date(entry.expiresAt.getTime() + this.config.staleWhileRevalidate);
  }

  /**
   * Create cache entry with TTL (for order caching - maintained for backward compatibility)
   */
  createEntry(
    orders: AllOrdersViewItem[],
    totalCount: number,
    filters: OrderFilters,
    search: SearchParams,
    pagination: PaginationParams
  ): CacheEntry {
    return {
      orders,
      totalCount,
      timestamp: new Date(),
      filters,
      search,
      pagination,
      expiresAt: new Date(Date.now() + this.config.defaultTTL),
    } as CacheEntry;
  }

  /**
   * Clean up expired cache entries
   */
  cleanupExpired(): number {
    const now = new Date();
    const expiredKeys: string[] = [];
    
    for (const [key, entry] of this.cache.entries()) {
      if (now > new Date(entry.expiresAt.getTime() + this.config.staleWhileRevalidate)) {
        expiredKeys.push(key);
      }
    }
    
    expiredKeys.forEach(key => {
      this.cache.delete(key);
    });
    
    return expiredKeys.length;
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    totalEntries: number;
    totalSize: number;
    oldestEntry: Date | null;
    newestEntry: Date | null;
  } {
    let oldestEntry: Date | null = null;
    let newestEntry: Date | null = null;
    let totalSize = 0;
    
    for (const entry of this.cache.values()) {
      // For order cache entries, count number of orders
      if ('orders' in entry) {
        totalSize += (entry as unknown as CacheEntry).orders.length;
      } else {
        totalSize++; // For other entry types, just count the entry itself
      }
      
      if (!oldestEntry || entry.timestamp < oldestEntry) {
        oldestEntry = entry.timestamp;
      }
      
      if (!newestEntry || entry.timestamp > newestEntry) {
        newestEntry = entry.timestamp;
      }
    }
    
    return {
      totalEntries: this.cache.size,
      totalSize,
      oldestEntry,
      newestEntry,
    };
  }
} 