import React, { memo, useCallback, useMemo } from 'react';
import { AllOrdersViewItem } from '@/types';
import { Icon } from '@/shared/ui/core/Icon';
import { formatCurrency, formatDate, createStatusBadge, createFlagBadges } from '@/shared/lib/utils/domain/orders/orders-table-helpers';

interface OrderRowProps {
  order: AllOrdersViewItem;
  isSelected: boolean;
  onRowClick: (orderId: string) => void;
}

// Deep equality check for order data
const areOrdersEqual = (prev: AllOrdersViewItem, next: AllOrdersViewItem): boolean => {
  return (
    prev.id === next.id &&
    prev.orderNumber === next.orderNumber &&
    prev.orderDate === next.orderDate &&
    prev.totalAmount === next.totalAmount &&
    prev.status === next.status &&
    prev.itemCount === next.itemCount &&
    prev.platform === next.platform &&
    prev.channel === next.channel &&
    prev.customerName === next.customerName &&
    prev.trackingNumber === next.trackingNumber &&
    prev.isUrgent === next.isUrgent &&
    prev.isProblem === next.isProblem &&
    prev.isResent === next.isResent &&
    prev.hasNotes === next.hasNotes
  );
};

const OrderRow = memo<OrderRowProps>(({ order, isSelected, onRowClick }) => {
  // Memoize click handler to prevent recreation
  const handleClick = useCallback(() => {
    onRowClick(order.id);
  }, [onRowClick, order.id]);

  // Memoize row styling
  const rowClassName = useMemo(() => {
    return `cursor-pointer hover:bg-gray-50 active:bg-blue-100 transition-colors duration-150 ${
      isSelected ? 'bg-blue-50 border-l-4 border-blue-500' : ''
    }`;
  }, [isSelected]);

  // Lightweight computed values for performance
  const computedValues = useMemo(() => ({
    formattedDate: formatDate(order.orderDate),
    formattedAmount: formatCurrency(order.totalAmount),
    statusBadge: createStatusBadge(order.status),
    flagBadges: createFlagBadges(order),
    itemText: `${order.itemCount} item${order.itemCount !== 1 ? 's' : ''}`
  }), [
    order.orderDate,
    order.totalAmount, 
    order.status,
    order.itemCount,
    order.isUrgent,
    order.isProblem,
    order.isResent,
    order.hasNotes
  ]);

  return (
    <tr onClick={handleClick} className={rowClassName}>
      {/* Order Details */}
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center">
          <Icon 
            platform={order.platform} 
            className="h-6 w-6 mr-3" 
            variant="none"
          />
          <div>
            <div className="text-sm font-medium text-gray-900">
              {order.orderNumber}
            </div>
            <div className="text-xs text-gray-400">
              {computedValues.itemText}
            </div>
          </div>
        </div>
      </td>

      {/* Order Date */}
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm text-gray-900">{computedValues.formattedDate}</div>
      </td>

      {/* Channel */}
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm text-gray-900">{order.channel}</div>
        <div className="text-xs text-gray-500 capitalize">{order.platform}</div>
      </td>

      {/* Customer */}
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm text-gray-900">{order.customerName}</div>
      </td>

      {/* Status */}
      <td className="px-6 py-4 whitespace-nowrap">
        {computedValues.statusBadge}
      </td>

      {/* Amount */}
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm font-medium text-gray-900">
          {computedValues.formattedAmount}
        </div>
      </td>

      {/* Flags */}
      <td className="px-6 py-4">
        <div className="flex flex-wrap gap-1">
          {computedValues.flagBadges}
        </div>
      </td>

      {/* Tracking */}
      <td className="px-6 py-4 whitespace-nowrap">
        {order.trackingNumber ? (
          <div className="text-sm text-gray-900 font-mono">
            {order.trackingNumber}
          </div>
        ) : (
          <div className="text-sm text-gray-400">-</div>
        )}
      </td>
    </tr>
  );
}, (prevProps, nextProps) => {
  // Comprehensive memoization strategy
  
  // First, check if selected state changed
  if (prevProps.isSelected !== nextProps.isSelected) return false;
  
  // If callback reference changed, component needs to update
  if (prevProps.onRowClick !== nextProps.onRowClick) return false;
  
  // Deep comparison of order data
  return areOrdersEqual(prevProps.order, nextProps.order);
});

OrderRow.displayName = 'OrderRow';

export default OrderRow; 