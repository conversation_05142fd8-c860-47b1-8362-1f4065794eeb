import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import BulkCreateOrderModal from '../bulk-create-order/BulkCreateOrderModal';
import { useBulkOrderCreation } from '@/shared/lib/hooks/orders/useBulkOrderCreation';

// Mock the hook
jest.mock('@/shared/lib/hooks/orders/useBulkOrderCreation', () => ({
  useBulkOrderCreation: jest.fn()
}));

// Mock the components that will be used in the test
jest.mock('./components/CSVUpload', () => ({
  __esModule: true,
  default: ({ onFileUpload }: { onFileUpload: (file: File) => void }) => (
    <div data-testid="csv-upload">
      <button onClick={() => {
        const mockFile = new File([''], 'test.csv', { type: 'text/csv' });
        onFileUpload(mockFile);
      }}>
        Upload File
      </button>
    </div>
  )
}));

jest.mock('./components/FailedOrdersReport', () => ({
  __esModule: true,
  default: ({ failedOrders, onDownload }: { failedOrders: any[], onDownload: () => void }) => (
    <div data-testid="failed-orders-report">
      <span>Failed orders: {failedOrders.length}</span>
      <button onClick={onDownload}>Download Failed Orders</button>
    </div>
  )
}));

// Mock the Modal component
jest.mock('@/shared/ui/overlay', () => ({
  Modal: ({ isOpen, children }: { isOpen: boolean; children: React.ReactNode }) => (
    isOpen ? <div data-testid="modal">{children}</div> : null
  )
}));

describe('BulkCreateOrderModal', () => {
  // Set up the mock hook implementation
  const mockUploadCsv = jest.fn();
  const mockDownloadTemplate = jest.fn();
  const mockDownloadFailedOrdersReport = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    (useBulkOrderCreation as jest.Mock).mockReturnValue({
      uploadCsv: mockUploadCsv,
      downloadTemplate: mockDownloadTemplate,
      processingStatus: null,
      error: null,
      failedOrders: null,
      successfulOrders: [],
      isProcessing: false,
      downloadFailedOrdersReport: mockDownloadFailedOrdersReport
    });
  });
  
  it('renders correctly when open', () => {
    render(<BulkCreateOrderModal isOpen={true} onClose={() => {}} />);
    
    expect(screen.getByTestId('modal')).toBeInTheDocument();
    expect(screen.getByTestId('csv-upload')).toBeInTheDocument();
    expect(screen.getByText('Download Template')).toBeInTheDocument();
  });
  
  it('does not render when closed', () => {
    render(<BulkCreateOrderModal isOpen={false} onClose={() => {}} />);
    
    expect(screen.queryByTestId('modal')).not.toBeInTheDocument();
  });
  
  it('handles template download', () => {
    render(<BulkCreateOrderModal isOpen={true} onClose={() => {}} />);
    
    fireEvent.click(screen.getByText('Download Template'));
    expect(mockDownloadTemplate).toHaveBeenCalledTimes(1);
  });
  
  it('handles file upload and shows processing state', async () => {
    // Mock a successful upload
    mockUploadCsv.mockResolvedValueOnce({ success: true });
    
    render(<BulkCreateOrderModal isOpen={true} onClose={() => {}} />);
    
    fireEvent.click(screen.getByText('Upload File'));
    
    // Should call the upload function
    expect(mockUploadCsv).toHaveBeenCalledWith(expect.any(File));
    
    // Wait for state updates to complete
    await waitFor(() => {
      expect(screen.queryByText('Successfully created 0 orders')).toBeInTheDocument();
    });
  });
  
  it('shows error message when upload fails', async () => {
    // Mock the hook with an error
    (useBulkOrderCreation as jest.Mock).mockReturnValue({
      uploadCsv: jest.fn().mockResolvedValue({ success: false }),
      downloadTemplate: mockDownloadTemplate,
      processingStatus: null,
      error: 'Failed to upload CSV',
      failedOrders: null,
      successfulOrders: null,
      isProcessing: false,
      downloadFailedOrdersReport: mockDownloadFailedOrdersReport
    });
    
    render(<BulkCreateOrderModal isOpen={true} onClose={() => {}} />);
    
    fireEvent.click(screen.getByText('Upload File'));
    
    // Wait for the error message to appear
    await waitFor(() => {
      expect(screen.queryByText('Error: Failed to upload CSV')).toBeInTheDocument();
    });
  });
  
  it('renders failed orders report when there are failed orders', async () => {
    // Mock the hook with failed orders
    (useBulkOrderCreation as jest.Mock).mockReturnValue({
      uploadCsv: jest.fn().mockResolvedValue({ success: true }),
      downloadTemplate: mockDownloadTemplate,
      processingStatus: null,
      error: null,
      failedOrders: [{ row: 2, reason: 'Invalid data', data: {} }],
      successfulOrders: [{ id: '1', order_number: 'ORDER-123' }],
      isProcessing: false,
      downloadFailedOrdersReport: mockDownloadFailedOrdersReport
    });
    
    render(<BulkCreateOrderModal isOpen={true} onClose={() => {}} onSuccess={jest.fn()} />);
    
    fireEvent.click(screen.getByText('Upload File'));
    
    // Wait for the results to appear
    await waitFor(() => {
      expect(screen.getByTestId('failed-orders-report')).toBeInTheDocument();
    });
    
    // Test download failed orders report
    fireEvent.click(screen.getByText('Download Failed Orders'));
    expect(mockDownloadFailedOrdersReport).toHaveBeenCalledTimes(1);
  });
  
  it('calls onSuccess when upload is successful', async () => {
    const mockOnSuccess = jest.fn();
    mockUploadCsv.mockResolvedValueOnce({ 
      success: true,
      data: { id: '1', order_number: 'ORDER-123' }
    });
    
    render(<BulkCreateOrderModal isOpen={true} onClose={() => {}} onSuccess={mockOnSuccess} />);
    
    fireEvent.click(screen.getByText('Upload File'));
    
    await waitFor(() => {
      expect(mockOnSuccess).toHaveBeenCalled();
    });
  });
}); 