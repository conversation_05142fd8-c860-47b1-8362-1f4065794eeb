import { useMemo } from 'react';
import { FulfillPackingListItem } from '@/shared/api/orders/ordersAPI_types';
import { FulfillOrderItem } from '@/types';

interface OrderDataProcessingResult {
  productBatches: any[];
  packedOrders: FulfillOrderItem[];
  openOrderCount: number;
  readyToShipCount: number;
}

export function useOrderDataProcessing(
  packingList: FulfillPackingListItem[]
): OrderDataProcessingResult {
  return useMemo(() => {
    const productMap = new Map();
    const packedOrdersMap = new Map();
    let openCount = 0;
    let readyToShipCount = 0;
    
    // Task 3.8: Use for...of instead of forEach for better performance with large datasets
    for (const item of packingList) {
      // Count open orders
      if (item.order_status === 'open') {
        openCount++;
      }
      // Count ready_to_ship orders
      if (item.order_status === 'ready_to_ship') {
        readyToShipCount++;
      }
      
      // Build product batches
      const key = item.product_sku;
      if (!productMap.has(key)) {
        productMap.set(key, {
          productName: item.product_name,
          sku: item.product_sku,
          packingListItems: [],
          stockInfo: {
            currentStock: item.available_quantity,
            status: item.available_quantity > 50 ? 'In Stock' as const : 
                    item.available_quantity > 10 ? 'Low Stock' as const : 
                    'Out of Stock' as const
          }
        });
      }
      
      // Add all items for this product (open and packed)
      productMap.get(key).packingListItems.push(item);
      
      // Build packed orders simultaneously
      if (item.order_status === 'packed' || item.order_status === 'ready_to_ship') {
        if (!packedOrdersMap.has(item.order_id)) {
          packedOrdersMap.set(item.order_id, {
            id: item.order_id,
            order_number: item.order_number,
            order_date: item.order_date,
            status: item.order_status,
            store: item.channel,
            platform: item.platform,
            shipping_fee_paid: true,
            buyer: {
              name: item.customer_name,
              email: '',
              address: ''
            },
            items: [],
            tracking_number: item.order_tracking_number
          });
        }
        
        // Add item to the order
        packedOrdersMap.get(item.order_id).items.push({
          id: `${item.order_id}-${item.product_sku}`,
          productName: item.product_name,
          sku: item.product_sku,
          quantity: item.ordered_quantity,
          price: 29.99
        });
      }
    }

    // Task 3.8: Optimize filter+map chain - combine operations for better performance
    const batches = [];
    for (const batch of productMap.values()) {
      // Check if batch has open orders while iterating
      let hasOpenOrders = false;
      for (const item of batch.packingListItems) {
        if (item.order_status === 'open') {
          hasOpenOrders = true;
          break; // Early exit optimization
        }
      }
      if (hasOpenOrders) {
        batches.push(batch);
      }
    }

    const packedOrdersArray = Array.from(packedOrdersMap.values());

    return {
      productBatches: batches,
      packedOrders: packedOrdersArray,
      openOrderCount: openCount,
      readyToShipCount: readyToShipCount,
    };
  }, [packingList]);
} 