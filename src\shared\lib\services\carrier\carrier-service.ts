import { carriersApi } from '@/shared/api/deliveries/carriers/carriersAPI';
import { Carrier } from '@/types';

export const getCarriers = async (): Promise<Carrier[]> => {
  try {
    const response = await carriersApi.list();
    if (!response.success) {
      throw new Error(response.message || 'Failed to fetch carriers');
    }
    return response.data;
  } catch (error) {
    console.error('Failed to get carriers:', error);
    return [];
  }
}; 