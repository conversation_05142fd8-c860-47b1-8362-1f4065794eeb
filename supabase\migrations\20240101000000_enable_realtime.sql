-- Enable the Realtime functionality for specific tables
-- This creates a publication that Supabase Realtime will use to broadcast changes

-- First, check if the publication already exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_publication WHERE pubname = 'supabase_realtime'
  ) THEN
    -- Create the publication if it doesn't exist
    CREATE PUBLICATION supabase_realtime;
  END IF;
END
$$;

-- Add tables to the publication
ALTER PUBLICATION supabase_realtime ADD TABLE orders;
ALTER PUBLICATION supabase_realtime ADD TABLE order_items;
ALTER PUBLICATION supabase_realtime ADD TABLE order_notes;
ALTER PUBLICATION supabase_realtime ADD TABLE deliveries;
ALTER PUBLICATION supabase_realtime ADD TABLE delivery_events;
ALTER PUBLICATION supabase_realtime ADD TABLE inventory;

-- Create a function to notify the client about order changes
CREATE OR REPLACE FUNCTION notify_order_changes()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM pg_notify(
    'order_changes',
    json_build_object(
      'type', TG_OP,
      'table', TG_TABLE_NAME,
      'id', CASE 
              WHEN TG_OP = 'DELETE' THEN OLD.id 
              ELSE NEW.id 
            END,
      'record', CASE 
                 WHEN TG_OP = 'DELETE' THEN row_to_json(OLD)
                 ELSE row_to_json(NEW)
               END
    )::text
  );
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for each table
DROP TRIGGER IF EXISTS orders_notify_changes ON orders;
CREATE TRIGGER orders_notify_changes
AFTER INSERT OR UPDATE OR DELETE ON orders
FOR EACH ROW EXECUTE FUNCTION notify_order_changes();

DROP TRIGGER IF EXISTS order_items_notify_changes ON order_items;
CREATE TRIGGER order_items_notify_changes
AFTER INSERT OR UPDATE OR DELETE ON order_items
FOR EACH ROW EXECUTE FUNCTION notify_order_changes();

DROP TRIGGER IF EXISTS deliveries_notify_changes ON deliveries;
CREATE TRIGGER deliveries_notify_changes
AFTER INSERT OR UPDATE OR DELETE ON deliveries
FOR EACH ROW EXECUTE FUNCTION notify_order_changes(); 