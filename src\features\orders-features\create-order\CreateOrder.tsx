import React, { useState, useEffect, useMemo } from 'react';
import { supabase } from '../../../../supabase/supabase_client/client';
import LoadingSpinner from '@/shared/ui/feedback/LoadingSpinner';
import ErrorMessage from '@/shared/ui/feedback/ErrorMessage';
import Modal from '@/shared/ui/overlay/Modal';
import { 
  CreateOrderFormData, 
  Platform, 
  Channel, 
  ProductIdentifier,
} from '@/types';
import CustomerInfoSection from './components/CustomerInfoSection';
import PlatformChannelSection from './components/PlatformChannelSection';
import ShippingAddressSection from './components/ShippingAddressSection';
import OrderItemsSection from './components/OrderItemsSection';

interface CreateOrderProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (orderData: any) => void;
}

const CreateOrder: React.FC<CreateOrderProps> = ({ isOpen, onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [platforms, setPlatforms] = useState<Platform[]>([]);
  const [channels, setChannels] = useState<Channel[]>([]);
  const [productIdentifiers, setProductIdentifiers] = useState<ProductIdentifier[]>([]);
  const [filteredChannels, setFilteredChannels] = useState<Channel[]>([]);

  const [formData, setFormData] = useState<CreateOrderFormData>({
    customer_email: '',
    customer_name: '',
    platform_id: '',
    channel_id: '',
    order_number: '',
    order_quantity: 1,
    order_items: [{ product_id: '' }],
    shipping_street: '',
    shipping_city: '',
    shipping_state: '',
    shipping_zip_code: '',
    status: 'open',
    subtotal: 0
  });

  // Task 3.5: Optimize channel filtering with Map for O(1) platform-based lookups
  const channelsByPlatform = useMemo(() => {
    const map = new Map<string, Channel[]>();
    channels.forEach(channel => {
      if (!map.has(channel.platform_id)) {
        map.set(channel.platform_id, []);
      }
      map.get(channel.platform_id)!.push(channel);
    });
    return map;
  }, [channels]);

  // Load initial data
  useEffect(() => {
    if (isOpen) {
      loadInitialData();
    }
  }, [isOpen]);

  // Filter channels and load products based on selected platform
  useEffect(() => {
    if (formData.platform_id) {
      // Task 3.5: Use Map.get() for O(1) channel filtering instead of array.filter() O(n)
      const filtered = channelsByPlatform.get(formData.platform_id) || [];
      setFilteredChannels(filtered);
      if (formData.channel_id && !filtered.find(c => c.id === formData.channel_id)) {
        setFormData(prev => ({ ...prev, channel_id: '' }));
      }
      // Load product identifiers for the selected platform
      loadProductIdentifiers(formData.platform_id);
    } else {
      setFilteredChannels([]);
      setProductIdentifiers([]);
    }
  }, [formData.platform_id, channelsByPlatform]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      
      // Fetch directly from Supabase (your real backend)
      const [platformsResult, channelsResult] = await Promise.all([
        supabase.from('platforms').select('id, name, key, is_active, created_at, updated_at').eq('is_active', true),
        supabase.from('channels').select('id, name, code, platform_id, is_active, created_at, updated_at').eq('is_active', true)
      ]);
      
      // Initially load empty product identifiers - will load based on platform selection
      const productIdentifiersResult = { data: [], error: null };

      if (platformsResult.error) throw platformsResult.error;
      if (channelsResult.error) throw channelsResult.error;
      if (productIdentifiersResult.error) throw productIdentifiersResult.error;

      setPlatforms(platformsResult.data || []);
      setChannels(channelsResult.data || []);
      setProductIdentifiers(productIdentifiersResult.data || []);

      const orderNumber = generateOrderNumber();
      setFormData(prev => ({ ...prev, order_number: orderNumber }));

    } catch (err) {
      console.error('Data loading error:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to load data';
      setError(`Failed to load initial data: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const generateOrderNumber = () => {
    const timestamp = Date.now().toString().slice(-8);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `ORD-${timestamp}-${random}`;
  };

  const updateFormData = (updates: Partial<CreateOrderFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const addOrderItem = () => {
    setFormData(prev => ({
      ...prev,
      order_items: [...prev.order_items, { product_id: '' }]
    }));
  };

  const removeOrderItem = (index: number) => {
    if (formData.order_items.length > 1) {
      setFormData(prev => ({
        ...prev,
        order_items: prev.order_items.filter((_, i) => i !== index)
      }));
    }
  };

  const loadProductIdentifiers = async (platformId: string) => {
    if (!platformId) {
      setProductIdentifiers([]);
      return;
    }

    try {
      const result = await supabase
        .from('product_identifiers')
        .select('id, product_id, platform_id, platform_identifier, code_name, pack_size, created_at')
        .eq('platform_id', platformId)
        .limit(100);

      if (result.error) throw result.error;
      setProductIdentifiers(result.data || []);
    } catch (err) {
      console.error('Failed to load product identifiers:', err);
      setProductIdentifiers([]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.customer_email || !formData.customer_name || 
        !formData.platform_id || !formData.channel_id ||
        formData.order_items.some(item => !item.product_id)) {
      setError('Please fill in all required fields');
      return;
    }
    
    try {
      setLoading(true);
      setError(null);

      // Validate required fields before sending
      if (!formData.customer_email || !formData.customer_name || 
          !formData.platform_id || !formData.channel_id ||
          !formData.order_number || !formData.status || 
          formData.subtotal <= 0 || formData.order_items.length === 0) {
        throw new Error('Please fill in all required fields');
      }

      // Prepare the request body
      const requestBody = {
        customer_email: formData.customer_email,
        customer_name: formData.customer_name,
        platform_id: formData.platform_id,
        channel_id: formData.channel_id,
        order_number: formData.order_number,
        order_quantity: formData.order_quantity,
        order_items: formData.order_items,
        shipping_street: formData.shipping_street,
        shipping_city: formData.shipping_city,
        shipping_state: formData.shipping_state,
        shipping_zip_code: formData.shipping_zip_code,
        status: formData.status,
        subtotal: formData.subtotal,
      };

      console.log('Calling Edge Function with:', requestBody);

      // Log the final payload being sent to Edge Function
      console.log("THE FINAL OBJECT BEING SENT IS:", JSON.stringify(requestBody, null, 2));

      // Call the Supabase Edge Function
      const { data, error } = await supabase.functions.invoke('rapid-order-create', {
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('Edge Function response:', { data, error });

      if (error) {
        console.error('Edge Function error:', error);
        
        // Check if it's a CORS error
        if (error.message && error.message.includes('CORS')) {
          console.error('CORS Error detected:', error.message);
          throw new Error('Network error: Please check your connection and try again');
        }
        
        // Check if it's a network error
        if (error.message && (error.message.includes('fetch') || error.message.includes('network'))) {
          console.error('Network Error detected:', error.message);
          throw new Error('Network error: Unable to reach the server');
        }
        
        // If Edge Function fails, fall back to direct RPC call
        console.log('Edge Function failed, attempting direct RPC call...');
        
        const { data: rpcData, error: rpcError } = await supabase.rpc('create_order', {
          customer_email: requestBody.customer_email,
          customer_name: requestBody.customer_name,
          p_order_number: requestBody.order_number,
          p_platform_id: requestBody.platform_id,
          p_channel_id: requestBody.channel_id,
          p_order_quantity: requestBody.order_quantity,
          order_items: requestBody.order_items,
          p_shipping_street: requestBody.shipping_street,
          p_shipping_city: requestBody.shipping_city,
          p_shipping_state: requestBody.shipping_state,
          p_shipping_zip_code: requestBody.shipping_zip_code,
          p_status: requestBody.status,
          p_subtotal: requestBody.subtotal
        });

        if (rpcError) {
          console.error('Database error:', rpcError);
          throw new Error('Failed to create order');
        }

        onSuccess(rpcData);
        onClose();
        return;
      }

      onSuccess(data);
      onClose();
      
    } catch (err) {
      console.error('Order creation error:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to create order';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Create New Order" size="lg" showDefaultFooter={false}>
      {loading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50 rounded-lg">
          <div className="text-center">
            <LoadingSpinner size="lg" />
            <p className="mt-2 text-gray-600">Creating order...</p>
          </div>
        </div>
      )}
      <form onSubmit={handleSubmit} className={`space-y-6 ${loading ? 'pointer-events-none opacity-50' : ''}`}>
        {error && <ErrorMessage message={error} />}
        
        {/* Customer Information */}
        <CustomerInfoSection 
          formData={formData} 
          onChange={updateFormData} 
        />

        {/* Platform & Channel */}
        <PlatformChannelSection 
          formData={formData}
          platforms={platforms}
          filteredChannels={filteredChannels}
          onChange={updateFormData}
        />

        {/* Order Details */}
        <div className="grid grid-cols-3 gap-4">
          <input
            type="text"
            placeholder="Order Number"
            value={formData.order_number}
            onChange={(e) => updateFormData({ order_number: e.target.value })}
            className="px-3 py-2 border rounded-md"
            required
          />
          <input
            type="number"
            placeholder="Order Quantity"
            min="1"
            value={formData.order_quantity}
            onChange={(e) => updateFormData({ order_quantity: parseInt(e.target.value) || 1 })}
            className="px-3 py-2 border rounded-md"
            required
          />
          <input
            type="number"
            step="0.01"
            placeholder="Subtotal ($)"
            min="0"
            value={formData.subtotal === 0 ? '' : formData.subtotal}
            onChange={(e) => {
              const value = e.target.value;
              updateFormData({ subtotal: value === '' ? 0 : parseFloat(value) });
            }}
            className="px-3 py-2 border rounded-md"
            required
          />
        </div>

        {/* Shipping Address */}
        <ShippingAddressSection 
          formData={formData} 
          onChange={updateFormData} 
        />

        {/* Order Items */}
        <OrderItemsSection
          formData={formData}
          productIdentifiers={productIdentifiers}
          onUpdateFormData={updateFormData}
          onAddItem={addOrderItem}
          onRemoveItem={removeOrderItem}
        />

        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-gray-600 border rounded-md hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Creating...' : 'Create Order'}
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default CreateOrder; 