# Washolding Internal App

An internal management system for inventory, orders, deliveries, and user management with a structured React architecture.

## Overview

Washolding Internal App is a comprehensive business management solution that handles:

- Order management and fulfillment
- Inventory tracking and stock adjustments
- Delivery tracking
- Sales reporting and analytics
- User management with role-based permissions (master, admin, staff)

## Architecture

The application follows a strict layered architecture pattern:

```
UI Components → Hooks → Services → API Layer
```

Each layer has specific responsibilities:

1. **UI Components** (`/features`, `/ui`): 
   - Render UI and capture user interactions
   - Should NOT contain business logic or direct API calls

2. **Hooks** (`/shared/lib/hooks`): 
   - Manage state and prepare data for UI 
   - Coordinate operations by calling services

3. **Services** (`/shared/lib/services`): 
   - Implement business logic and domain rules
   - Handle complex operations and validations

4. **API Layer** (`/shared/api`): 
   - Direct communication with Supabase
   - Format requests and handle API-specific errors

## Tech Stack

- **Frontend**: React with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **Database**: Supabase
- **Authentication**: Supabase Auth
- **Backend Functions**: Supabase Edge Functions

## Setup & Installation

### Prerequisites

- Node.js (v16+)
- npm or yarn
- Supabase account and project

### Installation

1. Clone the repository:
   ```
   git clone [repository-url]
   cd Washolding-internalapp
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Create a `.env.local` file with your Supabase credentials:
   ```
   VITE_SUPABASE_URL=your-supabase-url
   VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
   ```

4. Run the development server:
   ```
   npm run dev
   ```

## Development Guidelines

### Code Structure

```
src/
├── app/                    # Application setup, context providers
├── features/               # Feature-specific components by domain
│   ├── {feature}-features/ # Feature domain grouping
├── pages/                  # Page-level components and routing
├── shared/                 # Shared utilities and components
│   ├── api/                # API integrations by domain
│   ├── lib/                # Business logic
│   │   ├── hooks/          # Custom hooks organized by feature
│   │   ├── services/       # Service layer for API operations
│   │   ├── utils/          # Utility functions
│   │   └── mock-data/      # Test/mock data
│   └── ui/                 # Reusable UI components
└── types/                  # TypeScript definitions
```

### File Naming Conventions

- **Components**: PascalCase (e.g., `OrdersTable.tsx`)
- **Hooks**: camelCase with "use" prefix (e.g., `useOrderData.ts`)
- **Services**: camelCase with domain (e.g., `order-service.ts`)

### Key Development Rules

1. UI components must only handle presentation and local UI state
2. Business logic must reside in services, never in UI components
3. Components must use hooks for all stateful logic
4. API calls must only exist in service layers
5. Use TypeScript interfaces for all props and return types

## Features

### Order Management

- Order tracking with comprehensive status management
- Detailed order views with customer information
- Fulfillment workflow with carrier integration

### Inventory

- Product management with stock levels
- Low stock alerts and reorder notifications
- Stock adjustment with movement history

### Delivery Tracking

- Shipment management with carrier integration
- Tracking status updates
- Delivery confirmation workflow

### User Management

- Role-based access control
- User invitation system
- Profile management

## Documentation

Additional documentation can be found in the `docs/` directory:

- Business logic guidelines
- Carrier CSV processing
- Bundle optimization

## Internal Use Only

This application is for internal use only. Unauthorized access is prohibited.
