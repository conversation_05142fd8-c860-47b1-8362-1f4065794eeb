// Import retry utilities
import { createRetryableFunction } from '@/shared/lib/utils/api/retry';
import { 
  readOperationRetryOptions,
  writeOperationRetryOptions,
  bulkOperationRetryOptions
} from '@/shared/lib/utils/api/api-retry-config';

// Import product types
import {
  ApiResponse,
  formatResponse,
  formatVoidResponse,
  handleError,
  handleVoidError,
  Product,
  ProductIdentifier,
  Platform,
  ProductListParams,
  ProductCreateParams,
  ProductUpdateParams,
  ProductIdentifierCreateParams,
  ProductIdentifierUpdateParams
} from './productAPI-types';

// Import Supabase
import { supabase } from '../../../../supabase/supabase_client/client';

// Base implementation of API functions without retry
const baseProductApi = {
  // List products with filtering and pagination
  async list(params: ProductListParams = {}): Promise<ApiResponse<Product[]>> {
    try {
      let query = supabase
        .from('products')
        .select('*', { count: 'exact' });

      // Apply filters
      if (params.search) {
        query = query.or(`name.ilike.%${params.search}%,sku.ilike.%${params.search}%,description.ilike.%${params.search}%`);
      }

      if (params.category) {
        query = query.eq('category', params.category);
      }

      if (params.product_type) {
        query = query.eq('product_type', params.product_type);
      }

      if (params.business_unit_id) {
        query = query.eq('business_unit_id', params.business_unit_id);
      }

      if (params.status && params.status.length > 0) {
        query = query.in('status', params.status);
      }

      // Apply sorting
      if (params.sortBy) {
        const direction = params.sortDirection === 'desc' ? true : false;
        query = query.order(params.sortBy, { ascending: !direction });
      } else {
        query = query.order('name', { ascending: true });
      }

      // Apply pagination
      const page = params.page || 1;
      const limit = params.limit || 20;
      const start = (page - 1) * limit;
      const end = start + limit - 1;
      query = query.range(start, end);

      // Execute query
      const { data, count, error } = await query;

      if (error) throw error;

      return formatResponse(data || [], count || 0);
    } catch (error) {
      return handleError(error, { operation: 'list', params });
    }
  },

  // Get product by ID
  async getById(id: string): Promise<ApiResponse<Product>> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      if (!data) throw new Error(`Product with ID ${id} not found`);

      return formatResponse(data);
    } catch (error) {
      return handleError(error, { operation: 'getById', id });
    }
  },

  // Get product by SKU
  async getBySku(sku: string): Promise<ApiResponse<Product>> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('sku', sku)
        .single();

      if (error) throw error;
      if (!data) throw new Error(`Product with SKU ${sku} not found`);

      return formatResponse(data);
    } catch (error) {
      return handleError(error, { operation: 'getBySku', sku });
    }
  },

  // Create a new product
  async create(data: ProductCreateParams): Promise<ApiResponse<Product>> {
    try {
      // Check if SKU already exists
      const { data: existingProduct, error: checkError } = await supabase
        .from('products')
        .select('id')
        .eq('sku', data.sku)
        .maybeSingle();

      if (checkError) throw checkError;
      if (existingProduct) {
        throw new Error(`A product with SKU ${data.sku} already exists`);
      }

      const { data: newProduct, error } = await supabase
        .from('products')
        .insert({
          ...data,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      // Create inventory record for the new product
      const { error: inventoryError } = await supabase
        .from('inventory')
        .insert({
          product_id: newProduct.id,
          current_stock: 0,
          reserved_stock: 0,
          available_stock: 0,
          created_at: new Date().toISOString()
        });

      if (inventoryError) {
        console.error('Failed to create inventory record:', inventoryError);
        // Continue anyway - the inventory service can recover this later
      }

      return formatResponse(newProduct);
    } catch (error) {
      return handleError(error, { operation: 'create', data });
    }
  },

  // Update an existing product
  async update(id: string, data: ProductUpdateParams): Promise<ApiResponse<Product>> {
    try {
      const { data: updatedProduct, error } = await supabase
        .from('products')
        .update({
          ...data,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      return formatResponse(updatedProduct);
    } catch (error) {
      return handleError(error, { operation: 'update', id, data });
    }
  },

  // Delete a product
  async delete(id: string): Promise<ApiResponse<void>> {
    try {
      // First, check if the product exists
      const { data: product, error: checkError } = await supabase
        .from('products')
        .select('id')
        .eq('id', id)
        .single();

      if (checkError) throw checkError;
      if (!product) throw new Error(`Product with ID ${id} not found`);

      // Delete related records first (product_identifiers)
      const { error: identifiersError } = await supabase
        .from('product_identifiers')
        .delete()
        .eq('product_id', id);

      if (identifiersError) {
        console.error('Failed to delete product identifiers:', identifiersError);
      }

      // Delete inventory record
      const { error: inventoryError } = await supabase
        .from('inventory')
        .delete()
        .eq('product_id', id);

      if (inventoryError) {
        console.error('Failed to delete inventory record:', inventoryError);
      }

      // Finally, delete the product
      const { error } = await supabase
        .from('products')
        .delete()
        .eq('id', id);

      if (error) throw error;

      return formatVoidResponse();
    } catch (error) {
      return handleVoidError(error, { operation: 'delete', id });
    }
  },

  // Discontinue a product (mark as inactive)
  async discontinue(id: string, reason?: string): Promise<ApiResponse<Product>> {
    try {
      const { data: updatedProduct, error } = await supabase
        .from('products')
        .update({
          status: 'discontinued',
          updated_at: new Date().toISOString(),
          notes: reason ? `Discontinued: ${reason}` : 'Discontinued'
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      return formatResponse(updatedProduct);
    } catch (error) {
      return handleError(error, { operation: 'discontinue', id });
    }
  },

  // Get product categories
  async getCategories(): Promise<ApiResponse<string[]>> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('category')
        .not('category', 'is', null)
        .order('category', { ascending: true });

      if (error) throw error;

      // Extract unique categories
      const categories = [...new Set(data.map(item => item.category))];
      return formatResponse(categories, categories.length);
    } catch (error) {
      return handleError(error, { operation: 'getCategories' });
    }
  },

  // Get product types
  async getProductTypes(): Promise<ApiResponse<string[]>> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('product_type')
        .not('product_type', 'is', null)
        .order('product_type', { ascending: true });

      if (error) throw error;

      // Extract unique product types
      const productTypes = [...new Set(data.map(item => item.product_type))];
      return formatResponse(productTypes, productTypes.length);
    } catch (error) {
      return handleError(error, { operation: 'getProductTypes' });
    }
  },
  
  // Get all platforms
  async getPlatforms(): Promise<ApiResponse<Platform[]>> {
    try {
      const { data, error } = await supabase
        .from('platforms')
        .select('*')
        .order('name', { ascending: true });
        
      if (error) throw error;
      
      return formatResponse(data || [], data?.length || 0);
    } catch (error) {
      return handleError(error, { operation: 'getPlatforms' });
    }
  },
  
  // Get product identifiers for a specific product
  async getProductIdentifiers(productId: string): Promise<ApiResponse<ProductIdentifier[]>> {
    try {
      const { data, error } = await supabase
        .from('product_identifiers')
        .select('*, platforms(name, key)')
        .eq('product_id', productId);
        
      if (error) throw error;
      
      // Transform the result to include platform name
      const transformedData = data?.map(item => ({
        ...item,
        platform_name: item.platforms?.name || '',
        platforms: undefined
      })) || [];
      
      return formatResponse(transformedData, transformedData.length);
    } catch (error) {
      return handleError(error, { operation: 'getProductIdentifiers', productId });
    }
  },
  
  // Create a new product identifier
  async createProductIdentifier(data: ProductIdentifierCreateParams): Promise<ApiResponse<ProductIdentifier>> {
    try {
      // Check if an identifier for this platform already exists
      const { data: existingData, error: checkError } = await supabase
        .from('product_identifiers')
        .select('id')
        .eq('product_id', data.product_id)
        .eq('platform_id', data.platform_id)
        .maybeSingle();
      
      if (checkError) throw checkError;
      
      if (existingData) {
        throw new Error('An identifier for this platform already exists for this product');
      }
      
      const { data: newIdentifier, error } = await supabase
        .from('product_identifiers')
        .insert(data)
        .select()
        .single();
        
      if (error) throw error;
      
      return formatResponse(newIdentifier);
    } catch (error) {
      return handleError(error, { operation: 'createProductIdentifier', data });
    }
  },
  
  // Bulk create multiple product identifiers
  async bulkCreateProductIdentifiers(data: ProductIdentifierCreateParams[]): Promise<ApiResponse<ProductIdentifier[]>> {
    try {
      if (!data.length) {
        return formatResponse([], 0, false, 'No product identifiers provided');
      }
      
      const { data: newIdentifiers, error } = await supabase
        .from('product_identifiers')
        .insert(data)
        .select();
        
      if (error) throw error;
      
      return formatResponse(newIdentifiers || [], newIdentifiers?.length || 0);
    } catch (error) {
      return handleError(error, { operation: 'bulkCreateProductIdentifiers', count: data.length });
    }
  },
  
  // Upsert a product identifier (insert if not exists, update if exists)
  async upsertProductIdentifier(data: ProductIdentifierCreateParams): Promise<ApiResponse<ProductIdentifier>> {
    try {
      const { data: upsertedIdentifier, error } = await supabase
        .from('product_identifiers')
        .upsert(data, { onConflict: 'product_id,platform_id' })
        .select()
        .single();
        
      if (error) throw error;
      
      return formatResponse(upsertedIdentifier);
    } catch (error) {
      return handleError(error, { operation: 'upsertProductIdentifier', data });
    }
  },
  
  // Update a product identifier
  async updateProductIdentifier(id: string, data: ProductIdentifierUpdateParams): Promise<ApiResponse<ProductIdentifier>> {
    try {
      const { data: updatedIdentifier, error } = await supabase
        .from('product_identifiers')
        .update(data)
        .eq('id', id)
        .select()
        .single();
        
      if (error) throw error;
      
      return formatResponse(updatedIdentifier);
    } catch (error) {
      return handleError(error, { operation: 'updateProductIdentifier', id, data });
    }
  },
  
  // Delete a product identifier
  async deleteProductIdentifier(id: string): Promise<ApiResponse<void>> {
    try {
      const { error } = await supabase
        .from('product_identifiers')
        .delete()
        .eq('id', id);
        
      if (error) throw error;
      
      return formatVoidResponse();
    } catch (error) {
      return handleVoidError(error, { operation: 'deleteProductIdentifier', id });
    }
  },

  // Bulk update products
  async bulkUpdate(ids: string[], data: ProductUpdateParams): Promise<ApiResponse<number>> {
    try {
      if (!ids.length) {
        return formatResponse(0, 0, false, 'No product IDs provided');
      }

      const { data: updateResult, error } = await supabase
        .from('products')
        .update({
          ...data,
          updated_at: new Date().toISOString()
        })
        .in('id', ids);

      if (error) throw error;
      
      return formatResponse(ids.length, ids.length);
    } catch (error) {
      return handleError(error, { operation: 'bulkUpdate', count: ids.length });
    }
  }
};

// Create retryable versions of API methods with appropriate retry strategies
export const productApi = {
  list: createRetryableFunction(baseProductApi.list, readOperationRetryOptions),
  getById: createRetryableFunction(baseProductApi.getById, readOperationRetryOptions),
  getBySku: createRetryableFunction(baseProductApi.getBySku, readOperationRetryOptions),
  create: createRetryableFunction(baseProductApi.create, writeOperationRetryOptions),
  update: createRetryableFunction(baseProductApi.update, writeOperationRetryOptions),
  delete: createRetryableFunction(baseProductApi.delete, writeOperationRetryOptions),
  discontinue: createRetryableFunction(baseProductApi.discontinue, writeOperationRetryOptions),
  getCategories: createRetryableFunction(baseProductApi.getCategories, readOperationRetryOptions),
  getProductTypes: createRetryableFunction(baseProductApi.getProductTypes, readOperationRetryOptions),
  getPlatforms: createRetryableFunction(baseProductApi.getPlatforms, readOperationRetryOptions),
  getProductIdentifiers: createRetryableFunction(baseProductApi.getProductIdentifiers, readOperationRetryOptions),
  createProductIdentifier: createRetryableFunction(baseProductApi.createProductIdentifier, writeOperationRetryOptions),
  bulkCreateProductIdentifiers: createRetryableFunction(baseProductApi.bulkCreateProductIdentifiers, bulkOperationRetryOptions),
  upsertProductIdentifier: createRetryableFunction(baseProductApi.upsertProductIdentifier, writeOperationRetryOptions),
  updateProductIdentifier: createRetryableFunction(baseProductApi.updateProductIdentifier, writeOperationRetryOptions),
  deleteProductIdentifier: createRetryableFunction(baseProductApi.deleteProductIdentifier, writeOperationRetryOptions),
  bulkUpdate: createRetryableFunction(baseProductApi.bulkUpdate, bulkOperationRetryOptions)
};

// Export for use in the application
export default productApi; 