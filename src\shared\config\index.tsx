import { NavItemType } from '@/types';
import {
  ShoppingCartIcon,
  ArchiveBoxIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  ArrowPathIcon,
  ChatBubbleLeftEllipsisIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  TruckIcon,
  // EllipsisVerticalIcon, // Removed as it's no longer directly used in DashboardHeader
  ClockIcon,
  ArchiveBoxArrowDownIcon,
  CubeIcon,
  CheckCircleIcon,
  InboxStackIcon,
  ExclamationTriangleIcon,
  FunnelIcon, 
  MagnifyingGlassIcon, 
  PlusCircleIcon, 
  DocumentArrowDownIcon, 
  CloudArrowUpIcon, 
  CloudArrowDownIcon, 
  Squares2X2Icon, 
  InformationCircleIcon, 
  XMarkIcon,
  FlagIcon, 
  PaperAirplaneIcon,
  ArrowLeftOnRectangleIcon, // For Logout
  EllipsisVerticalIcon, // Re-added here, as it might be used by other components via direct import
  ArrowUpIcon, // Added for TopSellers report
  ArrowDownIcon, // Added for TopSellers report
  UsersIcon // Added for user management
} from '@heroicons/react/24/outline';


export const SIDEBAR_TOP_ITEMS: NavItemType[] = [
  {
    label: 'Orders',
    icon: (props) => <ShoppingCartIcon {...props} />,
    path: '/orders', // Or a new specific path like '/orders/overview'
  },
  { label: 'Fulfill', icon: (props) => <InboxStackIcon {...props} />, path: '/fulfill', children: [] }, // Fulfill as a top-level item
  { label: 'Delivery', icon: (props) => <TruckIcon {...props} />, path: '/delivery', children: [] },
  { label: 'Inventory', icon: (props) => <ArchiveBoxIcon {...props} />, path: '/inventory', children: [] },
  { label: 'Reports', icon: (props) => <ChartBarIcon {...props} />, path: '/reports', children: [] },
  { label: 'User Management', icon: (props) => <UsersIcon {...props} />, path: '/user-management', children: [] },
  { label: 'Settings', icon: (props) => <Cog6ToothIcon {...props} />, path: '/settings', children: [] },
];

export const SIDEBAR_BOTTOM_ITEMS: NavItemType[] = [
  {
    label: 'Logout',
    icon: (props) => <ArrowLeftOnRectangleIcon {...props} />,
    path: '#logout', // Special path to trigger logout, not a react-router route
    isBottom: true,
  }
];

// KPI_DATA and SALES_CHART_DATA are now in lib/mock-data/dashboard.ts
export {
    ShoppingCartIcon,
    ArchiveBoxIcon,
    ChartBarIcon,
    Cog6ToothIcon,
    ArrowPathIcon,
    ChatBubbleLeftEllipsisIcon,
    ChevronRightIcon,
    ChevronDownIcon,
    TruckIcon,
    EllipsisVerticalIcon, // Keep EllipsisVerticalIcon exported for potential other uses
    ClockIcon,
    ArchiveBoxArrowDownIcon,
    CubeIcon,
    CheckCircleIcon,
    InboxStackIcon,
    ExclamationTriangleIcon,
    FunnelIcon,
    MagnifyingGlassIcon,
    PlusCircleIcon,
    DocumentArrowDownIcon,
    CloudArrowUpIcon,
    CloudArrowDownIcon,
    Squares2X2Icon,
    InformationCircleIcon,
    XMarkIcon,
    FlagIcon,
    PaperAirplaneIcon,
    ArrowLeftOnRectangleIcon,
    ArrowUpIcon,
    ArrowDownIcon,
    UsersIcon
};