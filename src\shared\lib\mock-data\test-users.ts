import { UserProfile } from '@/types';

/**
 * Mock Users for Testing Role-Based Access Control
 * 
 * These test users can be used to verify the staff dashboard functionality
 * and role-based access control throughout the application.
 * 
 * Usage:
 * - Use these users to test authentication flows
 * - Verify role-based UI components (sidebar, dashboard access)
 * - Test staff dashboard functionality
 */

export const TEST_USERS: UserProfile[] = [
  // STAFF USER - Can access staff dashboard
  {
    id: 'test-staff-001',
    full_name: 'Sarah Staff',
    role: 'staff',
    business_unit_id: 'bu-001'
  },
  {
    id: 'test-staff-002', 
    full_name: '<PERSON>',
    role: 'staff',
    business_unit_id: 'bu-002'
  },

  // ADMIN USER - Cannot access staff dashboard but has broader access
  {
    id: 'test-admin-001',
    full_name: 'Alice Admin',
    role: 'admin', 
    business_unit_id: 'bu-001'
  },
  {
    id: 'test-admin-002',
    full_name: 'Bob Manager',
    role: 'admin',
    business_unit_id: null // Cross-business unit admin
  },

  // MASTER USER - Full system access but cannot access staff dashboard
  {
    id: 'test-master-001',
    full_name: 'Mary Master',
    role: 'master',
    business_unit_id: null // System-wide access
  }
];

/**
 * Mock Supabase-style user objects for testing authentication
 * These simulate what Supabase would return for authenticated users
 */
export const TEST_AUTH_USERS = [
  // Staff Users
  {
    id: 'test-staff-001',
    email: '<EMAIL>',
    created_at: '2024-01-15T08:00:00Z',
    last_sign_in_at: '2024-01-20T09:30:00Z',
    user_metadata: {
      full_name: 'Sarah Staff'
    }
  },
  {
    id: 'test-staff-002',
    email: '<EMAIL>', 
    created_at: '2024-01-10T10:15:00Z',
    last_sign_in_at: '2024-01-20T08:45:00Z',
    user_metadata: {
      full_name: 'John Worker'
    }
  },

  // Admin Users
  {
    id: 'test-admin-001',
    email: '<EMAIL>',
    created_at: '2024-01-05T14:20:00Z', 
    last_sign_in_at: '2024-01-20T07:15:00Z',
    user_metadata: {
      full_name: 'Alice Admin'
    }
  },
  {
    id: 'test-admin-002',
    email: '<EMAIL>',
    created_at: '2024-01-01T16:30:00Z',
    last_sign_in_at: '2024-01-20T10:00:00Z', 
    user_metadata: {
      full_name: 'Bob Manager'
    }
  },

  // Master User
  {
    id: 'test-master-001',
    email: '<EMAIL>',
    created_at: '2023-12-01T12:00:00Z',
    last_sign_in_at: '2024-01-20T06:30:00Z',
    user_metadata: {
      full_name: 'Mary Master'
    }
  }
];

/**
 * Business Units for testing
 */
export const TEST_BUSINESS_UNITS = [
  {
    id: 'bu-001',
    name: 'Shrimp Products Division',
    description: 'Fresh and frozen shrimp products',
    active: true
  },
  {
    id: 'bu-002', 
    name: 'Dropship Operations',
    description: 'Third-party dropship fulfillment',
    active: true
  },
  {
    id: 'bu-003',
    name: 'Direct Sales',
    description: 'Direct-to-consumer sales channel',
    active: false
  }
];

/**
 * Helper function to get user profile by email
 * Useful for authentication testing
 */
export const getUserProfileByEmail = (email: string): UserProfile | null => {
  const authUser = TEST_AUTH_USERS.find(user => user.email === email);
  if (!authUser) return null;
  
  return TEST_USERS.find(profile => profile.id === authUser.id) || null;
};

/**
 * Helper function to get users by role
 * Useful for testing role-based functionality
 */
export const getUsersByRole = (role: 'master' | 'admin' | 'staff'): UserProfile[] => {
  return TEST_USERS.filter(user => user.role === role);
};

/**
 * Quick access test user emails for magic link authentication
 * Use these emails for easy testing in development with Supabase magic links
 */
export const QUICK_TEST_EMAILS = {
  STAFF_USER: {
    email: '<EMAIL>',
    expectedRole: 'staff',
    canAccessStaffDashboard: true,
    description: 'Staff user - can access staff dashboard'
  },
  ADMIN_USER: {
    email: '<EMAIL>', 
    expectedRole: 'admin',
    canAccessStaffDashboard: false,
    description: 'Admin user - broader access but no staff dashboard'
  },
  MASTER_USER: {
    email: '<EMAIL>',
    expectedRole: 'master',
    canAccessStaffDashboard: false,
    description: 'Master user - full system access but no staff dashboard'
  }
} as const;

/**
 * All test emails for quick reference
 * Copy these into your Supabase magic link login form
 * 
 * TODO: Replace with your actual email addresses for testing
 */
export const ALL_TEST_EMAILS = [
  '<EMAIL>',      // Staff user - REPLACE WITH YOUR EMAIL
  '<EMAIL>',      // Staff user - REPLACE WITH YOUR EMAIL
  '<EMAIL>',      // Admin user - REPLACE WITH YOUR EMAIL
  '<EMAIL>',      // Admin user - REPLACE WITH YOUR EMAIL
  '<EMAIL>'       // Master user - REPLACE WITH YOUR EMAIL
] as const;

/**
 * Example: Replace with your actual emails
 * 
 * export const YOUR_TEST_EMAILS = [
 *   '<EMAIL>',      // Staff user
 *   '<EMAIL>',      // Admin user  
 *   '<EMAIL>',     // Master user
 * ] as const;
 */ 