import { supabase } from '../../../../../supabase/supabase_client/client';
import { UserProfile } from '../../../../types';
import { AuthCacheService } from './cache.service';

export class AuthSessionService {
  // Get current session from Supabase
  static async getCurrentSession() {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        return { session: null, error };
      }
      return { session, error: null };
    } catch (error) {
      // Session retrieval failed
      return { session: null, error };
    }
  }

  // Validate cached session against current Supabase session
  static async validateCachedSession(cachedSession: any): Promise<boolean> {
    try {
      const { session: currentSession } = await this.getCurrentSession();
      
      // Check if cached session is valid
      if (!cachedSession || !cachedSession.access_token) {
        // Invalid cached session format
        return false;
      }
      
      if (!currentSession || currentSession.access_token !== cachedSession.access_token) {
        // Cached session is stale
        return false;
      }
      
      // Cached session is still valid
      return true;
    } catch (error) {
      // Error validating cached session
      return false;
    }
  }

  // Create basic profile from auth user (no database query)
  static createBasicProfile(user: any): UserProfile {
    // Use role from user metadata if available (from invitation), otherwise default to master for existing users
    const metadataRole = user.user_metadata?.role;
    const defaultRole = metadataRole || 'master'; // Changed from 'staff' to 'master'
    
    return {
      id: user.id,
      full_name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',
      role: defaultRole as 'staff' | 'admin' | 'master',
      business_unit_id: user.user_metadata?.business_unit_id || null
    };
  }

  // Initialize session with cache-first approach
  static async initializeSession(): Promise<{
    session: any;
    profile: UserProfile | null;
    fromCache: boolean;
    needsRefresh?: boolean;
  }> {
    // Try cache first
    const cachedData = AuthCacheService.loadFromCache();
    if (cachedData) {
      const shouldRefresh = AuthCacheService.shouldRefreshCache();
      
      return {
        session: cachedData.session,
        profile: cachedData.profile,
        fromCache: true,
        needsRefresh: shouldRefresh
      };
    }

    // No cache, get fresh session
    const { session, error } = await this.getCurrentSession();
    
    if (error || !session) {
      return {
        session: null,
        profile: null,
        fromCache: false
      };
    }

    // Create basic profile for fast loading
    const basicProfile = this.createBasicProfile(session.user);
    
    // Save to cache
    AuthCacheService.saveToCache(session, basicProfile);
    
    return {
      session,
      profile: basicProfile,
      fromCache: false
    };
  }

  // Handle auth state changes
  static async handleAuthStateChange(
    event: string,
    session: any,
    onSessionChange: (session: any, profile: UserProfile | null) => void
  ) {
    if (session?.user) {
      // Only fetch from database on actual sign-in events
      if (event === 'SIGNED_IN') {
        // For now, use basic profile - database fetch will be handled by profile service
        const basicProfile = this.createBasicProfile(session.user);
        AuthCacheService.saveToCache(session, basicProfile);
        onSessionChange(session, basicProfile);
      } else {
        const basicProfile = this.createBasicProfile(session.user);
        AuthCacheService.saveToCache(session, basicProfile);
        onSessionChange(session, basicProfile);
      }
    } else {
      // User signed out
      AuthCacheService.clearCache();
      onSessionChange(null, null);
    }
  }
} 