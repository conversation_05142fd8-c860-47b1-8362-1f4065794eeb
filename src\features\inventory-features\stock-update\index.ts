import React from 'react';

// Export the modal component directly
export { default as StockAdjustmentModal } from './StockAdjustmentModal';

// Export types for use by other components
export interface StockUpdateFormData {
  productId: string;
  adjustmentType: 'increase' | 'decrease' | 'set';
  quantity: number;
  reason: string;
  reasonCategory: string;
  notes?: string;
}

export interface BulkStockUpdateFormData {
  productIds: string[];
  adjustmentType: 'increase' | 'decrease' | 'set';
  quantity: number;
  reason: string;
  reasonCategory: string;
  notes?: string;
} 