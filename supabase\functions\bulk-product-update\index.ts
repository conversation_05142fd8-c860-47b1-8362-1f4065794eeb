// @ts-nocheck
// deno-lint-ignore-file

import { serve } from 'std/http/server.ts'
import { createClient } from 'supabase'
import { corsHeaders } from '../_shared/cors.ts'
import type { ProductUpdateParams } from '../../../src/shared/api/inventory/productAPI-types'

interface RequestBody {
  ids: string[];
  data: ProductUpdateParams;
  userId?: string;
}

// Helper to safely get error messages
type ErrorWithMessage = { message: string };
function isErrorWithMessage(error: unknown): error is ErrorWithMessage {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof (error as Record<string, unknown>).message === 'string'
  );
}

function getErrorMessage(error: unknown): string {
  if (isErrorWithMessage(error)) return error.message;
  return String(error);
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { ids, data, userId } = await req.json() as RequestBody
    
    // Validate input
    if (!ids || !ids.length) {
      return new Response(
        JSON.stringify({ success: false, error: 'No product IDs provided' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
    
    if (!data) {
      return new Response(
        JSON.stringify({ success: false, error: 'No update data provided' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create Supabase client
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing Authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
    
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      { global: { headers: { Authorization: authHeader } } }
    )
    
    // Update the products with transaction safety
    const timestamp = new Date().toISOString()
    
    const { data: updatedProducts, error } = await supabaseClient
      .from('products')
      .update({
        ...data,
        updated_at: timestamp,
        updated_by: userId || null
      })
      .in('id', ids)
      .select('id')
    
    if (error) {
      throw error
    }
    
    return new Response(
      JSON.stringify({
        success: true,
        data: updatedProducts,
        count: updatedProducts?.length || 0,
        totalCount: ids.length
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (err) {
    const errorMessage = getErrorMessage(err)
    console.error('Error processing bulk product update:', errorMessage)
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: errorMessage
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
}) 