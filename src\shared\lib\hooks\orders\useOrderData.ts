import { useState, useEffect, useCallback } from 'react';
import { AllOrdersViewItem, AllOrdersDetail, UserRole } from '@/types';
import { 
  checkUserPermissions, 
  getCurrentUserRole,
  OrderFilters
} from '@/shared/lib/services/order/live-order-service';
// Import shared utility functions
import { fetchOrdersData, fetchOrderDetailsData } from '@/shared/lib/utils/domain/orders/order-utils';

export interface UseOrderDataReturn {
  // Order list data
  orders: AllOrdersViewItem[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  loading: boolean;
  error: string | null;
  
  // Single order data
  selectedOrder: AllOrdersDetail | null;
  orderLoading: boolean;
  orderError: string | null;
  
  // User permissions
  userRole: UserRole | undefined;
  canRefund: boolean;
  canReshipment: boolean;
  canDelete: boolean;
  
  // Actions
  fetchOrders: (filters?: OrderFilters) => Promise<void>;
  fetchOrder: (orderId: string) => Promise<void>;
  refreshOrders: (options?: { skipCache?: boolean }) => Promise<void>;
  clearSelectedOrder: () => void;
}

export const useOrderData = (initialFilters: OrderFilters = {}): UseOrderDataReturn => {
  // Order list state
  const [orders, setOrders] = useState<AllOrdersViewItem[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [hasPreviousPage, setHasPreviousPage] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Single order state
  const [selectedOrder, setSelectedOrder] = useState<AllOrdersDetail | null>(null);
  const [orderLoading, setOrderLoading] = useState(false);
  const [orderError, setOrderError] = useState<string | null>(null);
  
  // User permissions state
  const [userRole, setUserRole] = useState<UserRole | undefined>(undefined);
  const [canRefund, setCanRefund] = useState(false);
  const [canReshipment, setCanReshipment] = useState(false);
  const [canDelete, setCanDelete] = useState(false);
  
  // Current filters state
  const [currentFilters, setCurrentFilters] = useState<OrderFilters>(initialFilters);

  // Fetch user role and permissions on mount
  useEffect(() => {
    const initializeUserData = async () => {
      try {
        const role = await getCurrentUserRole();
        setUserRole(role);
        
        if (role) {
          const [refundPerm, reshipPerm, deletePerm] = await Promise.all([
            checkUserPermissions('refund'),
            checkUserPermissions('reshipment'),
            checkUserPermissions('delete')
          ]);
          
          setCanRefund(refundPerm);
          setCanReshipment(reshipPerm);
          setCanDelete(deletePerm);
        }
      } catch (err) {
        console.error('Failed to initialize user data:', err);
      }
    };

    initializeUserData();
  }, []);

  // Use shared utility for fetching orders
  const fetchOrdersWrapped = useCallback(async (filters: OrderFilters = {}) => {
    try {
      const response = await fetchOrdersData(
        filters, 
        setLoading,
        setError
      );
      
      if (response) {
        setOrders(response.orders);
        setTotalCount(response.totalCount);
        setCurrentPage(response.currentPage);
        setTotalPages(response.totalPages);
        setHasNextPage(response.hasNextPage);
        setHasPreviousPage(response.hasPreviousPage);
        setCurrentFilters(filters);
      }
    } catch (err) {
      // Error is already handled in the utility
    }
  }, []);

  // Use shared utility for fetching a single order
  const fetchOrderWrapped = useCallback(async (orderId: string) => {
    try {
      await fetchOrderDetailsData(
        orderId,
        setOrderLoading,
        setOrderError,
        setSelectedOrder
      );
    } catch (err) {
      // Error is already handled in the utility
    }
  }, []);

  // Refresh orders with current filters
  const refreshOrders = useCallback(async (options?: { skipCache?: boolean }) => {
    await fetchOrdersWrapped({
      ...currentFilters,
      skipCache: options?.skipCache
    });
  }, [fetchOrdersWrapped, currentFilters]);

  // Clear selected order
  const clearSelectedOrder = useCallback(() => {
    setSelectedOrder(null);
    setOrderError(null);
  }, []);

  return {
    // Order list data
    orders,
    totalCount,
    currentPage,
    totalPages,
    hasNextPage,
    hasPreviousPage,
    loading,
    error,
    
    // Single order data
    selectedOrder,
    orderLoading,
    orderError,
    
    // User permissions
    userRole,
    canRefund,
    canReshipment,
    canDelete,
    
    // Actions
    fetchOrders: fetchOrdersWrapped,
    fetchOrder: fetchOrderWrapped,
    refreshOrders,
    clearSelectedOrder
  };
}; 