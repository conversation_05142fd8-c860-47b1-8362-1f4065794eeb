import { createContext, useContext } from 'react';
import { AuthContextType } from '../../types';

// Create and export the AuthContext
export const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Custom hook for accessing AuthContext
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
}; 