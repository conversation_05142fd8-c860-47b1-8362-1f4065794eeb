import { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { FulfillPackingListItem } from '@/shared/api/orders/ordersAPI_types';
import { useAuth } from '@/app/providers/AuthProvider';
import { bulkPackOrders } from '@/shared/lib/services/fulfill/fulfill-service';

export interface UseProductAccordionProps {
  packingListItems: FulfillPackingListItem[];
  selectedItems: Set<string>;
  setSelectedItems: (selected: Set<string> | ((prev: Set<string>) => Set<string>)) => void;
  onDataRefresh?: () => Promise<void>;
  onStatusUpdate?: (orderIds: string[], newStatus: string) => Promise<void>;
}

export interface UseProductAccordionReturn {
  // State
  isExpanded: boolean;
  setIsExpanded: (expanded: boolean) => void;
  error: string | null;
  setError: (error: string | null) => void;
  isProcessing: boolean;
  showAllClear: boolean;
  
  // Computed values
  openItems: FulfillPackingListItem[];
  packedItems: FulfillPackingListItem[];
  totalNeeded: number;
  totalCount: number;
  packedCount: number;
  
  // Actions
  handleSelectItem: (orderId: string, checked: boolean) => void;
  handleBulkPack: () => Promise<void>;
}

export const useProductAccordion = ({
  packingListItems,
  selectedItems,
  setSelectedItems,
  onDataRefresh,
  onStatusUpdate
}: UseProductAccordionProps): UseProductAccordionReturn => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [initialOpenCount, setInitialOpenCount] = useState<number>(() => {
    return packingListItems.filter(item => item.order_status === 'open').length;
  });
  const [showAllClear, setShowAllClear] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const { user } = useAuth();
  
  // Use refs to track timeouts across renders and effects
  const refreshTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  
  // Keep a stable reference to current selectedItems and setSelectedItems
  const selectedItemsRef = useRef(selectedItems);
  const setSelectedItemsRef = useRef(setSelectedItems);
  
  // Update refs when props change
  useEffect(() => {
    selectedItemsRef.current = selectedItems;
    setSelectedItemsRef.current = setSelectedItems;
  }, [selectedItems, setSelectedItems]);

  // Calculate metrics with single-pass optimization
  const { openItems, packedItems, totalNeeded } = useMemo(() => {
    return packingListItems.reduce(
      (acc, item) => {
        acc.totalNeeded += item.ordered_quantity;
        
        if (item.order_status === 'open') {
          acc.openItems.push(item);
        } else if (item.order_status === 'packed') {
          acc.packedItems.push(item);
        }
        
        return acc;
      },
      {
        openItems: [] as FulfillPackingListItem[],
        packedItems: [] as FulfillPackingListItem[],
        totalNeeded: 0
      }
    );
  }, [packingListItems]);

  // Store initial open count on first render and clear on unmount
  useEffect(() => {
    const currentOpenCount = packingListItems.filter(item => item.order_status === 'open').length;
    setInitialOpenCount(currentOpenCount);
    
    // Clean up function that runs when component unmounts
    return () => {
      setInitialOpenCount(0);
      setShowAllClear(false);
      
      // Clean up any existing timeouts
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
        refreshTimeoutRef.current = null;
      }
    };
  }, []);

  // Check if all orders are complete and handle auto-refresh with proper cleanup
  useEffect(() => {
    let timer: ReturnType<typeof setTimeout> | undefined;
    
    if (initialOpenCount !== null && openItems.length === 0 && initialOpenCount > 0) {
      setShowAllClear(true);
      
      // Use a much shorter timeout (30 seconds instead of 5 minutes)
      // to avoid holding memory for long periods
      timer = setTimeout(() => {
        setShowAllClear(false);
        if (onDataRefresh) {
          onDataRefresh();
        }
      }, 30 * 1000); // 30 seconds instead of 5 * 60 * 1000 (5 minutes)
    } else {
      setShowAllClear(false);
    }
    
    // Clean up timer when component unmounts or dependencies change
    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [openItems.length, initialOpenCount, onDataRefresh]);

  const totalCount = initialOpenCount;
  const packedCount = totalCount - openItems.length;

  // Selection handlers - memoized once and never re-created
  const handleSelectItem = useCallback((orderId: string, checked: boolean) => {
    setSelectedItemsRef.current((prevSelected: Set<string>) => {
      // Use functional update to avoid capturing stale state
      const newSelected = new Set(prevSelected);
      if (checked) {
        newSelected.add(orderId);
      } else {
        newSelected.delete(orderId);
      }
      return newSelected;
    });
  }, []); // Empty dependency array - never recreated

  const handleBulkPack = useCallback(async () => {
    setIsProcessing(true);
    setError(null);
    
    try {
      // Get the current selected items
      const orderIds = Array.from(selectedItemsRef.current);
      
      // If onStatusUpdate is provided, use it to handle animations
      if (onStatusUpdate) {
        await onStatusUpdate(orderIds, 'packed');
      } else {
        // Otherwise, use bulkPackOrders directly which is working correctly
        if (!user?.id) {
          throw new Error('User ID is required for packing orders');
        }
        
        await bulkPackOrders(orderIds, user.id);
        
        setSelectedItemsRef.current(new Set<string>());
        
        // Clear any existing timeout
        if (refreshTimeoutRef.current) {
          clearTimeout(refreshTimeoutRef.current);
        }
        
        // Set new timeout and store reference
        refreshTimeoutRef.current = setTimeout(async () => {
          if (onDataRefresh) {
            await onDataRefresh();
          }
          refreshTimeoutRef.current = null;
        }, 500);
      }
    } catch (error) {
      console.error('Failed to bulk pack orders:', error);
      setError(
        error instanceof Error 
          ? `Failed to pack orders: ${error.message}` 
          : 'Failed to pack orders. Please try again.'
      );
    } finally {
      setIsProcessing(false);
    }
  }, [onDataRefresh, onStatusUpdate, user?.id]); // Only depend on what might change

  return {
    // State
    isExpanded,
    setIsExpanded,
    error,
    setError,
    isProcessing,
    showAllClear,
    
    // Computed values
    openItems,
    packedItems,
    totalNeeded,
    totalCount,
    packedCount,
    
    // Actions
    handleSelectItem,
    handleBulkPack
  };
}; 