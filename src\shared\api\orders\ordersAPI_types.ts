// API Response type
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

// Orders API specific types
export interface OrdersListParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  platform?: string;
  channel?: string;
  dateFrom?: string;
  dateTo?: string;
  isUrgent?: boolean;
  isProblem?: boolean;
  businessUnit?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface OrderUpdateData {
  status?: string;
  trackingNumber?: string;
  notes?: string;
  isUrgent?: boolean;
  isProblem?: boolean;
  isResent?: boolean;
}

export interface PackOrderData {
  orderId: string;
  packedItems: Array<{
    itemId: string;
    packedQuantity: number;
  }>;
  packingNotes?: string;
}

export interface ShipOrderData {
  orderId: string;
  trackingNumber: string;
  carrier: string;
  shippingMethod: string;
  shipDate?: string;
  notes?: string;
}

// Fulfill-specific API types
export interface BulkPackData {
  orderIds: string[];
  userId: string;
  notes?: string;
}

export interface ProductBatchPackData {
  productSku: string;
  userId: string;
  notes?: string;
}

export interface FulfillStatsResponse {
  readyToPack: number;
  packed: number;
  shipped: number;
  total: number;
}

export interface StatusUpdateData {
  orderId: string;
  newStatus: string;
  userId: string;
  trackingNumber?: string;
}

// Database View Types
export interface PackingStationSummaryItem {
  product_sku: string;
  order_count: number;
  total_quantity: number;
  available_quantity: number;
}

export interface FulfillPackingListItem {
  order_id: string;
  order_number: string;
  order_tracking_number: string;
  platform: string;
  channel: string;
  customer_name: string;
  product_sku: string;
  product_name: string;
  ordered_quantity: number;
  stock_quantity: number;
  available_quantity: number;
  can_fulfill: boolean;
  order_date: string;
  order_status: string;
} 