import React, { memo } from 'react';
import { 
  XMarkIcon, 
  DocumentDuplicateIcon,
  ChevronDownIcon,
  ChevronUpIcon
} from '@heroicons/react/24/outline';
import { useShipmentData, useShipmentProcessing, useShipmentActions } from '@/shared/lib/hooks/delivery';
import { usePanelState } from '@/shared/lib/hooks/ui/usePanelState';
import { PanelLoadingSkeleton } from '@/shared/ui/feedback/LoadingSkeleton';
import { DeliveryDetails } from '@/types';

interface ShipmentDetailPanelProps {
  shipmentId: string | null;
  isOpen: boolean;
  onClose: () => void;
}

// Sub-component that ONLY renders when shipment data is available
const ShipmentContent: React.FC<{ shipment: DeliveryDetails, onClose: () => void, handleMarkAsResend: (event: React.MouseEvent<HTMLButtonElement>) => void, isMarkingAsResent: boolean }> = ({ shipment, onClose, handleMarkAsResend, isMarkingAsResent }) => {
  const { showFullHistory, toggleFullHistory } = usePanelState();
  const { 
    statusIcon: { icon: StatusIcon, color: statusColor },
    timeMetrics,
    visibleEvents,
    getEventStyling,
    capitalizeStatus,
    formatDate
  } = useShipmentProcessing({ shipment, showFullHistory });

  return (
    <div className="flex-1 p-6 space-y-6">
      {/* Status Summary */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center space-x-4">
          <StatusIcon className={`h-8 w-8 ${statusColor}`} />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {capitalizeStatus(shipment.status)}
            </h3>
            <p className="text-sm text-gray-600">
              via {shipment.carrier_name} • {shipment.shipping_method}
            </p>
          </div>
        </div>
      </div>

      {/* Order Details */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <h4 className="text-sm font-medium text-gray-500 mb-1">Order Number</h4>
          <p className="text-sm text-gray-900">{shipment.order_number}</p>
        </div>
        <div>
          <h4 className="text-sm font-medium text-gray-500 mb-1">Customer</h4>
          <p className="text-sm text-gray-900">{shipment.customer_name}</p>
        </div>
        <div>
          <h4 className="text-sm font-medium text-gray-500 mb-1">Ship Date</h4>
          <p className="text-sm text-gray-900">
            {formatDate(shipment.ship_date)}
          </p>
        </div>
        <div>
          <h4 className="text-sm font-medium text-gray-500 mb-1">Est. Delivery</h4>
          <p className="text-sm text-gray-900">
            {formatDate(shipment.estimated_delivery)}
          </p>
        </div>
      </div>

      {/* Destination */}
      <div>
        <h4 className="text-sm font-medium text-gray-500 mb-1">Destination</h4>
        <p className="text-sm text-gray-900">
          {shipment.destination.city}, {shipment.destination.state} {shipment.destination.zipCode}
        </p>
      </div>

      {/* Time Info Section */}
      <div className="border border-gray-200 rounded-lg">
        <button
          onClick={toggleFullHistory}
          className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors"
        >
          <h4 className="text-sm font-medium text-gray-900">Time Info</h4>
          {showFullHistory ? (
            <ChevronUpIcon className="h-4 w-4 text-gray-400" />
          ) : (
            <ChevronDownIcon className="h-4 w-4 text-gray-400" />
          )}
        </button>
        
        {showFullHistory && timeMetrics && (
          <div className="px-4 pb-4 border-t border-gray-200 space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Days after first event</span>
              <span className="text-sm font-medium text-gray-900">{timeMetrics.daysAfterFirstEvent}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Days in transit</span>
              <span className="text-sm font-medium text-gray-900">{timeMetrics.daysInTransit}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Days to delivery</span>
              <span className="text-sm font-medium text-gray-900">{timeMetrics.daysToDelivery}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Total transit time</span>
              <span className="text-sm font-medium text-gray-900">{timeMetrics.totalTransitTime} days</span>
            </div>
          </div>
        )}
      </div>

      {/* Shipping Events Timeline*/}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-4">Shipping Events</h4>
        <div className="relative">
          {/* Continuous Vertical Line - positioned to go through center of all 24px containers */}
          <div className="absolute left-[11px] top-0 bottom-0 w-0.5 bg-gray-300" />
          
          {visibleEvents.map((event, index) => {
            const isCurrentEvent = index === 0;
            const eventStyling = getEventStyling(isCurrentEvent, shipment.status);
            const EventIcon = eventStyling.icon;
            
            return (
              <div key={index} className="relative z-10 flex pb-6 last:pb-0">
                {/* Timeline Dot - current events get solid background wrapper to block line */}
                <div className="relative z-10 flex-shrink-0 w-6 h-6">
                  {isCurrentEvent ? (
                    <div className={eventStyling.wrapperClass}>
                      <div className={eventStyling.glowClass}></div>
                      <div className={eventStyling.dotClass}>
                        {EventIcon && (
                          <EventIcon className={eventStyling.iconClass} />
                        )}
                      </div>
                    </div>
                  ) : (
                    <div className={eventStyling.dotClass}>
                      {EventIcon && (
                        <EventIcon className={eventStyling.iconClass} />
                      )}
                    </div>
                  )}
                </div>
              
                {/* Event Content */}
                <div className="ml-4 flex-1">
                  <p className={`text-sm font-medium ${
                    isCurrentEvent 
                      ? shipment.status === 'exception' 
                        ? 'text-red-600' 
                        : 'text-gray-900'
                        : 'text-gray-700'
                  }`}>
                    {event.location}
                  </p>
                  <p className={`text-sm mt-0.5 ${isCurrentEvent ? 'text-gray-900' : 'text-gray-600'}`}>
                    {event.description}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {event.date} at {event.time}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Notes */}
      {shipment.notes && (
        <div>
          <h4 className="text-sm font-medium text-gray-500 mb-1">Notes</h4>
          <p className="text-sm text-gray-900 bg-yellow-50 p-3 rounded-lg">
            {shipment.notes}
          </p>
        </div>
      )}

      {/* Mark as Resend Button */}
      <div className="pt-4 border-t border-gray-200">
        <button
          onClick={handleMarkAsResend}
          disabled={isMarkingAsResent}
          className="min-w-none bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isMarkingAsResent ? 'Marking as Resend...' : 'Mark as Resend'}
        </button>
      </div>
    </div>
  );
};

const ShipmentDetailPanel: React.FC<ShipmentDetailPanelProps> = ({
  shipmentId,
  isOpen,
  onClose
}) => {
  const { shipment, isLoading, error } = useShipmentData({ shipmentId });
  const { handleCopyTracking, handleMarkAsResend, isMarkingAsResent } = useShipmentActions({ 
    isOpen, 
    onClose, 
    shipmentOrderNumber: shipment?.order_number 
  });

  if (!isOpen) {
    return null;
  }
  
  return (
    <>
      {/* Backdrop overlay */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300"
        onClick={onClose}
      />
      
      {/* Panel */}
      <div 
        className={`fixed inset-y-0 right-0 w-full max-w-2xl bg-white shadow-xl z-50 transform transition-transform duration-500 ease-in-out overflow-y-auto ${isOpen ? 'translate-x-0' : 'translate-x-full'}`}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <h2 className="text-2xl font-bold text-gray-900">
                {isLoading || !shipment ? (
                  <span className="h-8 bg-gray-200 rounded w-48 inline-block animate-pulse"></span>
                ) : (
                  shipment.tracking_number
                )}
              </h2>
              {/* This is safe because it's only rendered when !isLoading and shipment exists */}
              {!isLoading && shipment && (
                <button
                  onClick={() => handleCopyTracking(shipment.tracking_number)}
                  className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                  title="Copy tracking number"
                >
                  <DocumentDuplicateIcon className="h-5 w-5" />
                </button>
              )}
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Content Area */}
          {isLoading ? (
            <PanelLoadingSkeleton />
          ) : error ? (
            <div className="p-6 text-red-500">
              <p>Error loading shipment details.</p>
              <p>{(error as Error).message}</p>
            </div>
          ) : shipment ? (
            <ShipmentContent 
              shipment={shipment} 
              onClose={onClose} 
              handleMarkAsResend={handleMarkAsResend}
              isMarkingAsResent={isMarkingAsResent}
            />
          ) : null}
        </div>
      </div>
    </>
  );
};

export default memo(ShipmentDetailPanel); 