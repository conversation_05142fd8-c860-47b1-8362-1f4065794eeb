// API Response type
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

// Dashboard API specific types
interface DashboardStatsParams {
  period?: string;
  businessUnit?: string;
  dateFrom?: string;
  dateTo?: string;
  refreshCache?: boolean;
}

interface ReportsParams {
  period?: string;
  businessUnit?: string;
  dateFrom?: string;
  dateTo?: string;
  limit?: number;
  category?: string;
  platform?: string;
  channel?: string;
}
import type {
  KpiDataType,
  SalesDataPoint,
  IntelligenceChartDataPoint,
  TopSellerData,
  UserRole
} from '../../../types';

// Dashboard API Functions
export const dashboardApi = {
  // Get KPI stats for dashboard
  async getStats(params: DashboardStatsParams = {}): Promise<ApiResponse<{
    kpis: KpiDataType[];
    summary: {
      totalOrders: number;
      totalRevenue: number;
      totalCustomers: number;
      totalProducts: number;
      ordersToday: number;
      revenueToday: number;
      pendingOrders: number;
      lowStockItems: number;
    };
  }>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, String(value));
      }
    });

    const response = await fetch(`/api/dashboard/stats?${queryParams}`);
    return response.json();
  },

  // Get order status counts for staff dashboard
  async getOrderStatusCounts(): Promise<ApiResponse<{
    open: number;
    packed: number;
    shipped: number;
    completed: number;
    cancelled: number;
    total: number;
  }>> {
    const response = await fetch('/api/dashboard/order-status-counts');
    return response.json();
  },

  // Get delivery status counts for staff dashboard
  async getDeliveryStatusCounts(): Promise<ApiResponse<{
    inTransit: number;
    delivered: number;
    delayed: number;
    exception: number;
    total: number;
  }>> {
    const response = await fetch('/api/dashboard/delivery-status-counts');
    return response.json();
  },

  // Get sales over time data for charts
  async getSalesOverTime(params: {
    period: 'daily' | 'weekly' | 'monthly' | 'yearly';
    dateFrom?: string;
    dateTo?: string;
    businessUnit?: string;
    platform?: string;
  }): Promise<ApiResponse<SalesDataPoint[]>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, String(value));
      }
    });

    const response = await fetch(`/api/dashboard/sales-over-time?${queryParams}`);
    return response.json();
  },

  // Get intelligence chart data (revenue + orders)
  async getIntelligenceData(params: {
    period: 'daily' | 'weekly' | 'monthly';
    dateFrom?: string;
    dateTo?: string;
    businessUnit?: string;
  }): Promise<ApiResponse<IntelligenceChartDataPoint[]>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, String(value));
      }
    });

    const response = await fetch(`/api/dashboard/intelligence?${queryParams}`);
    return response.json();
  },

  // Get top sellers data
  async getTopSellers(params: ReportsParams = {}): Promise<ApiResponse<TopSellerData[]>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, String(value));
      }
    });

    const response = await fetch(`/api/dashboard/top-sellers?${queryParams}`);
    return response.json();
  },

  // Get business unit performance data
  async getBusinessUnitPerformance(params?: {
    period?: string;
    metric?: 'revenue' | 'orders' | 'customers';
  }): Promise<ApiResponse<Array<{
    businessUnitId: string;
    businessUnitName: string;
    revenue: number;
    orders: number;
    customers: number;
    growth: number;
    marketShare: number;
  }>>> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, String(value));
        }
      });
    }

    const response = await fetch(`/api/dashboard/business-unit-performance?${queryParams}`);
    return response.json();
  },

  // Get channel performance data
  async getChannelPerformance(params?: {
    period?: string;
    businessUnit?: string;
  }): Promise<ApiResponse<Array<{
    channelId: string;
    channelName: string;
    channelCode: string;
    platformName: string;
    revenue: number;
    orders: number;
    averageOrderValue: number;
    growth: number;
    conversionRate: number;
  }>>> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, String(value));
        }
      });
    }

    const response = await fetch(`/api/dashboard/channel-performance?${queryParams}`);
    return response.json();
  },

  // Get recent activities/notifications
  async getRecentActivities(params?: {
    limit?: number;
    type?: string;
    businessUnit?: string;
  }): Promise<ApiResponse<Array<{
    id: string;
    type: 'order' | 'delivery' | 'inventory' | 'user' | 'system';
    title: string;
    description: string;
    timestamp: string;
    priority: 'low' | 'medium' | 'high';
    actionUrl?: string;
    metadata?: any;
  }>>> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, String(value));
        }
      });
    }

    const response = await fetch(`/api/dashboard/activities?${queryParams}`);
    return response.json();
  },

  // Get alerts and notifications
  async getAlerts(params?: {
    severity?: 'low' | 'medium' | 'high' | 'critical';
    isRead?: boolean;
    category?: string;
  }): Promise<ApiResponse<Array<{
    id: string;
    category: string | null;
    title: string;
    message: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    isRead: boolean | null;
    actionUrl: string | null;
    createdAt: string | null;
    expiresAt: string | null;
  }>>> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, String(value));
        }
      });
    }

    const response = await fetch(`/api/dashboard/alerts?${queryParams}`);
    return response.json();
  },

  // Mark alert as read
  async markAlertRead(alertId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`/api/dashboard/alerts/${alertId}/read`, {
      method: 'PATCH',
    });
    return response.json();
  },

  // Get role-specific dashboard config
  async getDashboardConfig(role: UserRole): Promise<ApiResponse<{
    widgets: Array<{
      id: string;
      type: string;
      title: string;
      position: { x: number; y: number; w: number; h: number };
      config: any;
      permissions: string[];
    }>;
    layout: string;
    refreshInterval: number;
  }>> {
    const response = await fetch(`/api/dashboard/config/${role}`);
    return response.json();
  },

  // Update dashboard widget configuration
  async updateWidgetConfig(widgetId: string, config: any): Promise<ApiResponse<void>> {
    const response = await fetch(`/api/dashboard/widgets/${widgetId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ config }),
    });
    return response.json();
  }
};

export default dashboardApi; 