import { supabase } from '../../../../../supabase/supabase_client/client';
import { DB_TABLES, ORDER_FIELDS, SELECT_CONFIGS } from './database-config';

/**
 * Database query modules for order-related data
 */

// Order queries
export const orderQueries = {
  /**
   * Fetch order by ID from orders_view
   */
  fetchOrderById: async (orderId: string) => {
    const { data, error } = await supabase
      .from(DB_TABLES.ORDERS_VIEW)
      .select(SELECT_CONFIGS.ORDER_BASIC)
      .eq(ORDER_FIELDS.ID, orderId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null;
      }
      throw new Error(`Failed to fetch order: ${error.message}`);
    }

    return data;
  }
};

// Customer queries
export const customerQueries = {
  /**
   * Fetch customer by customer_id
   */
  fetchCustomerById: async (customerId: string) => {
    // Try to find customer by either id or customer_id
    const { data, error } = await supabase
      .from(DB_TABLES.CUSTOMERS)
      .select(SELECT_CONFIGS.CUSTOMER_BASIC)
      .or(`id.eq.${customerId},customer_id.eq.${customerId}`)
      .limit(1);

    if (error) {
      console.warn(`Failed to fetch customer ${customerId}:`, error.message);
      return null;
    }

    // If no results, try querying just by id directly as fallback
    if (!data || data.length === 0) {
      const { data: fallbackData, error: fallbackError } = await supabase
        .from(DB_TABLES.CUSTOMERS)
        .select(SELECT_CONFIGS.CUSTOMER_BASIC)
        .eq('id', customerId)
        .limit(1);
        
      if (fallbackError) {
        console.warn(`Failed to fetch customer by id ${customerId}:`, fallbackError.message);
        return null;
      }
      
      return fallbackData?.[0] || null;
    }

    return data?.[0] || null;
  }
};

// Order items queries
export const orderItemQueries = {
  /**
   * Fetch order items by order_id
   */
  fetchOrderItems: async (orderId: string) => {
    const { data, error } = await supabase
      .from(DB_TABLES.ORDER_ITEMS)
      .select(SELECT_CONFIGS.ORDER_ITEMS_BASIC)
      .eq('order_id', orderId)
      .order('created_at', { ascending: true });

    if (error) {
      console.warn(`Failed to fetch order items for order ${orderId}:`, error.message);
      return [];
    }

    return data || [];
  }
};

// Order notes queries (for future use)
export const orderNotesQueries = {
  /**
   * Fetch order notes by order_id
   */
  fetchOrderNotes: async (orderId: string) => {
    const { data, error } = await supabase
      .from(DB_TABLES.ORDER_NOTES)
      .select(SELECT_CONFIGS.ORDER_NOTES_BASIC)
      .eq('order_id', orderId)
      .order('created_at', { ascending: false });

    if (error) {
      console.warn(`Failed to fetch order notes for order ${orderId}:`, error.message);
      return [];
    }

    return data || [];
  }
}; 