import js from "@eslint/js";
import globals from "globals";
import tseslint from "typescript-eslint";
import pluginReact from "eslint-plugin-react";
import unusedImports from "eslint-plugin-unused-imports";
// import { defineConfig } from "eslint/config"; // defineConfig is not standard and might cause issues, using plain array.

export default [
  {
    ignores: ["stats.html", "dist/**/*"]
  },
  { 
    files: ["**/*.{js,mjs,cjs,ts,mts,cts,jsx,tsx}"], 
    plugins: { 
      "unused-imports": unusedImports,
      "react": pluginReact,
      // 'js' was not defined, assuming it was a placeholder or from a missing import.
      // If 'js' is a specific plugin, it needs to be imported and added here.
    },
    // extends: ["js/recommended"], // 'js/recommended' is not a standard ESLint config, assuming it was a placeholder.
    languageOptions: { 
      globals: globals.browser,
      parser: tseslint.parser,
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
        ecmaVersion: "latest",
        sourceType: "module",
      },
    },
    rules: {
      "no-unused-vars": "off", // Disable base rule as unused-imports handles this
      "unused-imports/no-unused-imports": "warn",
      "unused-imports/no-unused-vars": [
        "warn",
        { "vars": "all", "varsIgnorePattern": "^_", "args": "after-used", "argsIgnorePattern": "^_" }
      ],
      // Add other React specific rules if needed from pluginReact.configs.flat.recommended
      ...pluginReact.configs.flat.recommended.rules 
    }
  },
  ...tseslint.configs.recommended, // Spread the recommended TypeScript configurations
  // pluginReact.configs.flat.recommended, // This is now merged into the main config object
];
