import React from 'react';

interface ContentSectionProps {
  children: React.ReactNode;
  className?: string;
  background?: 'white' | 'gray';
  hasBorder?: boolean;
  noPadding?: boolean;
}

/**
 * Consistent content section component used for filter bars, content areas, etc.
 * 
 * Features:
 * - Consistent max-width and padding
 * - Optional background colors
 * - Optional border styling
 * - Flexible padding control
 */
const ContentSection: React.FC<ContentSectionProps> = ({
  children,
  className = "",
  background = 'white',
  hasBorder = true,
  noPadding = false
}) => {
  const bgClass = background === 'white' ? 'bg-white' : 'bg-gray-50';
  const borderClass = hasBorder ? 'border-b border-gray-100' : '';
  const paddingClass = noPadding ? '' : 'px-6 py-4';

  return (
    <section className={`flex-shrink-0 ${bgClass} ${borderClass} ${className}`}>
      <div className={`max-w-7xl mx-auto ${paddingClass}`}>
        {children}
      </div>
    </section>
  );
};

export default ContentSection; 