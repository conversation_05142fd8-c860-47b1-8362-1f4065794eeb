import React, { useState, useRef, useEffect, useCallback } from 'react';
import { ExtendedOrderFilters, clearUrlFilters, getFiltersFromUrl } from '@/shared/lib/utils/data/url-params';

interface OrdersHeaderProps {
  searchTerm: string;
  onSearchChange: (searchTerm: string) => void;
  onFiltersClick: () => void;
  onFiltersChange?: (filters: ExtendedOrderFilters) => void;
  activeFiltersCount?: number;
}

const OrdersHeader: React.FC<OrdersHeaderProps> = ({
  searchTerm,
  onSearchChange,
  onFiltersClick,
  onFiltersChange,
  activeFiltersCount = 0
}) => {
  const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm);
  const debounceTimer = useRef<NodeJS.Timeout | null>(null);

  // Handle search input change with debounce effect - memoized with useCallback
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setLocalSearchTerm(value);
    
    // Clear previous timeout
    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }
    
    // Set new timeout
    debounceTimer.current = setTimeout(() => {
      onSearchChange(value);
    }, 300);
  }, [onSearchChange]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
    };
  }, []);

  // Handle search form submit - memoized with useCallback
  const handleSearchSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    onSearchChange(localSearchTerm);
  }, [localSearchTerm, onSearchChange]);

  // Handle "All Orders" button click - memoized with useCallback
  const handleAllOrdersClick = useCallback(() => {
    setLocalSearchTerm('');
    onSearchChange('');
    clearUrlFilters();
    onFiltersChange?.({ page: 1 });
  }, [onSearchChange, onFiltersChange]);

  // Handle "Open Orders" button click - memoized with useCallback
  const handleOpenOrdersClick = useCallback(() => {
    const currentFilters = getFiltersFromUrl();
    onFiltersChange?.({ 
      ...currentFilters, 
      status: ['open'], 
      page: 1,
      search: searchTerm || undefined
    });
  }, [onFiltersChange, searchTerm]);

  // Handle "Shipped Orders" button click - memoized with useCallback
  const handleShippedOrdersClick = useCallback(() => {
    const currentFilters = getFiltersFromUrl();
    onFiltersChange?.({ 
      ...currentFilters, 
      status: ['shipped'], 
      page: 1,
      search: searchTerm || undefined
    });
  }, [onFiltersChange, searchTerm]);

  // Handle "Urgent Orders" button click - memoized with useCallback
  const handleUrgentOrdersClick = useCallback(() => {
    const currentFilters = getFiltersFromUrl();
    onFiltersChange?.({ 
      ...currentFilters, 
      isUrgent: true, 
      page: 1,
      search: searchTerm || undefined
    });
  }, [onFiltersChange, searchTerm]);

  return (
    <div className="py-4">
      <div className="flex items-center justify-between">
        {/* Search Section */}
        <div className="flex-1 max-w-lg">
          <form onSubmit={handleSearchSubmit} className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg 
                className="h-5 w-5 text-gray-400" 
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" 
                />
              </svg>
            </div>
            <input
              type="text"
              value={localSearchTerm}
              onChange={handleSearchChange}
              placeholder="Search orders by number, customer, channel, or tracking..."
              className="
                block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md 
                leading-5 bg-white placeholder-gray-500 text-gray-900
                focus:outline-none focus:placeholder-gray-400 focus:ring-1 
                focus:ring-blue-500 focus:border-blue-500
              "
            />
          </form>
        </div>

        {/* Filters Button */}
        <div className="ml-4">
          <button
            onClick={onFiltersClick}
            className="
              relative inline-flex items-center px-4 py-2 border border-gray-300 
              rounded-md shadow-sm bg-white text-sm font-medium text-gray-700
              hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 
              focus:ring-blue-500 transition-colors duration-150
            "
          >
            {/* Filter Icon */}
            <svg 
              className="h-4 w-4 mr-2" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z" 
              />
            </svg>
            Filters
            
            {/* Active Filters Count Badge */}
            {activeFiltersCount > 0 && (
              <span className="
                absolute -top-2 -right-2 bg-blue-600 text-white text-xs 
                rounded-full h-5 w-5 flex items-center justify-center
                min-w-[20px] px-1
              ">
                {activeFiltersCount}
              </span>
            )}
          </button>
        </div>
      </div>

      {/* Quick Filter Buttons */}
      <div className="mt-3 flex flex-wrap gap-2">
        <button 
          onClick={handleAllOrdersClick}
          className="
            inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium
            bg-gray-100 text-gray-800 hover:bg-gray-200 transition-colors duration-150
          "
        >
          All Orders
        </button>
        <button 
          onClick={handleOpenOrdersClick}
          className="
            inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium
            bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors duration-150
          "
        >
          Open Orders
        </button>
        <button 
          onClick={handleShippedOrdersClick}
          className="
            inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium
            bg-yellow-100 text-yellow-800 hover:bg-yellow-200 transition-colors duration-150
          "
        >
          Shipped Orders
        </button>
        <button 
          onClick={handleUrgentOrdersClick}
          className="
            inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium
            bg-red-100 text-red-800 hover:bg-red-200 transition-colors duration-150
          "
        >
          🚨 Urgent
        </button>
      </div>
    </div>
  );
};

export default OrdersHeader; 