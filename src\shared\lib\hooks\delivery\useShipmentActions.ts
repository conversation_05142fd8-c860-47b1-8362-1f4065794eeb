import { useState, useEffect, useCallback, useMemo } from 'react';
import { markOrderAsResent } from '@/shared/lib/services/order/live-order-service';

interface UseShipmentActionsProps {
  isOpen: boolean;
  onClose: () => void;
  shipmentOrderNumber?: string;
}

interface UseShipmentActionsReturn {
  isMarkingAsResent: boolean;
  handleCopyTracking: (trackingNumber: string) => Promise<void>;
  handleMarkAsResend: () => Promise<void>;
}

export const useShipmentActions = ({ 
  isOpen, 
  onClose, 
  shipmentOrderNumber 
}: UseShipmentActionsProps): UseShipmentActionsReturn => {
  const [isMarkingAsResent, setIsMarkingAsResent] = useState(false);

  // Handle escape key to close panel - memoize the handler to stabilize dependencies
  const handleEscapeKey = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      onClose();
    }
  }, [onClose]);

  // Handle escape key to close panel
  useEffect(() => {
    if (!isOpen) return;
    
    document.addEventListener('keydown', handleEscapeKey);
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isOpen, handleEscapeKey]); // Use memoized handler in dependencies

  // Handle copying tracking number to clipboard
  const handleCopyTracking = useCallback(async (trackingNumber: string) => {
    if (!trackingNumber) return;
    try {
      await navigator.clipboard.writeText(trackingNumber);
    } catch (err) {
      console.error('Failed to copy tracking number:', err);
    }
  }, []);

  // Handle marking shipment as resend
  const handleMarkAsResend = useCallback(async () => {
    if (!shipmentOrderNumber) return;
    
    setIsMarkingAsResent(true);
    try {
      await markOrderAsResent(shipmentOrderNumber);
      onClose(); // Close panel after successful action
    } catch (error) {
      console.error('Failed to mark order as resent:', error);
    } finally {
      setIsMarkingAsResent(false);
    }
  }, [shipmentOrderNumber, onClose]);

  // Memoize return object to prevent unnecessary re-renders in consumers
  return useMemo(() => ({
    isMarkingAsResent,
    handleCopyTracking,
    handleMarkAsResend
  }), [isMarkingAsResent, handleCopyTracking, handleMarkAsResend]);
}; 