import { supabase } from '../../../../../supabase/supabase_client/client';
import { UserProfile } from '../../../../types';
import { AuthCacheService } from './cache.service';

export class AuthProfileService {
  // Fetch user profile from database with retry logic
  static async fetchUserProfile(
    user: any, 
    isLoginFlow: boolean = false
  ): Promise<UserProfile> {
    if (!user) {
      throw new Error('No user provided');
    }

    const maxRetries = isLoginFlow ? 3 : 1;
    const timeout = isLoginFlow ? 5000 : 2000;
    
    // Fetching user profile
    
    let userData = null;
    let userError = null;
    
    // Retry logic for robust profile fetching
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Attempting to fetch user role from database
        
        // Create timeout promise
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error(`Users table query timeout (attempt ${attempt})`)), timeout);
        });

        // Database query promise
        const queryPromise = supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .single();

        const result = await Promise.race([queryPromise, timeoutPromise]);
        const queryResult = result as any;
        
        userData = queryResult.data;
        userError = queryResult.error;
        
        if (userData && !userError) {
          break; // Success, exit retry loop
        } else {
          if (attempt < maxRetries) {
            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
      } catch (attemptError) {
        // Attempt failed
        if (attempt === maxRetries) {
          throw attemptError; // Re-throw on final attempt
        }
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // Create profile from database data or fallback
    if (userData && !userError) {
      return {
        id: userData.id,
        full_name: userData.full_name || user.email?.split('@')[0] || 'User',
        role: userData.role,
        business_unit_id: userData.business_unit_id
      };
    } else {
      // Fallback profile - use role from user metadata if available (from invitation)
      // NOTE: user.role is Supabase's system role ("authenticated"), not your app role
      const metadataRole = user.user_metadata?.role;
      const fallbackRole = metadataRole || 'master'; // Default to master for existing users
      
      return {
        id: user.id,
        full_name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',
        role: fallbackRole as 'staff' | 'master' | 'admin',
        business_unit_id: user.user_metadata?.business_unit_id || null
      };
    }
  }

  // Quick role refresh for background updates
  static async refreshUserRole(user: any, currentSession: any): Promise<UserProfile | null> {
    if (!user || !user.email) {
      // No user email for role refresh
      return null;
    }

    try {
      // Refreshing user role in background
      
      // Quick query with short timeout for background refresh
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Background role query timeout')), 1500);
      });

      const queryPromise = supabase
        .from('users')
        .select('role, full_name, business_unit_id')
        .eq('id', user.id)
        .single();

      const result = await Promise.race([queryPromise, timeoutPromise]);
      const { data: userData, error: userError } = result as any;

      if (userData && !userError) {
        const updatedProfile: UserProfile = {
          id: user.id,
          role: userData.role,
          full_name: userData.full_name || user.email?.split('@')[0] || 'User',
          business_unit_id: userData.business_unit_id
        };

        // Update cache with fresh data
        AuthCacheService.saveToCache(currentSession, updatedProfile);
        
        return updatedProfile;
      } else {
        return null;
      }
    } catch (error) {
      // Background role refresh error
      return null;
    }
  }

  // Manual role fetch for when exact role is needed
  static async fetchFreshUserRole(userEmail: string, currentSession: any): Promise<UserProfile | null> {
    if (!userEmail) {
      // No user email provided
      return null;
    }

    try {
      // Manually fetching user role from database
      
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('role, full_name, business_unit_id')
        .eq('id', currentSession.user.id)
        .single();

      if (userData && !userError) {
        const updatedProfile: UserProfile = {
          id: currentSession.user.id,
          role: userData.role,
          full_name: userData.full_name || userEmail.split('@')[0] || 'User',
          business_unit_id: userData.business_unit_id
        };

        // Update cache with fresh data
        AuthCacheService.saveToCache(currentSession, updatedProfile);
        
        return updatedProfile;
      } else {
        return null;
      }
    } catch (error) {
      // Manual role fetch error
      return null;
    }
  }
} 