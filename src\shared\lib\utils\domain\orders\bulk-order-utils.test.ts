import {
  generateOrderNumber,
  isValidOrderNumber,
  isPotentialDuplicate,
  calculateOrderSubtotal,
  formatDataForCsvExport,
  generateBulkOrderTemplate
} from './bulk-order-utils';

describe('Bulk Order Utilities', () => {
  describe('generateOrderNumber', () => {
    it('generates order number with default prefix', () => {
      const orderNumber = generateOrderNumber();
      
      // Should start with default prefix
      expect(orderNumber.startsWith('ORD-')).toBe(true);
      
      // Should have the correct format (prefix-timestamp-random)
      const parts = orderNumber.split('-');
      expect(parts.length).toBe(3);
      expect(parts[0]).toBe('ORD');
      expect(parts[1].length).toBeGreaterThan(4); // timestamp
      expect(parts[2].length).toBe(3); // random number
    });
    
    it('generates order number with custom prefix', () => {
      const orderNumber = generateOrderNumber('CUSTOM');
      expect(orderNumber.startsWith('CUSTOM-')).toBe(true);
    });
    
    it('generates unique order numbers on successive calls', () => {
      const orderNumber1 = generateOrderNumber();
      const orderNumber2 = generateOrderNumber();
      
      expect(orderNumber1).not.toBe(orderNumber2);
    });
  });
  
  describe('isValidOrderNumber', () => {
    it('validates correct order numbers', () => {
      expect(isValidOrderNumber('ORDER-123')).toBe(true);
      expect(isValidOrderNumber('AMZN-123456')).toBe(true);
      expect(isValidOrderNumber('EBAY-12345-67890')).toBe(true);
    });
    
    it('invalidates incorrect order numbers', () => {
      expect(isValidOrderNumber('')).toBe(false);
      expect(isValidOrderNumber('   ')).toBe(false);
      expect(isValidOrderNumber('123')).toBe(false);
      expect(isValidOrderNumber('AB')).toBe(false);
    });
    
    it('handles edge cases', () => {
      expect(isValidOrderNumber('ORDER-')).toBe(true); // Meets length requirement
      expect(isValidOrderNumber('12345')).toBe(true); // Meets length requirement
    });
  });
  
  describe('isPotentialDuplicate', () => {
    it('identifies duplicates', () => {
      const existingOrders = ['ORDER-123', 'ORDER-456', 'ORDER-789'];
      
      expect(isPotentialDuplicate('ORDER-123', existingOrders)).toBe(true);
      expect(isPotentialDuplicate('order-123', existingOrders)).toBe(true); // Case insensitive
    });
    
    it('passes non-duplicates', () => {
      const existingOrders = ['ORDER-123', 'ORDER-456', 'ORDER-789'];
      
      expect(isPotentialDuplicate('ORDER-999', existingOrders)).toBe(false);
      expect(isPotentialDuplicate('DIFFERENT-123', existingOrders)).toBe(false);
    });
    
    it('handles edge cases', () => {
      expect(isPotentialDuplicate('ORDER-123', [])).toBe(false);
      expect(isPotentialDuplicate('', ['ORDER-123'])).toBe(false);
      expect(isPotentialDuplicate('ORDER-123', null as any)).toBe(false);
    });
  });
  
  describe('calculateOrderSubtotal', () => {
    it('calculates subtotal correctly with number quantities', () => {
      const items = [
        { quantity: 2, unit_price: 10 },
        { quantity: 3, unit_price: 15 }
      ];
      
      // (2 * 10) + (3 * 15) = 20 + 45 = 65
      expect(calculateOrderSubtotal(items)).toBe(65);
    });
    
    it('calculates subtotal correctly with string quantities', () => {
      const items = [
        { quantity: '2', unit_price: 10 },
        { quantity: '3', unit_price: 15 }
      ];
      
      // (2 * 10) + (3 * 15) = 20 + 45 = 65
      expect(calculateOrderSubtotal(items)).toBe(65);
    });
    
    it('handles missing or invalid values', () => {
      expect(calculateOrderSubtotal([])).toBe(0);
      expect(calculateOrderSubtotal([{ quantity: 'abc', unit_price: 10 }] as any)).toBe(0);
      expect(calculateOrderSubtotal([{ quantity: 2 }])).toBe(0);
      expect(calculateOrderSubtotal([{ quantity: 2, unit_price: undefined }])).toBe(0);
    });
  });
  
  describe('formatDataForCsvExport', () => {
    it('formats data with line numbers', () => {
      const data = [
        { name: 'Item 1', value: 10 },
        { name: 'Item 2', value: 20 }
      ];
      
      const formatted = formatDataForCsvExport(data);
      
      expect(formatted).toEqual([
        { row: 1, name: 'Item 1', value: 10 },
        { row: 2, name: 'Item 2', value: 20 }
      ]);
    });
    
    it('formats data without line numbers', () => {
      const data = [
        { name: 'Item 1', value: 10 },
        { name: 'Item 2', value: 20 }
      ];
      
      const formatted = formatDataForCsvExport(data, false);
      
      expect(formatted).toEqual([
        { name: 'Item 1', value: 10 },
        { name: 'Item 2', value: 20 }
      ]);
    });
    
    it('handles empty data', () => {
      expect(formatDataForCsvExport([])).toEqual([]);
      expect(formatDataForCsvExport(null as any)).toEqual([]);
    });
  });
  
  describe('generateBulkOrderTemplate', () => {
    it('generates a valid template', () => {
      const template = generateBulkOrderTemplate();
      
      expect(Array.isArray(template)).toBe(true);
      expect(template.length).toBe(1);
      
      const templateRow = template[0];
      expect(templateRow).toHaveProperty('order_number');
      expect(templateRow).toHaveProperty('platform');
      expect(templateRow).toHaveProperty('channel');
      expect(templateRow).toHaveProperty('customer_email');
      expect(templateRow).toHaveProperty('product_sku');
      expect(templateRow).toHaveProperty('quantity');
    });
  });
}); 