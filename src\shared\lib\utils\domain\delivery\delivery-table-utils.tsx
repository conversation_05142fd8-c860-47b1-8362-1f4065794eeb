import { DeliveryStatusEnum } from '@/types';
import { 
  CheckCircleIcon, 
  TruckIcon, 
  ExclamationTriangleIcon,
  ClockIcon,
  XCircleIcon,
  ArrowUturnLeftIcon
} from '@heroicons/react/24/outline';
import React from 'react';

/**
 * Capitalizes and formats a status string by replacing underscores with spaces
 * and capitalizing each word
 */
export const capitalizeStatus = (status: string = '') => {
  return status.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
};

/**
 * Returns a styled badge component for the given delivery status
 */
export const getStatusBadge = (status: DeliveryStatusEnum) => {
  const baseClasses = "inline-flex items-center px-2 py-0.5 text-xs font-medium rounded-full";
  const capitalizedStatus = capitalizeStatus(status);
  
  switch (status) {
    case 'delivered':
      return (
        <span className={`${baseClasses} bg-green-100 text-green-700`}>
          <CheckCircleIcon className="h-3 w-3 mr-1" />
          {capitalizedStatus}
        </span>
      );
    case 'in_transit':
      return (
        <span className={`${baseClasses} bg-blue-100 text-blue-700`}>
          <TruckIcon className="h-3 w-3 mr-1" />
          {capitalizedStatus}
        </span>
      );
    case 'out_for_delivery':
      return (
        <span className={`${baseClasses} bg-purple-100 text-purple-700`}>
          <TruckIcon className="h-3 w-3 mr-1" />
          {capitalizedStatus}
        </span>
      );
    case 'delayed':
      return (
        <span className={`${baseClasses} bg-yellow-100 text-yellow-700`}>
          <ClockIcon className="h-3 w-3 mr-1" />
          {capitalizedStatus}
        </span>
      );
    case 'lost':
      return (
        <span className={`${baseClasses} bg-red-100 text-red-700`}>
          <XCircleIcon className="h-3 w-3 mr-1" />
          {capitalizedStatus}
        </span>
      );
    case 'exception':
      return (
        <span className={`${baseClasses} bg-orange-100 text-orange-700`}>
          <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
          {capitalizedStatus}
        </span>
      );
    case 'returned':
      return (
        <span className={`${baseClasses} bg-gray-100 text-gray-700`}>
          <ArrowUturnLeftIcon className="h-3 w-3 mr-1" />
          {capitalizedStatus}
        </span>
      );
    default:
      return (
        <span className={`${baseClasses} bg-gray-100 text-gray-700`}>
          {capitalizedStatus}
        </span>
      );
  }
};

/**
 * Formats a date string into a localized format
 */
export const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'numeric',
    day: 'numeric',
    year: 'numeric'
  });
};

/**
 * Copies text to clipboard with fallback for older browsers
 */
export const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
  } catch (err) {
    console.error('Failed to copy text:', err);
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
  }
}; 