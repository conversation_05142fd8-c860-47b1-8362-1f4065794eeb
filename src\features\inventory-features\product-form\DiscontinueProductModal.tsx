import React, { useState } from 'react';
import Modal from '@/shared/ui/overlay/Modal';
import { ExclamationTriangleIcon, CheckCircleIcon, ExclamationCircleIcon } from '@heroicons/react/24/outline';
import { Product, ProductStatusEnum } from '@/types';

interface DiscontinueProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  productId: string;
  productName: string;
  onDiscontinue: (productId: string, reason: string, notes: string) => Promise<void>;
}

type DiscontinuationReason = 
  | 'discontinued_by_manufacturer' 
  | 'low_demand' 
  | 'replaced_by_newer_model'
  | 'quality_issues'
  | 'seasonal_product_end'
  | 'other';

const DISCONTINUATION_REASONS: { value: DiscontinuationReason; label: string }[] = [
  { value: 'discontinued_by_manufacturer', label: 'Discontinued by manufacturer' },
  { value: 'low_demand', label: 'Low demand for product' },
  { value: 'replaced_by_newer_model', label: 'Replaced by newer model/version' },
  { value: 'quality_issues', label: 'Quality or performance issues' },
  { value: 'seasonal_product_end', label: 'Seasonal product end of lifecycle' },
  { value: 'other', label: 'Other reason' },
];

const DiscontinueProductModal: React.FC<DiscontinueProductModalProps> = ({
  isOpen,
  onClose,
  productId,
  productName,
  onDiscontinue
}) => {
  // Form state
  const [selectedReason, setSelectedReason] = useState<DiscontinuationReason>('discontinued_by_manufacturer');
  const [notes, setNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    
    try {
      // Get the selected reason label for better readability in notes
      const reasonLabel = DISCONTINUATION_REASONS.find(r => r.value === selectedReason)?.label || selectedReason;
      
      // Call the onDiscontinue function provided by the parent
      await onDiscontinue(productId, selectedReason, notes);
      
      // Set success state
      setSuccess(true);
      
      // Close the modal after a delay
      setTimeout(() => {
        handleClose();
      }, 2000);
    } catch (err) {
      console.error('Error discontinuing product:', err);
      setError(err instanceof Error ? err.message : 'Failed to discontinue product');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Reset form state when closing the modal
  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
      
      // Reset form after closing animation
      setTimeout(() => {
        setSelectedReason('discontinued_by_manufacturer');
        setNotes('');
        setError(null);
        setSuccess(false);
      }, 300);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Discontinue Product"
      size="md"
      showDefaultFooter={false}
    >
      {success ? (
        <div className="p-4 flex flex-col items-center text-center">
          <CheckCircleIcon className="h-16 w-16 text-green-500 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Product Discontinued</h3>
          <p className="text-gray-600 mb-4">
            {productName} has been successfully marked as discontinued.
          </p>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6 p-1">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 flex items-start">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 mt-0.5 mr-3" />
            <div>
              <h3 className="font-medium text-yellow-800">You are about to discontinue a product</h3>
              <p className="text-yellow-700 text-sm mt-1">
                Discontinuing <span className="font-medium">{productName}</span> will mark it as unavailable for new orders. 
                Existing inventory will remain in the system, but the product will no longer be available for new sales.
              </p>
            </div>
          </div>
          
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start">
              <ExclamationCircleIcon className="h-5 w-5 text-red-400 mt-0.5 mr-3" />
              <p className="text-red-800">{error}</p>
            </div>
          )}
          
          <div>
            <label htmlFor="discontinue-reason" className="block text-sm font-medium text-gray-700 mb-1">
              Reason for Discontinuation <span className="text-red-500">*</span>
            </label>
            <select
              id="discontinue-reason"
              name="reason"
              value={selectedReason}
              onChange={(e) => setSelectedReason(e.target.value as DiscontinuationReason)}
              className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
              required
              disabled={isSubmitting}
            >
              {DISCONTINUATION_REASONS.map((reason) => (
                <option key={reason.value} value={reason.value}>
                  {reason.label}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label htmlFor="discontinue-notes" className="block text-sm font-medium text-gray-700 mb-1">
              Additional Notes
            </label>
            <textarea
              id="discontinue-notes"
              name="notes"
              rows={4}
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
              placeholder="Add any additional context about why this product is being discontinued..."
              disabled={isSubmitting}
            />
          </div>
          
          <div className="pt-5 border-t border-gray-200">
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={handleClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Processing...' : 'Discontinue Product'}
              </button>
            </div>
          </div>
        </form>
      )}
    </Modal>
  );
};

export default DiscontinueProductModal; 