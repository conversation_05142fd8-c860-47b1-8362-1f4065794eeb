import React, { useMemo, useCallback } from 'react';
import { OrderFilters } from '@/shared/lib/services/order/live-order-service';

interface FilterChipsProps {
  filters: OrderFilters;
  onRemoveFilter: (filterType: string, value?: string) => void;
  onClearAll: () => void;
}

interface FilterChip {
  id: string;
  label: string;
  type: string;
  value?: string;
}

const FilterChips: React.FC<FilterChipsProps> = ({ filters, onRemoveFilter, onClearAll }) => {
  // Memoize expensive filter-to-chips conversion to avoid recalculation on every render
  const chips = useMemo((): FilterChip[] => {
    // Optimize: Use array.flatMap instead of forEach+push for array building
    const filterArrays = [
      // Status filters - use map instead of forEach
      filters.status?.map(status => ({
        id: `status-${status}`,
        label: `Status: ${status}`,
        type: 'status',
        value: status
      })) || [],

      // Platform filters - use map instead of forEach  
      filters.platform?.map(platform => ({
        id: `platform-${platform}`,
        label: `Platform: ${platform}`,
        type: 'platform',
        value: platform
      })) || [],

      // Channel filters - use map instead of forEach
      filters.channel?.map(channel => ({
        id: `channel-${channel}`,
        label: `Channel: ${channel}`,
        type: 'channel',
        value: channel
      })) || [],

      // Date range filter - conditional array
      (filters.dateFrom || filters.dateTo) ? [{
        id: 'dateRange',
        label: (() => {
          let dateLabel = 'Date: ';
          if (filters.dateFrom && filters.dateTo) {
            dateLabel += `${filters.dateFrom} to ${filters.dateTo}`;
          } else if (filters.dateFrom) {
            dateLabel += `from ${filters.dateFrom}`;
          } else if (filters.dateTo) {
            dateLabel += `until ${filters.dateTo}`;
          }
          return dateLabel;
        })(),
        type: 'dateRange'
      }] : [],

      // Flag filters - use filter+map for conditional inclusion
      [
        filters.isUrgent !== undefined && {
          id: 'isUrgent',
          label: filters.isUrgent ? 'Urgent Orders' : 'Non-Urgent Orders',
          type: 'isUrgent'
        },
        filters.isProblem !== undefined && {
          id: 'isProblem',
          label: filters.isProblem ? 'Problem Orders' : 'No Problem Orders',
          type: 'isProblem'
        },
        filters.isResent !== undefined && {
          id: 'isResent',
          label: filters.isResent ? 'Resent Orders' : 'Original Orders',
          type: 'isResent'
        },
        filters.hasNotes !== undefined && {
          id: 'hasNotes',
          label: filters.hasNotes ? 'Orders with Notes' : 'Orders without Notes',
          type: 'hasNotes'
        }
      ].filter((item): item is FilterChip => Boolean(item)) // Type-safe filter
    ];

    // Task 3.8: Use flat() for efficient array flattening instead of multiple forEach operations
    return filterArrays.flat();
  }, [filters]);

  // Memoize chip removal handler to prevent recreation on every render
  const handleRemoveChip = useCallback((chip: FilterChip) => {
    onRemoveFilter(chip.type, chip.value);
  }, [onRemoveFilter]);

  // Don't render if no active filters - MOVED AFTER ALL HOOKS
  if (chips.length === 0) {
    return null;
  }

  return (
    <div className="py-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 flex-wrap">
          <span className="text-sm text-gray-600 font-medium">Active Filters:</span>
          {chips.map((chip) => (
            <div
              key={chip.id}
              className="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full border border-blue-200"
            >
              <span>{chip.label}</span>
              <button
                onClick={() => handleRemoveChip(chip)}
                className="ml-1 p-0.5 hover:bg-blue-200 rounded-full transition-colors"
                aria-label={`Remove ${chip.label} filter`}
              >
                <svg
                  className="w-3 h-3"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          ))}
        </div>
        
        {chips.length > 1 && (
          <button
            onClick={onClearAll}
            className="text-sm text-gray-500 hover:text-gray-700 font-medium transition-colors"
          >
            Clear All
          </button>
        )}
      </div>
    </div>
  );
};

export default FilterChips; 