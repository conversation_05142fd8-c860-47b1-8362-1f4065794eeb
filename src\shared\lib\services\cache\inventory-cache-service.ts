import { InventoryView } from '@/types';
import { InventoryListParams } from '@/shared/lib/services/inventory';
import { CacheStorage, BaseCacheEntry } from './cache-storage';
import { CacheKeyManager } from './cache-key-manager';

// Types for inventory cache entries
export interface InventoryCacheEntry extends BaseCacheEntry {
  inventory: InventoryView[];
  totalCount: number;
  filters: InventoryListParams;
}

// Cache configuration
const INVENTORY_CACHE_CONFIG = {
  maxEntries: 25,          // Maximum number of unique query results to cache
  defaultTTL: 5 * 60000,   // 5 minutes default TTL
  staleWhileRevalidate: 2 * 60000 // Additional 2 minutes to serve stale data while revalidating
};

/**
 * Inventory cache service for caching inventory data with TTL
 */
class InventoryCacheService {
  private cache: CacheStorage<InventoryCacheEntry>;
  private keyManager: CacheKeyManager;

  constructor() {
    this.cache = new CacheStorage<InventoryCacheEntry>(INVENTORY_CACHE_CONFIG);
    this.keyManager = new CacheKeyManager();
  }

  /**
   * Generate a cache key from inventory list params
   */
  private generateCacheKey(params: InventoryListParams): string {
    const normalizedParams = {
      search: params.search || '',
      page: params.page || 1,
      pageSize: params.pageSize || 10,
      sortBy: params.sortBy || '',
      sortDirection: params.sortDirection || 'asc',
      status: params.status?.join(',') || '',
      productType: params.productType?.join(',') || '',
      stockCondition: params.stockCondition?.join(',') || '',
    };
    
    return this.keyManager.generateCacheKey('inventory_list', normalizedParams);
  }

  /**
   * Cache inventory list results
   */
  cacheInventoryList(
    params: InventoryListParams,
    data: InventoryView[],
    totalCount: number
  ): void {
    const key = this.generateCacheKey(params);
    
    const entry: InventoryCacheEntry = {
      inventory: data,
      totalCount,
      timestamp: new Date(),
      filters: params,
      expiresAt: new Date(Date.now() + INVENTORY_CACHE_CONFIG.defaultTTL)
    };
    
    // Log caching information for debugging
    // console.log(`Caching inventory data with key: ${key}`, {
    //   itemCount: data.length,
    //   totalCount,
    //   expiresAt: entry.expiresAt
    // });
    
    this.cache.set(key, entry);
  }

  /**
   * Get cached inventory list results
   */
  getCachedInventoryList(
    params: InventoryListParams
  ): { data: InventoryView[], totalCount: number, isStale: boolean } | null {
    const key = this.generateCacheKey(params);
    const cachedEntry = this.cache.get(key);
    
    if (!cachedEntry) {
      //console.log(`Cache miss for inventory list with key: ${key}`);
      return null;
    }
    
    // Check if the entry is still valid
    if (this.cache.isValid(cachedEntry)) {
      //console.log(`Cache hit for inventory list with key: ${key}`);
      return { 
        data: cachedEntry.inventory, 
        totalCount: cachedEntry.totalCount,
        isStale: false
      };
    }
    
    // Check if we can serve stale data while revalidating
    if (this.cache.isStaleButRevalidatable(cachedEntry)) {
      //console.log(`Serving stale data for inventory list with key: ${key}, will revalidate`);
      return { 
        data: cachedEntry.inventory, 
        totalCount: cachedEntry.totalCount,
        isStale: true
      };
    }
    
    //console.log(`Cache expired for inventory list with key: ${key}`);
    return null;
  }

  /**
   * Invalidate cache entry for specific inventory item
   * If no productId provided, invalidates all entries
   */
  invalidateInventoryItem(productId?: string): void {
    if (productId) {
      // In a real implementation, we could keep track of which cache keys contain 
      // which product IDs and selectively invalidate only those.
      // For simplicity, we're just clearing the entire cache.
      console.log(`Invalidating cache entries for product ID: ${productId}`);
    } else {
      console.log('Invalidating all inventory cache entries');
    }
    this.cache.clear();
  }

  /**
   * Invalidate all inventory cache entries
   */
  invalidateAllInventory(): void {
    console.log('Invalidating all inventory cache entries');
    this.cache.clear();
  }

  /**
   * Clean up expired cache entries
   */
  cleanupExpired(): number {
    return this.cache.cleanupExpired();
  }

  /**
   * Get cache statistics
   */
  getStats() {
    return this.cache.getStats();
  }
}

// Export singleton instance
export const inventoryCacheService = new InventoryCacheService(); 