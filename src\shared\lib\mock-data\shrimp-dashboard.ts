// Mock data for Shrimp Products Dashboard
import { 
  KpiDataType, 
  SalesDataPoint, 
  DropdownOption,
  IntelligenceChartDataPoint // Import IntelligenceChartDataPoint
} from '@/types/index';
import {
  InboxStackIcon,
  ArchiveBoxArrowDownIcon,
  PaperAirplaneIcon,
  CheckCircleIcon,
} from '@/shared/config';

// Shrimp Products Dashboard metrics
export const SHRIMP_DASHBOARD_METRICS = {
  totalGrossSales: '$95,230',
  totalOrders: '456',
  avgProfitMargin: '38.5%',
  activeProducts: '24'
};

// Shrimp Products KPI Data
export const SHRIMP_KPI_DATA: KpiDataType[] = [
  { value: '8', label: 'Open Orders', icon: InboxStackIcon, color: 'text-blue-600', progress: 20 },
  { value: '12', label: 'Packed Orders', icon: ArchiveBoxArrowDownIcon, color: 'text-yellow-600', progress: 30 },
  { value: '145', label: 'Ship', icon: PaperAirplaneIcon, color: 'text-green-600', progress: 75 },
  { value: '1', label: 'Delivered order', icon: CheckCircleIcon, color: 'text-purple-600', progress: 5 },
];

// Shrimp Products Sales Chart Data
export const SHRIMP_SALES_CHART_DATA: SalesDataPoint[] = [
  { name: 'January', currentValue: 8500, previousValue: 6200 },
  { name: 'February', currentValue: 12000, previousValue: 9800 },
  { name: 'March', currentValue: 15500, previousValue: 11200 },
  { name: 'April', currentValue: 18200, previousValue: 14500 },
  { name: 'May', currentValue: 22000, previousValue: 17800 },
  { name: 'June', currentValue: 19500, previousValue: 16200 },
  { name: 'July', currentValue: 0, previousValue: 0 },
  { name: 'August', currentValue: 0, previousValue: 0 },
  { name: 'September', currentValue: 0, previousValue: 0 },
  { name: 'October', currentValue: 0, previousValue: 0 },
  { name: 'November', currentValue: 0, previousValue: 0 },
  { name: 'December', currentValue: 0, previousValue: 0 },
];

// Top Selling Shrimp Products
export const SHRIMP_TOP_SELLERS = [
  { name: 'Jumbo Fresh Shrimp', sales: '$18,500', units: 245, growth: '+12%' },
  { name: 'Medium Frozen Shrimp', sales: '$15,200', units: 189, growth: '+8%' },
  { name: 'Cooked Cocktail Shrimp', sales: '$12,800', units: 156, growth: '+15%' },
  { name: 'Breaded Butterfly Shrimp', sales: '$9,600', units: 98, growth: '+5%' },
  { name: 'Large Raw Shrimp', sales: '$8,900', units: 87, growth: '+3%' },
];

// Low Stock Shrimp Products
export const SHRIMP_LOW_STOCK_ALERTS = [
  { sku: 'SHR-001', productName: 'Jumbo Fresh Shrimp 1lb', stockLeft: 12, minThreshold: 25 },
  { sku: 'SHR-015', productName: 'Medium Frozen Shrimp 2lb', stockLeft: 8, minThreshold: 20 },
  { sku: 'SHR-023', productName: 'Cooked Cocktail Shrimp 500g', stockLeft: 5, minThreshold: 15 },
  { sku: 'SHR-031', productName: 'Breaded Butterfly Shrimp 1.5lb', stockLeft: 3, minThreshold: 10 },
];

import { MOCK_TOP_SELLERS_DATA } from './reports'; // Import MOCK_TOP_SELLERS_DATA

// Shrimp Top Sellers Data for Recharts Pie Chart
const ALL_STORES_DATA = MOCK_TOP_SELLERS_DATA;
const DESIRED_STORES = [
  'Amazon-NHU',
  'Amazon-YASHKUN',
  'Amazon-NextTech', // Matched with 'Amazon-NEXT TECH' in previous data, MOCK_TOP_SELLERS_DATA has 'Amazon-NextTech'
  'Walmart-Shrimps',
  'Ebay-Shrimps',
  'Website-Seam'
];

export const SHRIMP_TOP_SELLERS_DATA = ALL_STORES_DATA
  .filter(storeData => DESIRED_STORES.includes(storeData.store))
  .map(({ store, sales, color }) => ({ store, sales, color }));


export const SHRIMP_INTELLIGENCE_CHART_DATA: IntelligenceChartDataPoint[] = [
  { date: '2025-05-01', revenue: 1200, orders: 15 },
  { date: '2025-05-02', revenue: 1500, orders: 20 },
  { date: '2025-05-03', revenue: 1300, orders: 18 },
  { date: '2025-05-04', revenue: 1700, orders: 22 },
  { date: '2025-05-05', revenue: 1600, orders: 20 },
  { date: '2025-05-06', revenue: 1900, orders: 25 },
  { date: '2025-05-07', revenue: 2100, orders: 28 },
  { date: '2025-05-08', revenue: 1800, orders: 23 },
  { date: '2025-05-09', revenue: 2200, orders: 30 },
  { date: '2025-05-10', revenue: 2000, orders: 26 },
  { date: '2025-05-11', revenue: 2300, orders: 32 },
  { date: '2025-05-12', revenue: 2500, orders: 35 },
];

// Shrimp Processing Methods
export const SHRIMP_PROCESSING_METHODS = [
  { method: 'Fresh/Raw', orders: 156, revenue: '$35,200' },
  { method: 'Frozen', orders: 134, revenue: '$28,800' },
  { method: 'Cooked', orders: 89, revenue: '$18,600' },
  { method: 'Breaded', orders: 77, revenue: '$12,630' },
];

export const shrimpSalesTypeOptions: DropdownOption[] = [
  { value: 'net_sales', label: 'Net Sales' },
  { value: 'gross_sales', label: 'Gross Sales' },
  { value: 'by_size', label: 'Sales by Size' },
  { value: 'by_processing', label: 'Sales by Processing Method' },
];

export const shrimpYearComparisonOptions: DropdownOption[] = [
  { value: 'yearly_this_vs_last', label: 'Yearly: This Year vs. Last Year' },
  { value: 'monthly_this_vs_last', label: 'Monthly: This Month vs. Last Month' },
  { value: 'quarterly_this_vs_last', label: 'Quarterly: This Quarter vs. Last Quarter' },
  { value: 'seasonal_comparison', label: 'Seasonal Comparison' },
];