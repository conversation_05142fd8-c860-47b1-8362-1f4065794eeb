import path from 'path';
import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import { visualizer } from 'rollup-plugin-visualizer';
import tailwindcss from 'tailwindcss';
import autoprefixer from 'autoprefixer';

export default defineConfig(({ mode }) => {
  // Load environment variables from .env files
  const env = loadEnv(mode, '.', '');

  return {
    // Define global constants for your application
    define: {
      'process.env.API_KEY': JSON.stringify(env.GEMINI_API_KEY),
      'process.env.GEMINI_API_KEY': JSON.stringify(env.GEMINI_API_KEY)
    },
    // Configure path aliases for easier imports
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      }
    },
    // Optimize dependencies for faster development server startup
    // This pre-bundles commonly used npm modules
    optimizeDeps: {
      include: [
        'react', 
        'react-dom', 
        'react/jsx-runtime', // Essential for React 17+ new JSX transform
        '@heroicons/react',
        'react-icons',
        'react-icons/fa',
        'react-icons/si',
        'react-icons/tb'
      ],
      // No need to explicitly exclude, Vite handles this intelligently
      exclude: [], 
    },
    // Configure Vite plugins
    plugins: [
      react(), // React plugin for fast refresh and JSX transformation
      visualizer({ 
        open: false, // Don't auto-open in production builds
        gzipSize: true, // Show gzip size
        brotliSize: true, // Show brotli size
        filename: 'dist/stats.html' // Save analysis report to dist folder
      })
    ],
    // Development server configuration
    server: {
      // Disable error overlay for a cleaner development experience, especially during database work
      hmr: {
        overlay: false 
      },
      cors: true, // Enable CORS for API calls
      proxy: {
        // Proxy configuration for backend API calls (uncomment and configure when needed)
        // '/api': {
        //   target: 'http://localhost:3001', // Your backend server URL
        //   changeOrigin: true, // Needed for virtual hosted sites
        //   secure: false // Not recommended for production with self-signed certs
        // }
      }
    },
    // CSS pre-processor configuration (PostCSS for Tailwind CSS and Autoprefixer)
    css: {
      postcss: {
        plugins: [
          tailwindcss, // Tailwind CSS plugin
          autoprefixer, // Autoprefixer for vendor prefixes
        ],
      },
    },
    // Build configuration for production output
    build: {
      chunkSizeWarningLimit: 1000, // Increase warning limit for larger bundles (e.g., due to database integration)
      assetsInlineLimit: 4096, // Inline assets smaller than this limit (default is 4KB)
      assetsDir: 'assets', // Directory for generated assets
      target: 'esnext', // Target modern browsers for smaller bundles and better performance
      minify: 'terser', // Use Terser for better code compression (can also be 'esbuild')
      sourcemap: false, // Disable sourcemaps in production for smaller builds and to prevent source leaks
      rollupOptions: {
        output: {
          // Define vendor chunks for better caching
          manualChunks: {
            'vendor-react-core': ['react', 'react-dom'],
            'vendor-recharts': ['recharts'],
            'vendor-react-icons': ['react-icons'],
            'vendor-react-router': ['react-router-dom'],
            'vendor-stagewise-toolbar': ['@stagewise/toolbar-react'],
            'vendor-stagewise-plugins': ['@stagewise-plugins/react'],
            'vendor-supabase': ['@supabase/supabase-js'],
            'vendor-ui-utils': ['clsx', 'tailwind-merge'],
            'vendor-data-utils': ['zustand', '@tanstack/react-query', 'papaparse']
          },
          // Define how asset files (like CSS, fonts, images) are named
          assetFileNames: (assetInfo) => {
            if (assetInfo.name && assetInfo.name.endsWith('.css')) {
              return 'assets/[name]-[hash].css';
            }
            return 'assets/[name]-[hash][extname]';
          },
          // Chunk naming strategy
          chunkFileNames: 'assets/[name]-[hash].js',
          // Keep entry point files separate
          entryFileNames: 'assets/[name]-[hash].js',
        }
      }
    },
  };
});
