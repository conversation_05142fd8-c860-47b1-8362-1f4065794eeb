/**
 * Route Preloading Performance Test
 * 
 * This script tests the performance impact and benefits of route preloading.
 * It measures load times for routes before and after preloading.
 */

import { performance } from 'perf_hooks';
// Instead of directly importing, we'll create mock implementations
// since the actual implementation uses browser APIs

// Track which modules have been "preloaded"
const preloadedModules = new Set();

// Mock route preloading functions
const preloadRouteComponent = async (importFn) => {
  try {
    const module = await importFn();
    // Mark as preloaded
    preloadedModules.add(module.moduleId);
    console.debug(`✅ Preloaded route component: ${module.moduleId}`);
  } catch (error) {
    console.warn('⚠️ Failed to preload route component:', error);
  }
};

// Mock critical routes - IMPORTANT: We deliberately exclude 'fulfill' for comparison
const getCriticalRoutes = () => {
  return [
    { path: '/orders', import: () => testRoutes.orders(), priority: 100 },
    { path: '/dashboard-center', import: () => testRoutes.dashboard(), priority: 90 },
    { path: '/delivery', import: () => testRoutes.delivery(), priority: 70 },
    // fulfill is intentionally omitted to demonstrate the difference
  ];
};

// Mock preload function
const preloadCriticalRoutes = (userContext, recentRoutes) => {
  const routes = getCriticalRoutes();
  
  // Apply role filtering if user context is provided
  let filteredRoutes = routes;
  if (userContext?.role) {
    console.log(`👤 Filtering routes for role: ${userContext.role}`);
  }
  
  // Boost priority of recent routes
  if (recentRoutes?.length) {
    console.log(`🔄 Boosting priority for recent routes: ${recentRoutes.join(', ')}`);
  }
  
  // Queue routes for preloading
  setTimeout(() => {
    filteredRoutes.forEach(route => {
      preloadRouteComponent(route.import);
    });
  }, 0);
  
  return true;
};

// Mock the window and requestIdleCallback for Node environment
global.window = {};
window.requestIdleCallback = (callback) => {
  return setTimeout(() => {
    callback({
      didTimeout: false,
      timeRemaining: () => 50
    });
  }, 0);
};

// Mock browser performance API if needed
window.performance = { now: () => performance.now() };

console.log('▶️ Starting route preloading performance test...');

// Simulate network delay for first-time module loads
const simulateNetworkDelay = (moduleId) => {
  // If already preloaded, return much faster
  if (preloadedModules.has(moduleId)) {
    return new Promise(resolve => setTimeout(resolve, 5)); // 5ms for cached modules
  } 
  
  // Simulate network latency for first load - fixed delays for consistent test results
  const delayMap = {
    orders: 150,
    dashboard: 200,
    fulfill: 180,
    delivery: 160
  };
  
  return new Promise(resolve => setTimeout(resolve, delayMap[moduleId] || 150));
};

// Test route imports with simulated network delay
const testRoutes = {
  orders: async () => {
    await simulateNetworkDelay('orders');
    return { default: () => ({ name: 'orders' }), moduleId: 'orders' };
  },
  dashboard: async () => {
    await simulateNetworkDelay('dashboard');
    return { default: () => ({ name: 'dashboard' }), moduleId: 'dashboard' };
  },
  fulfill: async () => {
    await simulateNetworkDelay('fulfill');
    return { default: () => ({ name: 'fulfill' }), moduleId: 'fulfill' };
  },
  delivery: async () => {
    await simulateNetworkDelay('delivery');
    return { default: () => ({ name: 'delivery' }), moduleId: 'delivery' };
  }
};

// Mock import functions with timing tracking
const importTimers = {};
const mockedImports = Object.keys(testRoutes).reduce((acc, key) => {
  importTimers[key] = {
    firstLoad: 0,
    secondLoad: 0
  };
  
  acc[key] = () => {
    const start = performance.now();
    const result = testRoutes[key]();
    
    // Record timing for first and subsequent loads
    result.then(() => {
      const elapsed = performance.now() - start;
      
      if (importTimers[key].firstLoad === 0) {
        importTimers[key].firstLoad = elapsed;
      } else {
        importTimers[key].secondLoad = elapsed;
      }
      
      console.log(`📦 Module "${key}" imported in ${elapsed.toFixed(2)}ms${preloadedModules.has(key) ? ' (preloaded)' : ''}`);
    });
    
    return result;
  };
  
  return acc;
}, {});

/**
 * Simulate route loading sequence
 */
async function runTest() {
  console.log('📝 Test scenario: Navigation simulation with preloading');

  // Step 1: First load of a route (without preloading)
  console.log('\n🔄 Step 1: Initial route load (no preloading)');
  const start1 = performance.now();
  await mockedImports.dashboard();
  console.log(`⏱️ Initial dashboard load complete in ${(performance.now() - start1).toFixed(2)}ms`);

  // Step 2: Trigger preloading in the background
  console.log('\n🔄 Step 2: Initiating background preloading');
  const preloadStart = performance.now();
  
  // Create a fake user for role-based preloading
  const mockUser = { role: 'admin' };
  
  // Simulate recent routes
  const recentRoutes = ['/dashboard-center', '/orders'];
  
  preloadCriticalRoutes(mockUser, recentRoutes);
  console.log(`⏱️ Preloading initiated after ${(performance.now() - preloadStart).toFixed(2)}ms`);
  
  // Wait for preloading to complete
  await new Promise(resolve => setTimeout(resolve, 500));
  console.log('⏱️ Background preloading completed');

  // Step 3: Navigate to a preloaded route
  console.log('\n🔄 Step 3: Navigate to previously preloaded route');
  const start3 = performance.now();
  await mockedImports.orders();
  console.log(`⏱️ Preloaded orders route loaded in ${(performance.now() - start3).toFixed(2)}ms`);

  // Step 4: Navigate to another preloaded route
  console.log('\n🔄 Step 4: Navigate to another preloaded route');
  const start4 = performance.now();
  await mockedImports.delivery();
  console.log(`⏱️ Preloaded delivery route loaded in ${(performance.now() - start4).toFixed(2)}ms`);
  
  // Step 5: Navigate to a non-preloaded route for comparison
  console.log('\n🔄 Step 5: Navigate to non-preloaded route (for comparison)');
  const start5 = performance.now();
  await mockedImports.fulfill();
  console.log(`⏱️ Non-preloaded fulfill route loaded in ${(performance.now() - start5).toFixed(2)}ms`);

  // Print summary
  console.log('\n📊 Test Results Summary:');
  Object.keys(importTimers).forEach(route => {
    const firstLoad = importTimers[route].firstLoad;
    const secondLoad = importTimers[route].secondLoad;
    
    if (firstLoad && secondLoad) {
      const improvement = ((firstLoad - secondLoad) / firstLoad * 100).toFixed(2);
      console.log(`📈 ${route}: First load ${firstLoad.toFixed(2)}ms → Second load ${secondLoad.toFixed(2)}ms (${improvement}% faster)`);
    } else if (firstLoad) {
      console.log(`📊 ${route}: Only loaded once - ${firstLoad.toFixed(2)}ms${preloadedModules.has(route) ? ' (preloaded)' : ''}`);
    }
  });
  
  // Overall improvement metrics
  const preloadedRoutes = ['orders', 'delivery'];
  const nonPreloadedRoutes = ['fulfill'];
  
  const preloadedAvg = preloadedRoutes
    .map(route => importTimers[route].firstLoad)
    .reduce((sum, time) => sum + time, 0) / preloadedRoutes.length;
    
  const nonPreloadedAvg = nonPreloadedRoutes
    .map(route => importTimers[route].firstLoad)
    .reduce((sum, time) => sum + time, 0) / nonPreloadedRoutes.length;
  
  const improvement = ((nonPreloadedAvg - preloadedAvg) / nonPreloadedAvg * 100).toFixed(2);
  console.log(`\n🚀 Preloading improvement: ${improvement}% faster navigation (${preloadedAvg.toFixed(2)}ms vs ${nonPreloadedAvg.toFixed(2)}ms)`);
}

// Run the test
runTest()
  .then(() => {
    console.log('\n✅ Route preloading performance test completed!');
  })
  .catch(error => {
    console.error('\n❌ Test failed:', error);
  }); 