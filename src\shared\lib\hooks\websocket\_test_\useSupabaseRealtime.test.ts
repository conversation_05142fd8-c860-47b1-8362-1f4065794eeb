import { renderHook } from '@testing-library/react';
import { useSupabaseRealtime } from '../useSupabaseRealtime';
import { supabase } from '../../../../../../supabase/supabase_client/client';

// Mock channel object
const mockOnFn = jest.fn().mockReturnThis();
const mockSubscribeFn = jest.fn().mockImplementation((callback) => {
  if (callback) callback('SUBSCRIBED');
  return mockChannel;
});
const mockChannel = {
  on: mockOnFn,
  subscribe: mockSubscribeFn
};

// Mock Supabase client
jest.mock('../../../../../../supabase/supabase_client/client', () => {
  return {
    supabase: {
      channel: jest.fn(() => mockChannel),
      removeChannel: jest.fn()
    }
  };
});

describe('useSupabaseRealtime', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should create a channel with the correct parameters', () => {
    const mockCallback = jest.fn();
    
    renderHook(() => 
      useSupabaseRealtime('orders', 'INSERT', mockCallback, 'public')
    );

    expect(supabase.channel).toHaveBeenCalledWith('public_orders_INSERT_{}');
  });

  it('should include filter in channel name when provided', () => {
    const mockCallback = jest.fn();
    const filter = { id: 'eq.123' };
    
    renderHook(() => 
      useSupabaseRealtime('orders', 'UPDATE', mockCallback, 'public', filter)
    );

    expect(supabase.channel).toHaveBeenCalledWith('public_orders_UPDATE_{"id":"eq.123"}');
  });

  it('should set up postgres_changes subscription', () => {
    const mockCallback = jest.fn();
    const filter = { id: 'eq.123' };
    
    renderHook(() => 
      useSupabaseRealtime('orders', 'UPDATE', mockCallback, 'public', filter)
    );

    // Verify the on function was called
    expect(mockOnFn).toHaveBeenCalledWith(
      'postgres_changes',
      expect.objectContaining({
        event: 'UPDATE',
        schema: 'public',
        table: 'orders',
        filter: filter
      }),
      expect.any(Function)
    );
    
    // Verify subscribe was called
    expect(mockSubscribeFn).toHaveBeenCalled();
  });

  it('should clean up subscription on unmount', () => {
    const mockCallback = jest.fn();
    
    const { unmount } = renderHook(() => 
      useSupabaseRealtime('orders', 'INSERT', mockCallback)
    );

    unmount();
    expect(supabase.removeChannel).toHaveBeenCalled();
  });

  it('should not log when debug is false', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    const mockCallback = jest.fn();
    
    renderHook(() => 
      useSupabaseRealtime('orders', 'INSERT', mockCallback, 'public', undefined, false)
    );

    expect(consoleSpy).not.toHaveBeenCalled();
    consoleSpy.mockRestore();
  });

  it('should log when debug is true', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    const mockCallback = jest.fn();
    
    renderHook(() => 
      useSupabaseRealtime('orders', 'INSERT', mockCallback, 'public', undefined, true)
    );

    expect(consoleSpy).toHaveBeenCalled();
    consoleSpy.mockRestore();
  });
}); 