import React, { useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '@/app/providers/AuthProvider';
// import KpiCard from '../dashboard/KpiCard'; // Component not yet relocated
import SalesChart from '@/features/report-features/channel-performance/SalesOverTime'; // Component not yet relocated
import TopSellersCard from '@/features/report-features/channel-performance/index'; // Adjusted path
import LowStockAlertsCard from '@/features/inventory-features/inventory-alerts'; // Adjusted path
// Assuming KPI_DATA and MOCK_LOW_STOCK_ALERTS are now part of channel/model.ts or a new product/model.ts
import { DASHBOARD_METRICS } from '@/shared/lib/mock-data/dashboard';
import { MOCK_LOW_STOCK_ALERTS } from '@/entities/channel/model';
import BusinessUnitPerformance from '@/features/report-features/business-unit-performance';
import IntelligenceChart from '@/features/report-features/intelligence-chart';
import DashboardHeader from '@/features/dashboard-header';
import ShrimpDashboard from './sub-dashboard/shrimp';
import DropshipDashboard from './sub-dashboard/dropship';
import StaffDashboard from './sub-dashboard/staff';


const DashboardView: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { profile } = useAuth();

  // Memoize the view change handler to prevent recreation on each render
  const handleViewChange = useMemo(() => {
    return (view: string) => {
      switch (view) {
        case 'all_units':
          navigate('/dashboard-center');
          break;
        case 'shrimp_products':
          navigate('/dashboard-shrimp');
          break;
        case 'dropship_products':
          navigate('/dashboard-dropship');
          break;
        case 'staff_dashboard':
          navigate('/dashboard-staff');
          break;
        default:
          navigate('/dashboard-center');
      }
    };
  }, [navigate]);

  // Memoize the getCurrentView function result to prevent recalculation on each render
  const currentView = useMemo(() => {
    switch (location.pathname) {
      case '/dashboard-center':
        return 'all_units';
      case '/dashboard-shrimp':
        return 'shrimp_products';
      case '/dashboard-dropship':
        return 'dropship_products';
      case '/dashboard-staff':
        return 'staff_dashboard';
      default:
        return 'all_units';
    }
  }, [location.pathname]);

  // Memoize view options to prevent recreation on each render
  const viewOptions = useMemo(() => {
    const baseOptions = [
      { value: "all_units", label: "Command View (All Units)" },
      { value: "shrimp_products", label: "Shrimp Products" },
      { value: "dropship_products", label: "Dropship Products" },
    ];

    // Add staff dashboard for staff users only
    if (profile?.role === 'staff') {
      return [
        { value: "staff_dashboard", label: "Staff Dashboard" },
        ...baseOptions,
      ];
    }

    return baseOptions;
  }, [profile?.role]);

  // Memoize the dashboard content to prevent unnecessary re-renders
  const dashboardContent = useMemo(() => {
    switch (location.pathname) {
      case '/dashboard-shrimp':
        return <ShrimpDashboard />;
      case '/dashboard-dropship':
        return <DropshipDashboard />;
      case '/dashboard-staff':
        return <StaffDashboard />;
      default:
        // Default: All Units Dashboard (dashboard-center)
        return (
          <main className="flex-1 overflow-x-hidden overflow-y-auto p-6 space-y-6">
            {/* Dashboard Metrics Section */}
            <section className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Total Gross Sales */}
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-sm font-medium text-gray-500 mb-2">Total Gross Sales</h3>
                <p className="text-3xl font-bold text-gray-900">{DASHBOARD_METRICS.totalGrossSales}</p>
              </div>
              
              {/* Total Orders */}
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-sm font-medium text-gray-500 mb-2">Total Orders</h3>
                <p className="text-3xl font-bold text-gray-900">{DASHBOARD_METRICS.totalOrders}</p>
              </div>
              
              {/* Avg. Profit Margin */}
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-sm font-medium text-gray-500 mb-2">Avg. Profit Margin</h3>
                <p className="text-3xl font-bold text-green-600">{DASHBOARD_METRICS.avgProfitMargin}</p>
              </div>
              
              {/* Active Business Units */}
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-sm font-medium text-gray-500 mb-2">Active Business Units</h3>
                <p className="text-3xl font-bold text-gray-900">{DASHBOARD_METRICS.activeBusinessUnits}</p>
              </div>
            </section>

            {/* Intelligence Chart Section */}
            <section >
                <IntelligenceChart />
            </section>

            {/* Business Unit Performance Section */}
            <section>
              <BusinessUnitPerformance />
            </section>

            {/* Sales Over Time Section - Original, now contains TopSellersCard */}
            <section className="grid grid-cols-1 lg:grid-cols-3 gap-6 items-stretch">
              <div className="lg:col-span-2">
                <SalesChart />
              </div>
              <div className="lg:col-span-1">
                <TopSellersCard />
              </div>
            </section>

            {/* Low Stock Alerts Section */}
            <section>
              <LowStockAlertsCard alerts={MOCK_LOW_STOCK_ALERTS} />
            </section>

            {/* Placeholder for more content if needed */}
            {/* <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 items-stretch">
            {/* Other sections can be added here */}
          </main>
        );
    }
  }, [location.pathname]);

  return (
    <>
      <DashboardHeader 
        onViewChange={handleViewChange} 
        selectedView={currentView}
        viewOptions={viewOptions}
      />
      {dashboardContent}
    </>
  );
};

export default DashboardView;