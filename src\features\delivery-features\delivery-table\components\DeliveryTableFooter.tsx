import React, { memo } from 'react';
import { Pagination } from '@/shared/ui/navigation/Pagination';

interface DeliveryTableFooterProps {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  itemsCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  onPageChange: (page: number) => void;
  isLoading?: boolean;
}

// Custom comparison function for memoization
const arePropsEqual = (
  prevProps: DeliveryTableFooterProps,
  nextProps: DeliveryTableFooterProps
) => {
  // Only re-render when these essential props change
  return (
    prevProps.currentPage === nextProps.currentPage &&
    prevProps.totalPages === nextProps.totalPages &&
    prevProps.totalCount === nextProps.totalCount &&
    prevProps.itemsCount === nextProps.itemsCount &&
    prevProps.hasNextPage === nextProps.hasNextPage &&
    prevProps.hasPreviousPage === nextProps.hasPreviousPage &&
    prevProps.isLoading === nextProps.isLoading
  );
};

const DeliveryTableFooter: React.FC<DeliveryTableFooterProps> = ({ 
  currentPage,
  totalPages,
  totalCount,
  itemsCount,
  hasNextPage,
  hasPreviousPage,
  onPageChange,
  isLoading = false
}) => {
  // Always show pagination controls if we have data or we're loading and had data before
  const shouldShowPagination = totalCount > 0;
  
  // Calculate display text to avoid jumping during loading
  const displayText = isLoading
    ? `Loading shipments...` 
    : totalCount > 0
      ? `Showing ${itemsCount} of ${totalCount.toLocaleString()} shipments (Page ${currentPage} of ${totalPages})` 
      : "No shipments found";
  
  return (
    <div className="px-6 py-3 border-t border-gray-200 bg-gray-50">
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-700 min-w-[200px]">
          {isLoading ? (
            <span className="inline-flex items-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Loading...
            </span>
          ) : (
            <span>
              {displayText}
            </span>
          )}
        </div>
        
        {/* Always show pagination if there are multiple pages or data is loading */}
        {(shouldShowPagination && (totalPages > 1 || hasNextPage)) && (
          <Pagination 
            currentPage={currentPage}
            totalPages={totalPages}
            hasNextPage={isLoading ? false : hasNextPage}
            hasPreviousPage={isLoading ? false : hasPreviousPage}
            onPageChange={isLoading ? () => {} : onPageChange}
          />
        )}
      </div>
    </div>
  );
};

// Export memoized component to prevent unnecessary re-renders
export default memo(DeliveryTableFooter, arePropsEqual); 