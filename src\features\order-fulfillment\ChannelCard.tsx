import React from 'react';
import { BuyerGroup, FulfillOrderItem, OrderStatus } from '@/types';
import { IndividualOrderItem } from './IndividualOrderItem';
import { CheckIcon } from '@heroicons/react/24/solid';
import { cn } from '@/shared/lib/utils/core/cn';

import { useAuth } from '@/app/providers/AuthProvider';

interface ChannelCardProps {
  buyerGroup: BuyerGroup;
  onStatusChange: (orderId: string, newStatus: OrderStatus, trackingId?: string) => void;
  isUpdating: boolean;
  onDataRefresh: () => void;
  onOpenTrackingModal: (orders: FulfillOrderItem[]) => void;
  selectedOrderIds: Set<string>;
  onToggleSelection: (orderId: string) => void;
}

export const ChannelCard: React.FC<ChannelCardProps> = ({
  buyerGroup,
  onOpenTrackingModal,
  selectedOrderIds,
  onToggleSelection,
}) => {
  const { profile } = useAuth();
  
  const { buyerName, orders } = buyerGroup;

  const allTrackingAssignedForBuyer = orders.every(order => !!order.tracking_number);
  const allReadyToShip = orders.every(order => order.status === 'ready_to_ship');
  const userCanAssignTracking = profile?.role === 'master' || profile?.role === 'admin';

  return (
    <div className={cn(
      "rounded-lg p-4 border shadow-sm relative",
      allReadyToShip 
        ? "bg-green-50 border-green-300" 
        : "bg-white border-gray-200"
    )}>
      {allReadyToShip && (
        <div className="absolute -top-3 -right-2 scale-125">
          <CheckIcon className="h-7 w-7 text-green-600" />
        </div>
      )}
      <div className="flex justify-between items-center mb-4">
        <div className="text-sm font-medium text-gray-900">{buyerName}</div>
        <div className="flex items-center space-x-2">
          {userCanAssignTracking && (
            <button
              onClick={() => !allReadyToShip && onOpenTrackingModal(orders)}
              disabled={allTrackingAssignedForBuyer || allReadyToShip}
              className="px-3 py-1 bg-white border border-gray-300 text-gray-700 text-xs font-medium rounded-md shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {allTrackingAssignedForBuyer || allReadyToShip ? 'Assigned' : 'Assign Tracking'}
            </button>
          )}
        </div>
      </div>

      <div className="space-y-3">
          {orders.map(order => (
            <IndividualOrderItem
              key={order.id}
              order={order}
              hideChannelBadge={true}
              isSelected={selectedOrderIds.has(order.id)}
              onToggleSelection={onToggleSelection}
              isGroupSelectable={allReadyToShip}
            />
          ))}
      </div>
    </div>
  );
}; 