import { useState, useCallback, useMemo, useEffect, useRef } from 'react';
import { InventoryView } from '@/types';
import { inventoryService, InventoryListParams } from '@/shared/lib/services/inventory';
import { inventoryCacheService } from '@/shared/lib/services/cache';

export interface SortConfig {
  column: keyof InventoryView | null;
  direction: 'asc' | 'desc';
}

interface UseInventoryDataReturn {
  products: InventoryView[];
  loading: boolean;
  isInitialLoading: boolean;
  error: Error | null;
  totalCount: number;
  fetchProducts: (filters: InventoryListParams) => Promise<void>;
  selectProduct: (productId: string) => void;
  selectAllProducts: (isSelected: boolean) => void;
  selectedProductIds: Set<string>;
  isAllSelected: boolean;
  isSomeSelected: boolean;
  clearSelection: () => void;
  // Pagination
  currentPage: number;
  totalPages: number;
  pageSize: number;
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  // Sorting
  sortConfig: SortConfig;
  setSortConfig: (config: SortConfig) => void;
  handleSort: (column: keyof InventoryView) => void;
  // Optimistic updates
  updateProductStock: (productId: string, adjustmentType: 'increase' | 'decrease' | 'set', quantity: number) => void;
  // Offline status
  isOffline: boolean;
}

/**
 * Custom hook for managing inventory data
 * Handles fetching, filtering, sorting, pagination and selection states with proper memoization
 * Now with offline persistence support
 */
export function useInventoryData(): UseInventoryDataReturn {
  // State management
  const [products, setProducts] = useState<InventoryView[]>([]);
  const [loading, setLoading] = useState<boolean>(true); // Start with loading true
  const [error, setError] = useState<Error | null>(null);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [selectedProductIds, setSelectedProductIds] = useState<Set<string>>(new Set());
  const [isOffline, setIsOffline] = useState<boolean>(!navigator.onLine);
  // Track if we're using stale data during revalidation
  const [isStaleData, setIsStaleData] = useState<boolean>(false);
  
  // Track initial loading separately from subsequent loading states
  const [isInitialLoading, setIsInitialLoading] = useState<boolean>(true);
  
  // Current filter ref to avoid unnecessary fetches
  const currentFiltersRef = useRef<InventoryListParams>({});
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  
  // Sorting state
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    column: null,
    direction: 'asc'
  });

  // Add online/offline detection
  useEffect(() => {
    const handleOnline = () => setIsOffline(false);
    const handleOffline = () => setIsOffline(true);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Calculate total pages
  const totalPages = useMemo(() => {
    return Math.max(1, Math.ceil(totalCount / pageSize));
  }, [totalCount, pageSize]);

  // Calculate if there's a next or previous page
  const hasNextPage = useMemo(() => {
    return currentPage < totalPages;
  }, [currentPage, totalPages]);

  const hasPreviousPage = useMemo(() => {
    return currentPage > 1;
  }, [currentPage]);

  // Fetch products with memoized callback
  const fetchProducts = useCallback(async (filters: InventoryListParams) => {
    // Update current filters ref for revalidation
    currentFiltersRef.current = { ...filters };
    
    // Always set loading to true for consistent behavior
    setLoading(true);
    setError(null);
    
    try {
      // Add pagination and sorting parameters
      const params: InventoryListParams = {
        ...filters,
        page: currentPage,
        pageSize: pageSize,
      };
      
      // Add sorting if available
      if (sortConfig.column) {
        params.sortBy = sortConfig.column as string;
        params.sortDirection = sortConfig.direction;
      }
      
      // Check cache first
      const cachedData = inventoryCacheService.getCachedInventoryList(params);
      
      if (cachedData) {
        // Use cached data
        setProducts(cachedData.data);
        setTotalCount(cachedData.totalCount);
        
        // If data is stale, revalidate in the background
        if (cachedData.isStale) {
          console.log('Using stale inventory data while revalidating...');
          setIsStaleData(true);
          
          // Background fetch to revalidate
          try {
            const result = await inventoryService.getInventoryList(params);
            // Cache the results for future use
            inventoryCacheService.cacheInventoryList(params, result.data, result.count);
            setIsStaleData(false);
          } catch (err) {
            console.error('Background revalidation failed:', err);
          }
        } else {
          // Data is fresh from cache
          setIsStaleData(false);
        }
        
        // Turn off loading state
        setLoading(false);
        
        // Initial load is complete if it was active
        setIsInitialLoading(false);
        
        return;
      }
      
      // No cached data, fetch from service
      const result = await inventoryService.getInventoryList(params);
      
      // Update state with fetched data
      setProducts(result.data);
      setTotalCount(result.count);
      setIsStaleData(false);
      
      // Cache the results for future use
      inventoryCacheService.cacheInventoryList(params, result.data, result.count);
      
    } catch (err) {
      console.error('Error fetching inventory data:', err);
      const error = err instanceof Error ? err : new Error('Failed to fetch inventory data');
      setError(error);
      // Clear products on error for consistent behavior
      setProducts([]);
      setTotalCount(0);
    } finally {
      // Always turn off loading state
      setLoading(false);
      
      // Initial load is complete regardless of the outcome
      setIsInitialLoading(false);
    }
  }, [currentPage, pageSize, sortConfig]);
  
  // Initial fetch on mount
  useEffect(() => {
    // Use empty filters for initial fetch
    fetchProducts({});
  }, [fetchProducts]);
  
  // Custom setCurrentPage that triggers data fetch with updated page
  const handleSetCurrentPage = useCallback((page: number) => {
    setCurrentPage(page);
    // Immediately fetch with the new page
    const updatedParams = {
      ...currentFiltersRef.current,
      page: page,
      pageSize: pageSize
    };
    
    // Add sorting if available
    if (sortConfig.column) {
      updatedParams.sortBy = sortConfig.column as string;
      updatedParams.sortDirection = sortConfig.direction;
    }
    
    // Fetch directly without relying on the effect
    setLoading(true);
    inventoryService.getInventoryList(updatedParams)
      .then(result => {
        setProducts(result.data);
        setTotalCount(result.count);
        setLoading(false);
      })
      .catch(err => {
        console.error('Error fetching inventory data after page change:', err);
        const error = err instanceof Error ? err : new Error('Failed to fetch inventory data');
        setError(error);
        setLoading(false);
      });
  }, [pageSize, sortConfig]);
  
  // Custom setPageSize that resets to page 1 and fetches
  const handleSetPageSize = useCallback((size: number) => {
    setPageSize(size);
    setCurrentPage(1); // Reset to first page
    
    // Immediately fetch with new page size
    const updatedParams = {
      ...currentFiltersRef.current,
      page: 1,
      pageSize: size
    };
    
    // Add sorting if available
    if (sortConfig.column) {
      updatedParams.sortBy = sortConfig.column as string;
      updatedParams.sortDirection = sortConfig.direction;
    }
    
    // Fetch directly without relying on the effect
    setLoading(true);
    inventoryService.getInventoryList(updatedParams)
      .then(result => {
        setProducts(result.data);
        setTotalCount(result.count);
        setLoading(false);
      })
      .catch(err => {
        console.error('Error fetching inventory data after page size change:', err);
        const error = err instanceof Error ? err : new Error('Failed to fetch inventory data');
        setError(error);
        setLoading(false);
      });
  }, [sortConfig]);
  
  // Handle sorting
  const handleSort = useCallback((column: keyof InventoryView) => {
    setSortConfig(prevConfig => {
      // If clicking the same column, toggle direction
      const newDirection: 'asc' | 'desc' = prevConfig.column === column 
        ? (prevConfig.direction === 'asc' ? 'desc' : 'asc')
        : 'asc';

      const newConfig: SortConfig = {
        column, 
        direction: newDirection
      };
      
      // Immediately fetch with new sort config
      const updatedParams: InventoryListParams = {
        ...currentFiltersRef.current,
        page: currentPage,
        pageSize: pageSize,
        sortBy: column as string,
        sortDirection: newDirection
      };
      
      // Fetch directly without relying on the effect
      setLoading(true);
      inventoryService.getInventoryList(updatedParams)
        .then(result => {
          setProducts(result.data);
          setTotalCount(result.count);
          setLoading(false);
        })
        .catch(err => {
          console.error('Error fetching inventory data after sort change:', err);
          const error = err instanceof Error ? err : new Error('Failed to fetch inventory data');
          setError(error);
          setLoading(false);
        });
      
      return newConfig;
    });
  }, [currentPage, pageSize]);
  
  // Memoized product selection handlers
  const selectProduct = useCallback((productId: string) => {
    setSelectedProductIds(prev => {
      const updated = new Set(prev);
      if (updated.has(productId)) {
        updated.delete(productId);
      } else {
        updated.add(productId);
      }
      return updated;
    });
  }, []);
  
  // Select or deselect all products
  const selectAllProducts = useCallback((isSelected: boolean) => {
    if (isSelected) {
      const allIds = new Set(products
        .filter(product => product.product_id !== null)
        .map(product => product.product_id as string));
      setSelectedProductIds(allIds);
    } else {
      setSelectedProductIds(new Set());
    }
  }, [products]);
  
  // Clear selection
  const clearSelection = useCallback(() => {
    setSelectedProductIds(new Set());
  }, []);
  
  // Memoized derived values
  const isAllSelected = useMemo(() => {
    if (products.length === 0) return false;
    
    // Count only products with valid IDs
    const validProducts = products.filter(product => product.product_id !== null);
    
    if (validProducts.length === 0) return false;
    
    return validProducts.every(product => 
      product.product_id && selectedProductIds.has(product.product_id)
    );
  }, [products, selectedProductIds]);
  
  const isSomeSelected = useMemo(() => {
    return selectedProductIds.size > 0 && !isAllSelected;
  }, [selectedProductIds.size, isAllSelected]);
  
  // Optimistically update product stock in the local state
  const updateProductStock = useCallback((
    productId: string, 
    adjustmentType: 'increase' | 'decrease' | 'set', 
    quantity: number
  ) => {
    setProducts(currentProducts => {
      return currentProducts.map(product => {
        if (product.product_id === productId) {
          // Calculate new stock value based on adjustment type
          let newStock: number;
          
          // Default to 0 if current_stock is null
          const currentStock = product.current_stock ?? 0;
          
          switch (adjustmentType) {
            case 'increase':
              newStock = currentStock + quantity;
              break;
            case 'decrease':
              newStock = currentStock - quantity;
              break;
            case 'set':
              newStock = quantity;
              break;
            default:
              newStock = currentStock;
          }
          
          // Calculate if product needs reordering based on updated stock
          const minimumThreshold = product.minimum_threshold ?? 0;
          const reorderPoint = minimumThreshold > 0 ? minimumThreshold * 1.5 : 5; // Default reorder point if no threshold
          const needsReorder = newStock <= reorderPoint;
          
          // Update available stock (preserving reserved)
          const reservedStock = product.reserved_stock ?? 0;
          const availableStock = Math.max(0, newStock - reservedStock);
          
          return {
            ...product,
            current_stock: newStock,
            available_stock: availableStock,
            needs_reorder: needsReorder
          };
        }
        return product;
      });
    });
    
    // Invalidate the specific product in inventory cache
    inventoryCacheService.invalidateInventoryItem(productId);
  }, []);
  
  return {
    products,
    loading,
    isInitialLoading,
    error,
    totalCount,
    fetchProducts,
    selectProduct,
    selectAllProducts,
    selectedProductIds,
    isAllSelected,
    isSomeSelected,
    clearSelection,
    // Pagination
    currentPage,
    totalPages,
    pageSize,
    setCurrentPage: handleSetCurrentPage,
    setPageSize: handleSetPageSize,
    hasNextPage,
    hasPreviousPage,
    // Sorting
    sortConfig,
    setSortConfig,
    handleSort,
    // Optimistic updates
    updateProductStock,
    // Offline status
    isOffline
  };
} 