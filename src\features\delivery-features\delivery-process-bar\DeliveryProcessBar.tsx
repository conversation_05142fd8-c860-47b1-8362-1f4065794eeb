import React from 'react';
import {
  TruckIcon,
  MapPinIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import {
  CheckCircleIcon as CheckCircleSolid,
  TruckIcon as TruckSolid,
  MapPinIcon as MapPinSolid,
  ClockIcon as ClockSolid,
  ExclamationTriangleIcon as ExclamationTriangleSolid,
  XCircleIcon as XCircleSolid,
  ArrowPathIcon as ArrowPathSolid
} from '@heroicons/react/24/solid';
import { DeliveryStatusEnum } from '@/types';

interface StatusBatch {
  status: DeliveryStatusEnum;
  label: string;
  icon: React.ComponentType<any>;
  iconSolid: React.ComponentType<any>;
  color: string;
  bgColor: string;
  textColor: string;
}

interface StatsData {
  total: number;
  inTransit: number;
  outForDelivery: number;
  delivered: number;
  delayed: number;
  exception: number;
  returned: number;
  lost: number;
  requiresAction: number;
}

interface DeliveryProcessBarProps {
  stats: StatsData | undefined;
  isLoading: boolean;
  onReviewIssues?: () => void;
  className?: string;
}

const statusBatches: StatusBatch[] = [
  {
    status: 'in_transit',
    label: 'In Transit',
    icon: TruckIcon,
    iconSolid: TruckSolid,
    color: 'border-blue-500',
    bgColor: 'bg-blue-50',
    textColor: 'text-blue-700'
  },
  {
    status: 'out_for_delivery',
    label: 'Out for Delivery',
    icon: MapPinIcon,
    iconSolid: MapPinSolid,
    color: 'border-indigo-500',
    bgColor: 'bg-indigo-50',
    textColor: 'text-indigo-700'
  },
  {
    status: 'delayed',
    label: 'Delayed',
    icon: ClockIcon,
    iconSolid: ClockSolid,
    color: 'border-yellow-500',
    bgColor: 'bg-yellow-50',
    textColor: 'text-yellow-700'
  },
  {
    status: 'exception',
    label: 'Exception',
    icon: ExclamationTriangleIcon,
    iconSolid: ExclamationTriangleSolid,
    color: 'border-orange-500',
    bgColor: 'bg-orange-50',
    textColor: 'text-orange-700'
  },
  {
    status: 'lost',
    label: 'Lost',
    icon: XCircleIcon,
    iconSolid: XCircleSolid,
    color: 'border-red-500',
    bgColor: 'bg-red-50',
    textColor: 'text-red-700'
  },
  {
    status: 'returned',
    label: 'Returned',
    icon: ArrowPathIcon,
    iconSolid: ArrowPathSolid,
    color: 'border-purple-500',
    bgColor: 'bg-purple-50',
    textColor: 'text-purple-700'
  },
  {
    status: 'delivered',
    label: 'Delivered',
    icon: CheckCircleIcon,
    iconSolid: CheckCircleSolid,
    color: 'border-green-500',
    bgColor: 'bg-green-50',
    textColor: 'text-green-700'
  }
];

const statusToStatKey: { [key in DeliveryStatusEnum]: keyof StatsData | null } = {
  in_transit: 'inTransit',
  out_for_delivery: 'outForDelivery',
  delayed: 'delayed',
  exception: 'exception',
  lost: 'lost',
  returned: 'returned',
  delivered: 'delivered',
  picked_up: null,
  label_created: null,
};

// Helper function to convert border color to background color
const getProgressBarColor = (borderColor: string): string => {
  const colorMap: Record<string, string> = {
    'border-blue-500': 'bg-blue-500',
    'border-indigo-500': 'bg-indigo-500',
    'border-yellow-500': 'bg-yellow-500',
    'border-orange-500': 'bg-orange-500',
    'border-red-500': 'bg-red-500',
    'border-purple-500': 'bg-purple-500',
    'border-green-500': 'bg-green-500'
  };
  return colorMap[borderColor] || 'bg-gray-500';
};

const DeliveryProcessBar: React.FC<DeliveryProcessBarProps> = ({
  stats,
  isLoading,
  onReviewIssues,
  className = ''
}) => {
  const getStatCount = (status: DeliveryStatusEnum): number => {
    if (!stats) return 0;
    const key = statusToStatKey[status];
    return key ? (stats[key] as number) : 0;
  };

  const totalOrders = stats?.total ?? 0;

  return (
    <div className={`bg-white border-b border-gray-200 ${className}`}>
      <div className="max-w-6xl mx-auto px-6 py-6">
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-2">Batch Progress Overview</h2>
          <p className="text-sm text-gray-600">
            Current delivery status distribution across all shipments
          </p>
        </div>

        {/* Batch Progress Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4 mb-6">
          {isLoading ? (
            Array.from({ length: 7 }).map((_, index) => (
              <div key={index} className="p-4 rounded-lg border-2 border-gray-200 bg-gray-50 animate-pulse">
                <div className="h-8 w-8 bg-gray-200 rounded-md mx-auto mb-3"></div>
                <div className="h-7 bg-gray-200 rounded w-1/2 mx-auto"></div>
                <div className="h-3 bg-gray-200 rounded w-1/4 mx-auto mt-1"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto mt-2"></div>
                <div className="mt-3 w-full bg-gray-200 rounded-full h-1.5"></div>
              </div>
            ))
          ) : (
            statusBatches.map((batch) => {
              const count = getStatCount(batch.status);
              const denominator = totalOrders;
              const percentage = denominator > 0 ? Math.round((count / denominator) * 100) : 0;
              const IconComponent = count > 0 ? batch.iconSolid : batch.icon;

              return (
                <div
                  key={batch.status}
                  className={`
                    relative p-4 rounded-lg border-2 transition-all duration-200 hover:shadow-md
                    ${count > 0 ? `${batch.color} ${batch.bgColor}` : 'border-gray-200 bg-gray-50'}
                  `}
                >
                  {/* Icon */}
                  <div className="flex items-center justify-center mb-3">
                    <IconComponent 
                      className={`h-8 w-8 ${count > 0 ? batch.textColor : 'text-gray-400'}`} 
                    />
                  </div>

                  {/* Count Display */}
                  <div className="text-center">
                    <div className={`text-2xl font-bold ${count > 0 ? batch.textColor : 'text-gray-400'}`}>
                      {count}
                    </div>
                    <div className="text-xs text-gray-600 mt-1">
                      / {totalOrders}
                    </div>
                  </div>

                  {/* Status Label */}
                  <div className={`text-sm font-medium text-center mt-2 ${count > 0 ? batch.textColor : 'text-gray-500'}`}>
                    {batch.label}
                  </div>

                  {/* Percentage Badge */}
                  {count > 0 && (
                    <div className="absolute -top-2 -right-2">
                      <div className={`px-2 py-1 text-xs font-semibold rounded-full ${batch.bgColor} ${batch.textColor} border ${batch.color}`}>
                        {percentage}%
                      </div>
                    </div>
                  )}

                  {/* Progress Bar */}
                  <div className="mt-3 w-full bg-gray-200 rounded-full h-1.5">
                    <div
                      className={`h-1.5 rounded-full transition-all duration-500 ${
                        count > 0 ? getProgressBarColor(batch.color) : 'bg-gray-300'
                      }`}
                      style={{ width: `${Math.min(percentage, 100)}%` }}
                    />
                  </div>
                </div>
              );
            })
          )}
        </div>

        {/* Summary Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center">
              <TruckIcon className="h-5 w-5 text-blue-500 mr-3" />
              <div>
                <div className="text-lg font-semibold text-gray-900">
                  {isLoading ? <span className="h-6 bg-gray-200 rounded w-12 inline-block animate-pulse"></span> : totalOrders}
                </div>
                <div className="text-sm text-gray-600">Active Shipments</div>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center">
              <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3" />
              <div>
                <div className="text-lg font-semibold text-gray-900">
                  {isLoading ? <span className="h-6 bg-gray-200 rounded w-12 inline-block animate-pulse"></span> : getStatCount('delivered')}
                </div>
                <div className="text-sm text-gray-600">Completed Deliveries</div>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center">
              <ExclamationTriangleIcon className="h-5 w-5 text-orange-500 mr-3" />
              <div>
                <div className="text-lg font-semibold text-gray-900">
                  {isLoading ? <span className="h-6 bg-gray-200 rounded w-12 inline-block animate-pulse"></span> : stats?.requiresAction ?? 0}
                </div>
                <div className="text-sm text-gray-600">Require Attention</div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        {getStatCount('exception') > 0 && (
          <div className="mt-6 flex flex-wrap gap-3">
            <button className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
              Update All Tracking
            </button>
            <button className="px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 transition-colors">
              Export Report
            </button>
            <button 
              onClick={onReviewIssues}
              className="px-4 py-2 bg-orange-600 text-white text-sm font-medium rounded-lg hover:bg-orange-700 transition-colors"
            >
              Review Issues ({stats?.requiresAction ?? 0})
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default DeliveryProcessBar; 