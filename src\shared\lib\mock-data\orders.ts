import { Order, DeliveryOrderItem, PlatformKey } from '@/types';

const platforms = [
  { platform: 'Amazon' as PlatformKey, stores: ['Amz-NHU', 'Amz-YASH', 'Amz-Deosai'] },
  { platform: 'Ebay' as PlatformKey, stores: ['ebay-SeamS', 'ebay-Drop', 'ebay-WAS'] },
  { platform: 'Website' as PlatformKey, stores: ['Website-WAS'] },
  { platform: 'Shopify' as PlatformKey, stores: ['Shopify-WAS'] },
  { platform: 'Walmart' as PlatformKey, stores: ['Walmart-SeamS'] },
];

const buyers = [
  { name: '<PERSON>', email: '<EMAIL>', address: '123 Main St, City, State' },
  { name: '<PERSON>', email: '<EMAIL>', address: '456 Oak St, City, State' },
  { name: '<PERSON>', email: '<EMAIL>', address: '789 Pine St, City, State' },
];

const products = [
  { id: '1', productName: 'Product A', quantity: 2, price: 29.99 },
  { id: '2', productName: 'Product B', quantity: 1, price: 49.99 },
  { id: '3', productName: 'Product C', quantity: 3, price: 19.99 },
];

let orderId = 1;
export const mockOrders: Order[] = [];

platforms.forEach(({ platform, stores }) => {
  stores.forEach((store) => {
    for (let i = 0; i < 30; i++) {
      const buyer = buyers[i % buyers.length];
      const product = products[i % products.length];
      mockOrders.push({
        id: String(orderId++),
        orderDate: `2024-03-${(i % 28) + 1}`,
        status: 'Open',
        store,
        platform,
        shippingFeePaid: i % 2 === 0,
        buyer,
        items: [product],
      });
    }
  });
});

export const MOCK_OPEN_ORDERS = mockOrders.filter(order => order.status === 'Open');
export const MOCK_PACKED_ORDERS = mockOrders.filter(order => order.status === 'Packed');

export const MOCK_DELIVERY_ORDERS: DeliveryOrderItem[] = [
  {
    id: 'D1',
    platform: 'Amazon',
    store: 'Amz-NHU',
    sku: 'SKU001',
    product: 'Product X',
    name: 'John Doe',
    date: '2024-03-15',
    trackingId: 'TRK789',
    status: 'In Transit',
    destinationState: 'CA',
    updateDate: '2024-03-16 10:00 AM'
  },
  {
    id: 'D2',
    platform: 'ebay',
    store: 'ebay-SeamS',
    sku: 'SKU002',
    product: 'Product Y',
    name: 'Jane Smith',
    date: '2024-03-14',
    trackingId: 'TRK456',
    status: 'Damaged',
    destinationState: 'NY',
    updateDate: '2024-03-16 09:30 AM'
  },
  {
    id: 'D3',
    platform: 'shopify',
    store: 'Shopify-WAS',
    sku: 'SKU003',
    product: 'Product Z',
    name: 'Bob Wilson',
    date: '2024-03-13',
    trackingId: 'TRK123',
    status: 'Delivered',
    destinationState: 'TX',
    updateDate: '2024-03-15 02:15 PM'
  }
];

export const MOCK_STOCK_DATA = {
  'Product A': {
    available: 150,
    reserved: 60,
    lowStock: 15
  },
  'Product B': {
    available: 80,
    reserved: 30,
    lowStock: 15
  },
  'Product C': {
    available: 200,
    reserved: 40,
    lowStock: 15
  }
};