

import { useState, useEffect, useRef } from 'react';
import type { DropdownOption } from '../../../types'; // Adjusted to be more explicit if index.ts is not automatically resolved
import { ChevronDownIcon } from '../../../shared/config'; // Adjusted path

interface DropdownProps {
  options: DropdownOption[];
  selectedOption: DropdownOption | null;
  onSelect: (option: DropdownOption) => void;
  label?: string;
  className?: string;
  buttonClassName?: string;
  menuClassName?: string;
}

const Dropdown = ({
  options,
  selectedOption,
  onSelect,
  label,
  className = '',
  buttonClassName = 'bg-white border border-gray-300 rounded-md shadow-sm px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500',
  menuClassName = 'origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10'
}: DropdownProps) => {
  const [isOpen, setIsOpenState] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = () => setIsOpenState(!isOpen);

  const handleSelect = (option: DropdownOption) => {
    onSelect(option);
    setIsOpenState(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpenState(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className={`relative inline-block text-left ${className}`} ref={dropdownRef}>
      {label && <span className="text-sm text-gray-500 mr-2">{label}</span>}
      <div>
        <button
          type="button"
          className={`inline-flex justify-between items-center w-full ${buttonClassName}`}
          onClick={toggleDropdown}
          aria-haspopup="true"
          aria-expanded={isOpen}
        >
          {selectedOption ? selectedOption.label : 'Select...'}
          <ChevronDownIcon className="-mr-1 ml-2 h-5 w-5 text-gray-400" aria-hidden="true" />
        </button>
      </div>

      {isOpen && (
        <div
          className={menuClassName}
          role="menu"
          aria-orientation="vertical"
          aria-labelledby="options-menu"
        >
          <div className="py-1" role="none">
            {options.map((option) => (
              <button
                key={option.value}
                onClick={() => handleSelect(option)}
                className="text-gray-700 block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 hover:text-gray-900"
                role="menuitem"
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Dropdown;