import { supabase } from '../../../../supabase/supabase_client/client';
import { UserProfile } from '../../../types';
import { AuthCacheService } from '../services/auth/cache.service';
import { AuthSessionService } from '../services/auth/session.service';
import { AuthProfileService } from '../services/auth/profile.service';

// Configuration flags
const CONFIG = {
  ENABLE_BACKGROUND_VALIDATION: true,
  ENABLE_BACKGROUND_ROLE_REFRESH: false, // Disabled for maximum speed
};

// For throttling auth change events
let lastAuthEventTime = 0;
let lastAuthEventType = '';
let lastAuthUserId = '';

export class AuthController {
  // Initialize authentication system
  static async initialize(): Promise<{
    session: any;
    profile: UserProfile | null;
    isLoading: boolean;
  }> {
    try {
      const result = await AuthSessionService.initializeSession();
      
      // Optional background validation for cached sessions
      if (result.fromCache && CONFIG.ENABLE_BACKGROUND_VALIDATION) {
        this.validateCachedSessionInBackground(result.session);
      }
      
      // Optional background role refresh for cached sessions
      if (result.fromCache && CONFIG.ENABLE_BACKGROUND_ROLE_REFRESH) {
        this.refreshRoleInBackground(result.session.user, result.session);
      }
      
      // Auto-refresh if cache is getting old
      if (result.fromCache && result.needsRefresh) {
        this.autoRefreshInBackground(result.session.user, result.session);
      }
      
      return {
        session: result.session,
        profile: result.profile,
        isLoading: false
      };
    } catch (error) {
      // Auth initialization failed
      return {
        session: null,
        profile: null,
        isLoading: false
      };
    }
  }

  // Handle auth state changes with throttling
  static async handleAuthStateChange(
    event: string,
    session: any,
    onStateChange: (session: any, profile: UserProfile | null) => void
  ) {
    const now = Date.now();
    const userId = session?.user?.id || 'no-user';
    const eventSignature = `${event}-${userId}`;
    const isDuplicate = (
      eventSignature === `${lastAuthEventType}-${lastAuthUserId}` && 
      now - lastAuthEventTime < 2000 // 2 second threshold
    );
    
    if (isDuplicate) {
      return;
    }
    
    // Update tracking variables
    lastAuthEventTime = now;
    lastAuthEventType = event;
    lastAuthUserId = userId;
    
    await AuthSessionService.handleAuthStateChange(event, session, onStateChange);
  }

  // Force refresh authentication data
  static async refreshAuth(): Promise<{
    session: any;
    profile: UserProfile | null;
  }> {
    try {
      // Force refreshing auth data
      AuthCacheService.clearCache();
      
      const { session, error } = await AuthSessionService.getCurrentSession();
      if (error || !session) {
        // Error refreshing session
        return { session: null, profile: null };
      }
      
      // Create basic profile for fast refresh
      const basicProfile = AuthSessionService.createBasicProfile(session.user);
      AuthCacheService.saveToCache(session, basicProfile);
      
      return { session, profile: basicProfile };
    } catch (error) {
      // Error in refreshAuth
      return { session: null, profile: null };
    }
  }

  // Manually fetch user role from database
  static async fetchUserRole(userEmail: string, currentSession: any): Promise<UserProfile | null> {
    if (!userEmail || !currentSession) {
      // Missing user email or session for role fetch
      return null;
    }

    return await AuthProfileService.fetchFreshUserRole(userEmail, currentSession);
  }

  // Subscribe to auth state changes
  static subscribeToAuthChanges(
    onStateChange: (session: any, profile: UserProfile | null) => void
  ) {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event: any, session: any) => {
        await this.handleAuthStateChange(event, session, onStateChange);
      }
    );

    return subscription;
  }

  // Background validation (non-blocking)
  private static async validateCachedSessionInBackground(cachedSession: any) {
    try {
      const isValid = await AuthSessionService.validateCachedSession(cachedSession);
      if (!isValid) {
        // Cached session validation failed, clearing cache
        AuthCacheService.clearCache();
      }
    } catch (error) {
      // Background session validation error
      AuthCacheService.clearCache();
    }
  }

  // Background role refresh (non-blocking)
  private static async refreshRoleInBackground(user: any, currentSession: any) {
    try {
      const updatedProfile = await AuthProfileService.refreshUserRole(user, currentSession);
      if (updatedProfile) {
        // Background role refresh completed
        // Profile is already saved to cache by the service
      }
    } catch (error) {
      // Background role refresh failed
    }
  }

  // Auto-refresh cache in background when it's getting old
  private static async autoRefreshInBackground(user: any, currentSession: any) {
    try {
      // Fetch fresh profile data from database
      const updatedProfile = await AuthProfileService.fetchUserProfile(user, false);
      if (updatedProfile) {
        // Update cache with fresh data
        AuthCacheService.saveToCache(currentSession, updatedProfile);
      }
    } catch (error) {
      // Auto-refresh failed, but don't clear cache since it's still valid
    }
  }

  // Clear all authentication data
  static signOut() {
    AuthCacheService.clearCache();
    return supabase.auth.signOut();
  }

  // Get configuration
  static getConfig() {
    return { ...CONFIG };
  }

  // Update configuration
  static updateConfig(newConfig: Partial<typeof CONFIG>) {
    Object.assign(CONFIG, newConfig);
  }

  // Get cache status (for debugging/monitoring)
  static getCacheStatus() {
    return AuthCacheService.getCacheStatus();
  }

  // Force cache refresh
  static async forceCacheRefresh(): Promise<boolean> {
    try {
      AuthCacheService.expireCache();
      const result = await this.refreshAuth();
      return result.session !== null;
    } catch (error) {
      return false;
    }
  }
} 