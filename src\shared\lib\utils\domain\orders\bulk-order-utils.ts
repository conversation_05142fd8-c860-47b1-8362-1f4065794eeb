/**
 * Utility functions for bulk order processing
 */

/**
 * Generate a consistent order number with prefix and timestamp
 * @param prefix Order number prefix
 * @returns Generated order number
 */
export const generateOrderNumber = (prefix = 'ORD'): string => {
  const timestamp = Date.now().toString().slice(-8);
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}-${timestamp}-${random}`;
};

/**
 * Validate an order number format
 * @param orderNumber Order number to validate
 * @returns true if valid, false if invalid
 */
export const isValidOrderNumber = (orderNumber: string): boolean => {
  // Basic validation - can be expanded as needed
  if (!orderNumber || typeof orderNumber !== 'string' || orderNumber.trim() === '') {
    return false;
  }
  
  // Must be at least 5 characters
  if (orderNumber.trim().length < 5) {
    return false;
  }
  
  return true;
};

/**
 * Check if an order number might be a duplicate
 * @param orderNumber Order number to check
 * @param existingOrders Array of existing order numbers
 * @returns true if potential duplicate, false if not
 */
export const isPotentialDuplicate = (orderNumber: string, existingOrders: string[]): boolean => {
  if (!orderNumber || !existingOrders || !existingOrders.length) {
    return false;
  }
  
  return existingOrders.some(existing => existing.toLowerCase() === orderNumber.toLowerCase());
};

/**
 * Calculate subtotal from order items
 * @param items Order items with quantity and price
 * @returns Calculated subtotal
 */
export const calculateOrderSubtotal = (items: Array<{ quantity: number | string; unit_price?: number }>): number => {
  if (!items || !items.length) {
    return 0;
  }
  
  return items.reduce((total, item) => {
    const quantity = typeof item.quantity === 'string' ? parseInt(item.quantity, 10) || 0 : item.quantity || 0;
    const price = item.unit_price || 0;
    return total + (quantity * price);
  }, 0);
};

/**
 * Format CSV data for export
 * @param data Data to format for CSV export
 * @param includeLineNumbers Whether to include line numbers
 * @returns Formatted data for CSV export
 */
export const formatDataForCsvExport = (data: any[], includeLineNumbers = true): any[] => {
  if (!data || !data.length) {
    return [];
  }
  
  return data.map((item, index) => ({
    ...(includeLineNumbers ? { row: index + 1 } : {}),
    ...item
  }));
};

/**
 * Generate a template for bulk order creation
 * @returns Template object for CSV generation
 */
export const generateBulkOrderTemplate = (): any[] => {
  return [
    {
      order_number: 'ORDER-123',
      platform: 'amazon',
      channel: 'amazon_fba',
      customer_email: '<EMAIL>',
      customer_name: 'Customer Name',
      shipping_street: '123 Main St',
      shipping_city: 'New York',
      shipping_state: 'NY',
      shipping_zip_code: '10001',
      product_sku: 'SKU123',
      quantity: '1',
      subtotal: '100',

    }
  ];
}; 