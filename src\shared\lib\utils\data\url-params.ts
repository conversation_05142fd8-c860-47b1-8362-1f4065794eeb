/**
 * URL parameter utilities for filter persistence with shortened parameter names
 */

import { OrderFilters } from '../../services/order/live-order-service';
import { DeliveryListParams } from '@/types';

// Extended filters interface that includes pagination and search
export interface ExtendedOrderFilters extends OrderFilters {
  page?: number;
  limit?: number;
  search?: string;
  searchTerm?: string; // Add compatibility with mock service naming
  skipCache?: boolean; // Add skipCache flag for WebSocket refreshes
}

// Extended interface for DeliveryListParams
export interface ExtendedDeliveryParams extends DeliveryListParams {
  page?: number;
  limit?: number;
}

// Shortened parameter names to keep URLs clean
const PARAM_MAP = {
  // Core filters
  status: 's',
  channel: 'ch',
  carrier: 'ca',
  dateFrom: 'df',
  dateTo: 'dt',
  search: 'q',
  searchTerm: 'q', // Same mapping for compatibility
  
  // Pagination
  page: 'p',
  limit: 'l',
  
  // Sorting
  sortBy: 'sb',
  sortDirection: 'sd',
  sortOrder: 'so',  // Compatibility with older code
  
  // Flags
  isUrgent: 'u',
  isProblem: 'pr',
  hasNotes: 'n',
  isResent: 'r',
  requiresAction: 'ra'
} as const;

// Reverse mapping for decoding
const REVERSE_PARAM_MAP = Object.fromEntries(
  Object.entries(PARAM_MAP).map(([key, value]) => [value, key])
) as Record<string, keyof typeof PARAM_MAP>;

/**
 * Convert filters object to URL search parameters
 */
export function filtersToUrlParams<T extends Record<string, any>>(filters: T): URLSearchParams {
  const params = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      const paramKey = PARAM_MAP[key as keyof typeof PARAM_MAP];
      if (paramKey) {
        // Handle arrays (for multi-select filters)
        if (Array.isArray(value)) {
          value.forEach(item => params.append(paramKey, String(item)));
        } else {
          params.set(paramKey, String(value));
        }
      }
    }
  });
  
  return params;
}

/**
 * Convert URL search parameters to filters object
 */
export function urlParamsToFilters<T extends Record<string, any>>(searchParams: URLSearchParams, filterType: 'order' | 'delivery' = 'order'): T {
  const filters: Record<string, any> = {};
  
  searchParams.forEach((value, key) => {
    const filterKey = REVERSE_PARAM_MAP[key];
    if (filterKey) {
      // Handle arrays (when multiple values exist for same key)
      const existingValues = searchParams.getAll(key);
      if (existingValues.length > 1) {
        filters[filterKey] = existingValues;
      } else {
        // Convert string values to appropriate types
        let convertedValue: any = value;
        
        // Handle boolean flags
        if (['isUrgent', 'isProblem', 'hasNotes', 'isResent', 'requiresAction'].includes(filterKey)) {
          convertedValue = value === 'true';
        }
        // Handle numbers
        else if (['page', 'limit'].includes(filterKey)) {
          convertedValue = parseInt(value, 10);
        }
        
        // Handle search term compatibility
        if (filterKey === 'search' && filterType === 'order') {
          filters.searchTerm = convertedValue; // Also set searchTerm for compatibility
        }
        
        filters[filterKey] = convertedValue;
      }
    }
  });
  
  return filters as T;
}

/**
 * Update current URL with new filters
 */
export function updateUrlWithFilters<T extends Record<string, any>>(filters: T, replace: boolean = false): void {
  const params = filtersToUrlParams(filters);
  const url = new URL(window.location.href);
  
  // Clear existing filter params
  Object.values(PARAM_MAP).forEach(paramKey => {
    url.searchParams.delete(paramKey);
  });
  
  // Add new filter params
  params.forEach((value, key) => {
    url.searchParams.append(key, value);
  });
  
  // Update browser history
  if (replace) {
    window.history.replaceState({}, '', url.toString());
  } else {
    window.history.pushState({}, '', url.toString());
  }
}

/**
 * Get filters from current URL
 */
export function getFiltersFromUrl<T extends Record<string, any>>(filterType: 'order' | 'delivery' = 'order'): T {
  const searchParams = new URLSearchParams(window.location.search);
  return urlParamsToFilters<T>(searchParams, filterType);
}

/**
 * Clear all filter parameters from URL
 */
export function clearUrlFilters(replace: boolean = true): void {
  const url = new URL(window.location.href);
  
  // Remove all filter params
  Object.values(PARAM_MAP).forEach(paramKey => {
    url.searchParams.delete(paramKey);
  });
  
  // Update browser history
  if (replace) {
    window.history.replaceState({}, '', url.toString());
  } else {
    window.history.pushState({}, '', url.toString());
  }
}

/**
 * Subscribe to URL changes (for back/forward navigation)
 */
export function subscribeToUrlChanges<T extends Record<string, any>>(
  callback: (filters: T) => void, 
  filterType: 'order' | 'delivery' = 'order'
): () => void {
  const handlePopState = () => {
    const filters = getFiltersFromUrl<T>(filterType);
    callback(filters);
  };
  
  window.addEventListener('popstate', handlePopState);
  
  // Return unsubscribe function
  return () => {
    window.removeEventListener('popstate', handlePopState);
  };
} 