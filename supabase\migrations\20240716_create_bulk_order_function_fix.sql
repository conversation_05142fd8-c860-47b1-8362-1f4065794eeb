-- Drop existing functions if they exist
DROP FUNCTION IF EXISTS process_bulk_orders(JSONB);
DROP FUNCTION IF EXISTS create_bulk_order(TEXT, TEXT, TEXT, TEXT, TEXT, JSONB, JSONB);
DROP FUNCTION IF EXISTS bulk_create_order(JSONB);

-- Create PostgreSQL function for bulk order creation
CREATE OR REPLACE FUNCTION create_bulk_order(
  p_order_number TEXT,
  p_customer_email TEXT,
  p_customer_name TEXT,
  p_platform_key TEXT,
  p_channel_code TEXT,
  p_shipping_address JSONB,
  p_order_items JSONB
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_customer_id UUID; 
  v_platform_id UUID;
  v_channel_id UUID;
  v_order_id UUID;
  v_item_count INTEGER := 0;
  v_total_amount DECIMAL(10,2);
  v_item JSONB;
  v_product_id UUID;
  v_sku TEXT;
  v_unit_price DECIMAL(10,2);
  v_subtotal_item DECIMAL(10,2);
  v_quantity INTEGER;
  v_pack_size INTEGER;
BEGIN
  -- Check for duplicate order number
  IF EXISTS (SELECT 1 FROM orders WHERE order_number = p_order_number) THEN
    RETURN json_build_object(
      'success', FALSE,
      'message', 'Order number already exists: ' || p_order_number
    );
  END IF;

  -- Get or create customer
  SELECT id INTO v_customer_id
  FROM customers
  WHERE email = p_customer_email;
  
  IF v_customer_id IS NULL THEN
    -- Create new customer with address information
    INSERT INTO customers (
      email,
      name,
      customer_id,
      total_orders_count,
      total_spent,
      address_street,
      address_city,
      address_state,
      address_zip_code_1,
      address_country
    ) VALUES (
      p_customer_email,
      COALESCE(p_customer_name, 'Unknown'),
      'CUST_' || substring(to_char(extract(epoch from now()) * 1000, '************'), 7),
      1,
      0,
      p_shipping_address->>'street',
      p_shipping_address->>'city',
      p_shipping_address->>'state',
      p_shipping_address->>'zip_code',
      COALESCE(p_shipping_address->>'country', 'US')
    )
    RETURNING id INTO v_customer_id;
  ELSE
    -- Update existing customer's order count
    UPDATE customers
    SET total_orders_count = total_orders_count + 1
    WHERE id = v_customer_id;
  END IF;

  -- Get platform ID
  SELECT id INTO v_platform_id
  FROM platforms
  WHERE key = lower(p_platform_key);
  
  IF v_platform_id IS NULL THEN
    RETURN json_build_object(
      'success', FALSE,
      'message', 'Invalid platform: ' || p_platform_key
    );
  END IF;

  -- Get channel ID
  SELECT id INTO v_channel_id
  FROM channels
  WHERE code = lower(p_channel_code);
  
  IF v_channel_id IS NULL THEN
    RETURN json_build_object(
      'success', FALSE,
      'message', 'Invalid channel: ' || p_channel_code
    );
  END IF;

  -- Calculate item count from JSON directly
  SELECT SUM((item->>'quantity')::INTEGER) INTO v_item_count
  FROM jsonb_array_elements(p_order_items) AS item;

  -- Create order
  INSERT INTO orders (
    order_number,
    customer_id,
    platform_id,
    channel_id,
    status,
    item_count,
    total_amount,
    shipping_street,
    shipping_city,
    shipping_state,
    shipping_zip_code
  ) VALUES (
    p_order_number,
    v_customer_id,
    v_platform_id,
    v_channel_id,
    'open',
    v_item_count,
    0, -- Will be updated after items are inserted
    p_shipping_address->>'street',
    p_shipping_address->>'city',
    p_shipping_address->>'state',
    p_shipping_address->>'zip_code'
  )
  RETURNING id INTO v_order_id;

  -- Process order items
  FOR v_item IN SELECT * FROM jsonb_array_elements(p_order_items)
  LOOP
    -- Find product by identifier
    SELECT 
      pi.product_id,
      p.sku,
      p.unit_price,
      COALESCE(pi.pack_size, 1)
    INTO
      v_product_id,
      v_sku,
      v_unit_price,
      v_pack_size
    FROM 
      product_identifiers pi
      JOIN products p ON pi.product_id = p.id
    WHERE 
      pi.platform_identifier = v_item->>'product_identifier';
    
    IF v_product_id IS NULL THEN
      -- Roll back the transaction and return error
      RAISE EXCEPTION 'Invalid product identifier: %', v_item->>'product_identifier';
    END IF;

    -- Get quantity and subtotal_item
    v_quantity := (v_item->>'quantity')::INTEGER;
    v_subtotal_item := COALESCE((v_item->>'subtotal_item')::DECIMAL(10,2), v_unit_price);
    
    -- Insert order item with total_price calculation (quantity * subtotal_item)
    INSERT INTO order_items (
      order_id,
      product_id,
      sku,
      product_name,
      quantity,
      unit_price,
      subtotal_item,
      pack_size,
      total_price
    ) VALUES (
      v_order_id,
      v_product_id,
      v_sku,
      (SELECT name FROM products WHERE id = v_product_id),
      v_quantity,
      v_unit_price,
      v_subtotal_item,
      v_pack_size,
      v_quantity * v_subtotal_item
    );
  END LOOP;

  -- Update order with final total amount calculated from order_items
  UPDATE orders o
  SET total_amount = (
    SELECT COALESCE(SUM(total_price), 0)
    FROM order_items
    WHERE order_id = v_order_id
  )
  WHERE id = v_order_id
  RETURNING total_amount INTO v_total_amount;

  -- Return success with order details
  RETURN json_build_object(
    'success', TRUE,
    'order_id', v_order_id,
    'order_number', p_order_number,
    'total_amount', v_total_amount,
    'item_count', v_item_count
  );
  
EXCEPTION
  WHEN OTHERS THEN
    -- Return the error
    RETURN json_build_object(
      'success', FALSE,
      'message', SQLERRM
    );
END;
$$;

-- Function to process multiple orders at once
CREATE OR REPLACE FUNCTION process_bulk_orders(p_orders JSONB)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_order JSONB;
  v_result JSONB;
  v_successful JSONB := '[]'::JSONB;
  v_failed JSONB := '[]'::JSONB;
  v_shipping_address JSONB;
  v_response JSON;
BEGIN
  -- Process each order in the array
  FOR v_order IN SELECT * FROM jsonb_array_elements(p_orders)
  LOOP
    BEGIN
      -- Create shipping address object
      v_shipping_address := jsonb_build_object(
        'street', COALESCE(v_order->>'shipping_street', ''),
        'city', COALESCE(v_order->>'shipping_city', ''),
        'state', COALESCE(v_order->>'shipping_state', ''),
        'zip_code', COALESCE(v_order->>'shipping_zip_code', '')
      );

      -- Call the create_bulk_order function for this order
      v_response := create_bulk_order(
        v_order->>'order_number',
        v_order->>'customer_email',
        v_order->>'customer_name',
        v_order->>'platform',
        v_order->>'channel',
        v_shipping_address,
        v_order->'order_items'
      );

      -- Check if the order was created successfully
      IF (v_response->>'success')::BOOLEAN THEN
        -- Add to successful orders
        v_successful := v_successful || jsonb_build_object(
          'order_id', v_response->>'order_id',
          'order_number', v_response->>'order_number'
        );
      ELSE
        -- Add to failed orders
        v_failed := v_failed || jsonb_build_object(
          'row', COALESCE((v_order->'_row_indices'->0)::TEXT::INTEGER + 2, -1),
          'reason', v_response->>'message',
          'data', jsonb_build_object(
            'order_number', v_order->>'order_number',
            'platform', v_order->>'platform',
            'channel', v_order->>'channel',
            'customer_email', v_order->>'customer_email'
          )
        );
      END IF;

    EXCEPTION WHEN OTHERS THEN
      -- Add to failed orders
      v_failed := v_failed || jsonb_build_object(
        'row', COALESCE((v_order->'_row_indices'->0)::TEXT::INTEGER + 2, -1),
        'reason', SQLERRM,
        'data', jsonb_build_object(
          'order_number', v_order->>'order_number'
        )
      );
    END;
  END LOOP;

  -- Return the final result
  RETURN jsonb_build_object(
    'successful', v_successful,
    'failed', v_failed
  );
END;
$$;

-- -- Create a function with the name the Edge Function is expecting
-- CREATE OR REPLACE FUNCTION bulk_create_order(p_orders JSONB)
-- RETURNS JSONB
-- LANGUAGE plpgsql
-- SECURITY DEFINER
-- SET search_path = public
-- AS $$
-- BEGIN
--   -- This function simply calls our process_bulk_orders function
--   RETURN process_bulk_orders(p_orders);
-- END;
-- $$; 