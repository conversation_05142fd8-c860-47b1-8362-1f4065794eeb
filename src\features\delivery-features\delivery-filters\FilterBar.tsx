import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { MagnifyingGlassIcon, ArrowsUpDownIcon } from '@heroicons/react/24/outline';
import Dropdown from '@/shared/ui/input/Dropdown';
import DateRangePicker from '@/shared/ui/input/DateRangePicker';
import SortDropdown, { SortOption } from '@/shared/ui/input/SortDropdown';
import { DeliveryStatusEnum, DeliveryListParams } from '@/types';
import { useDebounce } from 'use-debounce';

interface FilterBarProps {
  initialFilters: Omit<DeliveryListParams, 'page' | 'limit'>;
  onFilterChange: (newFilters: Partial<Omit<DeliveryListParams, 'page' | 'limit'>>) => void;
}

const statusValues: DeliveryStatusEnum[] = [
  'picked_up',
  'label_created',
  'in_transit',
  'out_for_delivery',
  'delivered',
  'delayed',
  'exception',
  'lost',
  'returned',
];

const statusOptions = statusValues.map(s => ({ value: s, label: s.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) }));
const carrierOptions = [
    { value: 'ups', label: 'UPS' },
    { value: 'fedex', label: 'FedEx' },
    { value: 'usps', label: 'USPS' },
    { value: 'dhl', label: 'DHL' }
];
const sortOptions: SortOption[] = [
  { value: 'none', label: 'No Sorting' },
  { value: 'ship_date,desc', label: 'Ship Date: Newest' },
  { value: 'ship_date,asc', label: 'Ship Date: Oldest' },
  { value: 'estimated_delivery,desc', label: 'Est. Delivery: Newest' },
  { value: 'estimated_delivery,asc', label: 'Est. Delivery: Oldest' },
  { value: 'last_update,desc', label: 'Last Update: Newest' },
  { value: 'last_update,asc', label: 'Last Update: Oldest' },
];

const FilterBar: React.FC<FilterBarProps> = ({ initialFilters, onFilterChange }) => {
  const [internalFilters, setInternalFilters] = useState(initialFilters);
  const [debouncedFilters] = useDebounce(internalFilters, 500);

  useEffect(() => {
    onFilterChange(debouncedFilters);
  }, [debouncedFilters, onFilterChange]);

  // Memoized handlers to prevent child re-renders
  const handleUpdate = useCallback((key: keyof typeof internalFilters, value: any) => {
    setInternalFilters(prev => ({ ...prev, [key]: value }));
  }, []);

  const handleSortChange = useCallback((sortValue: string) => {
    if (sortValue === 'none') {
      // Clear sorting parameters
      handleUpdate('sortBy', undefined);
      handleUpdate('sortDirection', undefined);
    } else {
      const [sortBy, sortDirection] = sortValue.split(',');
      handleUpdate('sortBy', sortBy);
      handleUpdate('sortDirection', sortDirection as 'asc' | 'desc');
    }
  }, [handleUpdate]);

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    handleUpdate('search', e.target.value);
  }, [handleUpdate]);

  const handleStatusChange = useCallback((values: string[]) => {
    handleUpdate('status', values);
  }, [handleUpdate]);

  const handleCarrierChange = useCallback((values: string[]) => {
    handleUpdate('carrier', values);
  }, [handleUpdate]);

  const handleDateChange = useCallback(({ start, end }: { start: string | null; end: string | null }) => {
    handleUpdate('dateFrom', start);
    handleUpdate('dateTo', end);
  }, [handleUpdate]);

  // Memoized current sort value
  const currentSort = useMemo(() => {
    if (!internalFilters.sortBy && !internalFilters.sortDirection) {
      return 'none';
    }
    return `${internalFilters.sortBy || 'ship_date'},${internalFilters.sortDirection || 'desc'}`;
  }, [internalFilters.sortBy, internalFilters.sortDirection]);

  // Memoized date range value
  const dateRangeValue = useMemo(() => ({
    start: internalFilters.dateFrom || null,
    end: internalFilters.dateTo || null
  }), [internalFilters.dateFrom, internalFilters.dateTo]);

  return (
    <div className="bg-white border-b border-gray-200 p-4">
      <div className="space-y-3">
        {/* Single Clean Horizontal Line */}
        <div className="flex items-center gap-4">
          {/* Search Input - Large */}
          <div className="flex items-center flex-1 max-w-2xl">
            <div className="relative flex-1">
              <input
                type="text"
                placeholder="Search orders, tracking numbers, customers..."
                value={internalFilters.search || ''}
                onChange={handleSearchChange}
                className="block w-full px-4 py-2 border border-gray-300 rounded-l-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <button className="px-3 py-2 bg-gray-50 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-100">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </button>
          </div>

          {/* Filter Dropdowns - Compact */}
          <div className="flex items-center gap-3">
            {/* Status Filter */}
            <Dropdown
              options={statusOptions}
              selectedValues={internalFilters.status || []}
              onSelectionChange={handleStatusChange}
              placeholder="Package status"
              isMultiSelect={true}
            />

            {/* Carrier Filter */}
            <Dropdown
              options={carrierOptions}
              selectedValues={internalFilters.carrier || []}
              onSelectionChange={handleCarrierChange}
              placeholder="Carrier"
              isMultiSelect={true}
            />

            {/* Date Range Picker */}
            <DateRangePicker
              value={dateRangeValue}
              onConfirm={handleDateChange}
              placeholder="Date"
            />

            {/* Sort Dropdown */}
            <SortDropdown
              sortOptions={sortOptions}
              currentSort={currentSort}
              onSortChange={handleSortChange}
              icon={<ArrowsUpDownIcon className="h-5 w-5 text-gray-400" />}
            />
          </div>
        </div>


      </div>
    </div>
  );
};

export default memo(FilterBar); 