/**
 * Database configuration and field mappings
 * Centralized place to manage database-frontend synchronization
 */

// Database table names
export const DB_TABLES = {
  ORDERS_VIEW: 'orders_view',
  CUSTOMERS: 'customers',
  ORDER_ITEMS: 'order_items',
  ORDER_NOTES: 'order_notes',
  PRODUCTS: 'products'
} as const;

// Database field mappings for orders_view
export const ORDER_FIELDS = {
  // Primary fields
  ID: 'id',
  ORDER_NUMBER: 'order_number',
  ORDER_DATE: 'order_date',
  CUSTOMER_ID: 'customer_id',
  
  // Platform and channel
  PLATFORM_KEY: 'platform_key',
  PLATFORM: 'platform',
  CHANNEL_CODE: 'channel_code',
  CHANNEL: 'channel',
  
  // Status and flags
  STATUS: 'status',
  IS_URGENT: 'is_urgent',
  IS_PROBLEM: 'is_problem',
  IS_RESENT: 'is_resent',
  HAS_NOTES: 'has_notes',
  
  // Financial fields
  TOTAL_AMOUNT: 'total_amount',
  SUBTOTAL: 'subtotal',
  TAX: 'tax',
  TAX_AMOUNT: 'tax_amount',
  SHIPPING: 'shipping',
  SHIPPING_COST: 'shipping_cost',
  
  // Item info
  ITEM_COUNT: 'item_count',
  
  // Shipping info
  SHIPPING_METHOD: 'shipping_method',
  TRACKING_NUMBER: 'tracking_number',
  EXPECTED_DELIVERY: 'expected_delivery',
  SHIPPING_ADDRESS: 'shipping_address',
  SHIPPING_CITY: 'shipping_city',
  SHIPPING_STATE: 'shipping_state',
  SHIPPING_ZIP_CODE: 'shipping_zip_code',
  SHIPPING_COUNTRY: 'shipping_country',
  
  // Customer fallback fields (if available in orders_view)
  CUSTOMER_NAME: 'customer_name',
  CUSTOMER_EMAIL: 'customer_email',
  
  // Timestamps
  CREATED_AT: 'created_at',
  UPDATED_AT: 'updated_at'
} as const;

// Database field mappings for customers table
export const CUSTOMER_FIELDS = {
  ID: 'id',
  CUSTOMER_ID: 'customer_id',
  EMAIL: 'email',
  NAME: 'name',
  PHONE: 'phone',
  ADDRESS_STREET: 'address_street',
  ADDRESS_CITY: 'address_city',
  ADDRESS_ZIP_CODE: 'address_zip_code',
  ADDRESS_STATE: 'address_state',
  ADDRESS_COUNTRY: 'address_country',
  NOTES: 'notes',
  TOTAL_ORDERS_COUNT: 'total_orders_count',
  TOTAL_SPENT: 'total_spent',
  FIRST_ORDER_DATE: 'first_order_date',
  LAST_ORDER_DATE: 'last_order_date',
  CREATED_AT: 'created_at',
  UPDATED_AT: 'updated_at'
} as const;

// Database field mappings for order_items table
export const ORDER_ITEM_FIELDS = {
  ID: 'id',
  ORDER_ID: 'order_id',
  PRODUCT_ID: 'product_id',
  SKU: 'sku',
  PRODUCT_NAME: 'product_name',
  QUANTITY: 'quantity',
  UNIT_COST_OI: 'unit_cost_oi',
  SUBTOTAL_ITEM: 'subtotal_item',
  PACK_SIZE: 'pack_size',
  TOTAL_PRICE: 'total_price',
  CREATED_AT: 'created_at',
  UPDATED_AT: 'updated_at'
} as const;

// Database field mappings for order_notes table (for future use)
export const ORDER_NOTE_FIELDS = {
  ID: 'id',
  ORDER_ID: 'order_id',
  CONTENT: 'content',
  AUTHOR_NAME: 'author_name',
  NOTE_TYPE: 'note_type',
  CREATED_AT: 'created_at',
  UPDATED_AT: 'updated_at'
} as const;

// Select field configurations for queries
export const SELECT_CONFIGS = {
  ORDER_BASIC: '*',
  
  CUSTOMER_BASIC: [
    CUSTOMER_FIELDS.CUSTOMER_ID,
    CUSTOMER_FIELDS.EMAIL,
    CUSTOMER_FIELDS.NAME,
    CUSTOMER_FIELDS.PHONE,
    CUSTOMER_FIELDS.ADDRESS_STREET,
    CUSTOMER_FIELDS.ADDRESS_CITY,
    CUSTOMER_FIELDS.ADDRESS_ZIP_CODE,
    CUSTOMER_FIELDS.ADDRESS_STATE,
    CUSTOMER_FIELDS.ADDRESS_COUNTRY,
    CUSTOMER_FIELDS.NOTES
  ].join(', '),
  
  ORDER_ITEMS_BASIC: [
    ORDER_ITEM_FIELDS.ID,
    ORDER_ITEM_FIELDS.SKU,
    ORDER_ITEM_FIELDS.PRODUCT_NAME,
    ORDER_ITEM_FIELDS.QUANTITY,
    ORDER_ITEM_FIELDS.SUBTOTAL_ITEM,
    ORDER_ITEM_FIELDS.TOTAL_PRICE,
    ORDER_ITEM_FIELDS.PACK_SIZE
  ].join(', '),
  
  ORDER_NOTES_BASIC: [
    ORDER_NOTE_FIELDS.ID,
    ORDER_NOTE_FIELDS.CONTENT,
    ORDER_NOTE_FIELDS.AUTHOR_NAME,
    ORDER_NOTE_FIELDS.CREATED_AT,
    ORDER_NOTE_FIELDS.NOTE_TYPE
  ].join(', ')
} as const;

// Default values for missing data
export const DEFAULT_VALUES = {
  COUNTRY: 'US',
  SHIPPING_METHOD: 'Standard Shipping',
  CUSTOMER_NAME: 'Unknown Customer',
  CUSTOMER_EMAIL: '',
  AUTHOR: 'System',
  NOTE_TYPE: 'internal'
} as const; 