import React, { memo, useCallback } from 'react';
import { AllOrdersDetail } from '@/types';

interface OrderActionsProps {
  order: AllOrdersDetail;
  onResendConfirmation: () => void;
  onAddNote: () => void;
  onProcessRefund: () => void;
  onCancelOrder: () => void;
  onGoToFulfill: (orderId: string) => void;
  onClose: () => void;
}

const OrderActions: React.FC<OrderActionsProps> = ({
  order,
  onResendConfirmation,
  onAddNote,
  onProcessRefund,
  onCancelOrder,
  onGoToFulfill,
  onClose
}) => {
  // PERFORMANCE: Memoize fulfill handler to prevent recreation
  const handleGoToFulfill = useCallback(() => {
    onGoToFulfill(order.id);
  }, [order.id, onGoToFulfill]);

  return (
    <div className="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200">
      {/* Action Buttons */}
      <div className="p-4 border-b border-gray-200">
        <h4 className="text-lg font-medium text-gray-900 mb-3">Order Actions</h4>
        <div className="grid grid-cols-2 gap-3">
          <button
            onClick={onResendConfirmation}
            className="group relative flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg shadow-md hover:bg-blue-700 hover:shadow-lg hover:shadow-blue-500/25 hover:-translate-y-0.5 transform transition-all duration-200 ease-out disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:translate-y-0 disabled:hover:shadow-md overflow-hidden"
            disabled={order.status === 'refund'}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
            <div className="absolute inset-0 border-2 border-blue-300 rounded-lg opacity-0 group-hover:opacity-100 group-hover:animate-pulse"></div>
            <svg className="w-4 h-4 mr-2 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <span className="relative z-10">Resend Confirmation</span>
          </button>
          
          <button
            onClick={onAddNote}
            className="group relative flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg shadow-md hover:bg-green-700 hover:shadow-lg hover:shadow-green-500/25 hover:-translate-y-0.5 transform transition-all duration-200 ease-out overflow-hidden"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-green-600 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
            <div className="absolute inset-0 border-2 border-green-300 rounded-lg opacity-0 group-hover:opacity-100 group-hover:animate-pulse"></div>
            <svg className="w-4 h-4 mr-2 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            <span className="relative z-10">Add Note</span>
          </button>
          
          <button
            onClick={onProcessRefund}
            className="group relative flex items-center justify-center px-4 py-2 bg-orange-600 text-white rounded-lg shadow-md hover:bg-orange-700 hover:shadow-lg hover:shadow-orange-500/25 hover:-translate-y-0.5 transform transition-all duration-200 ease-out disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:translate-y-0 disabled:hover:shadow-md overflow-hidden"
            disabled={order.status === 'refund' || order.status === 'open'}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-orange-400 to-orange-600 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
            <div className="absolute inset-0 border-2 border-orange-300 rounded-lg opacity-0 group-hover:opacity-100 group-hover:animate-pulse"></div>
            <svg className="w-4 h-4 mr-2 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2zM10 8.5a.5.5 0 11-1 0 .5.5 0 011 0zm5 5a.5.5 0 11-1 0 .5.5 0 011 0z" />
            </svg>
            <span className="relative z-10">Process Refund</span>
          </button>
          
          <button
            onClick={onCancelOrder}
            className="group relative flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-lg shadow-md hover:bg-red-700 hover:shadow-lg hover:shadow-red-500/25 hover:-translate-y-0.5 transform transition-all duration-200 ease-out disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:translate-y-0 disabled:hover:shadow-md overflow-hidden"
            disabled={order.status === 'refund' || order.status === 'completed'}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-red-400 to-red-600 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
            <div className="absolute inset-0 border-2 border-red-300 rounded-lg opacity-0 group-hover:opacity-100 group-hover:animate-pulse"></div>
            <svg className="w-4 h-4 mr-2 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
            <span className="relative z-10">Cancel Order</span>
          </button>
        </div>
      </div>
      
      {/* Navigation Actions */}
      <div className="p-4">
        <div className="flex space-x-3">
          {order.status === 'open' && (
            <button
              onClick={handleGoToFulfill}
              className="group relative flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-md hover:bg-blue-700 hover:shadow-lg hover:shadow-blue-500/25 hover:-translate-y-0.5 transform transition-all duration-200 ease-out overflow-hidden"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
              <div className="absolute inset-0 border-2 border-blue-300 rounded-lg opacity-0 group-hover:opacity-100 group-hover:animate-pulse"></div>
              <span className="relative z-10">Go to Fulfill Page</span>
            </button>
          )}
          <button
            onClick={onClose}
            className={`group relative px-4 py-2 border-2 border-gray-300 text-gray-700 rounded-lg shadow-md hover:border-gray-400 hover:bg-gray-50 hover:shadow-lg hover:shadow-gray-500/10 hover:-translate-y-0.5 transform transition-all duration-200 ease-out overflow-hidden ${
              order.status === 'open' ? '' : 'flex-1'
            }`}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-gray-50 to-gray-100 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
            <div className="absolute inset-0 border-2 border-gray-400 rounded-lg opacity-0 group-hover:opacity-60 group-hover:animate-pulse"></div>
            <span className="relative z-10">Close</span>
          </button>
        </div>
      </div>
    </div>
  );
};

// PERFORMANCE: Minimal re-renders by comparing order status and handlers
export default memo(OrderActions, (prevProps, nextProps) => (
  prevProps.order.id === nextProps.order.id &&
  prevProps.order.status === nextProps.order.status &&
  prevProps.onResendConfirmation === nextProps.onResendConfirmation &&
  prevProps.onAddNote === nextProps.onAddNote &&
  prevProps.onProcessRefund === nextProps.onProcessRefund &&
  prevProps.onCancelOrder === nextProps.onCancelOrder &&
  prevProps.onGoToFulfill === nextProps.onGoToFulfill &&
  prevProps.onClose === nextProps.onClose
)); 