import React, { useState, useEffect, useRef, useCallback, useMemo, memo } from 'react';
import type { DropdownOption } from '@/types/index';
import { ChevronDownIcon } from '@/shared/config';

interface DropdownProps {
  options: DropdownOption[];
  selectedOption?: DropdownOption | null;
  onSelect?: (option: DropdownOption) => void;
  // Multi-select props
  selectedValues?: string[];
  onSelectionChange?: (values: string[]) => void;
  isMultiSelect?: boolean;
  placeholder?: string;
  label?: string;
  className?: string;
  buttonClassName?: string;
  menuClassName?: string;
}

// Custom comparison function for React.memo
const arePropsEqual = (prevProps: DropdownProps, nextProps: DropdownProps): boolean => {
  // Quick reference checks for functions
  if (prevProps.onSelect !== nextProps.onSelect || 
      prevProps.onSelectionChange !== nextProps.onSelectionChange) {
    return false;
  }

  // Compare primitive values
  if (prevProps.isMultiSelect !== nextProps.isMultiSelect ||
      prevProps.placeholder !== nextProps.placeholder ||
      prevProps.label !== nextProps.label ||
      prevProps.className !== nextProps.className ||
      prevProps.buttonClassName !== nextProps.buttonClassName ||
      prevProps.menuClassName !== nextProps.menuClassName) {
    return false;
  }

  // Compare selectedOption
  if (prevProps.selectedOption?.value !== nextProps.selectedOption?.value) {
    return false;
  }

  // Compare selectedValues array
  if (prevProps.selectedValues?.length !== nextProps.selectedValues?.length ||
      !prevProps.selectedValues?.every((val, index) => val === nextProps.selectedValues?.[index])) {
    return false;
  }

  // Compare options array (shallow comparison)
  if (prevProps.options.length !== nextProps.options.length ||
      !prevProps.options.every((option, index) => 
        option.value === nextProps.options[index]?.value &&
        option.label === nextProps.options[index]?.label
      )) {
    return false;
  }

  return true;
};

const Dropdown = ({
  options,
  selectedOption,
  onSelect,
  selectedValues = [],
  onSelectionChange,
  isMultiSelect = false,
  placeholder = 'Select...',
  label,
  className = '',
  buttonClassName = 'bg-white border border-gray-300 rounded-md shadow-sm px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500',
  menuClassName = 'origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10'
}: DropdownProps) => {
  const [isOpen, setIsOpenState] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Memoized toggle function
  const toggleDropdown = useCallback(() => setIsOpenState(!isOpen), [isOpen]);

  // Memoized select handler
  const handleSelect = useCallback((option: DropdownOption) => {
    if (isMultiSelect && onSelectionChange) {
      const newValues = selectedValues.includes(option.value)
        ? selectedValues.filter(v => v !== option.value)
        : [...selectedValues, option.value];
      onSelectionChange(newValues);
    } else if (onSelect) {
      onSelect(option);
      setIsOpenState(false);
    }
  }, [isMultiSelect, onSelectionChange, selectedValues, onSelect]);

  // Memoized click outside handler
  const handleClickOutside = useCallback((event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpenState(false);
      }
  }, []);

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [handleClickOutside]);

  // Memoized button text
  const buttonText = useMemo(() => {
    if (isMultiSelect) {
      return selectedValues.length > 0 
        ? `${selectedValues.length} selected`
        : placeholder;
    } else {
      return selectedOption ? selectedOption.label : placeholder;
    }
  }, [isMultiSelect, selectedValues.length, placeholder, selectedOption]);

  return (
    <div className={`relative inline-block text-left ${className}`} ref={dropdownRef}>
      {label && <span className="text-sm text-gray-500 mr-2">{label}</span>}
      <div>
        <button
          type="button"
          className={`inline-flex justify-between items-center w-full ${buttonClassName}`}
          onClick={toggleDropdown}
          aria-haspopup="true"
          aria-expanded={isOpen}
        >
          {buttonText}
          <ChevronDownIcon className="-mr-1 ml-2 h-5 w-5 text-gray-400" aria-hidden="true" />
        </button>
      </div>

      {isOpen && (
        <div
          className={menuClassName}
          role="menu"
          aria-orientation="vertical"
          aria-labelledby="options-menu"
        >
          <div className="py-1" role="none">
            {options.map((option) => (
              <DropdownOptionItem
                key={option.value}
                option={option}
                isMultiSelect={isMultiSelect}
                isSelected={selectedValues.includes(option.value)}
                onSelect={handleSelect}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Individual Option Component with memoization
interface DropdownOptionItemProps {
  option: DropdownOption;
  isMultiSelect: boolean;
  isSelected: boolean;
  onSelect: (option: DropdownOption) => void;
}

const DropdownOptionItem: React.FC<DropdownOptionItemProps> = memo(({ 
  option, 
  isMultiSelect, 
  isSelected, 
  onSelect 
}) => {
  const handleClick = useCallback(() => {
    onSelect(option);
  }, [onSelect, option]);

  return (
    <button
      onClick={handleClick}
      className={`
        text-gray-700 block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 hover:text-gray-900
        ${isMultiSelect && isSelected ? 'bg-blue-50 text-blue-600' : ''}
      `}
      role="menuitem"
    >
      {isMultiSelect && (
        <input
          type="checkbox"
          checked={isSelected}
          readOnly
          className="mr-2 w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
        />
      )}
      {option.label}
    </button>
  );
});

DropdownOptionItem.displayName = 'DropdownOptionItem';

export default memo(Dropdown, arePropsEqual);