// @ts-nocheck
// deno-lint-ignore-file

import { serve } from 'std/http/server.ts'
import { createClient } from 'supabase'
import { corsHeaders } from '../_shared/cors.ts'
import type { ProductIdentifierCreateParams } from '../../../src/shared/api/inventory/productAPI-types'

interface RequestBody {
  items: ProductIdentifierCreateParams[];
  operation: 'create' | 'update' | 'upsert';
  userId?: string;
}

// Helper to safely get error messages
type ErrorWithMessage = { message: string };
function isErrorWithMessage(error: unknown): error is ErrorWithMessage {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof (error as Record<string, unknown>).message === 'string'
  );
}

function getErrorMessage(error: unknown): string {
  if (isErrorWithMessage(error)) return error.message;
  return String(error);
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { items, operation, userId } = await req.json() as RequestBody
    
    // Validate input
    if (!items || !items.length) {
      return new Response(
        JSON.stringify({ success: false, error: 'No product identifiers provided' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
    
    if (!operation || !['create', 'update', 'upsert'].includes(operation)) {
      return new Response(
        JSON.stringify({ success: false, error: 'Invalid operation. Must be create, update, or upsert' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create Supabase client
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing Authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
    
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      { global: { headers: { Authorization: authHeader } } }
    )
    
    let result;
    const timestamp = new Date().toISOString();
    
    // Process based on operation
    switch (operation) {
      case 'create':
        // Add creation timestamp to items
        const itemsWithTimestamp = items.map(item => ({
          ...item,
          created_at: timestamp,
          created_by: userId || null
        }));
        
        const { data: createdData, error: createError } = await supabaseClient
          .from('product_identifiers')
          .insert(itemsWithTimestamp)
          .select();
          
        if (createError) throw createError;
        result = createdData;
        break;
        
      case 'update':
        // Process updates one by one to handle potential errors
        const updateResults = [];
        const updateErrors = [];
        
        for (const item of items) {
          if (!item.id) {
            updateErrors.push({ item, error: 'Missing ID for update operation' });
            continue;
          }
          
          const { data: updatedItem, error: updateError } = await supabaseClient
            .from('product_identifiers')
            .update({
              ...item,
              updated_at: timestamp,
              updated_by: userId || null
            })
            .eq('id', item.id)
            .select()
            .single();
            
          if (updateError) {
            updateErrors.push({ item, error: updateError.message });
          } else {
            updateResults.push(updatedItem);
          }
        }
        
        result = updateResults;
        break;
        
      case 'upsert':
        // Add timestamps to items
        const itemsForUpsert = items.map(item => ({
          ...item,
          created_at: timestamp,
          updated_at: timestamp,
          created_by: userId || null,
          updated_by: userId || null
        }));
        
        const { data: upsertedData, error: upsertError } = await supabaseClient
          .from('product_identifiers')
          .upsert(itemsForUpsert, { 
            onConflict: 'product_id,platform_id',
            ignoreDuplicates: false
          })
          .select();
          
        if (upsertError) throw upsertError;
        result = upsertedData;
        break;
    }
    
    return new Response(
      JSON.stringify({
        success: true,
        data: result,
        count: result?.length || 0,
        totalCount: items.length
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (err) {
    const errorMessage = getErrorMessage(err)
    console.error(`Error processing bulk product identifiers (${operation}):`, errorMessage)
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: errorMessage
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
}) 