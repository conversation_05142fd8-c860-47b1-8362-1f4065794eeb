import { renderHook, act, waitFor } from '@testing-library/react';
import { useInventoryData } from '../useInventoryData';
import { inventoryService } from '@/shared/lib/services/inventory';
import { inventoryCacheService } from '@/shared/lib/services/cache';
import { InventoryView } from '@/types';

// Mock the inventory service and cache service
jest.mock('@/shared/lib/services/inventory', () => ({
  inventoryService: {
    getInventoryList: jest.fn()
  },
  InventoryListParams: {}
}));

jest.mock('@/shared/lib/services/cache', () => ({
  inventoryCacheService: {
    getCachedInventoryList: jest.fn(),
    cacheInventoryList: jest.fn(),
    invalidateInventoryItem: jest.fn()
  }
}));

describe('useInventoryData Hook', () => {
  const mockProducts = [
    {
      inventory_id: '1',
      product_id: '1',
      name: 'Test Product 1',
      sku: 'TP001',
      current_stock: 10
    },
    {
      inventory_id: '2',
      product_id: '2',
      name: 'Test Product 2',
      sku: 'TP002',
      current_stock: 5
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    // Setup default mock implementations
    (inventoryService.getInventoryList as jest.Mock).mockResolvedValue({
      data: mockProducts,
      count: mockProducts.length
    });
    
    (inventoryCacheService.getCachedInventoryList as jest.Mock).mockReturnValue(null);
  });

  test('should fetch products on initial render', async () => {
    const { result } = renderHook(() => useInventoryData());
    
    // Initial state may or may not have loading=true depending on how fast the render is
    expect(result.current.products).toEqual([]);
    
    // Wait for loading to complete
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });
    
    // After loading, should have products
    expect(result.current.loading).toBe(false);
    expect(result.current.isInitialLoading).toBe(false);
    expect(result.current.products).toEqual(mockProducts);
    expect(result.current.totalCount).toBe(mockProducts.length);
    
    // Should have called the service
    expect(inventoryService.getInventoryList).toHaveBeenCalledWith(
      expect.objectContaining({
        page: 1,
        pageSize: 10
      })
    );
  });

  test('should use cached data when available', async () => {
    const cachedProducts = [
      { inventory_id: '3', product_id: '3', name: 'Cached Product', sku: 'CP001', current_stock: 15 }
    ];
    
    (inventoryCacheService.getCachedInventoryList as jest.Mock).mockReturnValue({
      data: cachedProducts,
      totalCount: cachedProducts.length,
      isStale: false
    });
    
    const { result } = renderHook(() => useInventoryData());
    
    // Wait for loading to complete
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });
    
    // Should use cached data instead of calling the service
    expect(result.current.products).toEqual(cachedProducts);
    expect(result.current.totalCount).toBe(cachedProducts.length);
    expect(inventoryService.getInventoryList).not.toHaveBeenCalled();
  });

  test('should handle pagination correctly', async () => {
    // Setup service to respond with different data for page 2
    const page2Products = [
      { inventory_id: '3', product_id: '3', name: 'Page 2 Product', sku: 'P2001', current_stock: 20 }
    ];

    (inventoryService.getInventoryList as jest.Mock).mockImplementation(params => {
      if (params.page === 2) {
        return Promise.resolve({
          data: page2Products,
          count: mockProducts.length + page2Products.length
        });
      }
      return Promise.resolve({
        data: mockProducts,
        count: mockProducts.length + page2Products.length
      });
    });
    
    const { result } = renderHook(() => useInventoryData());
    
    // Wait for initial loading to complete
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });
    
    // Verify first page data
    expect(result.current.products).toEqual(mockProducts);
    
    // Change page
    act(() => {
      result.current.setCurrentPage(2);
    });
    
    // Wait for page change to be reflected in the state
    await waitFor(() => {
      expect(result.current.currentPage).toBe(2);
    });
    
    // Should have called service with updated page
    expect(inventoryService.getInventoryList).toHaveBeenCalledWith(
      expect.objectContaining({
        page: 2,
        pageSize: 10
      })
    );
  });

  test('should handle sorting correctly', async () => {
    // Setup mock to track calls with different sort parameters
    let lastSortParams = {};
    (inventoryService.getInventoryList as jest.Mock).mockImplementation(params => {
      lastSortParams = params;
      return Promise.resolve({
        data: mockProducts,
        count: mockProducts.length
      });
    });
    
    const { result } = renderHook(() => useInventoryData());
    
    // Wait for initial loading to complete
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });
    
    // Change sort config
    act(() => {
      result.current.handleSort('name');
    });
    
    // First call sets the sort column
    expect(result.current.sortConfig).toEqual({
      column: 'name',
      direction: 'asc'
    });
    
    // Wait for sorting to update in the component
    await waitFor(() => {
      expect(result.current.sortConfig.column).toBe('name');
    });
    
    // Should have called service with sort params eventually
    await waitFor(() => {
      expect(lastSortParams).toEqual(
        expect.objectContaining({
          sortBy: 'name',
          sortDirection: 'asc'
        })
      );
    });
    
    // Toggle sort direction
    act(() => {
      result.current.handleSort('name');
    });
    
    // Second call toggles direction
    expect(result.current.sortConfig).toEqual({
      column: 'name',
      direction: 'desc'
    });
    
    // Wait for sorting to update in the component
    await waitFor(() => {
      expect(result.current.sortConfig.direction).toBe('desc');
    });
    
    // Should have called service with updated direction eventually
    await waitFor(() => {
      expect(lastSortParams).toEqual(
        expect.objectContaining({
          sortBy: 'name',
          sortDirection: 'desc'
        })
      );
    });
  });

  test('should handle product selection correctly', async () => {
    // Make sure products will be loaded for the test
    (inventoryService.getInventoryList as jest.Mock).mockResolvedValue({
      data: mockProducts,
      count: mockProducts.length
    });
    
    const { result } = renderHook(() => useInventoryData());
    
    // Wait for data to be loaded
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.products.length).toBeGreaterThan(0);
    });
    
    // Select a product
    act(() => {
      result.current.selectProduct('1');
    });
    
    // Should have selected the product
    expect(result.current.selectedProductIds.has('1')).toBe(true);
    expect(result.current.selectedProductIds.size).toBe(1);
    expect(result.current.isAllSelected).toBe(false);
    expect(result.current.isSomeSelected).toBe(true);
    
    // Select all products
    act(() => {
      result.current.selectAllProducts(true);
    });
    
    // Should have selected all products (2 in our mock data)
    expect(result.current.selectedProductIds.size).toBe(2);
    expect(result.current.isAllSelected).toBe(true);
    
    // Clear selection
    act(() => {
      result.current.clearSelection();
    });
    
    // Should have cleared selection
    expect(result.current.selectedProductIds.size).toBe(0);
    expect(result.current.isAllSelected).toBe(false);
    expect(result.current.isSomeSelected).toBe(false);
  });

  test('should handle errors correctly', async () => {
    const testError = new Error('Test error');
    (inventoryService.getInventoryList as jest.Mock).mockRejectedValue(testError);
    
    const { result } = renderHook(() => useInventoryData());
    
    // Wait for the error to be processed
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.error).not.toBeNull();
    });
    
    // Should have error state
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toEqual(testError);
    expect(result.current.products).toEqual([]);
  });

  test('should handle optimistic updates correctly', async () => {
    // Make sure products will be loaded for the test
    (inventoryService.getInventoryList as jest.Mock).mockResolvedValue({
      data: mockProducts,
      count: mockProducts.length
    });
    
    const { result } = renderHook(() => useInventoryData());
    
    // Wait for initial loading to complete
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.products.length).toBeGreaterThan(0);
    });
    
    // Reset invalidation mock
    (inventoryCacheService.invalidateInventoryItem as jest.Mock).mockClear();
    
    // Update product stock optimistically
    act(() => {
      result.current.updateProductStock('1', 'increase', 5);
    });
    
    // Product stock should be updated
    const updatedProduct = result.current.products.find(p => p.product_id === '1') as InventoryView;
    expect(updatedProduct?.current_stock).toBe(15); // 10 + 5
    
    // Should have invalidated cache
    expect(inventoryCacheService.invalidateInventoryItem).toHaveBeenCalledWith('1');
  });
}); 