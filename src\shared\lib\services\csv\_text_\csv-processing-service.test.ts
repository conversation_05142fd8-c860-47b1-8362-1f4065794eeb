import { csvProcessingService } from '../csv-processing-service';

// Mock <PERSON>.parse
jest.mock('papaparse', () => ({
  parse: jest.fn((file, options) => {
    // Simulate successful parsing
    if (file instanceof File && file.name === 'valid.csv') {
      options.complete({
        data: [
          {
            order_number: 'TEST-123',
            platform: 'amazon',
            channel: 'amazon_fba',
            customer_email: '<EMAIL>',
            product_sku: 'SKU123',
            quantity: '1'
          }
        ],
        meta: {
          fields: ['order_number', 'platform', 'channel', 'customer_email', 'product_sku', 'quantity']
        }
      });
    } 
    // Simulate file with missing required fields
    else if (file instanceof File && file.name === 'invalid-headers.csv') {
      options.complete({
        data: [
          {
            order_id: 'TEST-123',
            platform: 'amazon',
            sales_channel: 'amazon_fba',
          }
        ],
        meta: {
          fields: ['order_id', 'platform', 'sales_channel']
        }
      });
    }
    // Simulate invalid data format
    else if (file instanceof File && file.name === 'invalid-data.csv') {
      options.complete({
        data: [
          {
            order_number: 'TEST-123',
            platform: 'amazon',
            channel: 'amazon_fba',
            customer_email: 'not-an-email',
            product_sku: 'SKU123',
            quantity: 'abc'
          }
        ],
        meta: {
          fields: ['order_number', 'platform', 'channel', 'customer_email', 'product_sku', 'quantity']
        }
      });
    }
    // Simulate empty file
    else if (file instanceof File && file.name === 'empty.csv') {
      options.complete({
        data: [],
        meta: {
          fields: []
        }
      });
    }
    // Simulate parsing error
    else {
      options.error(new Error('Failed to parse file'));
    }
  })
}));

describe('CsvProcessingService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateBulkOrderCsv', () => {
    it('should validate a valid CSV file', async () => {
      // Create a mock File
      const file = new File([''], 'valid.csv', { type: 'text/csv' });
      Object.defineProperty(file, 'size', { value: 100 });

      const result = await csvProcessingService.validateBulkOrderCsv(file);
      
      expect(result.isValid).toBe(true);
      expect(result.data?.length).toBe(1);
      expect(result.data?.[0].order_number).toBe('TEST-123');
      expect(result.data?.[0].platform).toBe('amazon');
      expect(result.error).toBeUndefined();
    });

    it('should reject files that exceed the size limit', async () => {
      const file = new File([''], 'large.csv', { type: 'text/csv' });
      Object.defineProperty(file, 'size', { value: 10 * 1024 * 1024 }); // 10MB

      const result = await csvProcessingService.validateBulkOrderCsv(file);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('exceeds the maximum limit');
    });

    it('should validate CSV headers for required fields', async () => {
      const file = new File([''], 'invalid-headers.csv', { type: 'text/csv' });
      Object.defineProperty(file, 'size', { value: 100 });

      const result = await csvProcessingService.validateBulkOrderCsv(file);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('CSV is missing required columns');
    });

    it('should validate data content in each row', async () => {
      const file = new File([''], 'invalid-data.csv', { type: 'text/csv' });
      Object.defineProperty(file, 'size', { value: 100 });

      const result = await csvProcessingService.validateBulkOrderCsv(file);
      
      expect(result.isValid).toBe(true);
      expect(result.failedRows?.length).toBe(1);
      expect(result.failedRows?.[0].reason).toContain('Invalid email format');
    });

    it('should handle empty files', async () => {
      const file = new File([''], 'empty.csv', { type: 'text/csv' });
      Object.defineProperty(file, 'size', { value: 0 });

      const result = await csvProcessingService.validateBulkOrderCsv(file);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('No data found');
    });

    it('should handle parsing errors', async () => {
      const file = new File([''], 'error.csv', { type: 'text/csv' });
      Object.defineProperty(file, 'size', { value: 100 });

      const result = await csvProcessingService.validateBulkOrderCsv(file);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBeDefined();
    });
  });
}); 