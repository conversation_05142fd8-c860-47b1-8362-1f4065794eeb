import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../app/providers/AuthProvider';
import { activateUser } from '@/shared/lib/services/auth/invite.service';

const AuthCallback: React.FC = () => {
  const { user, isLoading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    const handleAuthCallback = async () => {
      if (!isLoading) {
        if (user) {
          // Activate user account when they verify their email
          try {
            const result = await activateUser(user.id);
            if (!result.success) {
              console.warn('Failed to activate user:', result.message);
            }
          } catch (error) {
            console.error('Error activating user:', error);
          }
          
          navigate('/dashboard-center', { replace: true });
        } else {
          navigate('/login', { replace: true });
        }
      }
    };

    handleAuthCallback();
  }, [user, isLoading, navigate]);

  return (
    <div className="flex items-center justify-center h-screen bg-main-bg">
      <div className="text-center">
        <p className="text-xl text-gray-700 mb-4">Completing login...</p>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
      </div>
    </div>
  );
};

export default AuthCallback; 