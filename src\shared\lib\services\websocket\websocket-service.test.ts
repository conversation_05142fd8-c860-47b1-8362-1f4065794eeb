import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { supabase } from '../../../../../supabase/supabase_client/client';
import {
  WebSocketService,
  webSocketService,
  WebSocketMessage,
  WebSocketConfig,
  WebSocketState,
} from './index';

// Mock Supabase client
jest.mock('@/shared/lib/utils/supabase/client', () => ({
  supabase: {
    channel: jest.fn(),
  },
  supabaseUrl: 'https://test-project.supabase.co',
}));

describe('WebSocketService', () => {
  let mockChannel: any;
  let service: WebSocketService;
  let consoleSpy: any;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    jest.useFakeTimers();

    // Mock console methods
    consoleSpy = {
      log: jest.spyOn(console, 'log').mockImplementation(() => {}),
      error: jest.spyOn(console, 'error').mockImplementation(() => {}),
      warn: jest.spyOn(console, 'warn').mockImplementation(() => {}),
    };

    // Create mock channel
    mockChannel = {
      subscribe: jest.fn(),
      unsubscribe: jest.fn(),
      on: jest.fn(),
      send: jest.fn(),
    };

    (supabase.channel as any).mockReturnValue(mockChannel);

    // Create fresh service instance
    service = new WebSocketService({
      heartbeatInterval: 1000,
      reconnectInterval: 500,
      maxReconnectAttempts: 3,
      maxReconnectInterval: 5000,
    });
  });

  afterEach(() => {
    jest.useRealTimers();
    jest.restoreAllMocks();
    service?.destroy();
  });

  describe('constructor', () => {
    it('should initialize with default config', () => {
      const defaultService = new WebSocketService();
      expect(defaultService.getState()).toEqual({
        isConnected: false,
        isReconnecting: false,
        reconnectAttempts: 0,
        lastHeartbeat: null,
        connectionStartTime: null,
      });
    });

    it('should initialize with custom config', () => {
      const customConfig: Partial<WebSocketConfig> = {
        heartbeatInterval: 2000,
        maxReconnectAttempts: 5,
      };
      const customService = new WebSocketService(customConfig);
      expect(customService).toBeDefined();
    });
  });

  describe('connect', () => {
    it('should establish WebSocket connection successfully', async () => {
      // Mock successful subscription
      mockChannel.subscribe.mockImplementation((callback: Function) => {
        setTimeout(() => callback('SUBSCRIBED'), 0);
        return Promise.resolve();
      });

      const stateHandler = jest.fn();
      service.onStateChange(stateHandler);

      await service.connect();

      expect(supabase.channel).toHaveBeenCalledWith('live-order-feed', {
        config: {
          broadcast: { self: true },
          presence: { key: 'user-presence' },
        },
      });

      expect(mockChannel.subscribe).toHaveBeenCalled();
      expect(mockChannel.on).toHaveBeenCalledTimes(3); // broadcast, postgres_changes, presence

      // Trigger the subscription callback
      jest.advanceTimersByTime(1);

      expect(stateHandler).toHaveBeenCalled();
      expect(service.getState().isConnected).toBe(true);
    });

    it('should handle connection already established', async () => {
      // First connection
      mockChannel.subscribe.mockImplementation((callback: Function) => {
        setTimeout(() => callback('SUBSCRIBED'), 0);
        return Promise.resolve();
      });

      await service.connect();
      jest.advanceTimersByTime(1);

      // Attempt second connection - should not log "already connected" since we use connection manager
      await service.connect();

      // The connection manager handles this internally, so we just verify no errors
      expect(service.getState().isConnected).toBe(true);
    });

    it('should handle subscription errors', async () => {
      mockChannel.subscribe.mockImplementation((callback: Function) => {
        setTimeout(() => callback('CHANNEL_ERROR'), 0);
        return Promise.resolve();
      });

      await service.connect();
      jest.runAllTimers();

      expect(service.getState().isConnected).toBe(false);
    });

    it('should throw error when service is destroyed', async () => {
      await service.destroy();

      await expect(service.connect()).rejects.toThrow('WebSocket service has been destroyed');
    });
  });

  describe('disconnect', () => {
    it('should disconnect successfully', async () => {
      // Set up connected state first
      mockChannel.subscribe.mockImplementation((callback: Function) => {
        setTimeout(() => callback('SUBSCRIBED'), 0);
        return Promise.resolve();
      });
      
      await service.connect();
      jest.advanceTimersByTime(1);
      
      mockChannel.unsubscribe.mockResolvedValue(undefined);

      await service.disconnect();

      expect(service.getState().isConnected).toBe(false);
    });

    it('should handle disconnect errors gracefully', async () => {
      // Set up connected state first
      mockChannel.subscribe.mockImplementation((callback: Function) => {
        setTimeout(() => callback('SUBSCRIBED'), 0);
        return Promise.resolve();
      });
      
      await service.connect();
      jest.advanceTimersByTime(1);
      
      mockChannel.unsubscribe.mockRejectedValue(new Error('Disconnect failed'));

      await service.disconnect();

      expect(consoleSpy.error).toHaveBeenCalledWith('Error during WebSocket disconnect:', expect.any(Error));
    });
  });

  describe('event handling', () => {
    it('should register and trigger event handlers', () => {
      const handler1 = jest.fn();
      const handler2 = jest.fn();

      service.on('order_update', handler1);
      service.on('order_update', handler2);

      // Simulate incoming message through the message handler
      const message: WebSocketMessage = {
        type: 'order_update',
        payload: { orderId: '123' },
        timestamp: new Date().toISOString(),
      };

      // Access private method for testing
      (service as any).handleIncomingMessage(message);

      expect(handler1).toHaveBeenCalledWith(message);
      expect(handler2).toHaveBeenCalledWith(message);
    });

    it('should unregister event handlers', () => {
      const handler = jest.fn();

      service.on('order_update', handler);
      service.off('order_update', handler);

      const message: WebSocketMessage = {
        type: 'order_update',
        payload: { orderId: '123' },
        timestamp: new Date().toISOString(),
      };

      (service as any).handleIncomingMessage(message);

      expect(handler).not.toHaveBeenCalled();
    });

    it('should handle state change subscriptions', () => {
      const stateHandler = jest.fn();

      service.onStateChange(stateHandler);
      
      // Trigger state update
      (service as any).updateState({ isConnected: true });

      expect(stateHandler).toHaveBeenCalledWith(expect.objectContaining({
        isConnected: true,
      }));
    });

    it('should handle errors in event handlers gracefully', () => {
      const errorHandler = jest.fn(() => {
        throw new Error('Handler error');
      });

      service.on('order_update', errorHandler);

      const message: WebSocketMessage = {
        type: 'order_update',
        payload: { orderId: '123' },
        timestamp: new Date().toISOString(),
      };

      (service as any).handleIncomingMessage(message);

      expect(consoleSpy.error).toHaveBeenCalledWith('Error in message handler:', expect.any(Error));
    });
  });

  describe('heartbeat system', () => {
    it('should start heartbeat after successful connection', async () => {
      mockChannel.subscribe.mockImplementation((callback: Function) => {
        setTimeout(() => callback('SUBSCRIBED'), 0);
        return Promise.resolve();
      });

      await service.connect();
      jest.advanceTimersByTime(1);

      // Fast-forward heartbeat interval
      jest.advanceTimersByTime(1000);

      expect(mockChannel.send).toHaveBeenCalledWith({
        type: 'broadcast',
        event: 'message',
        payload: expect.objectContaining({
          type: 'heartbeat',
        }),
      });
    });

    it('should update lastHeartbeat on heartbeat message', () => {
      const heartbeatMessage: WebSocketMessage = {
        type: 'heartbeat',
        payload: {},
        timestamp: new Date().toISOString(),
      };

      (service as any).handleIncomingMessage(heartbeatMessage);

      expect(service.getState().lastHeartbeat).toBeInstanceOf(Date);
    });
  });

  describe('reconnection logic', () => {
    it('should attempt reconnection on connection error', () => {
      // Simulate connection error
      (service as any).handleConnectionError(new Error('Connection failed'));

      expect(service.getState().isConnected).toBe(false);
      // The reconnection logic is now handled by the reconnection module
      // We can verify the state changes but not the internal scheduling
    });

    it('should use exponential backoff for reconnection delays', () => {
      // Reset state
      (service as any).updateState({ reconnectAttempts: 0, isReconnecting: false });

      // First error
      (service as any).handleConnectionError(new Error('Connection failed'));
      expect(service.getState().reconnectAttempts).toBe(1);

      // The exponential backoff logic is now in the reconnection module
      // We can verify state changes but the internal timing is handled by that module
    });

    it('should stop reconnection after max attempts', () => {
      // Reset state
      (service as any).updateState({ reconnectAttempts: 0, isReconnecting: false });

      // Simulate reaching max attempts
      (service as any).updateState({ reconnectAttempts: 3 });
      (service as any).handleConnectionError(new Error('Connection failed'));
      
      expect(consoleSpy.error).toHaveBeenCalledWith('Max reconnection attempts reached');
    });
  });

  describe('message sending', () => {
    it('should send messages when connected', async () => {
      // Set up connected state
      mockChannel.subscribe.mockImplementation((callback: Function) => {
        setTimeout(() => callback('SUBSCRIBED'), 0);
        return Promise.resolve();
      });
      
      await service.connect();
      jest.advanceTimersByTime(1);

      const message = {
        type: 'order_update' as const,
        payload: { orderId: '123' },
      };

      service.send(message);

      expect(mockChannel.send).toHaveBeenCalledWith({
        type: 'broadcast',
        event: 'message',
        payload: expect.objectContaining({
          type: 'order_update',
          payload: { orderId: '123' },
          timestamp: expect.any(String),
        }),
      });
    });

    it('should warn when sending while disconnected', () => {
      const message = {
        type: 'order_update' as const,
        payload: { orderId: '123' },
      };

      service.send(message);

      expect(consoleSpy.warn).toHaveBeenCalledWith('Cannot send message: WebSocket not connected');
      expect(mockChannel.send).not.toHaveBeenCalled();
    });
  });

  describe('database change handling', () => {
    it('should handle INSERT events', () => {
      const handler = jest.fn();
      service.on('order_insert', handler);

      const payload = {
        eventType: 'INSERT',
        new: { id: '123', status: 'open' },
        old: null,
        schema: 'public',
        table: 'orders',
      };

      (service as any).handleDatabaseChange(payload);

      expect(handler).toHaveBeenCalledWith(expect.objectContaining({
        type: 'order_insert',
        payload: {
          event: 'INSERT',
          new: { id: '123', status: 'open' },
          old: null,
          schema: 'public',
          table: 'orders',
        },
      }));
    });

    it('should handle UPDATE events', () => {
      const handler = jest.fn();
      service.on('order_update', handler);

      const payload = {
        eventType: 'UPDATE',
        new: { id: '123', status: 'packed' },
        old: { id: '123', status: 'open' },
        schema: 'public',
        table: 'orders',
      };

      (service as any).handleDatabaseChange(payload);

      expect(handler).toHaveBeenCalledWith(expect.objectContaining({
        type: 'order_update',
      }));
    });

    it('should handle DELETE events', () => {
      const handler = jest.fn();
      service.on('order_delete', handler);

      const payload = {
        eventType: 'DELETE',
        new: null,
        old: { id: '123', status: 'cancelled' },
        schema: 'public',
        table: 'orders',
      };

      (service as any).handleDatabaseChange(payload);

      expect(handler).toHaveBeenCalledWith(expect.objectContaining({
        type: 'order_delete',
      }));
    });

    it('should handle unknown event types gracefully', () => {
      const payload = {
        eventType: 'UNKNOWN',
        new: null,
        old: null,
        schema: 'public',
        table: 'orders',
      };

      (service as any).handleDatabaseChange(payload);

      expect(consoleSpy.warn).toHaveBeenCalledWith('Unknown database event type:', 'UNKNOWN');
    });
  });

  describe('state synchronization', () => {
    it('should trigger state sync on successful connection', async () => {
      const handler = jest.fn();
      service.on('connection_ack', handler);

      mockChannel.subscribe.mockImplementation((callback: Function) => {
        setTimeout(() => callback('SUBSCRIBED'), 0);
        return Promise.resolve();
      });

      await service.connect();
      jest.advanceTimersByTime(1);

      expect(handler).toHaveBeenCalledWith(expect.objectContaining({
        type: 'connection_ack',
        payload: expect.objectContaining({
          reconnected: false,
        }),
      }));
    });
  });

  describe('cleanup and destruction', () => {
    it('should clean up resources on destroy', async () => {
      const disconnectSpy = jest.spyOn(service, 'disconnect');

      await service.destroy();

      expect(disconnectSpy).toHaveBeenCalled();
      expect((service as any).isDestroyed).toBe(true);
      // The event handlers are now managed by the message handler module
    });

    it('should clear timers on disconnect', async () => {
      // Connect first to initialize components
      mockChannel.subscribe.mockImplementation((callback: Function) => {
        setTimeout(() => callback('SUBSCRIBED'), 0);
        return Promise.resolve();
      });
      
      await service.connect();
      jest.advanceTimersByTime(1);

      await service.disconnect();

      // The timers are now managed by individual modules (heartbeat, reconnection)
      // We can verify that the state is properly reset
      expect(service.getState().isConnected).toBe(false);
    });
  });

  describe('singleton instance', () => {
    it('should export a singleton instance', () => {
      expect(webSocketService).toBeInstanceOf(WebSocketService);
      expect(webSocketService).toBe(webSocketService); // Same reference
    });
  });

  describe('reference counting and component subscriptions', () => {
    it('should maintain connection count on multiple connect calls', async () => {
      // First connection
      await service.connect();
      
      // Access private property for testing purposes
      expect((service as any).connectionCount).toBe(1);
      
      // Second connection
      await service.connect();
      expect((service as any).connectionCount).toBe(2);
    });
    
    it('should only disconnect when all references are released', async () => {
      mockChannel.subscribe.mockImplementation((callback: Function) => {
        setTimeout(() => callback('SUBSCRIBED'), 0);
        return Promise.resolve();
      });
      
      // Connect twice
      await service.connect();
      await service.connect();
      jest.advanceTimersByTime(1);
      
      // First disconnect
      await service.disconnect();
      expect((service as any).connectionCount).toBe(1);
      expect(mockChannel.unsubscribe).not.toHaveBeenCalled(); // Should not disconnect yet
      
      // Second disconnect
      await service.disconnect();
      expect((service as any).connectionCount).toBe(0);
      expect(mockChannel.unsubscribe).toHaveBeenCalled(); // Should disconnect now
    });
    
    it('should force disconnect regardless of reference count when force=true', async () => {
      // Connect multiple times
      await service.connect();
      await service.connect();
      await service.connect();
      
      // Force disconnect
      await service.disconnect(true);
      
      // Should reset counter and disconnect
      expect((service as any).connectionCount).toBe(0);
      expect(mockChannel.unsubscribe).toHaveBeenCalled();
    });
    
    it('should register subscriptions with component IDs', () => {
      const handler1 = jest.fn();
      
      // Add subscription with component ID
      const unsubscribe = service.subscribeWithId('order_update', 'component-1', handler1);
      
      // Verify subscription tracking
      expect((service as any).subscriptions.has('order_update')).toBe(true);
      expect((service as any).subscriptions.get('order_update').length).toBe(1);
      expect((service as any).subscriptions.get('order_update')[0].componentId).toBe('component-1');
      
      // Trigger event
      const message: WebSocketMessage = {
        type: 'order_update',
        payload: { orderId: '123' },
        timestamp: new Date().toISOString(),
      };
      
      (service as any).handleIncomingMessage(message);
      expect(handler1).toHaveBeenCalledWith(message);
      
      // Test unsubscribe function
      unsubscribe();
      
      // Verify removal
      expect((service as any).subscriptions.has('order_update')).toBe(false);
      
      // Ensure handler isn't called anymore
      handler1.mockClear();
      (service as any).handleIncomingMessage(message);
      expect(handler1).not.toHaveBeenCalled();
    });
    
    it('should support multiple component subscriptions to the same event', () => {
      const handler1 = jest.fn();
      const handler2 = jest.fn();
      
      // Add subscriptions with different component IDs
      service.subscribeWithId('order_update', 'component-1', handler1);
      service.subscribeWithId('order_update', 'component-2', handler2);
      
      // Verify separate tracking
      expect((service as any).subscriptions.get('order_update').length).toBe(2);
      
      // Both handlers should be called
      const message: WebSocketMessage = {
        type: 'order_update',
        payload: { orderId: '123' },
        timestamp: new Date().toISOString(),
      };
      
      (service as any).handleIncomingMessage(message);
      expect(handler1).toHaveBeenCalledWith(message);
      expect(handler2).toHaveBeenCalledWith(message);
    });
    
    it('should properly unsubscribe only the specified component', () => {
      const handler1 = jest.fn();
      const handler2 = jest.fn();
      
      // Add subscriptions with different component IDs
      const unsubscribe1 = service.subscribeWithId('order_update', 'component-1', handler1);
      service.subscribeWithId('order_update', 'component-2', handler2);
      
      // Unsubscribe first component
      unsubscribe1();
      
      // Should still have one subscription
      expect((service as any).subscriptions.get('order_update').length).toBe(1);
      expect((service as any).subscriptions.get('order_update')[0].componentId).toBe('component-2');
      
      // Only second handler should be called
      const message: WebSocketMessage = {
        type: 'order_update',
        payload: { orderId: '123' },
        timestamp: new Date().toISOString(),
      };
      
      (service as any).handleIncomingMessage(message);
      expect(handler1).not.toHaveBeenCalled();
      expect(handler2).toHaveBeenCalledWith(message);
    });
    
    it('should return subscription count', () => {
      expect(service.getSubscriptionCount()).toBe(0);
      
      // Add subscriptions
      service.subscribeWithId('order_update', 'component-1', () => {});
      service.subscribeWithId('order_delete', 'component-1', () => {});
      service.subscribeWithId('order_update', 'component-2', () => {});
      
      // Check counts
      expect(service.getSubscriptionCount()).toBe(3);
      expect(service.getSubscriptionCount('order_update')).toBe(2);
      expect(service.getSubscriptionCount('order_delete')).toBe(1);
      expect(service.getSubscriptionCount('non_existent')).toBe(0);
    });
  });
}); 