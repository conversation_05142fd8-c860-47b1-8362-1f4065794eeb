import { productApi } from '@/shared/api/inventory/productAPI';
import type { 
  Product, 
  ProductListParams, 
  ProductCreateParams,
  ProductUpdateParams,
  ProductIdentifierCreateParams,
  ProductIdentifierUpdateParams,
  Platform,
  ProductIdentifier
} from '@/shared/api/inventory/productAPI-types';

/**
 * Service for product management operations
 */
export class ProductService {
  /**
   * Get a list of products with filtering
   */
  async getProducts(params: ProductListParams = {}) {
    const response = await productApi.list(params);
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to fetch products');
    }
    
    return {
      data: response.data,
      count: response.totalCount
    };
  }

  /**
   * Get a product by ID
   */
  async getProductById(id: string): Promise<Product> {
    const response = await productApi.getById(id);
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to fetch product');
    }
    
    return response.data;
  }

  /**
   * Get a product by SKU
   */
  async getProductBySku(sku: string): Promise<Product> {
    const response = await productApi.getBySku(sku);
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to fetch product');
    }
    
    return response.data;
  }

  /**
   * Create a new product
   */
  async createProduct(data: ProductCreateParams): Promise<Product> {
    const response = await productApi.create(data);
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to create product');
    }
    
    return response.data;
  }

  /**
   * Update an existing product
   */
  async updateProduct(id: string, data: ProductUpdateParams): Promise<Product> {
    const response = await productApi.update(id, data);
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to update product');
    }
    
    return response.data;
  }

  /**
   * Delete a product
   */
  async deleteProduct(id: string): Promise<void> {
    const response = await productApi.delete(id);
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to delete product');
    }
  }

  /**
   * Discontinue a product
   */
  async discontinueProduct(id: string, reason?: string): Promise<Product> {
    const response = await productApi.discontinue(id, reason);
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to discontinue product');
    }
    
    return response.data;
  }

  /**
   * Get all product categories
   */
  async getCategories(): Promise<string[]> {
    const response = await productApi.getCategories();
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to fetch categories');
    }
    
    return response.data;
  }

  /**
   * Get all product types
   */
  async getProductTypes(): Promise<string[]> {
    const response = await productApi.getProductTypes();
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to fetch product types');
    }
    
    return response.data;
  }

  /**
   * Get all platforms
   */
  async getPlatforms(): Promise<Platform[]> {
    const response = await productApi.getPlatforms();
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to fetch platforms');
    }
    
    return response.data;
  }

  /**
   * Get product identifiers for a specific product
   */
  async getProductIdentifiers(productId: string): Promise<ProductIdentifier[]> {
    const response = await productApi.getProductIdentifiers(productId);
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to fetch product identifiers');
    }
    
    return response.data;
  }

  /**
   * Create a product identifier
   */
  async createProductIdentifier(data: ProductIdentifierCreateParams): Promise<ProductIdentifier> {
    const response = await productApi.createProductIdentifier(data);
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to create product identifier');
    }
    
    return response.data;
  }

  /**
   * Update a product identifier
   */
  async updateProductIdentifier(id: string, data: ProductIdentifierUpdateParams): Promise<ProductIdentifier> {
    const response = await productApi.updateProductIdentifier(id, data);
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to update product identifier');
    }
    
    return response.data;
  }

  /**
   * Delete a product identifier
   */
  async deleteProductIdentifier(id: string): Promise<void> {
    const response = await productApi.deleteProductIdentifier(id);
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to delete product identifier');
    }
  }

  /**
   * Bulk update products using Edge Function for improved performance
   */
  async bulkUpdateProductsEdge(ids: string[], data: ProductUpdateParams, userId?: string): Promise<number> {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/bulk-product-update`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${sessionStorage.getItem('supabase.auth.token')}`
          },
          body: JSON.stringify({ ids, data, userId })
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Failed to update products (${response.status})`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to update products');
      }

      return result.count;
    } catch (error) {
      console.error('Error in bulk product update:', error);
      throw error;
    }
  }
  
  /**
   * Bulk create product identifiers using Edge Function
   */
  async bulkCreateProductIdentifiersEdge(
    items: ProductIdentifierCreateParams[], 
    userId?: string
  ): Promise<ProductIdentifier[]> {
    return this.bulkProductIdentifiersOperation(items, 'create', userId);
  }
  
  /**
   * Bulk upsert product identifiers using Edge Function
   */
  async bulkUpsertProductIdentifiersEdge(
    items: ProductIdentifierCreateParams[], 
    userId?: string
  ): Promise<ProductIdentifier[]> {
    return this.bulkProductIdentifiersOperation(items, 'upsert', userId);
  }
  
  /**
   * Helper method for bulk operations on product identifiers
   */
  private async bulkProductIdentifiersOperation(
    items: ProductIdentifierCreateParams[], 
    operation: 'create' | 'update' | 'upsert',
    userId?: string
  ): Promise<ProductIdentifier[]> {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/bulk-product-identifiers`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${sessionStorage.getItem('supabase.auth.token')}`
          },
          body: JSON.stringify({ items, operation, userId })
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Failed to process product identifiers (${response.status})`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to process product identifiers');
      }

      return result.data;
    } catch (error) {
      console.error(`Error in bulk product identifiers ${operation}:`, error);
      throw error;
    }
  }
}

// Export as singleton
export default new ProductService(); 