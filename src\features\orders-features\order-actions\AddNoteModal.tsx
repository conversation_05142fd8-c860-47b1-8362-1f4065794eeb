import React, { useState } from 'react';
import Modal from '@/shared/ui/overlay/Modal';

interface AddNoteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (content: string, author: string) => void;
  orderNumber: string;
  loading?: boolean;
}

const AddNoteModal: React.FC<AddNoteModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  orderNumber,
  loading = false
}) => {
  const [noteContent, setNoteContent] = useState('');
  const [noteAuthor, setNoteAuthor] = useState('Admin'); // Default author
  const [errors, setErrors] = useState<{ content?: string; author?: string }>({});

  const handleClose = () => {
    if (!loading) {
      setNoteContent('');
      setNoteAuthor('Admin');
      setErrors({});
      onClose();
    }
  };

  const validateForm = () => {
    const newErrors: { content?: string; author?: string } = {};
    
    if (!noteContent.trim()) {
      newErrors.content = 'Note content is required';
    } else if (noteContent.trim().length < 5) {
      newErrors.content = 'Note must be at least 5 characters long';
    }
    
    if (!noteAuthor.trim()) {
      newErrors.author = 'Author name is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleConfirm = () => {
    if (validateForm()) {
      onConfirm(noteContent.trim(), noteAuthor.trim());
      setNoteContent('');
      setNoteAuthor('Admin');
      setErrors({});
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Add Note to Order"
      size="lg"
    >
      <div className="space-y-4">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </div>
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Add Internal Note
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              Add an internal note to order <span className="font-medium">{orderNumber}</span>. 
              This note will be visible to staff members and will be logged in the order history.
            </p>

            <div className="space-y-4">
              {/* Author Field */}
              <div>
                <label htmlFor="noteAuthor" className="block text-sm font-medium text-gray-700 mb-1">
                  Author
                </label>
                <input
                  type="text"
                  id="noteAuthor"
                  value={noteAuthor}
                  onChange={(e) => setNoteAuthor(e.target.value)}
                  disabled={loading}
                  className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 disabled:opacity-50 ${
                    errors.author ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="Enter your name"
                />
                {errors.author && (
                  <p className="mt-1 text-sm text-red-600">{errors.author}</p>
                )}
              </div>

              {/* Note Content Field */}
              <div>
                <label htmlFor="noteContent" className="block text-sm font-medium text-gray-700 mb-1">
                  Note Content
                </label>
                <textarea
                  id="noteContent"
                  rows={4}
                  value={noteContent}
                  onChange={(e) => setNoteContent(e.target.value)}
                  disabled={loading}
                  className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 disabled:opacity-50 ${
                    errors.content ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="Enter your note here..."
                />
                <div className="mt-1 flex justify-between">
                  {errors.content ? (
                    <p className="text-sm text-red-600">{errors.content}</p>
                  ) : (
                    <p className="text-sm text-gray-500">Minimum 5 characters required</p>
                  )}
                  <p className="text-sm text-gray-500">{noteContent.length} characters</p>
                </div>
              </div>

              {/* Note Type Info */}
              <div className="p-3 bg-green-50 rounded-md">
                <p className="text-sm text-green-700">
                  <strong>Note Type:</strong> Internal - This note will only be visible to staff members 
                  and will not be shared with the customer.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6 flex justify-end space-x-3">
        <button
          type="button"
          onClick={handleClose}
          disabled={loading}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
        >
          Cancel
        </button>
        <button
          type="button"
          onClick={handleConfirm}
          disabled={loading || !noteContent.trim() || !noteAuthor.trim()}
          className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 flex items-center"
        >
          {loading ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Adding Note...
            </>
          ) : (
            'Add Note'
          )}
        </button>
      </div>
    </Modal>
  );
};

export default AddNoteModal; 