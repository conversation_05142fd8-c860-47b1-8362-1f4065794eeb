import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { AllOrdersStatus, PlatformKey, UserRole } from '@/types';
import { OrderFilters, getFilterOptions } from '@/shared/lib/services/order/live-order-service';
import Modal from '@/shared/ui/overlay/Modal';

interface FilterModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentFilters: OrderFilters;
  onApplyFilters: (filters: OrderFilters) => void;
  userRole?: UserRole | null;
}

const FilterModal: React.FC<FilterModalProps> = ({
  isOpen,
  onClose,
  currentFilters,
  onApplyFilters,
  userRole
}) => {
  // Local filter state
  const [filters, setFilters] = useState<OrderFilters>(currentFilters);
  const [availableOptions, setAvailableOptions] = useState<{
    statuses: AllOrdersStatus[];
    platforms: PlatformKey[];
    channels: string[];
  }>({
    statuses: [],
    platforms: [],
    channels: []
  });

  // Load available filter options - memoizing the loadOptions function to prevent recreating it on each render
  const loadOptions = useCallback(async () => {
    try {
      let options = await getFilterOptions();
      
      // For staff users, only show 'open' status option
      if (userRole === 'staff') {
        options = {
          ...options,
          statuses: options.statuses.filter(status => status === 'open')
        };
      }
      
      setAvailableOptions(options);
    } catch (error) {
      console.error('Failed to load filter options:', error);
    }
  }, [userRole]);

  // Effect to load options when modal opens
  useEffect(() => {
    if (isOpen) {
      loadOptions();
    }
  }, [isOpen, loadOptions]);

  // Reset filters when modal opens or currentFilters change
  useEffect(() => {
    if (isOpen) {
      setFilters(currentFilters);
    }
  }, [isOpen, currentFilters]);

  // Memoized status color mapping
  const statusColors = useMemo<Record<AllOrdersStatus, string>>(() => ({
    'open': 'text-blue-700',
    'on_hold': 'text-yellow-600',
    'packed': 'text-purple-700',
    'ready_to_ship': 'text-green-600',
    'shipped': 'text-yellow-700',
    'completed': 'text-green-700',
    'cancelled': 'text-red-700',
    'refund': 'text-orange-700',
    'reshipment_scheduled': 'text-purple-700',
  }), []);

  // Memoized handlers to prevent recreating on each render
  // Handle status filter change
  const handleStatusChange = useCallback((status: AllOrdersStatus, checked: boolean) => {
    const currentStatuses = filters.status || [];
    const newStatuses = checked
      ? [...currentStatuses, status]
      : currentStatuses.filter(s => s !== status);
    
    setFilters(prev => ({
      ...prev,
      status: newStatuses.length > 0 ? newStatuses : undefined
    }));
  }, [filters.status]);

  // Handle platform filter change
  const handlePlatformChange = useCallback((platform: PlatformKey, checked: boolean) => {
    const currentPlatforms = filters.platform || [];
    const newPlatforms = checked
      ? [...currentPlatforms, platform]
      : currentPlatforms.filter(p => p !== platform);
    
    setFilters(prev => ({
      ...prev,
      platform: newPlatforms.length > 0 ? newPlatforms : undefined
    }));
  }, [filters.platform]);

  // Handle channel filter change
  const handleChannelChange = useCallback((channel: string, checked: boolean) => {
    const currentChannels = filters.channel || [];
    const newChannels = checked
      ? [...currentChannels, channel]
      : currentChannels.filter(c => c !== channel);
    
    setFilters(prev => ({
      ...prev,
      channel: newChannels.length > 0 ? newChannels : undefined
    }));
  }, [filters.channel]);

  // Handle date range change
  const handleDateRangeChange = useCallback((field: 'start' | 'end', value: string) => {
    const currentRange = filters.dateRange || { start: '', end: '' };
    const newRange = { ...currentRange, [field]: value };
    
    setFilters(prev => ({
      ...prev,
      dateRange: (newRange.start || newRange.end) ? newRange : undefined
    }));
  }, [filters.dateRange]);

  // Handle flag filter change
  const handleFlagChange = useCallback((flag: 'isUrgent' | 'isProblem' | 'isResent' | 'hasNotes', value: boolean | undefined) => {
    setFilters(prev => ({
      ...prev,
      [flag]: value
    }));
  }, []);

  // Apply filters
  const handleApply = useCallback(() => {
    onApplyFilters(filters);
    onClose();
  }, [filters, onApplyFilters, onClose]);

  // Clear all filters
  const handleClearAll = useCallback(() => {
    const clearedFilters: OrderFilters = {
      page: filters.page,
      pageSize: filters.pageSize
    };
    setFilters(clearedFilters);
  }, [filters.page, filters.pageSize]);

  // Custom footer for the modal - memoized to prevent recreation on every render
  const customFooter = useMemo(() => (
    <div className="flex justify-between items-center">
      <button
        onClick={handleClearAll}
        className="text-sm text-gray-500 hover:text-gray-700 transition-colors duration-150"
      >
        Clear All
      </button>
      <div className="flex space-x-3">
        <button
          onClick={onClose}
          className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors duration-150"
        >
          Cancel
        </button>
        <button
          onClick={handleApply}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-150"
        >
          Apply Filters
        </button>
      </div>
    </div>
  ), [handleClearAll, handleApply, onClose]);

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose} 
      title="Filter Orders"
      size="lg"
      showDefaultFooter={false}
      footer={customFooter}
    >
      <div className="space-y-6 max-h-96 overflow-y-auto">
        {/* Date Range Filter */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Date Range</h4>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="block text-xs text-gray-500 mb-1">From</label>
              <input
                type="date"
                value={filters.dateRange?.start || ''}
                onChange={(e) => handleDateRangeChange('start', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-xs text-gray-500 mb-1">To</label>
              <input
                type="date"
                value={filters.dateRange?.end || ''}
                onChange={(e) => handleDateRangeChange('end', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Status Filter */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Order Status</h4>
          <div className="space-y-2 max-h-32 overflow-y-auto">
            {availableOptions.statuses.map((status) => (
              <label key={status} className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.status?.includes(status) || false}
                  onChange={(e) => handleStatusChange(status, e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className={`ml-2 text-sm ${statusColors[status]}`}>
                  {status}
                </span>
              </label>
            ))}
          </div>
        </div>

        {/* Platform Filter */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Platform</h4>
          <div className="space-y-2">
            {availableOptions.platforms.map((platform) => (
              <label key={platform} className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.platform?.includes(platform) || false}
                  onChange={(e) => handlePlatformChange(platform, e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700 capitalize">
                  {platform}
                </span>
              </label>
            ))}
          </div>
        </div>

        {/* Channel Filter */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Sales Channel</h4>
          <div className="space-y-2 max-h-32 overflow-y-auto">
            {availableOptions.channels.map((channel) => (
              <label key={channel} className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.channel?.includes(channel) || false}
                  onChange={(e) => handleChannelChange(channel, e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">
                  {channel}
                </span>
              </label>
            ))}
          </div>
        </div>

        {/* Flag Filters */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Order Flags</h4>
          <div className="space-y-3">
            {/* Urgent Filter */}
            <div>
              <label className="text-sm text-gray-700 mb-1 block">Urgent Orders</label>
              <select
                value={filters.isUrgent === undefined ? '' : filters.isUrgent.toString()}
                onChange={(e) => handleFlagChange('isUrgent', e.target.value === '' ? undefined : e.target.value === 'true')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All</option>
                <option value="true">Only Urgent</option>
                <option value="false">Not Urgent</option>
              </select>
            </div>

            {/* Problem Filter */}
            <div>
              <label className="text-sm text-gray-700 mb-1 block">Problem Orders</label>
              <select
                value={filters.isProblem === undefined ? '' : filters.isProblem.toString()}
                onChange={(e) => handleFlagChange('isProblem', e.target.value === '' ? undefined : e.target.value === 'true')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All</option>
                <option value="true">Only Problem Orders</option>
                <option value="false">No Problems</option>
              </select>
            </div>

            {/* Notes Filter */}
            <div>
              <label className="text-sm text-gray-700 mb-1 block">Orders with Notes</label>
              <select
                value={filters.hasNotes === undefined ? '' : filters.hasNotes.toString()}
                onChange={(e) => handleFlagChange('hasNotes', e.target.value === '' ? undefined : e.target.value === 'true')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All</option>
                <option value="true">With Notes</option>
                <option value="false">Without Notes</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default FilterModal; 