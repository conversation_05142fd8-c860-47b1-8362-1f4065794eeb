import { supabaseUrl } from '../../../../../supabase/supabase_client/client';

export interface WebSocketMessage {
  type: 'order_update' | 'order_insert' | 'order_delete' | 'heartbeat' | 'connection_ack' | 'DELIVERY_UPDATE';
  payload: any;
  timestamp: string;
}

export interface WebSocketConfig {
  edgeFunctionUrl: string;
  heartbeatInterval: number; // milliseconds
  reconnectInterval: number; // base interval for exponential backoff
  maxReconnectAttempts: number;
  maxReconnectInterval: number; // max interval cap
}

export interface WebSocketState {
  isConnected: boolean;
  isReconnecting: boolean;
  reconnectAttempts: number;
  lastHeartbeat: Date | null;
  connectionStartTime: Date | null;
}

export type WebSocketEventHandler = (message: WebSocketMessage) => void;
export type WebSocketStateHandler = (state: WebSocketState) => void;

// Construct the WebSocket URL from the environment variable or fallback to the imported value
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || supabaseUrl;
const WEBSOCKET_URL = SUPABASE_URL ? SUPABASE_URL.replace(/^http/, 'ws') + '/functions/v1/live-order-feed' : '';

// Log error if URL is missing
if (!WEBSOCKET_URL) {
  console.error('WebSocket URL could not be constructed: Missing Supabase URL');
}

export const DEFAULT_WEBSOCKET_CONFIG: WebSocketConfig = {
  edgeFunctionUrl: WEBSOCKET_URL,
  heartbeatInterval: 30000, // 30 seconds
  reconnectInterval: 1000, // start with 1 second
  maxReconnectAttempts: 10,
  maxReconnectInterval: 30000, // max 30 seconds
}; 