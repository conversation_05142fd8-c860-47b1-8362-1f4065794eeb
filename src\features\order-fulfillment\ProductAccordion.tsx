import * as React from 'react';
import { Profiler, memo, useMemo, useCallback, useRef } from 'react';
import { FulfillPackingListItem } from '@/shared/api/orders/ordersAPI_types';
import { OrderStatus } from '@/types';
import { Icon } from '@/shared/ui/core/Icon';
import { createProfilerCallback } from '@/shared/lib/utils/performance/profiler-utils';
import { useProductAccordion } from '@/shared/lib/hooks/order-fulfillment/useProductAccordion';
import { usePlatformGrouping } from '@/shared/lib/hooks/order-fulfillment/usePlatformGrouping';

export interface ProductBatch {
  productName: string;
  sku: string;
  packingListItems: FulfillPackingListItem[];
  stockInfo: {
    currentStock: number;
    status: 'In Stock' | 'Low Stock' | 'Out of Stock';
  };
}

interface ProductAccordionProps {
  batch: ProductBatch;
  onStatusChange: (orderId: string, newStatus: OrderStatus, userId: string) => void;
  onDataRefresh?: () => Promise<void>;
  selectedItems: Set<string>;
  setSelectedItems: (selected: Set<string> | ((prev: Set<string>) => Set<string>)) => void;
  animatingOut: Set<string>;
  onStatusUpdate: (orderIds: string[], newStatus: string) => void;
  isUpdating: boolean;
}

/**
 * Optimized deep comparison function for ProductAccordionProps
 */
const areEqual = (prevProps: ProductAccordionProps, nextProps: ProductAccordionProps) => {
  // First check if references are the same
  if (prevProps === nextProps) {
    return true;
  }

  // Check if any simple props changed
  if (
    prevProps.batch.productName !== nextProps.batch.productName ||
    prevProps.batch.sku !== nextProps.batch.sku ||
    prevProps.batch.stockInfo.currentStock !== nextProps.batch.stockInfo.currentStock ||
    prevProps.batch.stockInfo.status !== nextProps.batch.stockInfo.status ||
    prevProps.isUpdating !== nextProps.isUpdating
  ) {
    return false;
  }

  // Function reference stability check - only do deep checks if references changed
  const funcRefsChanged = 
    prevProps.onDataRefresh !== nextProps.onDataRefresh ||
    prevProps.setSelectedItems !== nextProps.setSelectedItems ||
    prevProps.onStatusUpdate !== nextProps.onStatusUpdate ||
    prevProps.onStatusChange !== nextProps.onStatusChange;
  
  // If all function references are stable, we can skip more expensive checks
  if (funcRefsChanged) {
    // We accept that function references have changed, but avoid re-renders
    // since the component shouldn't re-render just because function refs changed
    // We handle this with useCallbacks in parent components
  }
    
  // Get only relevant order IDs for this batch
  const relevantIds = new Set(nextProps.batch.packingListItems.map(item => item.order_id));

  // Check if list lengths changed (quickly detect additions/removals)
  if (prevProps.batch.packingListItems.length !== nextProps.batch.packingListItems.length) {
    return false;
  }
  
  // Check if any order in this batch has changed its status or properties
  const prevItemsMap = new Map<string, FulfillPackingListItem>();
  
  for (const item of prevProps.batch.packingListItems) {
    prevItemsMap.set(item.order_id, item);
  }
  
  for (const nextItem of nextProps.batch.packingListItems) {
    const prevItem = prevItemsMap.get(nextItem.order_id);
    if (!prevItem || 
        prevItem.order_status !== nextItem.order_status ||
        prevItem.ordered_quantity !== nextItem.ordered_quantity) {
      return false;
    }
  }

  // Extract the relevant selected and animated items for this batch only
  const relevantPrevSelectedItems = new Set<string>();
  const relevantNextSelectedItems = new Set<string>();
  const relevantPrevAnimatingItems = new Set<string>();
  const relevantNextAnimatingItems = new Set<string>();

  for (const id of relevantIds) {
    if (prevProps.selectedItems.has(id)) relevantPrevSelectedItems.add(id);
    if (nextProps.selectedItems.has(id)) relevantNextSelectedItems.add(id);
    if (prevProps.animatingOut.has(id)) relevantPrevAnimatingItems.add(id);
    if (nextProps.animatingOut.has(id)) relevantNextAnimatingItems.add(id);
  }

  // Check if the sets have different sizes
  if (
    relevantPrevSelectedItems.size !== relevantNextSelectedItems.size ||
    relevantPrevAnimatingItems.size !== relevantNextAnimatingItems.size
  ) {
    return false;
  }

  // Check if the sets contain different items
  for (const id of relevantPrevSelectedItems) {
    if (!relevantNextSelectedItems.has(id)) return false;
  }
  
  for (const id of relevantPrevAnimatingItems) {
    if (!relevantNextAnimatingItems.has(id)) return false;
  }

  // If no changes detected, prevent re-render
  return true;
};

// Top-level wrapper component with Profiler
export const ProductAccordion: React.FC<ProductAccordionProps> = memo((props) => {
  return (
    <Profiler id="ProductAccordion" onRender={createProfilerCallback('ProductAccordion')}>
      <ProductAccordionComponent {...props} />
    </Profiler>
  );
}, areEqual);

// Helper function to generate stable handlers
const createChannelHandler = (
  openItemsInChannel: FulfillPackingListItem[], 
  selectedItems: Set<string>, 
  setSelectedItems: (items: Set<string> | ((prev: Set<string>) => Set<string>)) => void
) => {
  return (e: React.ChangeEvent<HTMLInputElement>) => {
    // Use functional update to avoid stale closures
    setSelectedItems((prev: Set<string>) => {
      const newSelected = new Set(prev);
      
      // Use for...of instead of forEach for better performance
      if (e.target.checked) {
        for (const item of openItemsInChannel) {
          newSelected.add(item.order_id);
        }
      } else {
        for (const item of openItemsInChannel) {
          newSelected.delete(item.order_id);
        }
      }
      
      return newSelected;
    });
  };
};

// Helper functions for item handlers
const createItemRowClickHandler = (
  item: FulfillPackingListItem,
  isAnimating: boolean,
  handleSelectItem: (id: string, checked: boolean) => void,
  selectedItems: Set<string>
) => {
  return () => {
    if (!isAnimating) {
      handleSelectItem(item.order_id, !selectedItems.has(item.order_id));
    }
  };
};

const createItemCheckboxChangeHandler = (
  item: FulfillPackingListItem, 
  handleSelectItem: (id: string, checked: boolean) => void
) => {
  return (e: React.ChangeEvent<HTMLInputElement>) => {
    handleSelectItem(item.order_id, e.target.checked);
  };
};

// Stable handler for stopping propagation
const handleCheckboxClick = (e: React.MouseEvent) => {
  e.stopPropagation();
};

const ProductAccordionComponent: React.FC<ProductAccordionProps> = memo(({
  batch,
  onDataRefresh,
  selectedItems,
  setSelectedItems,
  animatingOut,
  onStatusUpdate
}) => {
  // Store stable references to handlers
  const handlersRef = useRef({
    channelHandlers: new Map<string, (e: React.ChangeEvent<HTMLInputElement>) => void>(),
    itemRowHandlers: new Map<string, () => void>(),
    itemCheckboxHandlers: new Map<string, (e: React.ChangeEvent<HTMLInputElement>) => void>()
  });

  // Use custom hooks for logic separation
  const {
    isExpanded,
    setIsExpanded,
    error,
    setError,
    isProcessing,
    showAllClear,
    openItems,
    totalNeeded,
    totalCount,
    packedCount,
    handleSelectItem,
    handleBulkPack
  } = useProductAccordion({
    packingListItems: batch.packingListItems,
    selectedItems,
    setSelectedItems,
    onDataRefresh,
    onStatusUpdate: onStatusUpdate as unknown as (orderIds: string[], newStatus: string) => Promise<void>
  });

  const platformGroups = usePlatformGrouping({
    packingListItems: batch.packingListItems,
    animatingOut
  });

  // PERFORMANCE FIX: Memoize stock status color to prevent recreation
  const stockStatusColor = useMemo(() => {
    switch (batch.stockInfo.status) {
      case 'Low Stock': return 'text-red-600';
      case 'Out of Stock': return 'text-red-700';
      default: return 'text-blue-600';
    }
  }, [batch.stockInfo.status]);

  // PERFORMANCE FIX: Pre-compute platform statistics to avoid inline calculations
  const platformStats = useMemo(() => {
    return platformGroups.map(({ platform, channels }) => {
      let totalOrders = 0;
      for (const items of channels.values()) {
        for (const item of items) {
          if (item.order_status === 'open') {
            totalOrders++;
          }
        }
      }
      return { platform, totalOrders };
    });
  }, [platformGroups]);

  // PERFORMANCE FIX: Memoize channel selection logic
  const getChannelSelectionProps = useCallback((items: FulfillPackingListItem[]) => {
    const visibleItems = items.filter((item: FulfillPackingListItem) => 
      item.order_status === 'open' || animatingOut.has(item.order_id)
    );
    
    if (visibleItems.length === 0) return null;
    
    let openItemsInChannel: FulfillPackingListItem[] = [];
    let allOpen = true;
    
    for (const item of visibleItems) {
      if (item.order_status === 'open') {
        openItemsInChannel.push(item);
        if (allOpen && !selectedItems.has(item.order_id)) {
          allOpen = false;
        }
      }
    }
    
    const isAllSelected = openItemsInChannel.length > 0 && allOpen;
    
    return {
      visibleItems,
      openItemsInChannel,
      isAllSelected
    };
  }, [animatingOut, selectedItems]);

  // Memoize accordion toggle handler
  const handleToggleExpanded = useCallback(() => {
    setIsExpanded(!isExpanded);
  }, [isExpanded, setIsExpanded]);

  // Memoize error clear handler
  const handleClearError = useCallback(() => {
    setError(null);
  }, [setError]);

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 mb-4 overflow-hidden">
      {/* Header */}
      <div className="p-6 cursor-pointer hover:bg-gray-50 transition-colors duration-200" onClick={handleToggleExpanded}>
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-1">{batch.productName}</h2>
            <p className="text-sm text-gray-500">{openItems.length} open orders</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <div className={`text-2xl font-bold ${stockStatusColor}`}>{totalNeeded} / {batch.stockInfo.currentStock}</div>
              <div className="text-xs text-gray-600 uppercase tracking-wide">Needed / In Stock</div>
            </div>
            <svg className={`w-5 h-5 text-gray-600 transform transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="mt-4">
          {showAllClear ? (
            <div className="mb-2 p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center">
                <svg className="h-5 w-5 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span className="text-green-800 font-medium">All Clear! All orders packed ✨</span>
              </div>
            </div>
          ) : (
            <>
              <div className="mb-2 flex items-center justify-between text-sm">
                <span className="text-blue-600 font-medium">
                  Batch Progress - {packedCount} of {totalCount} packed
                </span>
                <span className="text-blue-600 font-medium">
                  {totalCount === 0 ? 0 : Math.round((packedCount / totalCount) * 100)}% Complete
                </span>
              </div>
              <div className="will-change-transform w-full bg-gray-200 rounded-full h-2" style={{ contain: 'layout style paint' }}>
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: totalCount === 0 ? '0%' : `${(packedCount / totalCount) * 100}%` }}
                />
              </div>
            </>
          )}
        </div>
      </div>

      {/* Content - Conditionally render to reduce DOM size */}
      {isExpanded && (
        <div className="px-6 pb-6">
          {/* Error Message */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center">
                <svg className="h-5 w-5 text-red-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <span className="text-red-800 text-sm">{error}</span>
                <button
                  onClick={handleClearError}
                  className="ml-auto text-red-600 hover:text-red-800"
                  aria-label="Dismiss error message"
                >
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
          )}

          {/* Bulk Actions */}
          {selectedItems.size > 0 && (
            <div className="mb-4 p-3 bg-blue-50 rounded-lg flex items-center justify-between">
              <span className="text-blue-800 font-medium">{selectedItems.size} orders selected</span>
              <button
                onClick={handleBulkPack}
                disabled={isProcessing}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {isProcessing ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </>
                ) : (
                  'Mark as Packed'
                )}
              </button>
            </div>
          )}

          {/* Platform Groups */}
          {platformGroups.map(({ platform, channels }, platformIndex) => {
              const platformStat = platformStats[platformIndex];
              
              return (
              <div key={platform} className="mb-6 border border-gray-200 rounded-lg overflow-hidden">
                {/* Platform Header */}
                <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                  <div className="flex items-center">
                    <Icon platform={platform.toLowerCase()} variant="circle" className="mr-3" />
                    <h3 className="text-lg font-semibold text-gray-900">{platform}</h3>
                    <span className="ml-3 text-sm text-gray-500">
                      ({platformStat.totalOrders} orders)
                    </span>
                  </div>
                </div>

                {/* Channel Groups */}
                <div className="divide-y divide-gray-100">
                  {Array.from(channels.entries()).map(([channel, items]) => {
                    // PERFORMANCE FIX: Use memoized channel selection logic
                    const channelProps = getChannelSelectionProps(items);
                    
                    // Only show channel if it has visible items
                    if (!channelProps) return null;
                    
                    const { visibleItems, openItemsInChannel, isAllSelected } = channelProps;
                    
                    // Get or create a stable channel handler
                    const channelKey = `${platform}-${channel}`;
                    if (!handlersRef.current.channelHandlers.has(channelKey)) {
                      handlersRef.current.channelHandlers.set(
                        channelKey, 
                        createChannelHandler(openItemsInChannel, selectedItems, setSelectedItems)
                      );
                    }
                    
                    // Get the stable handler
                    const handleChannelSelectAll = handlersRef.current.channelHandlers.get(channelKey)!;
                    
                    return (
                    <div key={`${platform}-${channel}`} className="p-4">
                      {/* Channel Header */}
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center">
                          <span className="inline-block px-3 py-1 rounded-full text-sm font-medium bg-orange-100 text-orange-700 mr-3">
                            {channel}
                          </span>
                          <span className="text-sm text-gray-500">({visibleItems.length} orders)</span>
                        </div>
                        <input
                          type="checkbox"
                          checked={isAllSelected}
                          onChange={handleChannelSelectAll}
                          className="rounded border-gray-300"
                        />
                      </div>

                      {/* Orders Table */}
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b border-gray-200 bg-gray-50">
                              <th className="text-left py-2 px-3 font-medium text-gray-700">Select</th>
                              <th className="text-left py-2 px-3 font-medium text-gray-700">Order # / Date</th>
                              <th className="text-left py-2 px-3 font-medium text-gray-700">Customer</th>
                              <th className="text-left py-2 px-3 font-medium text-gray-700">Qty</th>
                            </tr>
                          </thead>
                          <tbody>
                            {visibleItems.map((item: FulfillPackingListItem) => {
                              const isAnimating = animatingOut.has(item.order_id);
                              const itemKey = `${item.order_id}-${item.product_sku}`;
                              
                              // Get or create stable handlers for this item
                              if (!handlersRef.current.itemRowHandlers.has(itemKey)) {
                                handlersRef.current.itemRowHandlers.set(
                                  itemKey, 
                                  createItemRowClickHandler(item, isAnimating, handleSelectItem, selectedItems)
                                );
                              }
                              
                              if (!handlersRef.current.itemCheckboxHandlers.has(itemKey)) {
                                handlersRef.current.itemCheckboxHandlers.set(
                                  itemKey,
                                  createItemCheckboxChangeHandler(item, handleSelectItem)
                                );
                              }
                              
                              // Get the stable handlers
                              const handleItemRowClick = handlersRef.current.itemRowHandlers.get(itemKey)!;
                              const handleItemCheckboxChange = handlersRef.current.itemCheckboxHandlers.get(itemKey)!;

                              return (
                                <tr 
                                  key={itemKey} 
                                  className={`border-b border-gray-100 ${
                                    isAnimating 
                                      ? 'animate-slide-out-left opacity-0 transform -translate-x-full bg-green-50' 
                                      : 'animate-slide-in-right opacity-100 transform translate-x-0 hover:bg-gray-50'
                                  } transition-all duration-500 ease-in-out cursor-pointer`}
                                  onClick={handleItemRowClick}
                                >
                                  <td className="py-2 px-3">
                                    {!isAnimating && (
                                      <input
                                        type="checkbox"
                                        checked={selectedItems.has(item.order_id)}
                                        onChange={handleItemCheckboxChange}
                                        onClick={handleCheckboxClick}
                                        className="rounded border-gray-300"
                                      />
                                    )}
                                    {isAnimating && (
                                      <svg className="h-5 w-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                      </svg>
                                    )}
                                  </td>
                                <td className="py-2 px-3">
                                  <div>
                                    <div className="font-medium text-gray-900">{item.order_number}</div>
                                    <div className="text-sm text-gray-500">
                                      {new Date(item.order_date).toLocaleDateString()}
                                    </div>
                                  </div>
                                </td>
                                <td className="py-2 px-3">
                                  <span className="text-gray-900">{item.customer_name}</span>
                                </td>
                                <td className="py-2 px-3">
                                  <span className="font-medium text-gray-900">{item.ordered_quantity}</span>
                                </td>
                              </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      </div>
                    </div>
                    );
                  })}
                </div>
              </div>
              );
            })}

          {batch.packingListItems.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">No orders for this product</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}, areEqual);

ProductAccordionComponent.displayName = 'ProductAccordionComponent';

export default ProductAccordion; 