// Hooks for bulk stock adjustment
import { useState, useCallback } from 'react';
import inventoryStockService from '@/shared/lib/services/inventory/inventory-stock-service';
import type { AdjustmentType } from '@/shared/lib/services/inventory/inventory-stock-service';
import type { InventoryReasonCategory, Product, InventoryView } from '@/types';
import { useAuth } from '@/shared/lib/hooks/auth/useAuth';

// Define the data structure for bulk stock adjustments
export interface BulkStockAdjustmentData {
  productIds: string[];
  quantity: number;
  type: AdjustmentType;
  reason: string;
  reasonCategory: InventoryReasonCategory;
  notes?: string;
}

// Return type for the hook
export interface UseBulkStockAdjustmentReturn {
  // Modal controls for UI
  isModalOpen: boolean;
  selectedProducts: InventoryView[];
  openModal: (products: InventoryView[]) => void;
  closeModal: () => void;
  updateStock: (data: BulkStockAdjustmentData) => Promise<{ success: boolean; processedCount?: number; failedCount?: number; results?: any[] }>;
  bulkAdjustStock: (data: BulkStockAdjustmentData) => Promise<{
    success: boolean;
    processedCount?: number;
    failedCount?: number;
    results?: any[];
  }>;
  bulkAdjustStockLegacy: (data: BulkStockAdjustmentData) => Promise<{
    success: boolean;
    result?: any;
  }>;
  isProcessing: boolean;
  error: Error | null;
  successCount: number;
  failedCount: number;
  reset: () => void;
}

/**
 * Hook for handling bulk stock adjustments
 * Uses the Edge Function implementation for better performance
 */
export function useBulkStockAdjustment({
  onSuccess,
  onOptimisticUpdate
}: {
  onSuccess?: () => void;
  onOptimisticUpdate?: (productId: string, type: AdjustmentType, quantity: number) => void;
} = {}): UseBulkStockAdjustmentReturn {
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [successCount, setSuccessCount] = useState<number>(0);
  const [failedCount, setFailedCount] = useState<number>(0);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [selectedProducts, setSelectedProducts] = useState<InventoryView[]>([]);
  const { user } = useAuth();

  // Process bulk adjustment using Edge Function
  const bulkAdjustStock = useCallback(async (data: BulkStockAdjustmentData) => {
    if (!data.productIds.length) {
      setError(new Error('No products selected for adjustment'));
      return { success: false };
    }

    setIsProcessing(true);
    setError(null);
    setSuccessCount(0);
    setFailedCount(0);

    try {
      // Prepare items for Edge Function
      const items = data.productIds.map(productId => ({
        productId,
        quantity: data.quantity,
        type: data.type,
        reason: data.reason,
        reasonCategory: data.reasonCategory,
        notes: data.notes
      }));
      
      // Call Edge Function through service
      const result = await inventoryStockService.bulkStockUpdateEdge(
        items, 
        user?.id || 'unknown'
      );
      
      // Update UI state based on results
      setSuccessCount(result.processedCount || 0);
      setFailedCount(result.failedCount || 0);
      
      return { 
        success: result.success,
        processedCount: result.processedCount,
        failedCount: result.failedCount,
        results: result.results
      };
    } catch (err) {
      console.error('Error in bulk stock adjustment:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      return { success: false };
    } finally {
      setIsProcessing(false);
    }
  }, [user?.id]);

  // Legacy method - uses direct API approach instead of Edge Function
  const bulkAdjustStockLegacy = useCallback(async (data: BulkStockAdjustmentData) => {
    if (!data.productIds.length) {
      setError(new Error('No products selected for adjustment'));
      return { success: false };
    }

    setIsProcessing(true);
    setError(null);

    try {
      const result = await inventoryStockService.bulkAdjustStock({
        productIds: data.productIds,
        quantity: data.quantity,
        reason: data.reason,
        reason_category: data.reasonCategory,
        type: data.type,
        notes: data.notes
      });
      
      setSuccessCount(data.productIds.length);
      return { success: true, result };
    } catch (err) {
      console.error('Error in bulk stock adjustment:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      setFailedCount(data.productIds.length);
      return { success: false };
    } finally {
      setIsProcessing(false);
    }
  }, []);

  // Modal reset/helper
  const reset = useCallback(() => {
    setError(null);
    setSuccessCount(0);
    setFailedCount(0);
  }, []);

  const openModal = useCallback((products: InventoryView[]) => {
    setSelectedProducts(products);
    setIsModalOpen(true);
  }, []);

  const closeModal = useCallback(() => {
    setIsModalOpen(false);
    setSelectedProducts([]);
    reset();
  }, [reset]);

  const updateStock = useCallback(async (data: BulkStockAdjustmentData) => {
    const result = await bulkAdjustStock(data);
    if (result.success) {
      closeModal();
      if (onSuccess) onSuccess();
    }
    return result;
  }, [bulkAdjustStock, closeModal, onSuccess]);

  return {
    isModalOpen,
    selectedProducts,
    openModal,
    closeModal,
    updateStock,
    bulkAdjustStock,
    bulkAdjustStockLegacy,
    isProcessing,
    error,
    successCount,
    failedCount,
    reset
  };
} 