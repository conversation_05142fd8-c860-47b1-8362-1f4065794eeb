import { AllOrdersViewItem, InventoryView } from '@/types';
import { WebSocketMessage } from '../websocket/websocket-config';
import { CacheStorage, BaseCacheEntry } from './cache-storage';
import { inventoryCacheService } from './inventory-cache-service';

export class CacheWebSocketHandler {
  private storage: CacheStorage;

  constructor(storage: CacheStorage) {
    this.storage = storage;
  }

  /**
   * Handle order updates from WebSocket
   */
  handleOrderUpdate(message: WebSocketMessage): void {
    console.log('Handling order update from WebSocket:', message.type);
    
    const payload = message.payload;
    if (!payload || !payload.new) return;

    const updatedOrder: AllOrdersViewItem = payload.new;
    const orderId = updatedOrder.id;
    
    if (!orderId) {
      console.warn('Received order update with missing ID, skipping cache update');
      return;
    }

    // Targeted cache update - only update entries that contain this order
    let updatedCount = 0;
    
    for (const [key, entry] of this.storage.entries()) {
      // Only process entries with orders array (to handle generic cache entries)
      if ('orders' in entry) {
        const orderIndex = entry.orders.findIndex((order: AllOrdersViewItem) => order.id === orderId);
        
        if (orderIndex !== -1) {
          // Server-wins conflict resolution
          entry.orders[orderIndex] = { ...updatedOrder };
          entry.timestamp = new Date();
          updatedCount++;
        }
      }
    }
    
    // Also invalidate any specific order cache entry for this order
    this.storage.delete(`order-${orderId}`);
    
    console.log(`Updated order ${orderId} in ${updatedCount} cache entries`);
  }

  /**
   * Handle order insertions from WebSocket
   */
  handleOrderInsert(message: WebSocketMessage): void {
    console.log('Handling order insert from WebSocket');
    
    const payload = message.payload;
    if (!payload || !payload.new) return;

    const newOrder: AllOrdersViewItem = payload.new;
    const orderId = newOrder.id;
    
    if (!orderId) {
      console.warn('Received order insert with missing ID, invalidating all cache');
      this.storage.clear();
      return;
    }
    
    // Selective invalidation based on order filters
    // Invalidate all paginated results but keep specific order details
    const keysToInvalidate: string[] = [];
    
    for (const [key, _] of this.storage.entries()) {
      // If the key represents an orders collection (not an individual order)
      if (key.startsWith('orders-') || key.startsWith('filtered-orders-')) {
        keysToInvalidate.push(key);
      }
    }
    
    // Invalidate identified keys
    if (keysToInvalidate.length > 0) {
      console.log(`Invalidating ${keysToInvalidate.length} collection cache entries due to new order ${orderId}`);
      keysToInvalidate.forEach(key => this.storage.delete(key));
    }
  }

  /**
   * Handle order deletions from WebSocket
   */
  handleOrderDelete(message: WebSocketMessage): void {
    console.log('Handling order delete from WebSocket');
    
    const payload = message.payload;
    if (!payload || !payload.old) return;

    const deletedOrder: AllOrdersViewItem = payload.old;
    const orderId = deletedOrder.id;
    
    if (!orderId) {
      console.warn('Received order delete with missing ID, invalidating all cache');
      this.storage.clear();
      return;
    }
    
    // Remove from all cache entries
    let updatedCount = 0;
    
    for (const [key, entry] of this.storage.entries()) {
      // Only process entries with orders array (to handle generic cache entries)
      if ('orders' in entry) {
        const orderIndex = entry.orders.findIndex((order: AllOrdersViewItem) => order.id === orderId);
        
        if (orderIndex !== -1) {
          // Remove the order from this cache entry
          entry.orders.splice(orderIndex, 1);
          entry.totalCount = Math.max(0, entry.totalCount - 1);
          entry.timestamp = new Date();
          updatedCount++;
        }
      }
    }
    
    // Also invalidate any specific order cache entry for this order
    this.storage.delete(`order-${orderId}`);
    
    console.log(`Removed order ${orderId} from ${updatedCount} cache entries`);
  }

  /**
   * Handle inventory updates from WebSocket
   */
  handleInventoryUpdate(message: WebSocketMessage): void {
    console.log('Handling inventory update from WebSocket:', message.type);
    
    const payload = message.payload;
    if (!payload || !payload.new) return;

    const updatedInventory = payload.new;
    const productId = updatedInventory.product_id;
    
    if (!productId) {
      console.warn('Received inventory update with missing product ID, invalidating all inventory cache');
      inventoryCacheService.invalidateAllInventory();
      return;
    }

    // For inventory updates, we'll invalidate the specific product
    inventoryCacheService.invalidateInventoryItem(productId);
  }

  /**
   * Handle inventory insertions from WebSocket
   */
  handleInventoryInsert(message: WebSocketMessage): void {
    console.log('Handling inventory insert from WebSocket');
    
    // For inventory inserts, we'll invalidate all inventory cache since
    // it could affect listings, counts, etc.
    inventoryCacheService.invalidateAllInventory();
  }

  /**
   * Handle inventory deletions from WebSocket
   */
  handleInventoryDelete(message: WebSocketMessage): void {
    console.log('Handling inventory delete from WebSocket');
    
    const payload = message.payload;
    if (!payload || !payload.old) return;

    const deletedInventory = payload.old;
    const productId = deletedInventory.product_id;
    
    if (!productId) {
      console.warn('Received inventory delete with missing product ID, invalidating all inventory cache');
      inventoryCacheService.invalidateAllInventory();
      return;
    }
    
    // For inventory deletes, invalidate the specific product and all listings
    inventoryCacheService.invalidateInventoryItem(productId);
  }

  /**
   * Handle user actions that require cache invalidation
   */
  handleUserAction(orderId: string, action: 'save' | 'edit' | 'delete'): void {
    if (!orderId) {
      console.warn('handleUserAction called without orderId, invalidating all cache');
      this.storage.clear();
      return;
    }
    
    console.log(`Handling user ${action} action on order ${orderId}`);
    
    if (action === 'delete') {
      // Targeted deletion for specific order
      let updatedCount = 0;
      
      for (const [key, entry] of this.storage.entries()) {
        // Only process entries with orders array (to handle generic cache entries)
        if ('orders' in entry) {
          const orderIndex = entry.orders.findIndex((order: AllOrdersViewItem) => order.id === orderId);
          if (orderIndex !== -1) {
            entry.orders.splice(orderIndex, 1);
            entry.totalCount = Math.max(0, entry.totalCount - 1);
            entry.timestamp = new Date();
            updatedCount++;
          }
        }
      }
      
      // Also invalidate any specific order cache entry for this order
      this.storage.delete(`order-${orderId}`);
      
      console.log(`Removed order ${orderId} from ${updatedCount} cache entries`);
    } else if (action === 'edit') {
      // Invalidate just the specific order data
      this.storage.delete(`order-${orderId}`);
      console.log(`Invalidated cache for order-${orderId}`);
    } else if (action === 'save') {
      // For new orders, we need to invalidate collection caches but can keep other order details
      const keysToInvalidate: string[] = [];
      
      for (const [key, _] of this.storage.entries()) {
        // If the key represents an orders collection (not an individual order)
        if (key.startsWith('orders-') || key.startsWith('filtered-orders-')) {
          keysToInvalidate.push(key);
        }
      }
      
      // Invalidate identified keys
      if (keysToInvalidate.length > 0) {
        console.log(`Invalidating ${keysToInvalidate.length} collection cache entries due to saved order ${orderId}`);
        keysToInvalidate.forEach(key => this.storage.delete(key));
      }
    }
  }
  
  /**
   * Handle user actions that require inventory cache invalidation
   */
  handleInventoryAction(productId: string, action: 'save' | 'edit' | 'delete' | 'adjust_stock'): void {
    if (!productId) {
      console.warn('handleInventoryAction called without productId, invalidating all inventory cache');
      inventoryCacheService.invalidateAllInventory();
      return;
    }
    
    console.log(`Handling user ${action} action on product ${productId}`);
    inventoryCacheService.invalidateInventoryItem(productId);
  }
  
  /**
   * Invalidate cache by key pattern
   */
  invalidateByPattern(pattern: string): void {
    const keysToInvalidate: string[] = [];
    
    for (const [key, _] of this.storage.entries()) {
      if (key.includes(pattern)) {
        keysToInvalidate.push(key);
      }
    }
    
    if (keysToInvalidate.length > 0) {
      console.log(`Invalidating ${keysToInvalidate.length} cache entries matching pattern: ${pattern}`);
      keysToInvalidate.forEach(key => this.storage.delete(key));
    }
  }
  
  /**
   * Invalidate cache by exact prefix
   */
  invalidateByPrefix(prefix: string): void {
    const keysToInvalidate: string[] = [];
    
    for (const [key, _] of this.storage.entries()) {
      if (key.startsWith(prefix)) {
        keysToInvalidate.push(key);
      }
    }
    
    if (keysToInvalidate.length > 0) {
      console.log(`Invalidating ${keysToInvalidate.length} cache entries with prefix: ${prefix}`);
      keysToInvalidate.forEach(key => this.storage.delete(key));
    }
  }
} 