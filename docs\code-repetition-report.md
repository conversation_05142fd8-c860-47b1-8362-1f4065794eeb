# Code Repetition Analysis Report

## Executive Summary

This report identifies significant code repetition patterns in the Washolding Internal App codebase. Refactoring these patterns could improve maintainability, reduce bugs, and decrease overall code size.

## 1. Table Component Pattern Repetition

### Affected Files
- `src/features/orders-features/orders-table/OrdersTable.tsx`
- `src/features/delivery-features/delivery-table/DeliveryTable.tsx`
- `src/features/inventory-features/inventory-management/ProductTable.tsx`

### Issues
- Nearly identical table layout structures with similar wrapper divs
- Duplicate `arePropsEqual` comparison functions with similar patterns
- Repetitive loading/error/empty state handling logic
- Similar pagination implementations

### Recommendation
Create a generic `DataTable` component with:
- Standard layout structure
- Built-in loading, error, and empty states
- Reusable comparison function patterns
- Generic pagination interface

Example refactoring:
```tsx
// src/shared/ui/DataTable.tsx
interface DataTableProps<T> {
  data: T[];
  columns: ColumnDefinition<T>[];
  isLoading?: boolean;
  error?: Error | string | null;
  pagination?: PaginationProps;
  onRowClick?: (id: string) => void;
  selectedId?: string | null;
  keyExtractor: (item: T) => string;
  renderRow: (item: T, isSelected: boolean) => React.ReactNode;
  emptyMessage?: string;
}

const DataTable = <T,>({ ... }) => {
  // Shared implementation
};
```

## 2. Custom Hook Duplication

### Affected Files
- `src/shared/lib/hooks/orders/useOrdersTable.ts`
- `src/shared/lib/hooks/delivery/useShipmentData.ts` 
- Other domain-specific hooks with similar patterns

### Issues
- Repetitive data fetching patterns
- Similar state management (loading, error, data)
- Duplicate URL parameter handling
- Common debounce implementation

### Recommendation
Create base hooks for common fetching patterns:
- `useDataFetching<T>` - Generic data fetching with loading/error states
- `useFilteredDataFetching<T, F>` - Add filter handling + URL synchronization
- `usePaginatedData<T>` - Add pagination capabilities

Example refactoring:
```tsx
// src/shared/lib/hooks/common/useDataFetching.ts
export function useDataFetching<T>({
  fetchFn,
  dependencies = [],
  initialData = null,
}: UseDataFetchingProps<T>): UseDataFetchingReturn<T> {
  const [data, setData] = useState<T | null>(initialData);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Shared fetching implementation
  
  return { data, loading, error, refetch };
}
```

## 3. Status Badge Duplication

### Affected Files
- `src/features/orders-features/orders-table/OrdersTable.tsx`
- `src/features/delivery-features/delivery-table/DeliveryTable.tsx`
- Multiple other components displaying status badges

### Issues
- Duplicate status badge styles and rendering logic
- Repetitive status-to-color mapping
- Duplicate icon usage patterns for status badges

### Recommendation
Create a reusable `StatusBadge` component:
```tsx
// src/shared/ui/StatusBadge.tsx
interface StatusBadgeProps {
  status: string;
  domain: 'order' | 'delivery' | 'inventory' | 'generic';
  showIcon?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status, domain, showIcon = true, size = 'md' }) => {
  // Centralized implementation
};
```

## 4. Modal Pattern Duplication

### Affected Files
- `src/features/orders-features/orders-filter-modal/FilterModal.tsx`
- `src/features/orders-features/order-actions/CancelOrderModal.tsx`
- `src/features/user-invite-modal/InviteUserModal.tsx`
- Several other modal implementations

### Issues
- Duplicate modal layout structure
- Similar form handling patterns
- Common loading/error state handling

### Recommendation
Extend your existing `Modal` component with specialized variants:
```tsx
// src/shared/ui/FormModal.tsx
interface FormModalProps<T> {
  title: string;
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: T) => Promise<void>;
  initialValues: T;
  fields: FormField[];
  submitLabel?: string;
  cancelLabel?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
}

const FormModal = <T,>({ ... }) => {
  // Implementation with standardized error handling, loading states
};
```

## 5. List/Grid Component Pattern Repetition

### Affected Files
- Various components that render lists or grids of items

### Issues
- Duplicate list/grid container structures
- Similar empty state handling
- Common loading patterns

### Recommendation
Create reusable container components:
```tsx
// src/shared/ui/ListContainer.tsx
interface ListContainerProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  isLoading?: boolean;
  error?: Error | string | null;
  emptyMessage?: string;
  emptyIcon?: React.ComponentType<any>;
  keyExtractor: (item: T) => string;
}

const ListContainer = <T,>({ ... }) => {
  // Implementation with standardized patterns
};
```

## 6. Hook Return Memoization Pattern

### Affected Files
- Most custom hooks in the codebase

### Issues
- Inconsistent use of `useMemo` for hook return values
- Some hooks properly memoize return objects, others don't

### Recommendation
Create a utility function to standardize this pattern:
```tsx
// src/shared/lib/utils/hook-utils.ts
export function createMemoizedResult<T>(deps: any[], createResult: () => T): T {
  // eslint-disable-next-line react-hooks/exhaustive-deps
  return useMemo(() => createResult(), deps);
}

// Usage in hooks
return createMemoizedResult(
  [data, loading, error],
  () => ({ data, loading, error, refetch })
);
```

## 7. Loading Components Duplication

### Affected Files
- `src/shared/ui/LoadingSpinner.tsx`
- `src/shared/ui/LoadingSkeleton.tsx`
- Various inline loading implementations

### Issues
- Inconsistent loading state presentations
- Multiple skeleton loading implementations
- Duplicated spinner sizes and variants

### Recommendation
Create a comprehensive loading system:
```tsx
// src/shared/ui/loading/index.ts
export { default as Spinner } from './Spinner';
export { default as TableSkeleton } from './TableSkeleton';
export { default as CardSkeleton } from './CardSkeleton';
export { default as PanelSkeleton } from './PanelSkeleton';
export { default as FormSkeleton } from './FormSkeleton';
```

## 8. Utility Function Duplication

### Affected Files
- Various files in `/src/shared/lib/utils`
- Inline utility functions in components

### Issues
- Duplicate date formatting across components
- Similar debounce implementations
- Repetitive array operations for filtering and mapping

### Recommendation
Extract and centralize in domain-specific utility files:
```tsx
// src/shared/lib/utils/date-utils.ts
export function formatDate(date: string | Date, format?: string): string;
export function getRelativeTime(date: string | Date): string;
export function isOverdue(date: string | Date): boolean;

// src/shared/lib/utils/array-utils.ts
export function groupBy<T>(items: T[], key: keyof T): Record<string, T[]>;
export function sortBy<T>(items: T[], key: keyof T, direction: 'asc' | 'desc'): T[];
```

## 9. Component Layout Duplication

### Affected Files
- Page layouts across the application
- Card components
- Section wrappers

### Issues
- Duplicate max-width containers
- Similar padding/margin patterns
- Common shadow and border styles

### Recommendation
Expand your existing `ContentSection` component and create additional layout primitives:
```tsx
// src/shared/ui/layouts/
export { default as PageLayout } from './PageLayout';
export { default as ContentSection } from './ContentSection';
export { default as CardContainer } from './CardContainer';
export { default as TwoColumnLayout } from './TwoColumnLayout';
```

## 10. API Call Pattern Duplication

### Affected Files
- Files in `/src/shared/api`
- Direct API calls in some components

### Issues
- Similar error handling patterns
- Duplicate loading state management
- Common response type transformations

### Recommendation
Create a standardized API calling utility:
```tsx
// src/shared/api/api-utils.ts
export async function apiRequest<T, R = any>(
  requestFn: (params?: R) => Promise<any>,
  params?: R,
  options?: ApiRequestOptions
): Promise<ApiResponse<T>> {
  // Standardized implementation
}

// Usage
const response = await apiRequest(ordersApi.getById, { id: orderId });
```

## Action Plan

1. **Phase 1: Create Shared Components** (1-2 weeks)
   - Implement generic DataTable component
   - Create StatusBadge component
   - Enhance Modal component family

2. **Phase 2: Refactor Hooks and Utilities** (1-2 weeks)
   - Create base data fetching hooks
   - Standardize hook return memoization
   - Centralize utility functions

3. **Phase 3: Apply Refactorings** (2-3 weeks)
   - Replace duplicate table implementations
   - Update modal usage
   - Migrate to shared loading components

4. **Phase 4: Testing and Validation** (1 week)
   - Verify all refactored components work correctly
   - Update tests for shared components
   - Measure code reduction and performance impact 