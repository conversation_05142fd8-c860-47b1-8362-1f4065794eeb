import React from 'react';

interface OrdersTableHeaderProps {
  totalCount: number;
}

const OrdersTableHeader: React.FC<OrdersTableHeaderProps> = ({ totalCount }) => {
  return (
    <div className="px-6 py-3 border-b border-gray-200 bg-gray-50">
      <h3 className="text-lg font-medium text-gray-900">
        Orders ({totalCount.toLocaleString()})
      </h3>
    </div>
  );
};

export default OrdersTableHeader; 