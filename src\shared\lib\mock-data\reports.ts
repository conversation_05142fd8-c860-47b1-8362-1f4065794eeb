import { TopSellerData } from '@/types/index';

export const MOCK_TOP_SELLERS_DATA: TopSellerData[] = [
  {
    id: 'ts-001',
    store: 'Amazon-NHU',
    platform: 'amazon',
    country: 'USA',
    sales: 94752.00,
    salesPercentage: 25.85,
    orders: 2229,
    ordersPercentage: 28.33,
    units: 2292,
    unitsPercentage: 28.14,
    changeInSalesValue: 3431.84,
    changeInSalesPercentage: 4,
    color: '#3B82F6', // blue-500
  },
  {
    id: 'ts-002',
    store: 'Walmart-Shrimps',
    platform: 'walmart',
    country: 'USA',
    sales: 91582.06,
    salesPercentage: 24.98,
    orders: 2111,
    ordersPercentage: 26.83,
    units: 2881,
    unitsPercentage: 35.38,
    changeInSalesValue: 21080.10,
    changeInSalesPercentage: 30,
    color: '#EF4444', // red-500
  },
  {
    id: 'ts-003',
    store: 'Tiktok-Dropship-Seam',
    platform: 'tiktok',
    country: 'USA',
    sales: 69561.08,
    salesPercentage: 18.97,
    orders: 1552,
    ordersPercentage: 19.73,
    units: 1752,
    unitsPercentage: 21.51,
    changeInSalesValue: 3557.06,
    changeInSalesPercentage: 5,
    color: '#F59E0B', // amber-500 (used instead of a direct yellow which might be hard to see)
  },
  {
    id: 'ts-004',
    store: 'Ebay-Shrimps',
    platform: 'ebay',
    country: 'USA',
    sales: 37791.06,
    salesPercentage: 10.31,
    orders: 558,
    ordersPercentage: 7.09,
    units: 719,
    unitsPercentage: 8.83,
    changeInSalesValue: 500.93,
    changeInSalesPercentage: 1,
    color: '#10B981', // green-500
  },
  {
    id: 'ts-005',
    store: 'Amazon-NextTech',
    platform: 'amazon',
    country: 'USA',
    sales: 34454.87,
    salesPercentage: 9.40,
    orders: 338,
    ordersPercentage: 4.30,
    units: 800,
    unitsPercentage: 9.82,
    changeInSalesValue: -644.31,
    changeInSalesPercentage: -2,
    color: '#6366F1', // indigo-500
  },
  {
    id: 'ts-006',
    store: 'Website-Seam',
    platform: 'website',
    country: 'USA',
    sales: 3119.11,
    salesPercentage: 0.85,
    orders: 85,
    ordersPercentage: 1.08,
    units: 90,
    unitsPercentage: 1.11,
    changeInSalesValue: 122.11,
    changeInSalesPercentage: 4,
    color: '#8B5CF6', // violet-500
  },
   {
    id: 'ts-007',
    store: 'Amazon-Yashkun',
    platform: 'amazon',
    country: 'USA',
    sales: 2050.00,
    salesPercentage: 0.56,
    orders: 50,
    ordersPercentage: 0.64,
    units: 60,
    unitsPercentage: 0.74,
    changeInSalesValue: -50.00,
    changeInSalesPercentage: -2,
    color: '#06B6D4', // cyan-500
  },
  {
    id: 'ts-008',
    store: 'Walmart-Dropship',
    platform: 'walmart',
    country: 'USA',
    sales: 1800.00,
    salesPercentage: 0.49,
    orders: 40,
    ordersPercentage: 0.51,
    units: 45,
    unitsPercentage: 0.55,
    changeInSalesValue: 200.00,
    changeInSalesPercentage: 12,
    color: '#EC4899', // pink-500
  },
  {
    id: 'ts-009',
    store: 'Tiktok-Dropship-Was',
    platform: 'tiktok',
    country: 'USA',
    sales: 1500.00,
    salesPercentage: 0.41,
    orders: 30,
    ordersPercentage: 0.38,
    units: 35,
    unitsPercentage: 0.43,
    changeInSalesValue: 150.00,
    changeInSalesPercentage: 11,
    color: '#D97706', // amber-600
  },
];

export const topSellersDropdownOptions = {
  stores: [
    { value: 'all', label: 'Top Stores' },
    { value: 'amazon', label: 'Amazon Stores' },
    { value: 'walmart', label: 'Walmart Stores' },
  ],
  metric: [
    { value: 'sales', label: 'By Sales' },
    { value: 'orders', label: 'By Orders' },
    { value: 'units', label: 'By Units' },
  ],
  period: [
    { value: 'daily_vs_yesterday', label: 'Daily: Today vs. Yesterday' },
    { value: 'weekly_vs_lastweek', label: 'Weekly: This Week vs. Last Week' },
    { value: 'monthly_vs_lastmonth', label: 'Monthly: This Month vs. Last Month' },
  ],
  filters: [
    { value: 'no_filters', label: 'No Filters' },
    { value: 'channel_usa', label: 'Channel: USA' },
    { value: 'channel_ca', label: 'Channel: Canada' },
  ],
};