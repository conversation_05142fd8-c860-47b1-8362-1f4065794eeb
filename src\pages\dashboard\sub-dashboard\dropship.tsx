import React from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardHeader from '@/features/dashboard-header';
import SalesChart from '@/features/report-features/channel-performance/SalesOverTime';
import LowStockAlertsCard from '@/features/inventory-features/inventory-alerts';
import {
  DROPSHIP_DASHBOARD_METRICS,
  DROPSHIP_LOW_STOCK_ALERTS,
  DROPSHIP_CATEGORY_PERFORMANCE_DATA,
  DROPSHIP_TOP_SELLERS,
  DROPSHIP_SUPPLIER_PERFORMANCE,
  DROPSHIP_FULFILLMENT_TIMES,
  DROPSHIP_SHIPPING_METHODS
} from '@/shared/lib/mock-data/dropship-dashboard';

const DropshipDashboard: React.FC = () => {
  const navigate = useNavigate();

  const handleViewChange = (view: string) => {
    switch (view) {
      case 'all_units':
        navigate('/dashboard-center');
        break;
      case 'shrimp_products':
        navigate('/dashboard-shrimp');
        break;
      case 'dropship_products':
        navigate('/dashboard-dropship');
        break;
      default:
        navigate('/dashboard-center');
    }
  };

  return (
    <>
      <DashboardHeader onViewChange={handleViewChange} selectedView="dropship_products" />
      <main className="flex-1 overflow-x-hidden overflow-y-auto p-6 space-y-6">
        {/* Dropship Dashboard Metrics Section */}
        <section className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Total Gross Sales */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Total Gross Sales</h3>
            <p className="text-3xl font-bold text-gray-900">{DROPSHIP_DASHBOARD_METRICS.totalGrossSales}</p>
          </div>
          
          {/* Total Orders */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Total Orders</h3>
            <p className="text-3xl font-bold text-gray-900">{DROPSHIP_DASHBOARD_METRICS.totalOrders}</p>
          </div>
          
          {/* Avg. Profit Margin */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Avg. Profit Margin</h3>
            <p className="text-3xl font-bold text-green-600">{DROPSHIP_DASHBOARD_METRICS.avgProfitMargin}</p>
          </div>
          
          {/* Active Suppliers */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Active Suppliers</h3>
            <p className="text-3xl font-bold text-gray-900">{DROPSHIP_DASHBOARD_METRICS.activeSuppliers}</p>
          </div>
        </section>

        {/* Dropship Category Performance Section */}
        <section>
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Dropship Category Performance</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {DROPSHIP_CATEGORY_PERFORMANCE_DATA.map((category, index) => (
                <div key={index} className="text-center">
                  <div 
                    className="w-16 h-16 rounded-full mx-auto mb-2 flex items-center justify-center text-white font-bold"
                    style={{ backgroundColor: category.color }}
                  >
                    ${(category.value / 1000).toFixed(0)}K
                  </div>
                  <h3 className="font-medium text-gray-900">{category.name}</h3>
                  <p className="text-sm text-gray-500">${category.value.toLocaleString()}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Supplier Performance Section */}
        <section>
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Top Supplier Performance</h2>
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2 px-4 font-medium text-gray-900">Supplier</th>
                    <th className="text-left py-2 px-4 font-medium text-gray-900">Orders</th>
                    <th className="text-left py-2 px-4 font-medium text-gray-900">Revenue</th>
                    <th className="text-left py-2 px-4 font-medium text-gray-900">Rating</th>
                  </tr>
                </thead>
                <tbody>
                  {DROPSHIP_SUPPLIER_PERFORMANCE.map((supplier, index) => (
                    <tr key={index} className="border-b">
                      <td className="py-2 px-4 text-gray-900">{supplier.supplier}</td>
                      <td className="py-2 px-4 text-gray-600">{supplier.orders}</td>
                      <td className="py-2 px-4 font-medium text-gray-900">{supplier.revenue}</td>
                      <td className="py-2 px-4">
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          ⭐ {supplier.rating}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </section>

        {/* Sales Over Time and Top Sellers Section */}
        <section className="grid grid-cols-1 lg:grid-cols-3 gap-6 items-stretch">
          <div className="lg:col-span-2">
            <SalesChart />
          </div>
          <div className="lg:col-span-1">
            <div className="bg-white p-6 rounded-lg shadow h-full">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Top Selling Dropship Products</h2>
              <div className="space-y-4">
                {DROPSHIP_TOP_SELLERS.map((product, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-900 text-sm">{product.name}</h3>
                      <p className="text-xs text-gray-500">{product.units} units</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">{product.sales}</p>
                      <p className="text-xs text-green-600">{product.growth}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Fulfillment Times and Shipping Methods Section */}
        <section className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Fulfillment Times */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Order Fulfillment Times</h2>
            <div className="space-y-4">
              {DROPSHIP_FULFILLMENT_TIMES.map((time, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-4 h-4 bg-orange-500 rounded" style={{ backgroundColor: `hsl(${30 + index * 15}, 70%, ${50 + index * 10}%)` }}></div>
                    <span className="font-medium text-gray-900">{time.timeRange}</span>
                  </div>
                  <div className="flex items-center space-x-4">
                    <span className="text-sm text-gray-500">{time.percentage}%</span>
                    <span className="font-medium text-gray-900">{time.orders} orders</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Shipping Methods */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Shipping Methods</h2>
            <div className="space-y-4">
              {DROPSHIP_SHIPPING_METHODS.map((method, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium text-gray-900">{method.method}</h3>
                    <p className="text-sm text-gray-500">Avg. {method.avgDays} days</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-gray-900">{method.orders} orders</p>
                    <p className="text-sm text-gray-500">{method.percentage}%</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Low Stock Alerts Section */}
        <section>
          <LowStockAlertsCard alerts={DROPSHIP_LOW_STOCK_ALERTS} />
        </section>
      </main>
    </>
  );
};

export default DropshipDashboard;