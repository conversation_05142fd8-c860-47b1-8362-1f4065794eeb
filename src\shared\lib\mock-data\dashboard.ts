
import { KpiDataType, SalesDataPoint, DropdownOption, IntelligenceChartDataPoint } from '@/types/index';
import {
  InboxStackIcon, // For Open Orders (was FlagIcon for Review)
  ArchiveBoxArrowDownIcon, // For Packed Orders (was CubeIcon for Pick)
  PaperAirplaneIcon, // For Ship (remains)
  CheckCircleIcon, // For Delivered order (was TruckIcon for Dropship)
} from '@/shared/config';


// Dashboard metrics data matching the provided image
export const DASHBOARD_METRICS = {
  totalGrossSales: '$112,450',
  totalOrders: '811',
  avgProfitMargin: '32.8%',
  activeBusinessUnits: '2'
};

// Updated KPI_DATA for 4 items as per user request
export const KPI_DATA: KpiDataType[] = [
  { value: '13', label: 'Open Orders', icon: InboxStackIcon, color: 'text-primary', progress: 25 },
  { value: '19', label: 'Packed Orders', icon: ArchiveBoxArrowDownIcon, color: 'text-primary', progress: 35 },
  { value: '202', label: 'Ship', icon: PaperAirplaneIcon, color: 'text-primary', progress: 80 },
  { value: '2', label: 'Delivered order', icon: CheckCircleIcon, color: 'text-primary', progress: 10 },
];


export const SALES_CHART_DATA: SalesDataPoint[] = [
  { name: 'January', currentValue: 4000, previousValue: 2400 },
  { name: 'February', currentValue: 95000, previousValue: 13980 },
  { name: 'March', currentValue: 10000, previousValue: 5000 },
  { name: 'April', currentValue: 15000, previousValue: 1000 },
  { name: 'May', currentValue: 18000, previousValue: 3000 },
  { name: 'June', currentValue: 2000, previousValue: 4000 },
  { name: 'July', currentValue: 0, previousValue: 0 },
  { name: 'August', currentValue: 0, previousValue: 0 },
  { name: 'September', currentValue: 0, previousValue: 0 },
  { name: 'October', currentValue: 0, previousValue: 0 },
  { name: 'November', currentValue: 0, previousValue: 0 },
  { name: 'December', currentValue: 0, previousValue: 0 },
];

export const salesTypeOptions: DropdownOption[] = [
  { value: 'net_sales', label: 'Net Sales' },
  { value: 'gross_sales', label: 'Gross Sales' },
];
// Business Unit Performance data for the chart
export const BUSINESS_UNIT_PERFORMANCE_DATA = [
  { name: 'Shrimp Products', value: 95000, color: '#8B5CF6' },
  { name: 'Dropship Products', value: 45000, color: '#A78BFA' },
];

export const yearComparisonOptions: DropdownOption[] = [
  { value: 'yearly_this_vs_last', label: 'Yearly: This Year vs. Last Year' },
  { value: 'monthly_this_vs_last', label: 'Monthly: This Month vs. Last Month' },
  { value: 'quarterly_this_vs_last', label: 'Quarterly: This Quarter vs. Last Quarter' },
];

// Intelligence Chart data for the dual-axis chart
export const INTELLIGENCE_CHART_DATA: IntelligenceChartDataPoint[] = [
  { date: 'May 1', revenue: 1800, orders: 20 },
  { date: 'May 2', revenue: 2200, orders: 25 },
  { date: 'May 3', revenue: 2800, orders: 30 },
  { date: 'May 4', revenue: 3200, orders: 35 },
  { date: 'May 5', revenue: 3800, orders: 40 },
  { date: 'May 6', revenue: 4200, orders: 45 },
  { date: 'May 7', revenue: 4800, orders: 50 },
  { date: 'May 8', revenue: 5200, orders: 55 },
  { date: 'May 9', revenue: 5600, orders: 60 },
  { date: 'May 10', revenue: 6000, orders: 65 },
  { date: 'May 11', revenue: 6200, orders: 70 },
  { date: 'May 12', revenue: 6400, orders: 75 },
  { date: 'May 13', revenue: 6600, orders: 80 },
  { date: 'May 14', revenue: 6800, orders: 85 },
  { date: 'May 15', revenue: 6700, orders: 94 },
  { date: 'May 16', revenue: 7200, orders: 95 },
  { date: 'May 17', revenue: 7600, orders: 100 },
  { date: 'May 18', revenue: 8000, orders: 105 },
  { date: 'May 19', revenue: 8400, orders: 110 },
  { date: 'May 20', revenue: 8800, orders: 115 },
  { date: 'May 21', revenue: 9200, orders: 120 },
  { date: 'May 22', revenue: 9600, orders: 125 },
  { date: 'May 23', revenue: 10000, orders: 130 },
  { date: 'May 24', revenue: 10400, orders: 135 },
  { date: 'May 25', revenue: 10800, orders: 140 },
  { date: 'May 26', revenue: 11200, orders: 145 },
  { date: 'May 27', revenue: 11600, orders: 150 },
  { date: 'May 28', revenue: 12000, orders: 155 },
  { date: 'May 29', revenue: 12400, orders: 160 },
  { date: 'May 30', revenue: 12800, orders: 165 },
];
// filterOptions is removed as per user request.