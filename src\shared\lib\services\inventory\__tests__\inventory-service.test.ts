import { inventoryService } from '@/shared/lib/services/inventory';
import { supabase } from '../../../../../../supabase/supabase_client/client';
import { OfflineStorage } from '@/shared/lib/utils/data/offline-storage';
import { ProductStatusEnum } from '@/types';

// Mock Supabase client
jest.mock('@/shared/lib/utils/api/supabase/client', () => ({
  supabase: {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    gt: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    single: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis()
  }
}));

// Mock OfflineStorage
jest.mock('@/shared/lib/utils/data/offline-storage', () => {
  const mockInstance = {
    saveOfflineData: jest.fn(),
    saveBulkOfflineData: jest.fn(),
    getOfflineData: jest.fn(),
    getAllOfflineData: jest.fn(),
    addPendingOperation: jest.fn()
  };

  return {
    OfflineStorage: {
      getInstance: jest.fn(() => mockInstance)
    }
  };
});

describe('Inventory Service', () => {
  // Mock data for tests
  const mockInventoryItems = [
    {
      inventory_id: '1',
      product_id: '1',
      name: 'Test Product 1',
      sku: 'TP001',
      product_type: 'Electronics',
      status: 'active',
      current_stock: 10
    },
    {
      inventory_id: '2',
      product_id: '2',
      name: 'Test Product 2',
      sku: 'TP002',
      product_type: 'Perishable',
      status: 'active',
      current_stock: 3
    }
  ];

  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default mock implementation for select
    (supabase.from as jest.Mock).mockImplementation(() => ({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      in: jest.fn().mockReturnThis(),
      or: jest.fn().mockReturnThis(),
      lt: jest.fn().mockReturnThis(),
      gt: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      range: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      single: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis()
    }));
  });

  describe('getInventoryList', () => {
    test('should fetch inventory items with default parameters', async () => {
      // Setup mock response
      const mockResponse = {
        data: mockInventoryItems,
        error: null,
        count: mockInventoryItems.length
      };
      
      // Mock the final query response
      const mockSelect = jest.fn().mockResolvedValue(mockResponse);
      (supabase.from as jest.Mock).mockImplementation(() => ({
        select: jest.fn().mockReturnValue({
          or: jest.fn().mockReturnThis(),
          in: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          lt: jest.fn().mockReturnThis(),
          gt: jest.fn().mockReturnThis(),
          order: jest.fn().mockReturnThis(),
          range: jest.fn().mockReturnValue(mockSelect())
        })
      }));
      
      // Call the service
      const result = await inventoryService.getInventoryList();
      
      // Verify results
      expect(result.data).toEqual(mockInventoryItems);
      expect(result.count).toBe(mockInventoryItems.length);
      
      // Verify Supabase was called correctly
      expect(supabase.from).toHaveBeenCalledWith('inventory_view');
      
      // Verify data was saved to offline storage
      const offlineStorage = OfflineStorage.getInstance();
      expect(offlineStorage.saveBulkOfflineData).toHaveBeenCalledWith(
        'inventory_view',
        expect.any(Object)
      );
    });

    test('should apply search filter when provided', async () => {
      // Setup mock response
      const mockResponse = {
        data: [mockInventoryItems[0]],
        error: null,
        count: 1
      };
      
      // Mock the query chain
      const mockOr = jest.fn().mockReturnThis();
      const mockRange = jest.fn().mockResolvedValue(mockResponse);
      
      (supabase.from as jest.Mock).mockImplementation(() => ({
        select: jest.fn().mockReturnValue({
          or: mockOr,
          range: mockRange
        })
      }));
      
      // Call the service with search parameter
      const searchTerm = 'TP001';
      await inventoryService.getInventoryList({ search: searchTerm });
      
      // Verify or filter was called with correct search pattern
      expect(mockOr).toHaveBeenCalledWith(
        expect.stringContaining(`name.ilike.%${searchTerm}%,sku.ilike.%${searchTerm}%`)
      );
    });

    test('should apply status filter when provided', async () => {
      // Setup mock response
      const mockResponse = {
        data: mockInventoryItems.filter(item => item.status === 'active'),
        error: null,
        count: 2
      };
      
      // Mock the query chain
      const mockIn = jest.fn().mockReturnThis();
      const mockRange = jest.fn().mockResolvedValue(mockResponse);
      
      (supabase.from as jest.Mock).mockImplementation(() => ({
        select: jest.fn().mockReturnValue({
          in: mockIn,
          range: mockRange
        })
      }));
      
      // Call the service with status filter
      const statusFilter: ProductStatusEnum[] = ['active'];
      await inventoryService.getInventoryList({ status: statusFilter });
      
      // Verify in filter was called with correct status
      expect(mockIn).toHaveBeenCalledWith('status', statusFilter);
    });

    test('should apply pagination parameters when provided', async () => {
      // Setup mock response
      const mockResponse = {
        data: [mockInventoryItems[0]],
        error: null,
        count: mockInventoryItems.length
      };
      
      // Mock the query chain
      const mockRange = jest.fn().mockResolvedValue(mockResponse);
      
      (supabase.from as jest.Mock).mockImplementation(() => ({
        select: jest.fn().mockReturnValue({
          range: mockRange
        })
      }));
      
      // Call the service with pagination parameters
      const page = 2;
      const pageSize = 10;
      await inventoryService.getInventoryList({ page, pageSize });
      
      // Verify range was called with correct pagination values
      // For page 2 with pageSize 10, range should be 10-19
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;
      expect(mockRange).toHaveBeenCalledWith(from, to);
    });

    test('should fall back to offline data when network error occurs', async () => {
      // Setup mock error response
      const mockError = new Error('Network error');
      
      (supabase.from as jest.Mock).mockImplementation(() => ({
        select: jest.fn().mockReturnValue({
          range: jest.fn().mockRejectedValue(mockError)
        })
      }));
      
      // Setup mock offline data
      const offlineStorage = OfflineStorage.getInstance();
      const mockOfflineData = {
        '1': mockInventoryItems[0],
        '2': mockInventoryItems[1]
      };
      
      (offlineStorage.getAllOfflineData as jest.Mock).mockReturnValue(mockOfflineData);
      
      // Mock navigator.onLine to be false
      Object.defineProperty(navigator, 'onLine', { value: false, writable: true });
      
      // Call the service
      const result = await inventoryService.getInventoryList();
      
      // Verify results came from offline storage
      expect(result.data).toEqual(Object.values(mockOfflineData));
      expect(offlineStorage.getAllOfflineData).toHaveBeenCalledWith('inventory_view');
      
      // Reset navigator.onLine
      Object.defineProperty(navigator, 'onLine', { value: true, writable: true });
    });
  });

  describe('update', () => {
    test('should update inventory record successfully', async () => {
      // Setup mock response
      const mockUpdatedInventory = {
        ...mockInventoryItems[0],
        current_stock: 15,
        notes: 'Updated stock'
      };
      
      const mockResponse = {
        data: mockUpdatedInventory,
        error: null
      };
      
      // Mock the query chain
      const mockUpdate = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue(mockResponse)
          })
        })
      });
      
      (supabase.from as jest.Mock).mockImplementation(() => ({
        update: mockUpdate
      }));
      
      // Call the service
      const updateData = {
        quantityOnHand: 15,
        notes: 'Updated stock'
      };
      
      const result = await inventoryService.update('1', updateData);
      
      // Verify results
      expect(result).toEqual(mockUpdatedInventory);
      
      // Verify Supabase was called correctly
      expect(supabase.from).toHaveBeenCalledWith('inventory');
      expect(mockUpdate).toHaveBeenCalledWith(
        expect.objectContaining({
          current_stock: updateData.quantityOnHand,
          notes: updateData.notes
        })
      );
      
      // Verify data was saved to offline storage
      const offlineStorage = OfflineStorage.getInstance();
      expect(offlineStorage.saveOfflineData).toHaveBeenCalledWith(
        'inventory',
        '1',
        mockUpdatedInventory
      );
    });

    test('should queue update for offline mode', async () => {
      // Setup mock error response for network failure
      const mockError = new Error('Network error');
      
      (supabase.from as jest.Mock).mockImplementation(() => ({
        update: jest.fn().mockRejectedValue(mockError)
      }));
      
      // Mock navigator.onLine to be false
      Object.defineProperty(navigator, 'onLine', { value: false, writable: true });
      
      // Setup offline storage mocks
      const offlineStorage = OfflineStorage.getInstance();
      const mockOperationId = 'op-123';
      (offlineStorage.addPendingOperation as jest.Mock).mockReturnValue(mockOperationId);
      (offlineStorage.getOfflineData as jest.Mock).mockReturnValue(mockInventoryItems[0]);
      
      // Call the service
      const updateData = {
        quantityOnHand: 15,
        notes: 'Updated stock'
      };
      
      const result = await inventoryService.update('1', updateData);
      
      // Verify operation was queued
      expect(offlineStorage.addPendingOperation).toHaveBeenCalled();
      
      // Verify optimistic result was returned and saved to offline storage
      expect(result.current_stock).toBe(updateData.quantityOnHand);
      expect(result.notes).toBe(updateData.notes);
      expect(offlineStorage.saveOfflineData).toHaveBeenCalledWith(
        'inventory',
        '1',
        expect.objectContaining({
          current_stock: updateData.quantityOnHand,
          notes: updateData.notes
        })
      );
      
      // Reset navigator.onLine
      Object.defineProperty(navigator, 'onLine', { value: true, writable: true });
    });
  });
}); 