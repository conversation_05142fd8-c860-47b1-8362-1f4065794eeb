import { supabase, supabaseAdmin, isAdminAvailable } from '../../../../../supabase/supabase_client/client';
import { UserRole } from '@/types';

export interface InviteUserData {
  email: string;
  fullName: string;
  role: UserRole;
  businessUnitId: string | null;
}

export interface InviteUserResponse {
  user: any;
  success: boolean;
  message: string;
}

/**
 * Invite a new user using Supabase's admin invite functionality
 * This sends a proper invitation email with magic link
 */
export const inviteUser = async (userData: InviteUserData): Promise<InviteUserResponse> => {
  try {
    // Check if admin client is available
    if (!isAdminAvailable()) {
      throw new Error('Admin operations not available. Please add VITE_SUPABASE_SERVICE_ROLE_KEY to your environment variables.');
    }

    // Step 1: Invite user using Supabase Admin API
    const { data: authData, error: authError } = await supabaseAdmin!.auth.admin.inviteUserByEmail(
      userData.email,
      {
        data: {
          full_name: userData.fullName,
          role: userData.role,
          business_unit_id: userData.businessUnitId,
          invited_at: new Date().toISOString(),
        },
        redirectTo: `${window.location.origin}/auth/callback`
      }
    );

    if (authError) {
      throw new Error(`Failed to send invitation: ${authError.message}`);
    }

    // Step 2: Create the user record with is_active = false (pending verification)
    if (authData.user) {
      const { error: userError } = await supabaseAdmin!
        .from('users')
        .insert({
          id: authData.user.id,
          email: userData.email,
          full_name: userData.fullName,
          role: userData.role,
          business_unit_id: userData.businessUnitId,
          is_active: false, // FALSE until user verifies their email
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (userError) {
        console.error('User record creation failed:', userError);
        
        // Try to cancel the auth invitation since we couldn't create the user record
        try {
          await supabaseAdmin!.auth.admin.deleteUser(authData.user.id);
          console.log('Cancelled auth invitation due to user record creation failure');
        } catch (cancelError) {
          console.error('Failed to cancel auth invitation:', cancelError);
        }
        
        throw new Error(`Failed to create user record: ${userError.message}. Invitation cancelled.`);
      }
    }

    return {
      user: authData.user,
      success: true,
      message: `Invitation sent successfully to ${userData.email}`
    };

  } catch (error) {
    console.error('Invite user error:', error);
    
    return {
      user: null,
      success: false,
      message: error instanceof Error ? error.message : 'Failed to send invitation'
    };
  }
};

/**
 * Resend invitation to an existing user
 */
export const resendInvitation = async (email: string): Promise<InviteUserResponse> => {
  try {
    if (!isAdminAvailable()) {
      throw new Error('Admin operations not available. Please add VITE_SUPABASE_SERVICE_ROLE_KEY to your environment variables.');
    }

    const { data, error } = await supabaseAdmin!.auth.admin.inviteUserByEmail(email, {
      redirectTo: `${window.location.origin}/auth/callback`
    });

    if (error) {
      throw new Error(`Failed to resend invitation: ${error.message}`);
    }

    return {
      user: data.user,
      success: true,
      message: `Invitation resent successfully to ${email}`
    };

  } catch (error) {
    console.error('Resend invitation error:', error);
    
    return {
      user: null,
      success: false,
      message: error instanceof Error ? error.message : 'Failed to resend invitation'
    };
  }
};

/**
 * Get all pending invitations from public.users table where is_active = false
 */
export const getPendingInvitations = async () => {
  try {
    if (!isAdminAvailable()) {
      throw new Error('Admin operations not available. Please add VITE_SUPABASE_SERVICE_ROLE_KEY to your environment variables.');
    }

    // Get users from public.users table where is_active = false (pending invitations)
    const { data: pendingUsers, error: usersError } = await supabaseAdmin!
      .from('users')
      .select('*')
      .eq('is_active', false)
      .order('created_at', { ascending: false });

    if (usersError) {
      console.warn('Failed to fetch pending users from database:', usersError);
    }

    // Get all auth users to find pending invitations
    const { data: authData, error: authError } = await supabaseAdmin!.auth.admin.listUsers();
    
    if (authError) {
      throw new Error(`Failed to fetch auth users: ${authError.message}`);
    }

    // Find auth users who are pending (invited but not confirmed)
    const pendingAuthUsers = authData.users.filter(user => 
      user.invited_at && !user.email_confirmed_at
    );

    // Create a map of database users by ID for quick lookup
    const dbUsersMap = new Map((pendingUsers || []).map(user => [user.id, user]));

    // Merge results: start with database users, then add orphaned auth users
    const allPendingInvitations = [];

    // Add users from database (these have complete profile data)
    for (const dbUser of pendingUsers || []) {
      const authUser = authData.users.find(au => au.id === dbUser.id);
      allPendingInvitations.push({
        ...dbUser,
        invited_at: authUser?.invited_at,
        email_confirmed_at: authUser?.email_confirmed_at,
        user_metadata: authUser?.user_metadata,
        source: 'database'
      });
    }

    // Add orphaned auth users (exist in auth but not in database)
    for (const authUser of pendingAuthUsers) {
      if (!dbUsersMap.has(authUser.id)) {
        allPendingInvitations.push({
          id: authUser.id,
          email: authUser.email,
          full_name: authUser.user_metadata?.full_name || 'Unknown',
          role: authUser.user_metadata?.role || 'staff',
          business_unit_id: authUser.user_metadata?.business_unit_id || null,
          is_active: false,
          created_at: authUser.created_at,
          updated_at: authUser.updated_at,
          invited_at: authUser.invited_at,
          email_confirmed_at: authUser.email_confirmed_at,
          user_metadata: authUser.user_metadata,
          source: 'auth_only'
        });
      }
    }

    // Sort by invitation date (most recent first)
    allPendingInvitations.sort((a, b) => 
      new Date(b.invited_at || b.created_at).getTime() - 
      new Date(a.invited_at || a.created_at).getTime()
    );

    return {
      invitations: allPendingInvitations,
      success: true,
      message: `Found ${allPendingInvitations.length} pending invitations`
    };

  } catch (error) {
    console.error('Get pending invitations error:', error);
    
    return {
      invitations: [],
      success: false,
      message: error instanceof Error ? error.message : 'Failed to fetch pending invitations'
    };
  }
};

/**
 * Activate user when they verify their email (call this from auth callback)
 */
export const activateUser = async (userId: string): Promise<{ success: boolean; message: string }> => {
  try {
    const { error } = await supabase
      .from('users')
      .update({ 
        is_active: true, 
        updated_at: new Date().toISOString() 
      })
      .eq('id', userId);

    if (error) {
      throw new Error(`Failed to activate user: ${error.message}`);
    }

    return {
      success: true,
      message: 'User activated successfully'
    };

  } catch (error) {
    console.error('Activate user error:', error);
    
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to activate user'
    };
  }
};

/**
 * Repair orphaned invitation by creating missing user record
 */
export const repairOrphanedInvitation = async (userId: string): Promise<{ success: boolean; message: string }> => {
  try {
    if (!isAdminAvailable()) {
      throw new Error('Admin operations not available. Please add VITE_SUPABASE_SERVICE_ROLE_KEY to your environment variables.');
    }

    // Get the auth user data
    const { data: authUser, error: authError } = await supabaseAdmin!.auth.admin.getUserById(userId);

    if (authError || !authUser) {
      throw new Error(`Failed to find auth user: ${authError?.message || 'User not found'}`);
    }

    // Create the missing user record
    const { error: userError } = await supabaseAdmin!
      .from('users')
      .insert({
        id: authUser.user.id,
        email: authUser.user.email!,
        full_name: authUser.user.user_metadata?.full_name || 'Unknown',
        role: authUser.user.user_metadata?.role || 'staff',
        business_unit_id: authUser.user.user_metadata?.business_unit_id || null,
        is_active: false, // FALSE until user verifies their email
        created_at: authUser.user.created_at,
        updated_at: new Date().toISOString()
      });

    if (userError) {
      throw new Error(`Failed to create user record: ${userError.message}`);
    }

    return {
      success: true,
      message: 'Orphaned invitation repaired successfully'
    };

  } catch (error) {
    console.error('Repair orphaned invitation error:', error);
    
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to repair orphaned invitation'
    };
  }
};

/**
 * Cancel/delete a pending invitation
 */
export const cancelInvitation = async (userId: string): Promise<{ success: boolean; message: string }> => {
  try {
    if (!isAdminAvailable()) {
      throw new Error('Admin operations not available. Please add VITE_SUPABASE_SERVICE_ROLE_KEY to your environment variables.');
    }

    // First delete from users table
    const { error: userError } = await supabaseAdmin!
      .from('users')
      .delete()
      .eq('id', userId);

    if (userError) {
      console.warn('Failed to delete user record:', userError);
    }

    // Then delete from auth
    const { error: authError } = await supabaseAdmin!.auth.admin.deleteUser(userId);

    if (authError) {
      throw new Error(`Failed to cancel invitation: ${authError.message}`);
    }

    return {
      success: true,
      message: 'Invitation cancelled successfully'
    };

  } catch (error) {
    console.error('Cancel invitation error:', error);
    
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to cancel invitation'
    };
  }
}; 