import { useState, useCallback } from 'react';
import <PERSON> from 'papapar<PERSON>';
import { bulkOrderService } from '@/shared/lib/services/order/bulk-order-service';
import { csvProcessingService } from '@/shared/lib/services/csv/csv-processing-service';

export interface FailedOrder {
  row: number;
  reason: string;
  data: Record<string, any>;
}

export interface BulkOrderCreationResult {
  success: boolean;
  successCount: number;
  failedCount: number;
  data?: any;
}

export interface UseBulkOrderCreationReturn {
  uploadCsv: (file: File) => Promise<BulkOrderCreationResult>;
  downloadTemplate: () => void;
  processingStatus: string | null;
  error: string | null;
  failedOrders: FailedOrder[] | null;
  successfulOrders: any[] | null;
  isProcessing: boolean;
  downloadFailedOrdersReport: () => void;
}

export const useBulkOrderCreation = (): UseBulkOrderCreationReturn => {
  const [processingStatus, setProcessingStatus] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [failedOrders, setFailedOrders] = useState<FailedOrder[] | null>(null);
  const [successfulOrders, setSuccessfulOrders] = useState<any[] | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  /**
   * Upload and process a CSV file for bulk order creation
   */
  const uploadCsv = useCallback(async (file: File): Promise<BulkOrderCreationResult> => {
    setIsProcessing(true);
    setError(null);
    setProcessingStatus('Validating CSV file...');
    
    try {
      // Validate the CSV file first
      const validationResult = await csvProcessingService.validateBulkOrderCsv(file);
      
      if (!validationResult.isValid) {
        setError(validationResult.error || 'Invalid CSV format');
        return { success: false, successCount: 0, failedCount: 0 };
      }
      
      setProcessingStatus('Processing orders...');
      
      // Process the validated data
      const result = await bulkOrderService.createBulkOrders(validationResult.data || []);
      
      if (result.success) {
        setSuccessfulOrders(result.successfulOrders || []);
        setFailedOrders(result.failedOrders || []);
        
        return {
          success: true,
          successCount: result.successfulOrders?.length || 0,
          failedCount: result.failedOrders?.length || 0,
          data: result.data
        };
      } else {
        setError(result.message || 'Failed to process orders');
        return {
          success: false,
          successCount: 0,
          failedCount: validationResult.data?.length || 0
        };
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(`Failed to process CSV: ${errorMessage}`);
      return { success: false, successCount: 0, failedCount: 0 };
    } finally {
      setIsProcessing(false);
      setProcessingStatus(null);
    }
  }, []);

  /**
   * Download a CSV template for bulk order creation
   */
  const downloadTemplate = useCallback(() => {
    // Template data structure for bulk orders
    const templateData = [
      {
        order_number: 'ORDER123',
        platform: 'amazon',
        channel: 'amazon_fba',
        customer_email: '<EMAIL>',
        customer_name: 'Customer Name',
        shipping_street: '123 Main St',
        shipping_city: 'New York',
        shipping_state: 'NY',
        shipping_zip_code: '10001',
        product_sku: 'SKU123',
        quantity: '1',
        subtotal_item: '19.99', // Price per item
      }
    ];
    
    // Convert to CSV
    const csv = Papa.unparse(templateData);
    
    // Create download link
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `bulk-order-template.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, []);

  /**
   * Download a CSV report of failed orders
   */
  const downloadFailedOrdersReport = useCallback(() => {
    if (!failedOrders || failedOrders.length === 0) return;
    
    // Extract the original data with error information
    const reportData = failedOrders.map(order => ({
      ...order.data,
      error_reason: order.reason,
      row_number: order.row
    }));
    
    // Convert to CSV
    const csv = Papa.unparse(reportData);
    
    // Create download link
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `failed-orders-${new Date().toISOString().slice(0, 10)}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, [failedOrders]);

  return {
    uploadCsv,
    downloadTemplate,
    processingStatus,
    error,
    failedOrders,
    successfulOrders,
    isProcessing,
    downloadFailedOrdersReport
  };
};

export default useBulkOrderCreation; 