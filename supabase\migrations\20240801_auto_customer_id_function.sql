-- Auto-generate customer_id for new customers
-- This migration adds a trigger function to automatically generate a customer_id
-- when a new customer is inserted without one

-- Drop the function and trigger if they already exist (for idempotent migrations)
DROP TRIGGER IF EXISTS tr_generate_customer_id ON customers;
DROP FUNCTION IF EXISTS generate_customer_id();

-- Create the function that will generate the customer_id
CREATE OR REPLACE FUNCTION generate_customer_id()
RETURNS TRIGGER AS $$
BEGIN
  -- Only generate a customer_id if one wasn't provided
  IF NEW.customer_id IS NULL OR NEW.customer_id = '' THEN
    -- Format: CUST_ + timestamp-based unique ID
    NEW.customer_id := 'CUST_' || substring(to_char(extract(epoch from now()) * 1000, '999999999999'), 7);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger that runs before insert
CREATE TRIGGER tr_generate_customer_id
BEFORE INSERT ON customers
FOR EACH ROW
EXECUTE FUNCTION generate_customer_id();

-- Add comment to explain the function
COMMENT ON FUNCTION generate_customer_id() IS 'Automatically generates a customer_id for new customers if one is not provided';

-- Create a simple test function to verify the trigger works
CREATE OR REPLACE FUNCTION test_customer_id_generation()
RETURNS TEXT AS $$
DECLARE
  v_customer_id TEXT;
  v_test_email TEXT := 'test_' || extract(epoch from now()) || '@example.com';
BEGIN
  -- Insert a test customer without specifying customer_id
  INSERT INTO customers (
    email,
    name,
    total_orders_count,
    total_spent
  ) VALUES (
    v_test_email,
    'Test Customer',
    0,
    0
  )
  RETURNING customer_id INTO v_customer_id;
  
  -- Delete the test customer to clean up
  DELETE FROM customers WHERE email = v_test_email;
  
  -- Return the generated customer_id for verification
  RETURN v_customer_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION test_customer_id_generation() IS 'Test function to verify customer_id auto-generation'; 