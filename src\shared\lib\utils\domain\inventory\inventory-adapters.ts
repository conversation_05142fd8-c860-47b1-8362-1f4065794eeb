import { InventoryMovementType, InventoryView, Product, InventoryReasonCategory } from '@/types';
import { BulkStockUpdateFormData } from '@/features/inventory-features/stock-update';
import { BulkStockAdjustmentData } from '@/shared/lib/hooks/inventory/useBulkStockAdjustment';

/**
 * Adapter function to convert between InventoryMovementType and the types expected by updateProductStock
 * @param productId - The ID of the product to update
 * @param adjustmentType - The type of inventory movement/adjustment
 * @param quantity - The quantity to adjust
 */
export const optimisticUpdateAdapter = (
  productId: string, 
  adjustmentType: InventoryMovementType, 
  quantity: number
): [string, "increase" | "decrease" | "set", number] => {
  // Convert InventoryMovementType to the types expected by updateProductStock
  // This assumes "count" should be handled as "set"
  let compatibleType: "increase" | "decrease" | "set";
  
  switch (adjustmentType) {
    case "increase":
      compatibleType = "increase";
      break;
    case "decrease":
      compatibleType = "decrease";
      break;
    case "count":
    case "set":
    default:
      compatibleType = "set";
      break;
  }
  
  // Return arguments in the format expected by updateProductStock
  return [productId, compatibleType, quantity];
};

/**
 * Adapter function to convert InventoryView to Product for bulk operations
 * @param inventoryView - The InventoryView object from the API
 * @returns A Product object compatible with bulk operations
 */
export const inventoryViewToProductAdapter = (inventoryView: InventoryView): Product => {
  return {
    id: inventoryView.product_id || '',
    name: inventoryView.name || '',
    sku: inventoryView.sku || '',
    description: null,
    category: null,
    product_type: inventoryView.product_type || null,
    unit_cost: null,
    unit_price: null,
    weight: null,
    dimensions_length: null,
    dimensions_width: null,
    dimensions_height: null,
    status: inventoryView.status || null,
    business_unit_id: null,
    created_at: null,
    updated_at: null
  };
};

/**
 * Adapter function to convert BulkStockUpdateFormData to BulkStockAdjustmentData
 * @param formData - The form data from the bulk stock update modal
 * @returns Data in the format expected by the bulk stock adjustment hook
 */
export const bulkStockUpdateAdapter = (formData: BulkStockUpdateFormData): BulkStockAdjustmentData => {
  return {
    productIds: formData.productIds,
    quantity: formData.quantity,
    type: formData.adjustmentType,
    reason: formData.reason,
    reasonCategory: formData.reasonCategory as InventoryReasonCategory,
    notes: formData.notes
  };
}; 