# Setting Up Test Users in Supabase

## 🎯 Steps to Create Test Users

### 1. **Go to Supabase Dashboard**
- Open your Supabase project dashboard
- Navigate to **Authentication > Users**

### 2. **Create Test Users**
Click "Create User" and add these users:

#### **Staff User #1**
```
Email: <EMAIL> (or <EMAIL>)
Auto-confirm: Yes
```

#### **Admin User**
```
Email: <EMAIL> (or <EMAIL>)
Auto-confirm: Yes
```

#### **Master User**
```
Email: <EMAIL> (or <EMAIL>)
Auto-confirm: Yes
```

### 3. **Set Up User Profiles Table**
In your Supabase SQL Editor, run:

```sql
-- Create profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  full_name TEXT,
  role TEXT CHECK (role IN ('master', 'admin', 'staff')),
  business_unit_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS (Row Level Security)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to read their own profile
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

-- Create policy to allow authenticated users to read all profiles (for admin features)
CREATE POLICY "Authenticated users can view all profiles" ON profiles
  FOR SELECT USING (auth.role() = 'authenticated');
```

### 4. **Insert Test User Profiles**
```sql
-- Insert test user profiles (replace UUIDs with actual user IDs from auth.users)
INSERT INTO profiles (id, full_name, role, business_unit_id) VALUES
  ('USER_ID_FROM_SUPABASE', 'Sarah Staff', 'staff', 'bu-001'),
  ('USER_ID_FROM_SUPABASE', 'Alice Admin', 'admin', 'bu-001'),
  ('USER_ID_FROM_SUPABASE', 'Mary Master', 'master', NULL)
ON CONFLICT (id) DO UPDATE SET
  full_name = EXCLUDED.full_name,
  role = EXCLUDED.role,
  business_unit_id = EXCLUDED.business_unit_id;
```

### 5. **Quick Testing Process**
1. Go to your login page
2. Enter one of the test emails
3. Check your email for magic link
4. Click the magic link
5. Should automatically create the user and redirect to app
6. The AuthProvider will fetch the user profile and role

## 🚀 Alternative: Use Gmail + Alias

If you have a Gmail account, you can use email aliases:
- `<EMAIL>` 
- `<EMAIL>`
- `<EMAIL>`

All emails will go to your main Gmail inbox but Supabase will treat them as different users.

## 🔧 Development Tip

For faster testing, you can temporarily modify the AuthProvider to mock different roles:

```typescript
// In AuthProvider.tsx - FOR DEVELOPMENT ONLY
const mockRole = localStorage.getItem('mockRole') as UserRole;
if (mockRole && process.env.NODE_ENV === 'development') {
  // Override role for testing
  setProfile({ ...profile, role: mockRole });
}
```

Then in browser console:
```javascript
// Test staff role
localStorage.setItem('mockRole', 'staff');
location.reload();

// Test admin role  
localStorage.setItem('mockRole', 'admin');
location.reload();

// Clear mock
localStorage.removeItem('mockRole');
location.reload();
``` 