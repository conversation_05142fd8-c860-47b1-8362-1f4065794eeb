import React from 'react';
import LoadingSpinner from '@/shared/ui/feedback/LoadingSpinner';
import ErrorMessage from '@/shared/ui/feedback/ErrorMessage';

interface OrdersTableStatesProps {
  loading: boolean;
  error: string | null;
  isEmpty: boolean;
  onRetry: () => void;
}

const OrdersTableStates: React.FC<OrdersTableStatesProps> = ({ 
  loading, 
  error, 
  isEmpty, 
  onRetry 
}) => {
  const containerClass = "bg-white shadow-sm rounded-lg overflow-hidden";
  const headerClass = "px-6 py-3 border-b border-gray-200 bg-gray-50";
  const contentClass = "flex items-center justify-center h-64";

  if (loading) {
    return (
      <div className={containerClass}>
        <div className={headerClass}>
          <h3 className="text-lg font-medium text-gray-900">Orders</h3>
        </div>
        <div className={contentClass}>
          <LoadingSpinner message="Loading orders..." />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={containerClass}>
        <div className={headerClass}>
          <h3 className="text-lg font-medium text-gray-900">Orders</h3>
        </div>
        <div className="p-6">
          <ErrorMessage 
            message={error} 
            onRetry={onRetry}
          />
        </div>
      </div>
    );
  }

  if (isEmpty) {
    return (
      <div className={containerClass}>
        <div className={headerClass}>
          <h3 className="text-lg font-medium text-gray-900">Orders</h3>
        </div>
        <div className={contentClass}>
          <div className="text-center">
            <div className="text-gray-500 mb-2">No orders found</div>
            <div className="text-sm text-gray-400">
              Try adjusting your filters or search criteria
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default OrdersTableStates; 