// API Response type
export interface ApiResponse<T> {
  data: T;
  totalCount: number;
  success: boolean;
  message?: string;
  error?: ApiError;
}

// API Error types for better error handling
export interface ApiError {
  code: string;
  message: string;
  context?: Record<string, any>;
  originalError?: any;
}

// Error codes
export enum ErrorCode {
  // Network errors
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  
  // Server errors
  SERVER_ERROR = 'SERVER_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  
  // Client errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTH_ERROR = 'AUTH_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  DUPLICATE_ERROR = 'DUPLICATE_ERROR',
  
  // Business logic errors
  INSUFFICIENT_STOCK = 'INSUFFICIENT_STOCK',
  INVALID_OPERATION = 'INVALID_OPERATION',
  
  // Unknown errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// Re-export inventory service types
export type {
  InventoryListParams,
  InventoryUpdateData
} from '@/shared/lib/services/inventory/inventory-service';

export type {
  StockAdjustmentParams,
  ThresholdUpdateParams,
  BulkStockAdjustmentParams
} from '@/shared/lib/services/inventory/inventory-stock-service';

export type {
  AlertFilterParams
} from '@/shared/lib/services/inventory/inventory-alerts-service';

export type {
  StockMovementParams
} from '@/shared/lib/services/inventory/inventory-history-service';

// Import product-related types from the new file
import type {
  Product,
  ProductStatusEnum,
  Platform,
  ProductIdentifier
} from './productAPI-types';

// Import inventory view types
import type {
  Inventory,
  InventoryView,
  ProductItem
} from '@/types';

// Re-export types needed by API consumers
export type {
  Inventory,
  InventoryView,
  Product,
  ProductItem,
  ProductStatusEnum,
  Platform,
  ProductIdentifier
};

// Helper function to format API responses consistently
export const formatResponse = <T>(data: T, count: number = 0, success: boolean = true, message?: string): ApiResponse<T> => {
  return {
    data,
    totalCount: count,
    success,
    message
  };
};

// Special helper function for void responses to handle null/void type compatibility
export const formatVoidResponse = (success: boolean = true, message?: string): ApiResponse<void> => {
  return {
    data: undefined as void,
    totalCount: 0,
    success,
    message
  };
};

/**
 * Categorizes errors based on type and creates standardized error objects
 */
export const categorizeError = (error: any): ApiError => {
  // Network errors
  if (!navigator.onLine || error.message?.includes('network') || error.message?.includes('fetch')) {
    return {
      code: ErrorCode.NETWORK_ERROR,
      message: 'Network connection error. Please check your internet connection.',
      originalError: error,
      context: { online: navigator.onLine }
    };
  }

  // Timeout errors
  if (error.message?.includes('timeout') || error.code === 'ECONNABORTED') {
    return {
      code: ErrorCode.TIMEOUT_ERROR,
      message: 'The request timed out. Please try again.',
      originalError: error
    };
  }

  // Supabase specific error handling
  if (error.code) {
    // PostgreSQL error codes
    if (error.code.startsWith('22')) {
      return {
        code: ErrorCode.VALIDATION_ERROR,
        message: 'Invalid data format.',
        originalError: error,
        context: { pgErrorCode: error.code }
      };
    }
    
    if (error.code.startsWith('23')) {
      if (error.code === '23505') {
        return {
          code: ErrorCode.DUPLICATE_ERROR,
          message: 'This record already exists.',
          originalError: error,
          context: { constraint: error.details }
        };
      }
      return {
        code: ErrorCode.DATABASE_ERROR,
        message: 'Database constraint violation.',
        originalError: error,
        context: { pgErrorCode: error.code }
      };
    }
    
    if (error.code === '42P01') {
      return {
        code: ErrorCode.DATABASE_ERROR,
        message: 'Table not found.',
        originalError: error
      };
    }
    
    // Auth errors
    if (error.code === 'PGRST301' || error.code.startsWith('4')) {
      return {
        code: ErrorCode.AUTH_ERROR,
        message: 'Authentication error. Please sign in again.',
        originalError: error
      };
    }
  }

  // Custom business logic errors
  if (error.message?.includes('insufficient stock') || error.message?.includes('not enough stock')) {
    return {
      code: ErrorCode.INSUFFICIENT_STOCK,
      message: 'Not enough stock available for this operation.',
      originalError: error
    };
  }

  if (error.message?.includes('already exists')) {
    return {
      code: ErrorCode.DUPLICATE_ERROR,
      message: 'This record already exists.',
      originalError: error
    };
  }

  if (error.message?.includes('not found') || error.status === 404) {
    return {
      code: ErrorCode.NOT_FOUND,
      message: 'The requested resource was not found.',
      originalError: error
    };
  }
  
  // Default - unknown error
  return {
    code: ErrorCode.UNKNOWN_ERROR,
    message: error.message || 'An unexpected error occurred.',
    originalError: error
  };
};

// Enhanced error handler with logging and categorization
export const handleError = (error: any, context?: Record<string, any>): ApiResponse<any> => {
  const apiError = categorizeError(error);
  
  // Add additional context if provided
  if (context) {
    apiError.context = { ...apiError.context, ...context };
  }
  
  // Log the error with all available context
  console.error('API Error:', {
    code: apiError.code,
    message: apiError.message,
    context: apiError.context,
    originalError: apiError.originalError
  });
  
  return {
    data: null,
    totalCount: 0,
    success: false,
    message: apiError.message,
    error: apiError
  };
};

// Enhanced void error handler
export const handleVoidError = (error: any, context?: Record<string, any>): ApiResponse<void> => {
  const apiError = categorizeError(error);
  
  // Add additional context if provided
  if (context) {
    apiError.context = { ...apiError.context, ...context };
  }
  
  // Log the error with all available context
  console.error('API Error (void):', {
    code: apiError.code,
    message: apiError.message,
    context: apiError.context,
    originalError: apiError.originalError
  });
  
  return {
    data: undefined as void,
    totalCount: 0,
    success: false,
    message: apiError.message,
    error: apiError
  };
}; 