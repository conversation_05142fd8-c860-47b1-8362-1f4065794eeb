import React, { useState } from 'react';
import Modal from '@/shared/ui/overlay/Modal';

interface CancelOrderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (reason: string) => void;
  orderNumber: string;
  loading?: boolean;
}

const CancelOrderModal: React.FC<CancelOrderModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  orderNumber,
  loading = false
}) => {
  const [cancelReason, setCancelReason] = useState('');
  const [errors, setErrors] = useState<{ reason?: string }>({});

  const handleClose = () => {
    if (!loading) {
      setCancelReason('');
      setErrors({});
      onClose();
    }
  };

  const validateForm = () => {
    const newErrors: { reason?: string } = {};
    
    if (!cancelReason.trim()) {
      newErrors.reason = 'Cancellation reason is required';
    } else if (cancelReason.trim().length < 10) {
      newErrors.reason = 'Cancellation reason must be at least 10 characters long';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleConfirm = () => {
    if (validateForm()) {
      onConfirm(cancelReason.trim());
      setCancelReason('');
      setErrors({});
    }
  };

  // Pre-defined cancellation reasons
  const commonReasons = [
    'Customer requested cancellation',
    'Payment failed or declined',
    'Item out of stock',
    'Duplicate order placed',
    'Customer changed mind',
    'Shipping address issue'
  ];

  const handleReasonSelect = (reason: string) => {
    setCancelReason(reason);
    setErrors({});
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Cancel Order"
      size="lg"
    >
      <div className="space-y-4">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Cancel Order
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              Are you sure you want to cancel order <span className="font-medium">{orderNumber}</span>? 
              This action cannot be undone.
            </p>

            <div className="space-y-4">
              {/* Common Reasons */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Common Cancellation Reasons
                </label>
                <div className="grid grid-cols-1 gap-2">
                  {commonReasons.map((reason) => (
                    <button
                      key={reason}
                      type="button"
                      onClick={() => handleReasonSelect(reason)}
                      disabled={loading}
                      className={`text-left px-3 py-2 text-sm border rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 transition-colors ${
                        cancelReason === reason 
                          ? 'border-red-300 bg-red-50 text-red-700' 
                          : 'border-gray-300 text-gray-700'
                      }`}
                    >
                      {reason}
                    </button>
                  ))}
                </div>
              </div>

              {/* Custom Reason Field */}
              <div>
                <label htmlFor="cancelReason" className="block text-sm font-medium text-gray-700 mb-1">
                  Cancellation Reason (Custom or Selected)
                </label>
                <textarea
                  id="cancelReason"
                  rows={3}
                  value={cancelReason}
                  onChange={(e) => setCancelReason(e.target.value)}
                  disabled={loading}
                  className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 disabled:opacity-50 ${
                    errors.reason ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="Enter the reason for cancelling this order..."
                />
                <div className="mt-1 flex justify-between">
                  {errors.reason ? (
                    <p className="text-sm text-red-600">{errors.reason}</p>
                  ) : (
                    <p className="text-sm text-gray-500">Minimum 10 characters required</p>
                  )}
                  <p className="text-sm text-gray-500">{cancelReason.length} characters</p>
                </div>
              </div>

              {/* Warning */}
              <div className="p-3 bg-red-50 rounded-md">
                <p className="text-sm text-red-700">
                  <strong>Warning:</strong> Cancelling this order will:
                </p>
                <ul className="mt-1 text-sm text-red-700 list-disc list-inside space-y-1">
                  <li>Set the order status to "Cancelled"</li>
                  <li>Send a cancellation notification to the customer</li>
                  <li>Release any reserved inventory</li>
                  <li>Log this action in the order history</li>
                  <li>This action cannot be undone</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6 flex justify-end space-x-3">
        <button
          type="button"
          onClick={handleClose}
          disabled={loading}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
        >
          Keep Order
        </button>
        <button
          type="button"
          onClick={handleConfirm}
          disabled={loading || !cancelReason.trim()}
          className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 flex items-center"
        >
          {loading ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Cancelling...
            </>
          ) : (
            'Cancel Order'
          )}
        </button>
      </div>
    </Modal>
  );
};

export default CancelOrderModal; 