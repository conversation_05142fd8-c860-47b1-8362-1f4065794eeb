import { useMemo } from 'react';
import { FulfillPackingListItem } from '@/shared/api/orders/ordersAPI_types';

export interface UsePlatformGroupingProps {
  packingListItems: FulfillPackingListItem[];
  animatingOut: Set<string>;
}

export interface PlatformGroup {
  platform: string;
  channels: Map<string, FulfillPackingListItem[]>;
  isVisible: boolean;
}

export const usePlatformGrouping = ({
  packingListItems,
  animatingOut
}: UsePlatformGroupingProps) => {
  return useMemo(() => {
    const platformMap = new Map<string, Map<string, FulfillPackingListItem[]>>();
    const platformVisibility = new Map<string, boolean>();
    
    // Single-pass optimization: Build map structure and track visibility efficiently
    for (const item of packingListItems) {
      const platformName = item.platform;
      const channelName = item.channel;
      
      // Track visibility with early exit optimization
      if (!platformVisibility.get(platformName) && 
          (item.order_status === 'open' || animatingOut.has(item.order_id))) {
        platformVisibility.set(platformName, true);
      }
      
      // Build nested map structure
      let channels = platformMap.get(platformName);
      if (!channels) {
        channels = new Map<string, FulfillPackingListItem[]>();
        platformMap.set(platformName, channels);
      }
      
      let items = channels.get(channelName);
      if (!items) {
        items = [];
        channels.set(channelName, items);
      }
      
      items.push(item);
    }

    // Convert to array format with visibility info
    return Array.from(platformMap.entries())
      .map(([platform, channels]): PlatformGroup | null => {
        const isVisible = platformVisibility.get(platform) || false;
        if (!isVisible) return null;
        
        return {
          platform,
          channels,
          isVisible
        };
      })
      .filter((group): group is PlatformGroup => group !== null);
  }, [packingListItems, animatingOut]);
}; 