import React from 'react';

interface PageLayoutProps {
  children: React.ReactNode;
  title: string;
  description: string;
  headerActions?: React.ReactNode;
  className?: string;
}

/**
 * Consistent page layout component used across all main pages (Orders, Fulfill, Delivery)
 * 
 * Layout structure:
 * - Full screen flex container with gray background
 * - Main content area with flex column
 * - Header with consistent styling, title, description, and optional actions
 * - Children content area with proper overflow handling
 */
const PageLayout: React.FC<PageLayoutProps> = ({
  children,
  title,
  description,
  headerActions,
  className = ""
}) => {
  return (
    <div className="flex min-h-screen bg-gray-50">
      {/* Main Content Area */}
      <div className={`flex-1 flex flex-col ${className}`}>
        {/* Page Header */}
        <header className="flex-shrink-0 bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-6 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
                <p className="text-gray-600 mt-1">{description}</p>
              </div>
              {headerActions && (
                <div className="flex items-center space-x-4">
                  {headerActions}
                </div>
              )}
            </div>
          </div>
        </header>

        {/* Content Area */}
        {children}
      </div>
    </div>
  );
};

export default PageLayout; 