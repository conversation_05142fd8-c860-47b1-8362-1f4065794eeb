import { ActiveFilter } from '@/types';

/**
 * Transforms UI filter objects into API parameters
 * @param filters - Active filter objects from the UI
 * @param search - Search term
 * @param stockStatus - Stock status filter value
 * @returns API parameters object
 */
export function transformFiltersToParams(
  filters: ActiveFilter[],
  search: string,
  stockStatus: string
): Record<string, any> {
  const params: Record<string, any> = {};
  
  // Group filters by type
  const statusFilters: string[] = [];
  const productTypeFilters: string[] = [];
  const stockConditionFilters: string[] = [];
  
  filters.forEach(filter => {
    if (filter.type === 'status') {
      statusFilters.push(filter.value);
    } else if (filter.type === 'productType') {
      productTypeFilters.push(filter.value);
    } else if (filter.type === 'stockCondition') {
      stockConditionFilters.push(filter.value);
    }
  });
  
  if (statusFilters.length > 0) {
    params.status = statusFilters;
  }
  
  if (productTypeFilters.length > 0) {
    params.productType = productTypeFilters;
  }
  
  if (stockConditionFilters.length > 0) {
    params.stockCondition = stockConditionFilters;
  }
  
  // Add search term if present
  if (search) {
    params.search = search;
  }
  
  // Add stock status filter if not 'all'
  if (stockStatus !== 'all') {
    switch (stockStatus) {
      case 'out_of_stock':
        params.outOfStock = true;
        break;
      case 'low_stock':
        params.lowStock = true;
        break;
      case 'needs_reorder':
        params.needsReorder = true;
        break;
      case 'good_stock':
        params.goodStock = true;
        break;
    }
  }
  
  return params;
}

/**
 * Creates a stock status filter object
 * @param status - Stock status value
 * @returns ActiveFilter object
 */
export function createStockStatusFilter(status: string): ActiveFilter {
  let label = '';
  switch (status) {
    case 'out_of_stock':
      label = 'Stock: Out of Stock';
      break;
    case 'low_stock':
      label = 'Stock: Low Stock';
      break;
    case 'needs_reorder':
      label = 'Stock: Needs Reorder';
      break;
    case 'good_stock':
      label = 'Stock: Good Level';
      break;
    default:
      label = `Stock: ${status}`;
  }
  
  return {
    id: `stockStatus_${status}`,
    type: 'stockStatus',
    value: status,
    label
  };
}

/**
 * Extracts unique product types from product data
 * @param products - Array of inventory products
 * @returns Array of filter options with value and label
 */
export function extractProductTypeOptions(products: any[]) {
  const productTypes = products
    .filter(p => p.product_type)
    .map(p => p.product_type as string)
    .filter((value, index, self) => self.indexOf(value) === index) // Unique values
    .sort();
    
  return productTypes.map(type => ({ value: type, label: type }));
} 