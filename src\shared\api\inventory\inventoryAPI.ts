// Import retry utilities
import { createRetryableFunction } from '@/shared/lib/utils/api/retry';
import { 
  readOperationRetryOptions,
  writeOperationRetryOptions,
  bulkOperationRetryOptions
} from '@/shared/lib/utils/api/api-retry-config';

// Import inventory types
import {
  ApiResponse,
  formatResponse,
  formatVoidResponse,
  handleError,
  handleVoidError,
  InventoryListParams,
  Inventory,
  InventoryView
} from './inventoryAPI-types';

// Import product types from productAPI-types
import { Product } from './productAPI-types';

// Import services
import { 
  inventoryService,
  inventoryAlertsService, 
  inventoryHistoryService
} from '@/shared/lib/services/inventory';

// Import inventoryStockService directly to avoid circular dependency
import inventoryStockService from '@/shared/lib/services/inventory/inventory-stock-service';

// Base implementation of API functions without retry
const baseInventoryApi = {
  // List inventory with filtering and pagination
  async list(params: InventoryListParams = {}): Promise<ApiResponse<InventoryView[]>> {
    try {
      const { data, count } = await inventoryService.getInventoryList(params);
      return formatResponse(data, count);
    } catch (error) {
      return handleError(error, { operation: 'list', params });
    }
  },

  // Get inventory details for a product
  async getByProductId(productId: string): Promise<ApiResponse<Inventory & { product: Product }>> {
    try {
      const data = await inventoryService.getByProductId(productId);
      return formatResponse(data);
    } catch (error) {
      return handleError(error, { operation: 'getByProductId', productId });
    }
  },

  // Get inventory details by inventory ID
  async getById(id: string): Promise<ApiResponse<Inventory & { product: Product }>> {
    try {
      const data = await inventoryService.getById(id);
      return formatResponse(data);
    } catch (error) {
      return handleError(error, { operation: 'getById', id });
    }
  },

  // Update inventory levels
  async update(id: string, data: any): Promise<ApiResponse<Inventory>> {
    try {
      const updatedData = await inventoryService.update(id, data);
      return formatResponse(updatedData);
    } catch (error) {
      return handleError(error, { operation: 'update', id, data });
    }
  },

  // Adjust stock levels
  async adjustStock(id: string, adjustment: any): Promise<ApiResponse<Inventory>> {
    try {
      const updatedData = await inventoryStockService.adjustStock(id, adjustment);
      return formatResponse(updatedData);
    } catch (error) {
      return handleError(error, { operation: 'adjustStock', id, adjustment });
    }
  },

  // Reserve stock for orders
  async reserveStock(productId: string, quantity: number, orderId: string): Promise<ApiResponse<void>> {
    try {
      await inventoryStockService.reserveStock(productId, quantity, orderId);
      return formatVoidResponse();
    } catch (error) {
      return handleVoidError(error, { operation: 'reserveStock', productId, quantity, orderId });
    }
  },

  // Release reserved stock
  async releaseStock(productId: string, quantity: number, orderId: string): Promise<ApiResponse<void>> {
    try {
      await inventoryStockService.releaseStock(productId, quantity, orderId);
      return formatVoidResponse();
    } catch (error) {
      return handleVoidError(error, { operation: 'releaseStock', productId, quantity, orderId });
    }
  },

  // Get low stock items
  async getLowStock(): Promise<ApiResponse<InventoryView[]>> {
    try {
      const { data, count } = await inventoryStockService.getLowStock();
      return formatResponse(data, count);
    } catch (error) {
      return handleError(error, { operation: 'getLowStock' });
    }
  },

  // Get items needing reorder
  async getNeedingReorder(): Promise<ApiResponse<InventoryView[]>> {
    try {
      const { data, count } = await inventoryStockService.getNeedingReorder();
      return formatResponse(data, count);
    } catch (error) {
      return handleError(error, { operation: 'getNeedingReorder' });
    }
  },

  // Get inventory alerts
  async getAlerts(params?: any): Promise<ApiResponse<any>> {
    try {
      const { data, count } = await inventoryAlertsService.getAlerts(params);
      return formatResponse(data, count);
    } catch (error) {
      return handleError(error, { operation: 'getAlerts', params });
    }
  },

  // Resolve inventory alert
  async resolveAlert(alertId: string, notes?: string): Promise<ApiResponse<void>> {
    try {
      await inventoryAlertsService.resolveAlert(alertId, notes);
      return formatVoidResponse();
    } catch (error) {
      return handleVoidError(error, { operation: 'resolveAlert', alertId, notes });
    }
  },

  // Get inventory movements/history
  async getMovements(id: string, params?: any): Promise<ApiResponse<any>> {
    try {
      const movementParams = {
        productId: id,
        fromDate: params?.dateFrom,
        toDate: params?.dateTo,
        type: params?.type ? [params.type] : undefined,
        page: params?.page,
        pageSize: params?.limit
      };
      
      const { data, count } = await inventoryHistoryService.getStockMovements(movementParams);
      return formatResponse(data, count);
    } catch (error) {
      return handleError(error, { operation: 'getMovements', id, params });
    }
  },

  // Perform stock count
  async performStockCount(inventoryId: string, countedStock: number, notes?: string): Promise<ApiResponse<any>> {
    try {
      const result = await inventoryStockService.performStockCount(inventoryId, countedStock, notes);
      return formatResponse(result);
    } catch (error) {
      return handleError(error, { operation: 'performStockCount', inventoryId, countedStock });
    }
  },

  // Get inventory statistics
  async getStats(params?: any): Promise<ApiResponse<any>> {
    try {
      // This is a placeholder - implement actual stats retrieval
      const stats = {
        totalProducts: 0,
        lowStockCount: 0,
        outOfStockCount: 0,
        businessUnitId: params?.businessUnitId,
        category: params?.category,
        period: params?.period
      };
      return formatResponse(stats);
    } catch (error) {
      return handleError(error, { operation: 'getStats', params });
    }
  },

  // Bulk update inventory thresholds
  async bulkUpdateThresholds(updates: any[]): Promise<ApiResponse<void>> {
    try {
      await inventoryStockService.bulkUpdateThresholds(updates);
      return formatVoidResponse();
    } catch (error) {
      return handleVoidError(error, { operation: 'bulkUpdateThresholds', updateCount: updates?.length });
    }
  },

  // Bulk adjust stock levels
  // REMOVED: bulkAdjustStock method (Direct DB update) - superseded by Edge Function implementation
};

// Create retryable versions of API methods with appropriate retry strategies
export const inventoryApi = {
  list: createRetryableFunction(baseInventoryApi.list, readOperationRetryOptions),
  getByProductId: createRetryableFunction(baseInventoryApi.getByProductId, readOperationRetryOptions),
  getById: createRetryableFunction(baseInventoryApi.getById, readOperationRetryOptions),
  update: createRetryableFunction(baseInventoryApi.update, writeOperationRetryOptions),
  adjustStock: createRetryableFunction(baseInventoryApi.adjustStock, writeOperationRetryOptions),
  reserveStock: createRetryableFunction(baseInventoryApi.reserveStock, writeOperationRetryOptions),
  releaseStock: createRetryableFunction(baseInventoryApi.releaseStock, writeOperationRetryOptions),
  getLowStock: createRetryableFunction(baseInventoryApi.getLowStock, readOperationRetryOptions),
  getNeedingReorder: createRetryableFunction(baseInventoryApi.getNeedingReorder, readOperationRetryOptions),
  getAlerts: createRetryableFunction(baseInventoryApi.getAlerts, readOperationRetryOptions),
  resolveAlert: createRetryableFunction(baseInventoryApi.resolveAlert, writeOperationRetryOptions),
  getMovements: createRetryableFunction(baseInventoryApi.getMovements, readOperationRetryOptions),
  performStockCount: createRetryableFunction(baseInventoryApi.performStockCount, writeOperationRetryOptions),
  getStats: createRetryableFunction(baseInventoryApi.getStats, readOperationRetryOptions),
  bulkUpdateThresholds: createRetryableFunction(baseInventoryApi.bulkUpdateThresholds, bulkOperationRetryOptions)
  // bulkAdjustStock removed – handled by Edge Function via service layer
};

// Specific inventory item API implementation
export async function getInventoryItems(params: InventoryListParams = {}) {
  return await createRetryableFunction(getInventoryItemsImpl, readOperationRetryOptions)(params);
}

async function getInventoryItemsImpl(params: InventoryListParams = {}): Promise<ApiResponse<any[]>> {
  try {
    // Implementation details...
    const { data, count } = await inventoryService.getInventoryList(params);
    
    // Transform data for UI
    const transformedData = data.map(item => ({
      id: item.product_id,
      selected: false,
      status: item.status || 'active',
      itemNumber: item.sku,
      name: item.name,
      productType: item.product_type || 'Unknown',
      onHand: String(item.current_stock || 0),
      cost: item.unit_cost || 0,
      price: item.unit_price || 0
    }));
    
    return formatResponse(transformedData, count);
  } catch (error) {
    return handleError(error, { operation: 'getInventoryItems', params });
  }
}

// Export for use in the application
export default inventoryApi; 